<template>
  <div>
    <div v-if="note" class="comment-box" @click="showBookNote">
      <div>
        {{ note.main_title }}
      </div>
      <div v-html="note.content"></div>
    </div>
    <div v-else style="font-size: 14px">暂无精彩笔记</div>
  </div>
</template>
<script>
// import axios from '@/http'
import request from '../../http/request'
export default {
  inject: ['book_id'],
  data () {
    return {
      note: []
    }
  },
  methods: {
    /* 获取笔记 */
    getNoteList () {
      const params = {
        page: 1,
        limit: 1,
        book_id: this.book_id
      }
      request('/api/books/getBookNoteList', params, 'get').then((result) => {
        this.note = result[0]
      })
    },
    showBookNote () {
      this.$store.commit('book/showBookNote', this.note.note_id)
    }
  },
  mounted () {
    this.getNoteList()
  }
}
</script>
<style scoped lang="scss">
.comment-box {
  height: 195px;
  overflow-y: scroll;
  padding: 5px 0;
  font-size: 14px;
  cursor: pointer;
}
.comment-box::-webkit-scrollbar {
  display: none;
}
</style>
