<template>
  <el-drawer
    title="编辑节点"
    :visible.sync="drawer"
    direction="rtl"
    :before-close="handleClose">
    <div class="node-editor">
      <div v-if="!isImage">
        <div class="item">
          <span class="label">节点内容：</span>
          <el-input class="input" placeholder="请输入节点内容" v-model="data.label"></el-input>
        </div>
        <div class="item">
          <span class="label">宽度：</span>
          <el-slider
            v-model="data.width"
            :min="10"
            :max="400"
            :step="10"
            @change="updateSize"
            show-input>
          </el-slider>
        </div>
        <div class="item">
          <span class="label">高度：</span>
          <el-slider
            v-model="data.height"
            :min="10"
            :max="400"
            :step="10"
            @change="updateSize"
            show-input>
          </el-slider>
        </div>
        <div class="item">
          <span class="label">文字颜色：</span>
          <el-color-picker v-model="data.textColor"></el-color-picker>
        </div>
        <div class="item">
          <span class="label">背景颜色：</span>
          <el-color-picker v-model="data.backgroundColor"></el-color-picker>
        </div>
        <div class="item">
          <span class="label">边框</span>
          <el-checkbox v-model="data.border"></el-checkbox>
        </div>
      </div>
      <div v-else>
        <span class="label">节点内容：</span>
        <el-input class="input" placeholder="请输入节点内容" v-model="data.label"></el-input>
      </div>
      <div class="add-child-node" v-if="!this.data.isGroup">
        <div class="top">
         <div class="left-con">
            <h3>子元素</h3>
         </div>
          <i class="el-icon-plus" @click="addNode"/>
        </div>
        <div class="node-list">
          <div class="item" v-for="(item, idx) in childNodeList" :key="idx">
            <div class="top">
              <h3>子内容{{ idx + 1 }}</h3>
              <i class="el-icon-close" @click="deleteNode(item.id, idx)"></i>
            </div>
            <div class="content">
              <p v-if="!item.input" class="text" @click="item.input =true">{{ item.label }}</p>
              <el-input v-else maxLength="100"  type="textarea" autosize v-model="item.originLabel" @keydown.enter.native="updateLabel(item, idx)" @blur="blur(idx)" focus></el-input>
            </div>
          </div>
        </div>
        <div class="node-content" v-show="addChildNode">
          <el-input  type="textarea" autosize v-model="keywordContent" placeholder="请输入子节点名称" ></el-input>
          <el-button type="primary" size="small" @click="handleEnter">确认</el-button>
        </div>
      </div>

      <div class="bottom-con">
        <el-button type="primary" size="small" @click="handleUpdate">确定</el-button>
        <el-button class="cancel" size="small" @click="handleClose">取消</el-button>
      </div>
    </div>
  </el-drawer>
</template>
<script>
import { getRandomId, getTextWidth } from './util';

export default {
  data () {
    return {
      graph: null,
      data: {
        id: '',
        label: '',
        textColor: '#000000',
        backgroundColor: '#ffffff',
        border: true
      },
      childNodeList: [],
      keywordContent: '',
      addChildNode: false,
      visible: true, // 子节点是否隐藏
      isNodeDown: false, // 节点是否被按下
      isImage: false // 是否为图片节点
    }
  },
  props: ['drawer', 'currentNode'],
  watch: {
    blur (idx) {
      this.childNodeList[idx].input = false
    },
    drawer (val) {
      if (val) {
        if (this.currentNode.shape === 'image') {
          this.isImage = true
        }
        const { x, y, width, height } = this.currentNode.getBBox()
        let backgroundColor = this.currentNode.getAttrs()?.body?.fill || null
        if (backgroundColor && backgroundColor === 'transparent') {
          backgroundColor = '#fff'
        }
        this.data = {
          x,
          y,
          id: this.currentNode.id,
          label: this.currentNode?.arrts?.originLabel || this.currentNode.label,
          shape: this.currentNode.shape,
          width,
          height,
          textColor: this.currentNode.attr()?.text?.fill || '',
          backgroundColor,
          border: !!this.currentNode.attr()?.body?.strokeWidth || '',
          isGroup: this.currentNode.getData()?.isGroup ?? false
        }
        this.graph = window.graph
        const nodeList = this.graph.getNodes()
        const list = nodeList.filter(item => {
          if (item?.data?.id?.includes(this.currentNode.id) && item?.data?.id !== this.currentNode.id) {
            return item
          }
        })
        this.childNodeList = list.map((item) => {
          return {
            label: item.label,
            originLabel: item.data?.originLabel || item.label,
            id: item.id,
            input: false
          }
        })
      }
    }
  },
  methods: {
    hideNode () {
      const nodeList = this.graph.getNodes()
      nodeList.filter(item => item.id.includes(this.currentNode.id) && item.id !== this.currentNode.id).forEach(element => {
        element.setVisible(false)
      })
    },
    showNode () {
      const nodeList = this.graph.getNodes()
      nodeList.filter(item => item.id.includes(this.currentNode.id) && item.id !== this.currentNode.id).forEach(element => {
        element.setVisible(true)
      })
    },
    clicKEnter (event) {
      if (event.key === 'Enter') {
        this.handleUpdate()
      }
    },
    updateSize (val) {
      // 是否为圆形
      if (this.data.shape === 'circle') {
        this.data.width = val
        this.data.height = val
      }
    },
    handleClose () {
      this.$emit('update:drawer', false)
    },
    handleUpdate () {
      this.$emit('update:node', this.data)
    },
    addNode () {
      this.addChildNode = true
    },
    deleteNode (id, idx) {
      this.childNodeList.splice(idx, 1)
      const node = this.graph.getCellById(id)
      this.graph.removeCell(node)
    },
    updateLabel (item, idx) {
      const originLabel = item.originLabel
      let label = originLabel
      let isMore = false
      if (label.length > 40) {
        label = label.substring(0, 40) + '...'
        isMore = true
      }
      this.childNodeList[idx].label = originLabel
      this.childNodeList[idx].input = false
      const node = this.graph.getCellById(item.id)
      node.label = label
      node.setData({
        isMore,
        originLabel
      })
    },
    handleEnter (e) {
      if (!this.keywordContent) { return }
      const id = getRandomId(this.data.id)
      this.childNodeList.push({
        id,
        label: this.keywordContent,
        originLabel: this.keywordContent,
        input: false
      })
      this.addChildNode = false
      const width = getTextWidth(this.keywordContent)
      this.$emit('add:node', {
        idx: this.childNodeList.length - 1,
        id,
        ids: this.childNodeList.map((item) => item.id),
        data: this.data,
        width,
        total: this.childNodeList.length,
        label: this.keywordContent
      })
      this.keywordContent = ''
    }
  }

}
</script>

<style lang="scss" scoped>
:deep(.el-slider) {
  width: 360px;
}
:deep(.el-slider__button) {
  width: 8px;
  height: 8px;
}
.node-editor{
  padding: 0 20px;
  .item{
    margin-top: 5px;
    .top{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    h3{
      color: #000;
      font-size: 16px;
    }
  }
  .label{
    display: inline-block;
    width: 100px;
    text-align: left;
  }
  .input{
    width: 240px;
  }
  .bottom-con{
    margin-top: 30px;
    .cancel{
      margin-left: 30px;
    }
  }
}
.add-child-node{
  margin-top: 20px;
  .top{
    display: flex;
    align-items: center;
  }
  .left-con{
    display: flex;
    gap: 4px;
  }
  .el-icon-plus{
    margin-left: 6px;
    font-size: 20px;
    cursor: pointer;
  }
  .node-content{
    margin-top: 6px;
    display: flex;
    gap: 8px;
    .el-input{
      width: 240px;
    }
  }
  .node-list {
    margin-top: 6px;
    .item{
      color: #999;
      .el-icon-close{
        cursor: pointer;
        margin-left: 4px;
      }
    }
    .el-input{
      width: 240px;
      margin-left: 8px;
    }
    .text{
      color: #1989fa;
    }
  }
}
</style>
