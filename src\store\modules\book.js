const book = {
  namespaced: true,
  state: {
    choosedBookId: 0, // 知觉: 228682

    isShowBookDetail: false,
    isShowPopup: false,
    isShowBookTopic: true,
    isShowBookScoreInfo: false,
    isShowBookAuthor: false,
    isShowTag: false,
    isShowBookNote: false,
    isShowPBookDetail: false,
    // 视频的弹窗
    isShowBookVideo: false,
    // 视频的当前item:
    bookVideoItem: null,
    // 书本详情pdf链接
    bookPDFUrl: '',

    scoreType: '',
    bookScore: [],
    bookCover: '',
    curAuthorInfo: [],

    curTagInfo: [],

    echartsData: null
  },
  mutations: {
    // 设置视频弹窗的详情信息
    setBookVideoItem (state, val) {
      state.bookVideoItem = val
    },
    // 设置书本详情pdf链接
    setBookPDFUrl (state, val) {
      state.bookPDFUrl = val
    },
    showDetails (state) {
      state.isShowBookDetail = true
    },
    closeDetails (state) {
      state.isShowBookDetail = false
    },
    showPopup (state) {
      state.isShowPopup = true
    },

    closePopup (state) {
      state.isShowPopup = false
      state.isShowBookScoreInfo = false
      state.isShowBookAuthor = false
      state.isShowTag = false
      state.isShowBookNote = false
      state.isShowPBookDetail = false
      // state.isShowBookTopic = false;
    },

    // 打分
    showBookScoreInfo (state, type) {
      state.isShowPopup = true
      state.isShowBookScoreInfo = true
      state.isShowBookTopic = false
      state.isShowBookNote = false
      state.scoreType = type
    },
    // 作者
    showBookAuthor (state, item) {
      state.isShowPopup = true
      state.isShowBookAuthor = true
      state.isShowBookTopic = false
      state.isShowBookScoreInfo = false
      state.isShowBookNote = false
      state.curAuthorInfo = item
    },
    closeBookAuthor (state) {
      state.isShowPopup = false
      state.isShowBookAuthor = false
      state.isShowBookNote = false
    },
    // Tag
    showTag (state, item) {
      state.isShowPopup = true
      state.isShowTag = true
      state.isShowBookScoreInfo = false
      state.isShowBookTopic = false
      state.isShowBookAuthor = false
      state.isShowBookNote = false
      state.curTagInfo = item
    },
    // Topic
    showBookTopic (state) {
      state.isShowPopup = true
      state.isShowTag = false
      state.isShowBookScoreInfo = false
      state.isShowBookAuthor = false
      state.isShowBookNote = false
      state.isShowBookTopic = true
    },

    // booknote
    showBookNote (state, item) {
      state.isShowPopup = true
      state.isShowTag = false
      state.isShowBookScoreInfo = false
      state.isShowBookAuthor = false
      state.isShowBookTopic = false
      state.isShowBookNote = true
      state.curBookNoteId = item
    },

    // showPBookDetail  点击弹窗的显示
    showPBookDetail (state, item) {
      // 点击遮罩层的显示与隐藏
      state.isShowPopup = true

      state.isShowTag = false
      state.isShowBookScoreInfo = false
      state.isShowBookAuthor = false
      state.isShowBookTopic = false
      state.isShowBookNote = false
      state.isShowPBookDetail = true
    },
    // 点击视频遮罩层
    showBookVideo (state) {
      // 点击遮罩层的显示与隐藏
      state.isShowPopup = true

      state.isShowTag = false
      state.isShowBookScoreInfo = false
      state.isShowBookAuthor = false
      state.isShowBookTopic = false
      state.isShowBookNote = false
      // state.isShowPBookDetail = true;
      state.isShowBookVideo = true
    },

    SET_CHOSED_BOOK_ID (state, id) {
      state.choosedBookId = id
    },

    SET_SCORE_DATA (state, data) {
      state.scoreData = data
    },

    SET_BOOKCOVER_DATA (state, data) {
      state.bookCover = data
    },

    SET_ECHARTS_DATA (state, data) {
      state.echartsData = data
    },

    SET_BOOKNOTE_DATA (state, data) {}
  },
  actions: {},
  getters: {
    // 对 author_sign 属性进行处理的 getter
    processedAuthorSign (state) {
      // 获取 curAuthorInfo
      const authorInfo = state.curAuthorInfo
      // 如果 curAuthorInfo 和 author_sign 都存在
      if (authorInfo && authorInfo.author_sign) {
        // 将 author_sign 中的 <img> 标签替换为空字符串，返回处理后的结果
        return authorInfo.author_sign.replace(/<img[^>]*>|<br>/g, '')
      }
      // 否则返回空字符串
      return ''
    }
  }
}

export default book
