import request from '@/http/request'
const user = {
  namespaced: true,
  state: {
    userInfo: JSON.parse(localStorage.getItem('userinfo')),
    userInfoMore: {
      permission: null, // 存储用户身份信息的状态
      is_enterprise: null,
      memberIdentity: 0,
    }
  },
  mutations: {
    SET_USERINFO (state) {
      state.userInfo = JSON.parse(localStorage.getItem('userinfo'))
    },

    SET_USERINFOMORE (state, hasPermission) {
      state.userInfoMore.permission = hasPermission
    },
    SET_ISENTERPRISE (state, isEnterprise) {
      state.userInfoMore.is_enterprise = isEnterprise
    },
    SET_MEMBERIDENTITY (state, memberIdentity) {
      state.userInfoMore.memberIdentity = memberIdentity
    }

  },
  actions: {
    async getUserInfoMore ({ commit }) {
      // 获取用户权限
      try {
        const params = {
          identity: 'privileged_user'
        }

        request('/api/member/checkMemberIdentity', params, 'get').then((res) => {
          const hasPermission = true
          commit('SET_USERINFOMORE', hasPermission)
        }).catch(() => {
          const hasPermission = false
          commit('SET_USERINFOMORE', hasPermission)
        })
      } catch (error) {
        console.error(error)
      }
    },

    async getUserInfoEnterprise ({ commit }) {
      // 判断用户是否为企业用户
      try {
        const memberId = JSON.parse(localStorage.getItem('userinfo'))?.userid
        if (memberId) {
          const params = {
            member_type: 'self'
          }
          let isEnterprise = null
          let memberIdentity = 0
          request('/mobile/member/index', params).then((res) => {
            // 是否为管理员
            memberIdentity = res.member_identity
            isEnterprise = res.is_enterprise
            commit('SET_ISENTERPRISE', isEnterprise)
            commit('SET_MEMBERIDENTITY', memberIdentity)
          })
        }
      } catch (error) {
        console.error(error)
      }
    }
  },
  getters: {}
}

export default user
