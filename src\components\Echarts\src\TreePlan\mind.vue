<template>
  <div class="mind-map-box">
    <div id="mindMapContainer"></div>
    <Toolbor
    :treeObj="treeObj"
    :readonly="readonly"
    :mindMap="mindMap"
    :currentNode="currentNode"
    :isStart="isStart"
    :isEnd="isEnd"/>
    <SearchBar v-if="mindMap" :mindMap="mindMap"/>
    <ContextMenu v-if="mindMap" :mindMap="mindMap"/>
    <ShortcutKey v-if="mindMap" :mindMap="mindMap"/>
    <Structure v-if="mindMap" :mindMap="mindMap"/>
    <Setting v-if="mindMap" :mindMap="mindMap" />
    <Style v-if="mindMap" :mindMap="mindMap"/>
    <el-dialog title="新增脑图" :visible.sync="addMindVisible" width="40%">
      <el-input v-model="mindTitle" placeholder="请输入脑图名称" clearable />
      <span slot="footer" class="dialog-footer">
        <el-button @click="addMindVisible = false">取 消</el-button>
        <el-button type="primary" @click="addMindMap">确 定</el-button>
      </span>
    </el-dialog>
    <ExportComponent></ExportComponent>
    <ImportComponent></ImportComponent>
    <List></List>
    <FormulaSidebar v-if="mindMap" :mindMap="mindMap"></FormulaSidebar>
  </div>
</template>

<script>
import axios from "@/http";
import MindMap from "simple-mind-map";
import Export from 'simple-mind-map/src/plugins/Export.js';
import Search from 'simple-mind-map/src/plugins/Search.js';
import Select from 'simple-mind-map/src/plugins/Select.js';
import Watermark from 'simple-mind-map/src/plugins/Watermark.js';
import NodeImgAdjust from 'simple-mind-map/src/plugins/NodeImgAdjust.js'
import RichText from 'simple-mind-map/src/plugins/RichText.js'
import Formula from 'simple-mind-map/src/plugins/Formula.js'
import Vue from 'vue';
import ContextMenu from "./components/ContextMenu.vue";
import ExportComponent from "./components/Export.vue";
import ImportComponent from './components/Import.vue';
import List from './components/List.vue';
import SearchBar from "./components/SearchBar.vue";
import Setting from './components/Setting.vue';
import ShortcutKey from "./components/ShortcutKey.vue";
import Structure from "./components/Structure.vue";
import FormulaSidebar from "./components/FormulaSidebar.vue";
import Style from "./components/Style.vue";
import Toolbor from "./components/Toolbor.vue";
import Excel from './excel.esm.min.js';
import { hideLoading, showLoading } from './utils/loading';

MindMap.usePlugin(RichText, {
  fontFamilyList: '微软雅黑, Microsoft YaHei'
})
MindMap.usePlugin(Formula)
MindMap.usePlugin(Search)
MindMap.usePlugin(Select)
MindMap.usePlugin(Export)
MindMap.usePlugin(Watermark)
MindMap.usePlugin(NodeImgAdjust)
const events = ['node_contextmenu', 'data_change', 'node_click', 'draw_click', 'node_active', 'showNodeLink', 'mouseup', 'svg_mousedown', 'generalization_node_contextmenu']
export default {
  props: {
    treeObj: {
      type: Object,
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    },
    width: {
      type: String
    },
    height: {
      type: String
    }
  },
  components: {
    Toolbor,
    SearchBar,
    ContextMenu,
    ShortcutKey,
    ExportComponent,
    ImportComponent,
    Structure,
    Setting,
    Style,
    FormulaSidebar,
    List
  },
  data() {
    return {
      num: 1,
      mindMap: null,
      isStart: false,
      isEnd: false,
      currentNode: null,
      currendMindId: null,
      isFirst: true,
      addMindVisible: false,
      mindTitle: ''
    }
  },
  methods: {
    // 递归处理节点
    processNode(item) {
      let node = {
        data: {
          text: item.content
        }
      };

      // 如果存在子节点，递归处理
      if (item.children && item.children.length > 0) {
        node.children = item.children.map(child => this.processNode(child));
      }

      return node;
    },
    openAddMindMap() {
      this.addMindVisible = true
    },

    addMindMap() {
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      this.addMindVisible = false
      axios.post('/ai/mind/add', {
          title: this.mindTitle,
          memberId: userInfo.userid
      }).then(res => {
        if(res.code === 200) {
          this.$message.success('添加成功')
          window.open(`#/mind/${res.data}`, '_blank');
        } else {
          this.$message.error('添加失败')
        }
      })
    },

    getMindMapData() {
      debugger
      console.log('111')
      if(!this?.treeObj?.children?.length) {
        return {
          "data": {
              "text": this.treeObj.content
          },
          "children": []
        }
      }
      let obj = {
        data: {
          text: this.treeObj.content
        },
        children: this.treeObj.children.map(item => this.processNode(item))
      }
      return obj;
    },
    getMindMap() {
      return new Promise((resolve, reject) => {
        if(this.treeObj?.detail) {
          resolve(JSON.parse(this.treeObj.detail))
        } else {
          resolve(null)
        }
      })
    },
    execCommand(...args) {
      this.mindMap.execCommand(...args)
    },
    allEvent() {
      this.busEvent()
      this.$bus.$on('execCommand', this.execCommand)
      this.$bus.$on('setData', this.setData)
      events.forEach((evt) => {
        this.mindMap.on(evt, (...args) => {
          this.$bus.$emit(evt, ...args)
        })
      })
    },
    busEvent() {
      this.$bus.$on('openAddMindMap', this.openAddMindMap)
      this.$bus.$on('export', (obj) => {
        this.mindMap.export(obj)
      })
      this.$bus.$on('data_change', data => {
          const params = {
            id: this.treeObj.mind_map_id,
            detail: JSON.stringify(data)
          }
          if(!this.isFirst) {
            axios.post('/ai/mind/save', params).then(res => {
              this.$message.success('保存成功')
            })
          }
          this.isFirst = false
      })
    },
    // 动态设置思维导图数据
    setData(data) {
      showLoading()
      if (data.root) {
        this.mindMap.setFullData(data)
      } else {
        this.mindMap.setData(data)
      }
      this.mindMap.view.reset()
      setTimeout(() => {
        hideLoading()
      }, 1000)
    },
    async init() {
      let data = await this.getMindMap()
      if(!data) {
        data = this.getMindMapData()
      }
      this.mindMap = new MindMap({
        el: document.getElementById('mindMapContainer'),
        data,
        enableShortcutOnlyWhenMouseInSvg: false,
        enableFreeDrag: false,
        readonly: this.readonly,
        // 是否仅搜索当前渲染的节点，被收起的节点不会被搜索到
        isOnlySearchCurrentRenderNodes: false,
      });
      this.allEvent()
      this.mindMap.on('back_forward', (index, len) => {
          this.isStart = index <= 0
          this.isEnd = index >= len - 1
      })
      this.mindMap.on('node_click', (e, node) => {
        this.currentNode = node
        this.$bus.$emit('node_click', e, node)
      })
      this.mindMap.on('draw_click', (e, node) => {
        this.currentNode = null
        this.$bus.$emit('draw_click', e, node)
      })
      this.mindMap.on('node_contextmenu', (e, node) => {
        this.currentNode = node
        this.$bus.$emit('node_contextmenu', e, node)
      })
      this.mindMap.view.setScale(0.8)
      this.mindMap.view.translateX(-500)
      this.mindMap.view.translateY(100)
    },
  },
  beforeDestroy() {
    this.$bus.$off('execCommand', this.execCommand)
  },
  async mounted() {
    showLoading()
    await this.init()
    if(!this.currendMindId) {
      this.currendMindId = this.treeObj.id
    }
    this.mindMap.addPlugin(Excel)
    Vue.prototype.Excel = Excel
    this.$nextTick(() => {
      hideLoading()
    })
  }
}

</script>
<style lang="scss" scoped>
#mindMapContainer{
  min-height: 200px;
  min-width: 200px;
  width: 100%;
  height: 100%;
}
#treePlan{
  background-color: #fff;
}
.mind-map-box{
  height: 100vh;
  position: relative;
  width: 100%;
}
</style>
