<template>
  <div class="editor">
    <div class="top">
      <!-- leftClick -->
      <div class="top-left">
        <div class="nav">
          <router-link class="item" to="/index">
            <img style="" src="@/assets/editor/homepage.png" alt="" />
          </router-link>
        </div>
        <div class="top-title">Bookor Note</div>
      </div>
      <div class="top-middle">
        <!-- <img @click="gotoPaperMatrix" src="./img/matrix.png" alt="" /> -->
        <Search @getUrlData="handleGetUrlData" @openSearchBar="openSearchBar"/>
      </div>
      <div class="right-box">
        <el-tooltip class="item" effect="dark" content="上传文件" placement="bottom">
          <el-popover placement="right" trigger="click" ref="pdfPopover" v-model="isShowUpload">
          <div
            class="pdf-choose-box"
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <input
              class="pdf-choose-btn"
              type="file"
              accept=".pdf,.docx,.epub"
              ref="pdfInput"
              @change="onUploadFile"
            />
          </div>
          <div class="doc-upload" slot="reference" @click="closeDocSwitch">
              <img src="@/assets/editor/upload.png" alt="" />
            </div>
        </el-popover>
        </el-tooltip>

        <router-link class="item" to="/personal">
          <!-- 个人用户 -->
          <img
            v-if="userinfo?.member_avatar"
            class="photo"
            :src="userinfo?.member_avatar"
          />
          <img v-else class="photo" src="@/assets/editor/icon.png" />
        </router-link>
      </div>
    </div>
    <div class="box" ref="box" @click="closeDocSwitch">
      <router-view
       :note-content="noteContent"
       :right-expanded="rightExpanded"
       @getArticleData="handleGetArticleData"
       @setMp3Url="setMp3Url"
       @openAi="openAi"
       >
      </router-view>
      <div
          class="right-btn"
          @click="showRight"
          v-if="!rightExpanded && !isMobile"
        >
          <img src="@/assets/editor/fold.png" alt="" />
      </div>
      <el-drawer
        :visible.sync="rightExpanded"
        direction="rtl"
        :before-close="hiddenRight">
        <AICHAT
          :rightExpanded="rightExpanded"
          :currentInfo="currentInfo"
          :defaultAi="defaultAi"/>
      </el-drawer>
    </div>
    <!-- @getUrlData="openSearchBarUrl" -->
     <!-- @getLocalUrlData="handleGetLocalUrlData" -->
     <!-- @close="handleCloseSearch" -->
     <!--  -->
    <SearchBar
      :searchList="searchList"
      :searchKeyword="this.searchKeyword"
      :searchTotal="searchTotal"
      :searchDrawer="searchDrawer"
      @getUrlData="openSearchBarUrl"
      @openArticleCollect="articleFormVisible = true"
      @updateSearchDrawer="updateSearchDrawer"
     >
    </SearchBar>
    <el-dialog title="文章收录" :visible.sync="articleFormVisible">
      <el-input v-model="articleUrl" placeholder="请输入网页链接" focus></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="articleFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="addArticle">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Search from '@/components/editor/search'
// import DocumentSwitch from '@/components/editor/document-switch'
import AICHAT from '@/components/ai-chat'
import SearchBar from '@/components/editor/searchBar'
import ePub from 'epubjs'
import { v4 as uuidv4 } from 'uuid'
import { mapState } from 'vuex'

// import axios from 'axios'
import Fetch from '@/http/fetch.js'
import request from '@/http/request.js'
import requestFromData from '@/http/requestFromData.js'
import { EventBus } from '@/main.js'
export default {
  name: 'Editor',
  components: {
    SearchBar,
    Search,
    // DocumentSwitch,
    AICHAT
  },
  data () {
    return {
      noteContent: '', // 文档内容
      articleUrl: '', // 收录文章链接
      articleFormVisible: false, // 收录文章dialog
      isShowUpload: false, // 是否显示上传文件弹窗
      leftLineMove: false,
      rightLineMove: false,
      leftExpanded: true,
      rightExpanded: false,
      defaultAi: null,
      // defaultText: '', // AI 默认回答
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      // isShowDocSwitch: false,
      isInput: true, // input输入框
      link: '',
      isDraft: false,
      draftId: '',
      pdfUrl: '', // 在线的pdf地址
      pdfFileUrl: null, // 本地的pdf地址
      isShowPdfIframe: false,
      wordHtml: null,
      wordId: null,
      wordTitle: null,
      epubFile: null,
      isMobile: false,
      // 编辑bookornote的标题和内容
      editBookorNoteContent: '',
      isUpdateBookorNote: false,
      updateid: null,

      // 搜索论文的pdf和epub
      isShowThesisPdf: false,
      isShowThesisEpub: false,

      // epub的变量
      // ifTitleAndMenuShow: true,
      bookAvailable: false,
      navigation: null,
      // progress: 0,
      totalPages: 0,

      currentPage: 1,
      isScrolling: false, // 滚动epub的节流变量

      pdfPaperTitle: '', // 外部论文的标题-用于转换为word后保存
      pdfLocalTitle: '', // 本地Pdf的标题-用于转换为word后保存
      epubLocalTitle: '', // 本地epub的标题-用于转换为word后保存

      curPaperId: -1, // 当前文章id
      currentInfo: null, // 当前文档基础信息
      entry_position: '', // 当前划线在文档中的位置
      uniqueEntryPositions: [],
      // 导读文章的标题&内容
      publicArticleTitle: '',
      publicArticleContent: '',
      publicArticleUrl: '',
      searchDrawer: false,
      searchKeyword: '',
      searchList: [],
      searchTotal: null
    }
  },
  computed: {
    ...mapState({
      choosedResult: (state) => state.choosedResult
    }),
    ...mapState(['uploadedFiles'])
  },
  watch: {
    choosedResult (newValue) {
      this.$store.commit('updateChoosedResult', newValue)
    }
  },
  created () {
    this.handleResize() // 在 created 钩子函数中调用一次 handleResize 方法
  },
  mounted () {
    // 论文矩阵页跳转而来的导读
    const articleId = this.$route.query.articleId
    const paperId = this.$route.query.paperId
    // 如果没有这个变量
    if (!articleId) {
      this.link = ''
    } else {
      this.$router.push({ path: '/editor/note' })
    }
    const url = this.$route.query.url
    this.currentInfo = {
      url,
      paperId,
      id: paperId
    }

    // 论文矩阵页跳转而来的论文Pdf
    const paperInpapermatrix = this.$route.query.paperInpapermatrix
    // 如果没有这个变量
    if (paperInpapermatrix) {
      this.pdfFileUrl = paperInpapermatrix
      this.isShowEditNew = false
      this.isShowPdfLocal = true
      this.isShowPdfIframe = true
      this.$router.push({ path: '/editor' })
    }
    window.addEventListener('mouseup', function () {
      this.leftLineMove = false
      this.rightLineMove = false
    })
    window.addEventListener('resize', this.handleResize)
  },
  methods: {
    setMp3Url (data) {
      this.currentInfo = {
        ...data,
        isMp3: true,
      }
    },
    leftClick () {
      this.$router.push({
        name: 'note'
      })
    },
    addArticle () {
      if (!this.articleUrl) {
        return this.$message({
          message: '请输入文章链接',
          type: 'error'
        })
      }
      request('/api/Keywords/gatherMemberNoteByUrl', {
        url: this.articleUrl
      }).then((result) => {
        this.articleFormVisible = false
        const content = result
        this.noteContent = content
        this.$store.commit('SET_URL_CONTENT', content)
        this.$router.push({
          name: 'note',
          query: {
            origin: 'Crawling'
          }
        })
        this.$message({
          message: '收录成功',
          type: 'success'
        })
      }).catch((result) => {
        this.$message.error({
          message: result?.message || '收录失败',
          type: 'error'
        })
      })
    },
    editDraftId (draftId) {
      this.draftId = draftId
    },
    handleCloseSearch () {
      this.searchDrawer = false
    },
    updateSearchDrawer (val) {
      this.searchDrawer = val
    },
    addNote () {
      if (this.$route.name === 'note') {
        EventBus.$emit('createNode')
      } else {
        this.$router.push({
          name: 'note',
          query: {
            id: ''
          }
        })
      }
    },
    addDraft () {
      this.$router.push({
        name: 'draft',
        query: {
          id: ''
        }
      })
    },
    handleToDraft (id) {
      this.isShowEditIframe = false
      this.isShowEditNew = false
      this.isShowPdfIframe = false
      this.isShowPreviewArticle = false
      this.isShowPdfEmbed = false
      // this.isShowDocSwitch = false
      this.isDraft = true
      this.draftId = id
      this.$refs.mid.style.background = '#fff'
    },
    showRight () {
      this.rightExpanded = true
    },
    hiddenRight () {
      this.rightExpanded = false
    },
    openAi (text, content) {
      this.defaultAi = {
        text,
        content
      }
      this.rightExpanded = true
    },
    showLeft () {
      this.leftExpanded = true
      this.$refs.left.style.width = this.$refs.box.clientWidth * 0.35
    },
    hiddenLeft () {
      const box = this.$refs.box
      this.leftExpanded = false
      this.$refs.mid.style.width = box.clientWidth + 'px'
    },
    // 确认是否为移动端
    handleResize () {
      this.isMobile = window.innerWidth < 768 // 这里只是读取 isMobile 的值
    },

    leavePage () {
      this.$message.error({
        message: '请保存内容',
        duration: 600
      })
    },

    // 展示文档切换
    // showDocSwitch () {
    //   this.isShowDocSwitch = !this.isShowDocSwitch
    //   if (this.isShowDocSwitch) {
    //     EventBus.$emit('refreshList')
    //   }
    // },
    closeDocSwitch () {
      // this.isShowDocSwitch = false
    },
    closeUpload () {
      this.isShowUpload = false
    },

    // 文章、笔记书评iframe
    handleGetArticleData (data) {
      this.currentInfo = data
    },
    openSearchBarUrl (data) {
      this.searchDrawer = false
      this.currentInfo = data
      if (data.type === 'article') {
        this.handleGetArticleData(data)
      }
      if (data.type === 'comment') {
        this.handleGetArticleData(data)
      }
      if (data.type === 'note') {
        this.handleGetArticleData(data)
      }
      if (data.type === 'draft') {
        this.handleToDraft(data.id)
      }
    },
    openSearchBar (keyword, list, total) {
      this.searchDrawer = true
      this.searchKeyword = keyword
      this.searchList = list
      this.searchTotal = total
    },
    // 获得论文地址
    async handleGetUrlData (data) {
      this.isShowEpub = false
      this.isShowNotePop = false
      this.entry_position = ''
      this.currentInfo = data
      if (data.pdfUrl) {
        this.isShowPdfEmbed = true
        // this.isShowDocSwitch = false
        this.isShowEditIframe = false
        this.isShowPreviewArticle = false
        this.isShowEditNew = false
        this.isShowPdfIframe = false
      }
      if (data.pdfUrl.endsWith('.pdf') || data.pdfUrl.contains('/pdf/')) {
        // pdf格式
        this.isShowThesisPdf = true
        this.isShowThesisEpub = false
        this.pdfUrl = data.pdfUrl
        this.pdfPaperTitle = data.paper_title
      } else {
        this.$message({
          message: '无法识别文件格式',
          type: 'error',
          duration: 1000
        })
      }
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    // 本地上传
    async onUploadFile () {
      const file = this.$refs.pdfInput.files[0]
      if (!file) {
        return
      }
      const fileName = file.name.toLowerCase()
      if (fileName.endsWith('.pdf')) {
        const url = URL.createObjectURL(file)
        const id = uuidv4()
        this.uploadedFiles.unshift({
          title: file.name,
          url,
          id,
          type: 'pdf'
        })
      } else if (fileName.endsWith('.docx')) {
        const id = uuidv4()
        const url = URL.createObjectURL(file)
        this.$router.push({
          name: 'docx',
          query: {
            paperId: id,
            title: fileName,
            url
          }
        })
      } else if (fileName.endsWith('.epub')) {
        const id = uuidv4()
        const url = URL.createObjectURL(file)
        this.$router.push({
          name: 'epub',
          query: {
            paperId: id,
            title: fileName,
            url
          }
        })
      }
      this.apiUploadFilesNew(file)
      // this.$store.commit('updateUploadedFiles', this.uploadedFiles)
      this.isShowUpload = false
    },
    // 上传到服务器
    async apiUploadFiles (file) {
      const params = {
        upload_type: 'mime'
      }

      const res = await Fetch.postFile(file, params)
      if (res.code === '100') {
        this.$message({
          message: '上传错误，请重新提交',
          type: 'error',
          duration: 1000
        })
        return
      }
      if (res.code === 200) {
        const completeUrl = res.result.complete_url
        this.wordHtml = completeUrl
        // 设置pdf的url地址
        this.$store.commit('setUploadData', completeUrl)
        const fileName = file.name.toLowerCase()
        // 上传到用户的本地空间
        this.apiAddFilesandWord(fileName, completeUrl)
      }
    },
    // 上传到服务器(新)
    async apiUploadFilesNew (file) {
      const params = {
        upload_type: 'mime',
        filename: file.name,
        file
      }
      requestFromData('/api/upload/uploadPaper', params).then((result) => {
        const completeUrl = result.complete_url
        this.wordHtml = completeUrl
        // 设置pdf的url地址
        this.$store.commit('setUploadData', completeUrl)
        const fileName = file.name.toLowerCase()
        this.closeUpload()
        // 上传到用户的本地空间
        this.apiAddFilesandWord(fileName, completeUrl)
      }).catch(() => {
        this.$message({
          message: '上传错误，请重新提交',
          type: 'error',
          duration: 1000
        })
      })
    },

    // 上传到个人数据库里
    apiAddFilesandWord (title, url, changeToWord) {
      if (changeToWord) {
        title = '[word]' + title
      }
      const params = {
        paper_title: title,
        paper_url: url,
        paper_type: 'others' // 区分上传的类型 other表示上传的文档和word
      }
      request('/api/Paper/addPaper', params).then((result) => {
        this.currentInfo = {
          id: result.paper_url,
          type: 'others',
          url: result.paper_url
        }
        if(result.paper_url.endsWith('.pdf') || result.paper_url.contains('/pdf/')) {
          this.$router.push({
            name: 'pdf',
            query: {
              paperId: result.paper_id,
              title,
              url: result.paper_url
            }
          })
        }
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 1000
        })
        // 清空页面
        this.editBookorNoteContent = ''
        // 刷新docswitch列表
        // this.$refs.docswitch.refreshFileFileList()
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // epub翻页
    prevPage () {
      if (this.rendition && this.totalPages) {
        this.isShowNotePop = false
        this.rendition.prev()
        this.currentPage-- // 向前翻页时更新 currentPage
        if (this.currentPage <= 1) {
          this.currentPage = 1
        }
      }
    },
    nextPage () {
      if (this.rendition && this.totalPages) {
        try {
          this.isShowNotePop = false
          this.rendition.next()
          this.currentPage++ // 向后翻页时更新 currentPage
          if (this.currentPage >= this.totalPages) {
            this.currentPage = this.totalPages
          }
        } catch (error) {
          alert('出错，请重试或检查 EPUB 文件格式。')
        }
      }
    },

    handleWheel (event) {
      if (this.totalPages) {
        this.isScrolling = true

        if (event.deltaY < 0) {
          this.prevPage()
        } else if (event.deltaY > 0) {
          this.nextPage()
        }

        setTimeout(() => {
          this.isScrolling = false
        }, 200) // 设置节流时间间隔为200毫秒
      }
    },

    // 读取epub信息并挂载
    async readEpubMes (file, entryPosition) {
      this.book = ePub(file)
      await this.book.ready
      if (this.book !== null && typeof this.book !== 'undefined') {
        this.rendition = this.book.renderTo('read', {
          flow: 'paginated',
          width: '100%',
          height: '93vh',
          spread: 'none' // 设置为单页显示
        })

        this.rendition.on('rendered', async () => {
          const cfiList = this.uniqueEntryPositions
          cfiList.forEach((cfi) => {
            const marker = document.createElement('span')
            marker.style.textDecoration = 'underline' // 设置下划线
            marker.style.textDecorationStyle = 'dashed' // 设置下划线样式
            marker.style.textDecorationColor = '#ffa500' // 设置下划线颜色
            marker.style.textDecorationOffset = '0.2em' // 设置下划线的偏移量为0.2em
            marker.style.lineHeight = '1.5' // 设置行高为1.5倍
            marker.classList.add('my-marker')

            const range = this.rendition.getRange(cfi.entry_position)
            if (range) {
              range.surroundContents(marker)

              // 创建并添加红色背景的div元素
              const div = document.createElement('div')
              div.innerText = cfi.keywords
              div.style.backgroundColor = 'rgba(165, 165, 249, 0.5)'
              div.style.display = 'inline'
              div.style.padding = '2px 4px'
              div.style.borderRadius = '10px'
              marker.parentNode.insertBefore(div, marker.nextSibling)
            }
          })
        })

        if (entryPosition) {
          this.rendition.display(entryPosition)
        } else {
          this.rendition.display()
        }

        // 添加选中事件监听器
        this.rendition.on('selected', (cfiRange, contents) => {
          this.isShowNotePop = false
          // 获取选中范围
          const range = contents.window.getSelection().getRangeAt(0)
          if (!range.toString().trim()) {
            this.isShowNotePop = false
          } else {
            const rect = range.getBoundingClientRect()
            let selectionLeft = rect.left
            const pageWidth = window.innerWidth * 0.6

            if (selectionLeft > pageWidth) {
              selectionLeft =
                selectionLeft -
                pageWidth * Math.floor(selectionLeft / pageWidth)
            }

            const relativeTop = range.getBoundingClientRect().top
            const relativeLeft = selectionLeft
            this.notePopTop = relativeTop
            this.notePopLeft = relativeLeft
            this.isShowNotePop = true

            this.selectText = range.toString()
            this.entry_position = cfiRange
            this.chapterInformation = this.chapterInformation_bookTitle
            for (let i = 0; i < contents.content.children.length; i++) {
              const element = contents.content.children[i]
              if (
                element.tagName.toLowerCase().startsWith('h1') ||
                element.tagName.toLowerCase().startsWith('h2')
              ) {
                this.chapterInformation =
                  this.chapterInformation_bookTitle + ' ' + element.textContent
                return
              }
            }
          }
        })
        // 获取locations对象 epubjs的钩子函数实现
        this.book.ready
          .then(() => {
            this.navigation = this.book.navigation

            return this.book.locations.generate()
          })
          .then((result) => {
            this.locations = this.book.locations
            this.bookAvailable = true
            // 获取总页数
            this.totalPages = this.locations.length()
          })
      }
    },

    // 卸载当前展示的epub文件
    destroyEpub () {
      if (this.book) {
        this.rendition.destroy()
        this.book = null
        this.locations = null
        this.navigation = null
        this.totalPages = 0
        this.currentPage = 1
        this.isShowNotePop = false
      }
    },

    // 获得本地上传文档的地址
    async handleGetLocalUrlData (data, entryPosition) {
      this.currentInfo = data
    },
    // epub的方法，拆组件之后不能正常渲染
    // 根据链接跳转到指定位置
    jumpTo (href) {
      this.rendition.display(href).then(() => {

      })
    },
    onProgressChange (progress) {
      const percentage = progress / 100
      const location =
        percentage > 0 ? this.locations.cfiFromPercentage(percentage) : 0
      this.rendition.display(location)
    },
    onPageChange (pageNumber) {
      const percentage = pageNumber / this.totalPages
      const location = this.locations.cfiFromPercentage(percentage)
      this.rendition.display(location)
      this.currentPage = pageNumber // 跳转页码时更新 currentPage
    },

    // // 跳转到论文矩阵
    // gotoPaperMatrix () {
    //   this.$router.push('/papermatrix')
    // },
    // 本地pdf滑选事件注册： 获取鼠标选中的文本
    // 查找大纲
    findOutlineByPageIndex (outline, pageIndex) {
      let previousOutline = null // 用于保存上一个目录项的引用
      for (let i = 0; i < outline.length; i++) {
        const currentOutline = outline[i]
        if (currentOutline.dest) {
          const destRef = currentOutline.dest[0]
          // 比较目录的num与当前页码对应的num
          if (destRef.num < pageIndex) {
            previousOutline = currentOutline // 记录上一个目录项
            continue // 如果num小于当前页码的num，则继续循环
          } else {
            break // 如果num大于当前页码的num，则退出循环
          }
        }
      }
      return previousOutline
    },
    // 获取位置信息
    getSelectedTextRect (range) {
      const rect = range.getBoundingClientRect()
      const span =
        range.startContainer.parentNode.nodeName === 'SPAN'
          ? range.startContainer.parentNode
          : null
      return {
        x: span?.style?.left,
        y: span?.style?.top,
        width: rect.width,
        height: rect.height
      }
    },
    // 监听是否需要划线
    addUnderlineToText () {
      const iframe = this.$refs.pdfViewer
      const viewerWindow = iframe.contentWindow
      const pdfDoc = viewerWindow.PDFViewerApplication.pdfDocument

      const viewportRect = iframe.getBoundingClientRect()
      const pageIndex = viewerWindow.PDFViewerApplication.page

      if (pageIndex === 0) {
        pdfDoc.getPage(pageIndex).then((page) => {
          page.getTextContent().then((textContent) => {
            textContent.items.forEach((textItem) => {
              const textRect = page
                .getViewport({ scale: 1 })
                .convertRect(
                  textItem.transform,
                  textItem.width,
                  textItem.height
                )

              if (
                textRect.top >= viewportRect.top &&
                textRect.bottom <= viewportRect.bottom
              ) {
                const underline = document.createElement('div')
                underline.classList.add('underline')
                underline.style.top = textRect.bottom + 'px'
                underline.style.left = textRect.left + 'px'
                underline.style.width = textRect.width + 'px'

                iframe.contentDocument.body.appendChild(underline)
              }
            })
          })
        })
      }
    },

    // eslint-disable-next-line camelcase
    async pool (wordId, paper_title) {
      const timer = setInterval(async () => {
        const result = await request('api/Paper/getAliyunWord', {
          id: wordId
        })
        if (result.body.Completed) {
          clearInterval(timer)
          this.hideLoading()
          if (result.body.Status === 'Success') {
            const url = result.body.Data[0].Url
            this.apiAddFilesandWord(paper_title, url, true)
          } else {
            this.$message({
              message: '转换失败',
              type: 'error',
              duration: 1000
            })
          }
        }
      }, 5000)
    },

    // 获取论文里的词条划线数据
    async apiGetPaperKeywords (paperId) {
      const params = {
        paper_id: paperId
      }
      request('/api/Keywords/getPaperKeywords', params).then((result) => {
        const entryList = result
        const keywords = entryList.map((item) => {
          return item.entry_position
        })
        localStorage.setItem('default_pdf', `[${keywords}]`)
        this.uniqueEntryPositions = entryList
      })
    },

    getContentText (con) {
      const el = document.createElement('div')
      el.innerHTML = con
      return el.textContent || el.innerText
    },

    // 获取导读的内容（分享文章的内容）
    async getPublicArticleById (id) {
      if (id) {
        const params = { id }
        request('api/wechatarticle/getArticleById', params).then((result) => {
          this.publicArticleTitle = result.title
          this.publicArticleUrl = result.url
          this.publicArticleContent = result.content_show || result.content
          const content = this.getContentText(this.publicArticleContent)
          this.currentInfo = {
            id: result.id,
            type: 'article',
            content: content
          }
        }).catch((result) => {
          this.publicArticleTitle = ''
          this.publicArticleContent = ''
          this.publicArticleUrl = ''
          this.$message({
            message: result.message,
            type: 'error',
            duration: 1000
          })
        })
      }
    },

    // 获取doc-switch里返回的词条信息
    handleGetEntryData (data) {
      this.getEntryPaperUrl(data)
    },

    // 获取选中词条对应文章的url
    getEntryPaperUrl (entryInfo) {
      request('/api/Paper/getPaperInfo', {
        paper_id: entryInfo.paper_id
      })
        .then((result) => {
          const data = {
            url: result.paper_url,
            type: 'epub',
            title: result.paper_title,
            id: result.paper_id
          }
          this.handleGetLocalUrlData(data, entryInfo.entry_position)
        })
    }
  },
  beforeRouteLeave (to, from, next) {
    this.leavePage()
    next()
  },
  beforeDestroy () {
    this.isShowNotePop = false
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/scss/editor.scss";
.ebook {
  position: relative;
  background-color: white;
}

.change-to-word {
  position: absolute;
  color: white;
  right: 132px;
  background-color: gray;
  top: 17px;
  border-radius: 5px;
  padding: 0 4px;
  cursor: pointer;
}

.close-btn{
  cursor: pointer;
  right: 8px;
  top: 4px;
  i{
    font-size: 18px;
  }
}

.underline {
  text-decoration: underline;
  color: blue;
}

@media (max-width: 600px) {
  .edit-padding-box {
    padding: 0 !important;
  }
  .change-to-word {
    display: none;
  }
}
.draft-canvas-con{
  height: 100vh;
  // background: #d5d5d8;
}
:deep(.el-drawer) {
  width: 40%!important;
  background-color: #eee;
}
.collect-article-detail{
  width: 80%;
}
.menu-tooltip{
  padding: 0;
}
:deep(.menu-tooltip) {
  padding: 0!important;
}
.menu-list{
  li{
    height: 30px;
    line-height: 30px;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    &:hover{
      background-color: #111;
    }
  }
}
:deep(.el-drawer__header) {
  margin-bottom: 0;
}
</style>

<style lang="scss">
.menu-tooltip{
  padding: 0;
}
</style>
