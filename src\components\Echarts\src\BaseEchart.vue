<template>
  <div
    :id="id"
    :class="className"
    :style="{ height: localHeight, width: width }"
    ref="chartContainer"
  ></div>
</template>

<script>
import * as echarts from 'echarts'
import reszie from '../mixins/resize'
export default {
  mixins: [reszie],
  props: {
    className: {
      type: String,
      default: 'base-echart'
    },
    id: {
      type: String,
      default: 'base-echart'
    },
    width: {
      type: String,
      default: '65vw'
    },
    height: {
      type: String,
      default: '80vh'
    },
    option: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      chart: null
    }
  },
  watch: {
    option () {
      this.initChart()
    },
    height: {
      handler (newValue) {
        this.localHeight = newValue
      },
      immediate: true
    }
  },
  mounted () {
    this.initChart()
    this.registerClickEvent()
  },
  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart () {
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption(this.option)
    },
    registerClickEvent () {
      // this.chart = echarts.init(document.getElementById(this.id));
      this.chart.on('click', (param) => {
        if (this.id === 'graph-echart' && param.dataType === 'node') {
          if (param?.data?.book_id) {
            this.$store.commit('book/SET_CHOSED_BOOK_ID', param.data.book_id)
          }
        }
      })
    }
  }
}
</script>

<style scoped></style>
