<template>
  <div class="container">
    <div class="top-box">输入邀请码</div>
    <div @click="toLogin">te22st</div>
    <div class="mid-box">
      <div class="row-center captcha_input_wrapper">
        <input
          v-for="(item, index) in captchas"
          :key="index"
          v-model="item.num"
          :id="'captcha' + index"
          @input="inputFinash(index)"
          @focus="adjust(index)"
          @keydown="inputDirection($event, index)"
          class="captcha_input_box row-center"
          type="text"
          maxlength="1"
        />
        <div class="line" style=""></div>
      </div>
    </div>

    <div class="bottom-box">(有效期至：{{ deadline }})</div>
  </div>
</template>
<script>
import Fetch from '@/http/fetch.js'
export default {
  data () {
    return {
      // 当前输入框
      activeInput: 0,
      captchas: [
        { num: '' },
        { num: '' },
        { num: '' },
        { num: '' },
        { num: '' },
        { num: '' }
      ],
      timestamp: null,
      deadline: null
    }
  },

  methods: {
    // 自动校准输入顺序
    adjust (index) {
      const dom = document.getElementById('captcha' + this.activeInput)
      if (index !== this.activeInput && dom) {
        dom.focus()
      }
      if (![0, 1, 2, 3, 4, 5].includes(this.activeInput)) {
        this.activeInput = 0
      }
    },
    // 控制前后方向
    inputDirection (event, index) {
      const val = this.captchas[index].num

      // 回退键处理
      if (event.keyCode == 8 && val == '') {
        // 重新校准
        const dom = document.getElementById('captcha' + (index - 1))
        this.activeInput = index - 1
        if (dom) dom.focus()
      }
      if (event.keyCode != 8 && val != '') {
        const dom = document.getElementById('captcha' + (index + 1))
        this.activeInput = index + 1
        if (dom) dom.focus()
      }
    },
    // 输入框相互联动
    inputFinash (index) {
      const val = this.captchas[index].num
      this.activeInput = val ? index + 1 : index - 1
      const dom = document.getElementById('captcha' + this.activeInput)
      if (dom) dom.focus()
      if (index == this.captchas.length - 1) {
        const code = this.captchas.map((x) => x.num).join('')
        if (code.length == 6) {
          this.apiVerifyInvitationCode(code)
        }
      }
    },

    async apiVerifyInvitationCode (code) {
      const url = '/mobile/Nokeyinterface/checkApply'
      const info = {
        invitation_code: code,
        verify_time: this.timestamp
      }
      const res = await Fetch.postData(url, info)
      if (res.code === 100) {
        this.$message({
          message: res.message,
          type: 'error',
          duration: 1500
        })
        this.captchas.forEach((item) => {
          item.num = ''
        })
        this.$nextTick(() => {
          this.activeInput = 0
          document.getElementById('captcha' + this.activeInput).focus()
        })
      }
      if (res.code === 200) {
        this.$message({
          message: '认证成功',
          type: 'success',
          duration: 1500
        })

        setTimeout(() => {
          this.$router.push('/index')
        }, 1500)
      }
    },

    toLogin () {
      this.$router.push({
        name: 'Login'
      })
    }
  },

  mounted () {
    // 获取url中的时间戳
    this.timestamp = this.$route.params.date
    const date = new Date(this.timestamp * 1000)
    // 增加14天
    date.setDate(date.getDate() + 14)
    // 获取年、月、日
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份要加1，不足两位数前面补0
    const day = date.getDate().toString().padStart(2, '0') // 日要加上前导0

    this.deadline = `${year}年${month}月${day}日`
  }
}
</script>

<style lang="scss">
@import "@/assets/scss/global.scss";
.container {
  @include flex-ccc;
  padding-top: 10vh;
  height: 80vh;
  .top-box {
    font-size: 22px;
    margin-bottom: 40px;
  }

  .mid-box {
    position: relative;
    .row-center {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }
    .captcha_input_wrapper {
      width: 100%;
    }
    .captcha_input_box {
      width: 5vw;
      height: 5vw;
      margin-right: 20px;
      background: #f2f2f2;
      border-radius: 6px;
      border: 2px solid black;
      font-size: 3vw;
      text-align: center;
      color: black;
      @include radius-shadow;
      &:nth-child(4n-1) {
        // border-right: none;
        position: relative;
        margin-right: 40px;
      }
      &:nth-child(7n-1) {
        margin-right: 0px;
      }
    }
    .line {
      @include radius-shadow;
      height: 2px;
      width: 20px;
      background-color: black;
      display: inline-block;
      position: absolute;
    }
  }

  .bottom-box {
    font-size: 14px;
    margin-top: 20px;
    font-weight: 600;
  }
}
</style>
