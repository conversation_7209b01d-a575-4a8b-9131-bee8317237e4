import { BaseLayout } from '@antv/g6';
export const clusterList = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
class BiLayout extends BaseLayout {
  id = 'bi-layout';

  async execute(data, options) {
    const {
      radius = 300,       // 增大外圈半径
      nodeSize = 32,
      centerNodeSpacing = 60,
      outerCircleSpacing = 140  // 新增：外圈节点组之间的间距
    } = { ...this.options, ...options };

    // 将节点分为有cluster和无cluster两组
    const centralNodes = [];
    const clusteredNodes = [];
    data.nodes.forEach(node => {
      if (node.data?.cluster) {
        clusteredNodes.push(node);
      } else {
        centralNodes.push(node);
      }
    });

    // 按 cluster 对节点进行分组
    const clusterMap = clusteredNodes.reduce((acc, node) => {
      const cluster = node.data.cluster;
      if (!acc[cluster]) {
        acc[cluster] = [];
      }
      acc[cluster].push(node);
      return acc;
    }, {});

    const uniqueClusters = Object.keys(clusterMap);

    // 计算画布尺寸和中心点
    const outerRadius = radius + uniqueClusters.length * outerCircleSpacing;
    const canvasSize = outerRadius * 2;
    const centerX = canvasSize / 2;
    const centerY = canvasSize / 2;

    return {
      nodes: [
        // 处理中心节点 - 以小圆形方式排列
        ...centralNodes.map((node, index) => {
          if (!node?.data?.cluster) {
            // 随机生成 x 和 y 的值
            // console.log('index', index)
            return {
              id: node.id,
              style: {
                fill: '#ccc',
                x: Math.random() * canvasSize, // 随机 x 值
                y: Math.random() * canvasSize, // 随机 y 值
              },
            };
          } else {
            const centralRadius = (centralNodes.length * centerNodeSpacing) / (2 * Math.PI);
            const angle = (2 * Math.PI * index) / centralNodes.length;
            return {
              id: node.id,
              style: {
                x: centerX + centralRadius * Math.cos(angle),
                y: centerY + centralRadius * Math.sin(angle),
              },
            };
          }
        }),
        // 处理有cluster的节点 - 以大圆形方式排列每个组
        ...uniqueClusters.map((cluster, clusterIndex) => {
          const clusterNodes = clusterMap[cluster];
          // 计算每个cluster组的中心位置（在大圆上）
          const clusterAngle = (2 * Math.PI * clusterIndex) / uniqueClusters.length;
          const clusterCenterX = centerX + radius * Math.cos(clusterAngle);
          const clusterCenterY = centerY + radius * Math.sin(clusterAngle);

          return clusterNodes.map((node, nodeIndex) => {
            // 在每个cluster组内部，节点围绕组中心点排列
            const nodeAngle = (2 * Math.PI * nodeIndex) / clusterNodes.length;
            const nodeRadius = 50; // 每个组内节点围绕的半径

            return {
              id: node.id,
              style: {
                x: clusterCenterX + nodeRadius * Math.cos(nodeAngle),
                y: clusterCenterY + nodeRadius * Math.sin(nodeAngle),
              },
            };
          });
        }).flat(),
      ],
    };
  }
}

export default BiLayout;
