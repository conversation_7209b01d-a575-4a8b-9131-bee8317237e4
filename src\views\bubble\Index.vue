<template>
  <div class="g6-demo">
    <div class="input-container">
      <el-input v-model="inputValue"  prefix-icon="el-icon-search" @keyup.enter.native="handlePressEnter" placeholder="输入搜索内容,按回车键进行搜索" />
    </div>

    <div class="add-tag">
      <el-input v-model="tagValue" prefix-icon="el-icon-search" placeholder="请输入需要添加的标签" />
      <el-button type="primary" icon="el-icon-plus" circle @click="addTag(tagValue)"></el-button>
    </div>
    <div id="container"></div>

    <div class="right-con" v-loading="loading">
      <div class="top">
        <h3 class="title">{{ label }}</h3>
        <p class="desc">{{ desc }}</p>
      </div>
      <div class="tag-list">
        <h3 class="title">相关词条</h3>
        <div class="content">
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-for="(item, idx) in tagList" :key="idx" :label="item"></el-checkbox>
          </el-checkbox-group>
        </div>

        <el-button size="mini" type="primary" @click="tagListSubmit">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/http/request';
import { ExtensionCategory, Graph, register } from '@antv/g6';
import BiLayout from './BiLayout';
import { getBiData, getPlugins, toFriendList, getLabelList } from './g6util';

let graph = null;

export default {
  data() {
    return {
      loading: true,
      graphData: null,
      inputValue: '林黛玉',
      label: '林黛玉',
      desc: '',
      tagList: [],
      tagValue: '',
      currentKeywordsId: null,
      tagList: [],
      source: 1, //  公共词条 1， 个人词条 2
      isSubmitting: false,
      checkList: [],
    }
  },
  methods: {
    tagListSubmit () {
      if(!this.checkList.length) {
        this.$message.warning('请选择标签');
        return;
      }
      const tagList = this.checkList.join(',')
      this.addTag(tagList)
    },
    async addTag(tagValue) {
      if (this.isSubmitting) {
        this.$message.warning('请勿重复提交');
        return;
      }

      if (tagValue && tagValue.trim() === '') {
        this.$message.warning('标签名称不能为空');
        return;
      }

      try {
        this.isSubmitting = true;
        const result = await request('/api/Keywords/addTagByKeywordsId', {
          keywords_id: this.currentKeywordsId,
          source: 1,
          tag_name: tagValue
        });

        this.tagValue = '';
        this.$message.success('添加标签成功');
        this.loading = true
        const allList = await this.getTagList()
        await this.getDetail()
        this.beforeRender(allList)
      } catch (error) {
        console.error('添加标签失败:', error);
        this.$message.error('添加标签失败，请重试');
      } finally {
        this.isSubmitting = false;
      }
    },
    async getDetail() {
      const result = await request('api/Keywords/getKeywordsInfoByName', {
        keywords: this.inputValue
      })
      this.currentKeywordsId = result.keywords_id
      this.label = this.inputValue
      this.tagList = result.ai_tag.split(',')
      this.desc = result.description
      console.log('getDetail', result)
    },
    getTagList() {
      return new Promise((resolve, reject) => {
        request('/api/Keywords/getTagList').then((result) => {
          // 使用 Map 进行去重
          const uniqueMap = new Map();
          result.forEach(item => {
            if (!uniqueMap.has(item.mk_tag_id)) {
              uniqueMap.set(item.mk_tag_id, {
                id: item.mk_tag_id,
                name: item.tag
              });
            }
          });
          const allList = Array.from(uniqueMap.values());
          resolve(allList)
        }).catch((err) => {
          reject(err)
        })
      })
    },
    getData () {
      return new Promise((resolve, reject) => {
        request('/api/Keywords/getCorrelationKeywordsList', {
          keywords_id: this.currentKeywordsId,
          source: 1
        }).then((result) => {
          console.log('result', result)
          resolve(result)
        }).catch((err) => {
          reject(err)
        })
      })
    },
    initGraph(data) {
        graph && graph.destroy()
        register(ExtensionCategory.LAYOUT, 'bi', BiLayout);
        let plugins = getPlugins(data, this.graphData);
        if(plugins?.length === 1 && plugins?.[0]?.members?.length === 0) {
          plugins = []
        }
        graph = new Graph({
          container: 'container',
          data: this.graphData,
          autoFit: 'center',
          node: {
            style: {
              labelFill: '#000',
              labelPlacement: 'top',
              labelText: (d) => {
                const show = Object.keys(d.data).length === 0 && d.data.constructor === Object
                if(show) {
                  return ''
                } else {
                  return d.label
                }
              },
            },
            palette: {
              type: 'group',
              field: 'cluster',
              color: ['#1783FF', '#D580FF'],
            },
          },
          zoom: 1,
          behaviors: ['drag-element', 'drag-canvas', 'zoom-canvas'],
          layout: {
            type: 'bi',
            sep: 300,
            nodeSep: 20,
            nodeSize: 32,
            preLayout: true,
          },
          plugins,
          // plugins,
          // autoFit: 'center',
        });
        graph.render();
    },
    beforeRender (allList) {
      this.getData().then(data => {
        this.loading = false
        const tags = data.correlation_keywords_list[0].tag_ids
        let friendList = toFriendList(data.correlation_keywords_list)
        data.keywords_info.label = Array.from(new Set(tags.split(','))).join(',');
        data.keywords_info.friend_list = friendList
        const labelList = getLabelList(data.keywords_tag_list.flat())
        data.keywords_info.label_list = labelList
        console.log('labelList', allList)
        data.label_all_list = allList
        const result = getBiData(data || {});
        if (result) {
          const { intersectionNodes, uniqueNodeData } = result;
          console.log('result1', intersectionNodes, )
          this.graphData = {
            nodes: [...intersectionNodes, ...uniqueNodeData]
          }
          // console.log('data', data)
          console.log('graphData', this.graphData)
          this.initGraph(data);
        }
      })
    },
    async handlePressEnter() {
      this.loading = true
      const allList = await this.getTagList()
      await this.getDetail()
      this.beforeRender(allList)
    }
  },
  async mounted() {
    const allList = await this.getTagList()
    await this.getDetail()
    this.beforeRender(allList)
  }
}

</script>
<style lang="scss" scoped>
.input-container {
  margin-bottom: 10px;
  width: 240px;
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 30px;
  left: 20%;
  z-index: 9;
}
.add-tag{
  width: 280px;
  position: absolute;
  bottom: 30px;
  left: 60%;
  display: flex;
  gap: 12px;
  z-index: 9;
  .el-button{
    cursor: pointer;
  }
}
.g6-demo {
  width: 100%;
  height: 100%;
  position: relative;
}
#container {
  width: 100%;
  height: 100%;
}

.right-con{
  position: absolute;
  right: 40px;
  top: 20px;
  height: 70vh;
  border-radius: 10px;
  width: 360px;
  justify-content: space-between;
  background-color: #fff;
  .top{
    height: 100px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    display: flex;
    height: 30vh;
    flex-direction: column;
    gap: 10px;
    .title{
      font-size: 16px;
      font-weight: 600;
    }
    .desc{
      font-size: 14px;
      color: #666;
      margin-top: 8px;
      line-height: 1.5;
    }
  }
  .tag-list{
    margin-top: 20px;
    height: 100px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    display: flex;
    height: 30vh;
    flex-direction: column;
    gap: 10px;
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>
