@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?es3q8w');
  src:  url('fonts/icomoon.eot?es3q8w#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?es3q8w') format('truetype'),
    url('fonts/icomoon.woff?es3q8w') format('woff'),
    url('fonts/icomoon.svg?es3q8w#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
  content: "\e900";
}
.icon-bright:before {
  content: "\e901";
}
.icon-cart:before {
  content: "\e902";
  color: #666;
}
.icon-menu:before {
  content: "\e903";
}
.icon-more:before {
  content: "\e904";
}
.icon-person:before {
  content: "\e905";
}
.icon-progress:before {
  content: "\e906";
}
.icon-share:before {
  content: "\e907";
}
