//鼠标经过事件
const onMouseOver = (event) => {
  const raycaster = new THREE.Raycaster();
  raycaster.near = 0;
  raycaster.far = 1000;
  const mouse = new THREE.Vector2();
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
  raycaster.setFromCamera(mouse, state.camera);
  const intersects = raycaster.intersectObjects(state.scene.children);

  if (intersects.length > 0) {

    // 将鼠标在屏幕中的位置转换为相对于 canvas 画布的位置
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // 生成随机颜色和字体大小的索引
    const colors = ["#869fce", "#efb400", "#8ea8b9"];
    const fontSizes = ["12px", "14px", "16px"];
    const colorIndex = Math.floor(Math.random() * colors.length);
    const fontSizeIndex = Math.floor(Math.random() * fontSizes.length);

    // 根据索引获取随机颜色和字体大小
    const color = colors[colorIndex];
    const fontSize = fontSizes[fontSizeIndex];

    // 设置标签的位置和样式
    labelTop.value = y - 25;
    labelLeft.value = x - 15;
    labelContent.value = intersects[0].object.userData.keywords;
    labelStyle.value = `color: ${color}; position: absolute; top: ${labelTop.value}px; left: ${labelLeft.value}px; font-size: ${fontSize};display:normal`;

    isShowLabel.value = true;
  } else {
    labelStyle.value = "display:none";
    isShowLabel.value = false;
  }
};

//鼠标经过事件 放大镜失败版。
let sphere;

// 鼠标经过事件
// 用于存储每个物体对应的标签
const spriteLabels = new Map();
const onMouseOver2 = (event) => {
  // 获取鼠标在屏幕上的坐标值
  const mouseX = event.clientX - window.innerWidth / 2;
  const mouseY = event.clientY - window.innerHeight / 2;

  // 将屏幕上的坐标值转换为三维坐标系下的坐标值
  const vector = new THREE.Vector3(-mouseX * 0.1, mouseY * 0.1, 0);

  // 将转换后的坐标值赋给圆球的位置属性
  sphere.position.copy(vector);

  const raycaster = new THREE.Raycaster();
  raycaster.near = 0;
  raycaster.far = 2000;
  // const mouse = new THREE.Vector2();
  // mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  // mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
  raycaster.setFromCamera(sphere.position, state.camera);

  // 获取与圆球相交的所有物体，包括子孙物体
  const intersects = raycaster.intersectObjects(state.scene.children, true);

  // 遍历之前添加的标签，并将已经移开的物体对应的标签清除
  for (let [sprite, label] of spriteLabels) {
    if (!intersects.some((item) => item.object === sprite)) {
      label.remove();
      spriteLabels.delete(sprite);
    }
  }

  if (intersects.length > 0) {
    // 筛选出 object 是 Sprite 的物体
    const sprites = intersects.filter((item) => {
      return item.object.type === "Sprite";
    });
    // 遍历符合条件的物体，并将它们的 userData.keywords 属性展示在页面上
    for (let i = 0; i < sprites.length; i++) {
      const sprite = sprites[i].object;
      const keywords = sprite.userData.keywords;

      // 如果当前物体已经有标签了，则跳过
      if (spriteLabels.has(sprite)) {
        continue;
      }

      // 创建一个 div 元素，并设置其内容为 keywords 属性
      const div = document.createElement("div");
      div.innerHTML = keywords;
      div.style.position = "absolute";
      div.style.color = "#fff"; // 将标签颜色修改为白色
      div.style.left = event.clientX + "px";
      div.style.top = event.clientY + "px";
      document.body.appendChild(div);

      // 将该物体和对应的标签记录到 Map 中
      spriteLabels.set(sprite, div);
    }
  }
};
//失败放大镜的放大圆球与监听（onMounted中）其实圆球要在一开始就在页面生成，那样跟随鼠标移动会准确一些，如果是长按后出现。效果会比较一般。
document.addEventListener("mousemove", onMouseOver, false);

//创建3D圆球对象
var sphereGeometry = new THREE.SphereGeometry(8, 32, 32);
var sphereMaterial = new THREE.MeshBasicMaterial({
  color: "#ffffff",
  transparent: true,
  opacity: 0.5,
});
sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
// 将圆球对象设为可交互
sphere.intersectable = true;

// 将圆球对象添加到场景中
state.scene.add(sphere);

//在长按中生成3d球体和监听
let sphere;
let isPressing = false;
const onMouseDown = (event) => {
  isPressing = true;

  // 创建3D圆球对象;
  var sphereGeometry = new THREE.SphereGeometry(8, 32, 32);
  var sphereMaterial = new THREE.MeshBasicMaterial({
    color: "#ffffff",
    transparent: true,
    opacity: 0.2,
  });
  sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
  // 将圆球对象设为可交互
  sphere.intersectable = true;

  // 将圆球对象添加到场景中
  state.scene.add(sphere);
};
const onMouseUp = () => {
  isPressing = false;

  // 从场景中移除圆球对象
  state.scene.remove(sphere);
};
//mounted里的
state.canvas.addEventListener("mousedown", onMouseDown, false);
state.canvas.addEventListener("mouseup", onMouseUp, false);

//下面的是2d遮罩
let timer = null;
let isLongPress = false; // 添加一个变量用于记录是否已经触发了长按事件

function mouseMoveHandler(event) {
  // 移动圆形遮罩的位置
  circle.style.left = event.clientX - 30 + "px";
  circle.style.top = event.clientY - 30 + "px";
}

function mouseUpHandler() {
  clearTimeout(timer);
  document.body.style.cursor = "auto";
  // 清空鼠标移动事件和鼠标松开事件
  document.removeEventListener("mousemove", mouseMoveHandler);
  document.removeEventListener("mouseup", mouseUpHandler);
  // 移除圆形遮罩
  let circle = document.querySelector(".circle");
  if (circle) {
    document.body.removeChild(circle);
  }
}
//长按事件
document.addEventListener("mousedown", (event) => {
  // 创建一个定时器，用于检测长按事件
  timer = setTimeout(() => {
    isLongPress = true;
    // 创建一个圆形遮罩
    let circle = document.createElement("div");
    circle.style.width = "60px";
    circle.style.height = "60px";
    circle.style.borderRadius = "50%";
    circle.style.background = "rgba(255,255,255,0.3)";
    circle.style.position = "absolute";
    circle.style.zIndex = "999";
    circle.style.border = "1px solid black";
    circle.style.left = event.clientX - 30 + "px";
    circle.style.top = event.clientY - 30 + "px";
    circle.className = "circle"; // 给圆形遮罩添加一个类名
    document.body.appendChild(circle);

    // 绑定鼠标移动事件
    document.addEventListener("mousemove", mouseMoveHandler);

    // 绑定鼠标松开事件
    document.addEventListener("mouseup", mouseUpHandler);

    // document.body.style.transform = "scale(1.1)";
  }, 1000); // 设置延时时间，单位为毫秒

  // 给 body 元素绑定 mouseup 事件，用于在短按事件中清除定时器
  document.body.addEventListener("mouseup", () => {
    clearTimeout(timer);
    isLongPress = false;
  });
});

document.addEventListener("mouseup", () => {
  if (isLongPress) {
    return; // 如果已经触发了长按事件，则不做任何操作
  }
  // document.body.style.transform = "scale(1)";
  document.body.style.cursor = "auto";
  // 移除圆形遮罩
  let circle = document.querySelector(".circle");
  if (circle) {
    document.body.removeChild(circle);
  }
  // 清空鼠标移动事件
  document.removeEventListener("mousemove", mouseMoveHandler);
});

//--------在init时候生成线条和箭头的写法-------------
let centerPos = new THREE.Vector3(0, 0, 0);
for (let i = 0; i < mylistStar.length; i++) {
  centerPos.add(mylistStar[i].obj.position);
}
centerPos.divideScalar(mylistStar.length);

const material = new THREE.LineBasicMaterial({ color: 0xffffff });
const geometry = new THREE.BufferGeometry();
const points = [];

mylistStar.forEach((star) => points.concat(star.obj.position));

points[0] = centerPos;

geometry.setFromPoints(points);
const line = new THREE.Line(geometry, material);
line.userData.group = `starList-${i}`; // 添加组别信息
scene.add(line);
// 为线条添加箭头
for (let i = 1; i < mylistStar.length; i++) {
  const prevStarPos = mylistStar[i - 1].obj.position.clone();
  const currStarPos = mylistStar[i].obj.position.clone();

  const direction = currStarPos.clone().sub(prevStarPos).normalize();
  const length = prevStarPos.distanceTo(currStarPos);
  const color = 0x4169e1;
  const headLength = length * 0.05;
  const headWidth = headLength * 0.2;

  const arrow = new THREE.ArrowHelper(
    direction,
    prevStarPos,
    length,
    color,
    headLength,
    headWidth
  );
  arrow.userData.group = `starList-${i}`; // 添加组别信息
  scene.add(arrow);
}

// 原始的移动。
const chooseSearchItem = (item) => {
  const previousLabel = document.getElementById("object-label");
  if (previousLabel) {
    previousLabel.remove();
  }
  const clickedObject = item;
  if (clickedObject.userData) {
    clickedObjectData.value = clickedObject.userData.keywords;
    getKeywordsInfo(clickedObject.userData);
    isShowInfo.value = true;
    searchResult.value = [];
    keyword.value = clickedObject.userData.keywords;
  } else {
    state.scene.remove(selectedObject.userData.borderMesh);
    isShowInfo.value = false;
    isShowInfoArray.value = false;
  }
};
//原始的移动
const moveObjectToCenter = (selectedObject) => {
  // 获取相机当前的位置
  const cameraPosition = state.camera.position.clone();
  // 获取需要移动物体的位置
  const objectPosition = selectedObject.position.clone();
  // // 计算移动目标点
  // const targetPosition = new THREE.Vector3(0, 0, 0);

  // 设置起始时间
  let startTime = null;
  const testPosition = {
    x: 0 + objectPosition.x,
    y: 0 + objectPosition.y,
    z: 0 + objectPosition.z,
  };
  // 定义一个更新函数，用于更新相机和物体的位置
  const update = (time) => {
    if (!startTime) startTime = time;
    const deltaTime = time - startTime;

    // 将相机从当前位置移动到物体位置，并且使其朝向中心
    const progress = Math.min(deltaTime / 2000, 1);
    state.camera.position.lerpVectors(cameraPosition, testPosition, progress);
    state.camera.lookAt(0, 0, 0);
    clearObjectsLabels();
    // 如果动画未完成，则继续执行下一帧动画
    if (progress < 1) {
      requestAnimationFrame(update);
    } else {
      // 创建DOM元素作为标签
      const label = document.createElement("div");
      label.id = "object-label";
      label.innerHTML = selectedObject.userData.keywords || "";
      label.style.position = "absolute";
      label.style.color = "#eab204";
      label.style.fontSize = "16px";
      label.style.border = "1px solid #9ab6c8";
      label.style.padding = "2px 4px";
      document.body.appendChild(label);

      // 设置标签的位置和显示状态
      const position = getObjectScreenPosition(selectedObject, state.camera);
      if (!isLocked) {
        label.style.display = "block";
        label.style.top = `${position.y - 32}px`;
        label.style.left = `${position.x - 10}px`;
      } else {
        label.style.display = "none";
      }
      isShowInfo.value = true;
    }
  };

  // 开始动画
  requestAnimationFrame(update);
};

//不知道有没有修改
const chooseSearchItem2 = (item) => {
  const previousLabel = document.getElementById("object-label");
  if (previousLabel) {
    previousLabel.remove();
  }
  const clickedObject = item;
  if (
    clickedObject.userData
    // && clickedObject !== selectedObject &&
    // clickedObject.userData.keywords !== selectedObject.userData.keywords
  ) {
    // Remove border from previously selected object

    // if (selectedObject.userData.borderMesh) {
    //   state.scene.remove(selectedObject.userData.borderMesh);
    //   selectedObject.userData.borderMesh = null;
    //   isShowInfo.value = false;
    //   isShowInfoArray.value = false;
    // }

    // Set border for newly selected object
    clickedObjectData.value = clickedObject.userData.keywords;
    getKeywordsInfo(clickedObject.userData);
    const borderGeometry = new THREE.SphereGeometry(0.95);
    const borderMaterial = new THREE.MeshBasicMaterial({
      color: 0xf77b00,
    });
    const borderMesh = new THREE.Mesh(borderGeometry, borderMaterial);
    borderMesh.position.copy(clickedObject.position);
    borderMesh.scale.copy(clickedObject.scale).multiplyScalar(1.1);
    clickedObject.userData.borderMesh = borderMesh;
    state.scene.add(borderMesh);

    moveObjectToCenter(clickedObject);

    isShowInfo.value = true;
    searchResult.value = [];
    keyword.value = clickedObject.userData.keywords;
  } else {
    state.scene.remove(selectedObject.userData.borderMesh);
    isShowInfo.value = false;
    isShowInfoArray.value = false;
  }
};
const moveObjectToCenter2 = (selectedObject) => {
  // 获取相机当前的位置
  const cameraPosition = state.camera.position.clone();
  // 获取需要移动物体的位置
  const objectPosition = selectedObject.position.clone();

  // 设置起始时间
  let startTime = null;
  const testPosition = {
    x: objectPosition.x,
    y: objectPosition.y,
    z: objectPosition.z,
  };
  // 定义一个更新函数，用于更新相机和物体的位置
  const update = (time) => {
    if (!startTime) startTime = time;
    const deltaTime = time - startTime;

    // 将相机从当前位置移动到物体位置，并且使其朝向中心
    const progress = Math.min(deltaTime / 2000, 1);
    state.camera.position.lerpVectors(cameraPosition, testPosition, progress);
    state.camera.lookAt(0, 0, 0);

    // 如果动画未完成，则继续执行下一帧动画
    if (progress < 1) {
      clearObjectsLabels();
      requestAnimationFrame(update);
    } else {
      clearObjectsLabels();
      // 创建DOM元素作为标签
      const label = document.createElement("div");
      label.id = "object-label";
      label.innerHTML = selectedObject.userData.keywords || "";
      label.style.position = "absolute";
      label.style.color = "#eab204";
      label.style.fontSize = "16px";
      label.style.border = "1px solid #9ab6c8";
      label.style.padding = "2px 4px";
      document.body.appendChild(label);

      // 设置标签的位置和显示状态
      const position = getObjectScreenPosition(selectedObject, state.camera);
      if (!isLocked) {
        label.style.display = "block";
        label.style.top = `${position.y - 32}px`;
        label.style.left = `${position.x - 10}px`;
      } else {
        label.style.display = "none";
      }
      isShowInfo.value = true;
    }
  };

  // 开始动画
  requestAnimationFrame(update);
};
