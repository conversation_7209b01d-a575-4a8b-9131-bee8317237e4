diff --git a/node_modules/@antv/layout/lib/supervisor.js b/node_modules/@antv/layout/lib/supervisor.js
index 57f8260..263fc9c 100644
--- a/node_modules/@antv/layout/lib/supervisor.js
+++ b/node_modules/@antv/layout/lib/supervisor.js
@@ -21,13 +21,13 @@ export class Supervisor extends EventEmitter {
         this.spawnWorker();
     }
     spawnWorker() {
-        this.proxy = Comlink.wrap(
-        // @ts-ignore
-        new Worker(new URL('./worker.js', import.meta.url), { type: 'module' }));
-        if (this.running) {
-            this.running = false;
-            this.execute();
-        }
+        // this.proxy = Comlink.wrap(
+        // // @ts-ignore
+        // new Worker(new URL('./worker.js', import.meta.url), { type: 'module' }));
+        // if (this.running) {
+        //     this.running = false;
+        //     this.execute();
+        // }
     }
     execute() {
         var _a;
