import request from '@/http/request'
export const searchEmu = {
  THESIS: 'thesis', // 论文
  PAPER: 'paper', // 本地文档
  ARTICLE: 'article', // 收藏文章
  BOOK_COMMENT: 'comment', // 书评
  NOTE: 'note', // 笔记
  DRAFT: 'draft' //白板
}

export const convertList = (result, tabName) => {
  let list = []
  // 论文
  if (tabName === searchEmu.PAPER) {
    list = result?.paper_list?.list.map((item) => {
      return {
        id: item.paper_id,
        title: item.paper_title,
        url: item.paper_url,
        content_arr: [],
        type: searchEmu.PAPER
      }
    })
  }
  if (tabName === searchEmu.THESIS) {
    list = result?.thesis_list?.list.map((item) => {
      return {
        id: item.paper_id,
        title: item.paper_title,
        url: item.paper_url,
        content_arr: [],
        type: searchEmu.THESIS
      }
    })
  }
  // 文章
  if (tabName === searchEmu.ARTICLE) {
    list = result?.article_list?.list.map((item) => {
      return {
        id: item.id,
        title: item.title,
        url: '',
        content_arr: [],
        type: searchEmu.ARTICLE
      }
    })
  }
  // 书评
  if (tabName === searchEmu.BOOK_COMMENT) {
    list = result?.comment_list?.list.map((item) => {
      return {
        id: item.book_comment_id,
        title: item.main_title,
        url: '',
        content_arr: [],
        type: searchEmu.BOOK_COMMENT
      }
    })
  }
  // 笔记
  if (tabName === searchEmu.NOTE) {
    list = result?.note_list?.list.map((item) => {
      return {
        id: item.note_id,
        title: item.main_title,
        url: '',
        content_arr: [],
        type: searchEmu.NOTE
      }
    })
  }
  if (tabName === searchEmu.DRAFT) {
    list = result?.draft_list.list.map((item) => {
      return {
        id: item.id,
        title: item.title,
        url: '',
        content_arr: [],
        type: searchEmu.DRAFT
      }
    })
  }
  return list
}

export const getSearchContent = (id, searchKeyword, tabName) => {
  return new Promise((resolve, reject) => {
    const params = {
      query: searchKeyword,
      search_type: tabName
    }
    if (tabName === searchEmu.PAPER) {
      params.paper_id = id
    }
    if (tabName === searchEmu.ARTICLE) {
      params.article_id = id
    }
    if (tabName === searchEmu.BOOK_COMMENT) {
      params.book_comment_id = id
    }
    if (tabName === searchEmu.NOTE) {
      params.note_id = id
    }
    if (tabName === searchEmu.DRAFT) {
      params.draft_id = id
    }
    request('api/Paper/searchContentDetail', params).then((result) => {
      const contentArr = result?.content_arr || []
      resolve(contentArr)
    }).catch(() => {
      // eslint-disable-next-line prefer-promise-reject-errors
      reject()
    })
  })
}
