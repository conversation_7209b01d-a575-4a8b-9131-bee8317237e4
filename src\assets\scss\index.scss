.index {
  background: black;
  width: 100vw;
  height: 100vh;
  position: relative;

  .circle {
    width: 100vw;
    height: 100vh;
    position: fixed;

    .item {
      display: block;
      width: 10px;
      height: 10px;
      position: absolute;
      border-radius: 50%;
      box-shadow: 0 0 2px 1px rgba(250, 250, 250, .2);
    }
  }

  .slider {
    position: fixed;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    width: 100%;
    // overflow-x: auto;
    user-select: none;
    margin-bottom: 20px;

    &::-webkit-scrollbar {
      height: 5px;
      background: transparent;
    }

    &::-webkit-scrollbar-button {
      display: none;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      height: 4px;
      border-radius: 2px;
      background: rgba(240, 240, 240, .5);
      cursor: pointer;
    }

    .list {
      display: flex;
      word-wrap: nowrap;

      .item {
        width: 170px;
        height: 200px;
        border-radius: 10px;
        overflow: hidden;
        flex-shrink: 0;
        cursor: pointer;

        &:hover {
          box-shadow: 0 0 10px rgb(255, 240, 0);
        }

        &:not(:first-child) {
          margin-left: 5px;
        }

        .img {
          -webkit-user-drag: none;
          width: 170px;
          height: 200px;
        }
      }
    }
  }

  .detail-box {
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .search-box {
    display: flex;
    align-items: center;
    height: 28px;
    padding: 0 6px;
    // background: rgb(255, 242, 204);
    border-radius: 3px;
    box-shadow: 0 0 1px 2px #494949;
    margin-left: 30px;
    transform: translateY(5px);
    z-index: 1000000000;

    .icon {
      width: 22px;
      height: 22px;

      &:last-of-type {
        cursor: pointer;
      }
    }

    .input {
      height: 24px;
      line-height: 24px;
      border: none;
      background: transparent;
      padding: 0 10px;

      &:focus {
        outline: none;
      }

      color: white;
    }
  }

  // .search-box-cover {
  //   position: fixed;
  //   left: 375px;
  //   top: 15px;
  //   display: flex;
  //   align-items: center;
  //   height: 28px;
  //   padding: 0 6px;
  //   border-radius: 3px;
  //   margin-left: 30px;
  //   transform: translateY(5px);
  //   z-index: 1000000000;
  //   width: 2.7rem;
  //   height: 2.8rem;
  // }

  .cube-info {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 10px;
    font-size: 12px;
    display: none;
  }

  // 动态搜索
  .search-btn-box {
    color: rgb(255, 255, 255);
    width: auto;
    border-radius: 20px;
    min-width: 30px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    position: relative;
    overflow: hidden;
    border: 2px solid #8faadc;
    background-size: 104% 104%;
    cursor: pointer;
  }

  .search-btn-box input {
    display: inline-block;
    background: 0 0;
    border: none;
    color: #8faadc;
    padding-left: 20px;
    line-height: 30px !important;
    height: 30px;
    box-sizing: border-box;
    vertical-align: 4px;
    font-size: 16px;
    width: 30px;
    transition: all 0.5s ease-in-out;
  }

  .search-btn-box:hover input {
    display: inline-block;
    width: 160px;
    padding-right: 30px;
  }

  .search-btn-box input:not(:placeholder-shown) {
    display: inline-block;
    width: 160px;
    padding-right: 30px;
  }

  .search-btn-box:hover+.search-line {
    opacity: 0;
    height: 15px;
    transition: opacity 0.5s ease-out;
  }

  .search-line {
    height: 20px;
    width: 2px;
    background-color: #fafafa;
    position: absolute;
    left: 35px;
    bottom: -5px;
    transform: rotate(135deg);
    opacity: 1;
    transition: opacity 0.5s ease-in;
  }

  //splash开屏动画
  .splash-box {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100vw;
    height: 100vh;
  }


  //logo
  .logo-box {
    position: absolute;
    top: 0px;
    left: 20px;
    cursor: pointer;

    .logo-before-B {
      display: flex;
      font-size: 43px;
      font-weight: bolder;
      color: #8faadc;
      align-items: center;
      display: flex;
      justify-content: center;
      font-family: math;
    }

    .logo-after-B {
      transform: translateX(-22px);
      opacity: 0.6;
    }
  }



}
