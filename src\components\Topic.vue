<template>
  <div class="topic">
    <div class="wrap-topic">
      <div class="title-box">
        {{ title }}
      </div>
      <div class="content-box">
        <div class="cont-name">话题内容</div>
        <div class="cont-txt">
          {{ content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Topic',
  props: {
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/scss/topic.scss';
</style>
