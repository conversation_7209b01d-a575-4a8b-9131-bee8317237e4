export const slideScroll = {
  inserted(el) {
    let isDown = false;
    let startX;
    let scrollLeft;

    el.addEventListener("mousedown", (e) => {
      isDown = true;
      el.classList.add("active");
      startX = e.pageX - el.offsetLeft;
      scrollLeft = el.scrollLeft;
    });

    el.addEventListener("mouseleave", () => {
      isDown = false;
      el.classList.remove("active");
    });

    el.addEventListener("mouseup", () => {
      isDown = false;
      el.classList.remove("active");
    });

    el.addEventListener("mousemove", (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - el.offsetLeft;
      const walk = (x - startX) * 3;
      el.scrollLeft = scrollLeft - walk;
    });
  },
};
