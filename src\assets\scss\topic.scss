.topic-detail {
  background: #283152;
  width: 100vw;
  height: 100vh;
  .wrap-user {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80vw;
    height: 70vh;
    .user-box {
      position: absolute;
      .user {
        width: 30px;
        height: 30px;
        position: relative;
        .photo {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          cursor: pointer;
        }
        .name {
          font-size: 14px;
          color: #000;
          white-space: nowrap;
          position: absolute;
          top: 0;
          right: 0;
          transform: translate(calc(100% - 30px), -100%);
          user-select: none;
        }
      }
      .theme {
        width: 200px;
        background: rgb(47, 85, 151);
        border: 1px dashed #fff;
        text-align: left;
        position: absolute;
        right: 0;
        bottom: 0;
        transform: translate(calc(100% + 5px), calc(100% - 25px));
        .title {
          font-size: 14px;
          color: #fff;
          font-weight: 600;
          line-height: 28px;
          border-bottom: 1px dashed #fff;
          box-shadow: 0 6px 20px 0 #000;
          padding: 0 20px;
          // animation-name: bounceInLeft;
          // animation-duration: 1s;
          cursor: pointer;
          user-select: none;
        }
        .content {
          font-size: 14px;
          color: #fff;
          line-height: 22px;
          padding: 10px 20px;
          word-wrap: break-word;
          // animation-name: bounceInDown;
          // animation-duration: 1s;
        }
      }
    }
    .msg-box1 {
      height: 14px;
      border: 1px dashed rgb(126, 126, 126);
      border-radius: 3px;
      margin-top: 10px;
      position: absolute;
      box-shadow: 0 8px 25px 0 #000;
      z-index: 9999999;
      .input {
        width: 120px;
        border: none;
        padding: 5px 10px 5px 0;
        border-radius: 3px;
        text-align: center;
        &:focus {
          outline: none;
        }
      }
      .arrow {
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        border-top: 1px dashed rgb(126, 126, 126);
        background: #fff;
        position: absolute;
        top: calc(50% - 6px);
      }
      .point {
        width: 10px;
        height: 10px;
        border-radius: 5px;
        position: absolute;
        &.red {
          background: #ee0000;
        }
        &.blue {
          background: rgb(0, 79, 139);
        }
      }
      &.white {
        background: #fff;
      }
      &.blue {
        background: rgb(143, 170, 220);
        .input {
          background: rgb(143, 170, 220);
        }
        .arrow {
          background: rgb(143, 170, 220);
        }
      }
      &.left {
        border-radius: 0 3px 3px 0;
        transform: translate(calc(100% + 15px), -15px);
        .arrow {
          border-left: 1px dashed rgb(126, 126, 126);
          left: -10px;
          transform: rotate(-45deg);
        }
        .point {
          top: calc(50% - 1px);
          left: -20px;
        }
      }
      &.right {
        border-radius: 0 3px 3px 0;
        transform: translate(calc(-100% + 15px), 15px);
        .arrow {
          border-right: 1px dashed rgb(126, 126, 126);
          right: -10px;
          transform: rotate(45deg);
        }
        .point {
          top: calc(50% - 1px);
          right: -20px;
        }
      }
    }
  }
}

.topic-index {
  background: #283152;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  .label {
    position: absolute;
    height: 28px;
    cursor: pointer;
    user-select: none;
    .point {
      display: block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: rgb(0, 112, 192);
      position: absolute;
      top: 50%;
      left: -22px;
      transform: translateY(-50%);
      background: rgb(143, 170, 220);
    }
    .arrow {
      display: block;
      width: 18px;
      height: 18px;
      border-left: 1px dashed #fff;
      border-top: 1px dashed #fff;
      background: rgb(143, 170, 220);
      position: absolute;
      top: 5px;
      left: -1px;
      transform: translate(-50%) rotate(-45deg);
    }
    .txt {
      width: 180px;
      height: 26px;
      text-align: center;
      line-height: 26px;
      font-size: 14px;
      color: #fff;
      background: rgb(143, 170, 220);
      border: 1px dashed #fff;
      border-left: none;
      border-radius: 0 3px 3px 0;
    }
    &.related {
      .arrow {
        background: rgb(47, 85, 151);
      }
      .txt {
        background: rgb(47, 85, 151);
      }
      .point {
        background: rgb(0, 112, 192);
        &.orange {
          background: rgb(197, 90, 17);
          width: 12px;
          height: 12px;
          left: -24px;
        }
        &.red {
          background: rgb(240, 0, 0);
          width: 14px;
          height: 14px;
          left: -26px;
        }
      }
    }
  }
}
