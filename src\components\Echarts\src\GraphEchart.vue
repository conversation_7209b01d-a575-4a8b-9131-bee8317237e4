<template>
  <base-echart
    @click.stop
    id="graph-echart"
    class="graph-echart"
    width="80vw"
    height="60vh"
    :option="option"
  />
</template>

<script>
import BaseEchart from "./BaseEchart.vue";

export default {
  components: {
    BaseEchart,
  },
  props: {
    echartsData: {
      type: Object,
    },
  },
  watch: {
    echartsData: {
      handler(newVal, oldVal) {
        if (newVal != null) {
          this.option = this.setOption();
        }
      },
      // immediate: true, // 初始立即执行一次
    },
  },
  data() {
    return {
      option: {},
      categories: [
        {
          name: "根节点",
          symbolSize: [120, 25],
          itemStyle: { normal: { color: "red" } },
        },
        {
          name: "一级节点",
          symbolSize: [80, 25],
          itemStyle: {
            normal: { color: "transparent" },
          },
          label: {
            // 设置 label 样式
            fontSize: 16, // 设置字体大小
            color: "#dbdb00",
          },
        },
        {
          name: "二级节点",
          symbolSize: [80, 25],
          itemStyle: { normal: { color: "transparent" } },
        },
      ],
    };
  },
  methods: {
    setOption: function () {
      let option = {
        animationDurationUpdate: 1500,
        animationEasingUpdate: "quinticInOut",
        series: [
          {
            type: "graph",
            layout: "force",
            symbol: "rect", //节点形状为矩形
            roam: true, //鼠标缩放功能
            draggable: true, // 指示节点是否可以拖动
            label: {
              show: true, //是否显示标签
              color: "#ffe699",
            },
            // focusNodeAdjacency: false, //鼠标移到节点上时突出显示结点以及邻节点和边
            edgeSymbol: ["none", "none"], //去掉箭头
            edgeLineStyle: {
              type: "dashed", //线条类型为虚线
              color: "#4371c2",
            },
            edgeLabel: {
              fontSize: 20, //关系（也即线）上的标签字体大小
            },
            force: {
              //力引导图基本配置
              gravity: 0.1, //节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
              repulsion: 200, //节点之间的斥力因子
              edgeLength: 120, //边的两个节点之间的距离，这个距离也会受 repulsion影响
            },
            lineStyle: {
              opacity: 0.9,
              width: 1,
              curveness: 0,
              type: "dashed", //线条类型
              color: "#4371c2", //虚线颜色
            },
            // data: this.echartsData?.data,
            data: this.echartsData?.data.map((node) => {
              if (node.category === 0) {
                return {
                  ...node,
                  // 设置根节点的图片属性
                  symbol: `image://${node.book_cover}`,
                  symbolSize: [80, 100], // 设置图片大小
                  label: {
                    show: false, // 不显示标签
                  },
                };
              } else {
                return node;
              }
            }),
            links: this.echartsData?.links,
            categories: this.categories,
          },
        ],
      };
      return option;
    },
  },
};
</script>

<style lang="scss" scoped></style>
