{"_from": "gantt-elastic-h", "_id": "gantt-elastic-h@1.0.5", "_inBundle": false, "_integrity": "sha512-vmatVcGcQ86jTVgFXGMWQ26MrWzi4Q6HJeouGaXd2c6RIPUwNladf69hhLmKk4nfW/PclEf1RktOKUNaJjERVQ==", "_location": "/gantt-elastic-h", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "gantt-elastic-h", "name": "gantt-elastic-h", "escapedName": "gantt-elastic-h", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/gantt-elastic-h/-/gantt-elastic-h-1.0.5.tgz", "_shasum": "6870e414a44929d63aa47497d7c162b40a55a0c5", "_spec": "gantt-elastic-h", "_where": "C:\\Users\\<USER>\\Desktop\\bookor_pc - 副本", "author": {"name": "h-luck", "email": "<EMAIL>", "url": "https://github.com/JianGuoHt"}, "bugs": {"url": "https://github.com/JianGuoHt/gantt-elastic-h/issues"}, "bundleDependencies": false, "dependencies": {"dayjs": "^1.8.16", "element-ui": "^2.15.13", "resize-observer-polyfill": "^1.5.1", "vue": "^2.6.10"}, "deprecated": false, "description": "Gantt chart. Elastic javascript gantt chart. Vue gantt. Project manager responsive gantt. jquery gantt.", "devDependencies": {"@types/chai": "^4.2.2", "@types/cypress": "^1.1.3", "@types/mocha": "^5.2.7", "@vue/cli-plugin-eslint": "^3.11.0", "@vue/cli-service": "^3.11.0", "@vue/eslint-config-prettier": "^4.0.1", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "babel-preset-env": "^1.7.0", "css-loader": "^1.0.1", "cypress": "^3.4.1", "eslint": "^5.16.0", "eslint-plugin-cypress": "^2.6.1", "gh-pages": "^5.0.0", "html-webpack-plugin": "^4.5.2", "sass": "^1.59.3", "sass-loader": "^10.4.1", "vue-loader": "^15.7.1", "vue-slider-component": "^3.0.40", "vue-switches": "^2.0.1", "vue-template-compiler": "^2.6.10", "vuepress": "^1.9.9", "webpack": "^4.39.3", "webpack-cli": "^3.3.8", "webpack-dev-server": "^3.8.0"}, "homepage": "https://github.com/JianGuoHt/gantt-elastic-h#readme", "keywords": ["gantt", "gantt js", "gantt chart", "gantt elastic", "javascript gantt", "javascript gantt chart", "j<PERSON><PERSON><PERSON> gantt", "vue gantt", "js gantt", "gantt-chart", "responsive", "vue", "vue-gantt", "javascript gantt", "project gantt", "project manger", "responsive gantt", "gantt component", "javascript gantt", "project", "task"], "license": "MIT", "main": "src/GanttElastic.vue", "name": "gantt-elastic-h", "repository": {"type": "git", "url": "git+https://github.com/JianGuoHt/gantt-elastic-h.git"}, "scripts": {"build": "webpack", "build-demo": "webpack --config webpack.demo.js", "build-docs": "npm run build-demo && npm run docs:build && npm run deploy", "deploy": "gh-pages -d docs/.vuepress/dist", "dev": "webpack-dev-server --config webpack.dev.js", "docs:build": "vuepress build docs", "docs:dev": "vuepress dev docs --temp .temp", "lint-fix": "eslint --ext .js,.vue src --fix", "publish": "npm publish"}, "version": "1.0.5"}