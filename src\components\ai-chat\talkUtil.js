import axios from "@/http";
const botId = "7428433686926164004"; //  '7413605251209117747' //
const host = "https://api.coze.cn";
const token =
  "pat_ZLYIFSVltEllZp2tIwkBOgcPIqhEOzdELIvgqTAZJH9kdJQKhZkGxljjpgx4O5TO";
let ali_token = "";
let timer = null;

export const clearTimer = () => {
  timer && clearTimeout(timer);
};
export const pollList = (chatId, conversationId) => {
  return new Promise((resolve, reject) => {
    const poll = () => {
      axios({
        url: `${host}/v3/chat/message/list?chat_id=${chatId}&conversation_id=${conversationId}`,
        method: "get",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })?.then((res) => {
        if (res.code === 0) {
          const result = res?.data || [];
          // const list = result?.filter((item) => item.type === 'follow_up')
          if (!result.length) {
            timer = setTimeout(() => {
              poll();
            }, 3000);
          } else {
            resolve(result);
          }
        }
      });
    };
    poll();
  });
};

export const pollDetail = (chatId, conversationId) => {
  return new Promise((resolve, reject) => {
    const poll = () => {
      axios({
        url: `${host}/v3/chat/retrieve?chat_id=${chatId}&conversation_id=${conversationId}`,
        method: "get",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }).then((res) => {
        if (res.code === 0) {
          if (
            ["completed", "failed", "requires_action"].includes(res.data.status)
          ) {
            resolve(true);
          } else {
            timer = setTimeout(() => {
              poll();
            }, 3000);
          }
        }
      });
    };
    setTimeout(() => {
      poll();
    }, 3000);
  });
};

export const createTalk = (str, conversationId) => {
  return new Promise((resolve, reject) => {
    axios({
      method: "POST",
      url: `${host}/v3/chat?conversation_id=${conversationId}`,
      data: {
        bot_id: botId,
        user_id: "123456",
        stream: false,
        auto_save_history: true,
        additional_messages: [
          {
            role: "user",
            content: str,
            content_type: "text",
          },
        ],
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then((res) => {
        if (res.code === 0) {
          resolve(res.data);
        } else {
          reject(new Error(`Unexpected response status: ${res.status}`));
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const pollTalk = (chatId, conversationId) => {
  return new Promise((resolve, reject) => {
    const poll = () => {
      axios({
        url: `${host}/v3/chat/message/list?chat_id=${chatId}&conversation_id=${conversationId}`,
        method: "get",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
        ?.then((res) => {
          if (!res.code) {
            const result = res?.data || [];
            const current = result?.find((item) => item.type === "answer");
            if (!current || current.content === "**") {
              setTimeout(() => {
                poll();
              }, 3000);
            } else {
              resolve(res.data);
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    };
    setTimeout(() => {
      poll();
    }, 3000);
  });
};

export const getAudioUrl = (content, voice) => {
  let taskId = "";
  let count = 0;
  return new Promise((resolve, reject) => {
    if (!content) {
      resolve("");
      return;
    }
    const poll = () => {
      count += 1;
      axios({
        url: `/ai/getAudio?token=${ali_token}&taskId=${taskId}`,
        method: "get",
      })
        .then((res) => {
          if (res.code === 200) {
            resolve(res.data);
          }
          if (res.code === 100) {
            if (count > 20) {
              reject("获取音频失败");
            }
            setTimeout(() => {
              poll();
            }, 3000);
          }
        })
        .catch((err) => {
          if (count > 20) {
            reject("获取音频失败");
          }
          setTimeout(() => {
            poll();
          }, 3000);
        });
    };
    if (ali_token) {
      axios
        .post("/ai/textToAudio", { content, token: ali_token, voice })
        .then((res) => {
          taskId = res.data;
          setTimeout(() => {
            poll();
          }, 3000);
        });
    } else {
      axios({
        url: `/ai/getToken`,
        method: "get",
      }).then((res) => {
        ali_token = res.data.Id;
        axios
          .post("/ai/textToAudio", { content, token: ali_token })
          .then((res) => {
            taskId = res.data;
            setTimeout(() => {
              poll();
            }, 3000);
          });
      });
    }
  });
};
