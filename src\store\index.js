import Vue from 'vue'
import Vuex from 'vuex'

import book from './modules/book'
import corporateAccountClassify from './modules/corporateAccountClassify'
import dropwelt from './modules/dropwelt'
import entryclassify from './modules/entryclassify'
import indexsearch from './modules/indexsearch'
import mind from './modules/mind'
import user from './modules/user'
Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    lastRoute: null, // 记录最后一个路由的信息
    choosedResult: [],
    uploadedFiles: [], // 本地 pdf 数组
    isShowSplash: true, // 是否展示开屏动画
    uploadData: '', // 上传的文件值
    urlContent: '' // 用于记录文章收录中爬取的文章内容
  },
  mutations: {
    // 设置上传文件的url
    setUploadData (state, data) {
      state.uploadData = data
    },

    setLastRoute (state, route) {
      // state.lastRoute = route
    },

    updateChoosedResult (state, payload) {
      state.choosedResult = payload
    },

    updateUploadedFiles (state, files) {
      state.uploadedFiles = files
    },

    SETISSHOWSPLASH (state, status) {
      state.isShowSplash = status
    },
    SET_URL_CONTENT (state, content) {
      state.urlContent = content
    }
  },
  getters: {
    getUrlContent (state) {
      return state.urlContent
    }
  },
  actions: {},
  modules: {
    mind,
    book,
    user,
    dropwelt,
    indexsearch,
    entryclassify,
    corporateAccountClassify
  }
})
