<template>
  <Sidebar ref="sidebar" title="脑图列表1">
    <div class="list-search-box">
      <el-input
        placeholder="请输入内容"
        prefix-icon="el-icon-search"
        v-model="keyword"
        @keyup.enter.native="handlePressEnter"
        >
      </el-input>
    </div>
    <div v-if="show" class="box infinite-list" v-infinite-scroll="getList">
      <div v-for="item in list" :key="item.mind_map_id" class="mind-title">
       <span class="mind-sec" @click="itemClick(item)"> {{item.content}}</span>
        <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
          <el-popconfirm title="确定删除该脑图吗？" @confirm="delMind(item)">
              <i slot="reference" class="el-icon-delete"></i>
          </el-popconfirm>
        </el-tooltip>
      </div>
    </div>
  </Sidebar>
</template>
<script>
import axios from "@/http";
import Sidebar from './Sidebar.vue';
export default {
  data() {
    return {
      show: false,
      isMore: true,
      keyword: '',
      page: 1,
      size: 20,
      list: []
    }
  },
  components: {
    Sidebar
  },
  methods: {
    handlePressEnter() {
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      axios.post('ai/mind/search', {
        keyword: this.keyword,
        memberId: userInfo.userid,
        page: 1,
        size: 20
      }).then(res => {
        if(res.code === 200) {
          this.list = res.data
        }
      })
    },
    itemClick(item) {
      window.open(`#/mind/${item.mind_map_id}`, '_blank');
    },
    delMind(item) {
      axios.post('/ai/mind/delete', {
        mindId: item.mind_map_id
      }).then((res) => {
        this.$message.success('删除成功')
        // 删除 this.list 中的对应项
        this.list = this.list.filter(i => i.mind_map_id !== item.mind_map_id);
      })
    },
    showSide() {
      this.show = true;
      this.$refs.sidebar.show = true
    },
    getList() {
      if(!this.isMore) {
        return
      }
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      axios({
        url: `/ai/mind/list?memberId=${userInfo.userid}&page=${this.page}&size=${this.size}`,
        method: "get",
      }).then(res => {
        if(res.code === 200) {
          if(res.data.length < 20) {
            this.isMore = false
          }
          if(this.page === 1) {
            this.list = res.data
          } else {
            this.list = this.list.concat(res.data)
          }
          this.page +=1
        }
      })
    },
  },
  created() {
    this.$bus.$on('showMindList', this.showSide)
  },
  beforeDestroy() {
    this.$bus.$off('showMindList', this.showSide)
  },
  mounted() {

  },
}
</script>
<style lang="scss" scoped>
.mind-title{
  font-size: 16px;
  line-height: 32px;
  display: flex;
  justify-content: space-between;
  .mind-sec{
    cursor: pointer;
    width: 200px;
    overflow: hidden;
    white-space: nowrap; /* 防止换行 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    &:hover{
      color: #409EFF;
    }
  }
}
</style>
