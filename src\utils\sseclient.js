import { fetchEventSource } from '@microsoft/fetch-event-source'

/**
 * 通用 SSE 客户端
 * @param {Object} options
 * @param {string} options.url
 * @param {Object} options.headers
 * @param {AbortSignal} options.signal
 * @param {function} options.onMessage  // 解析后的数据回调
 * @param {function} [options.onClose]
 * @param {function} [options.onError]
 * @param {function} [options.onOpen]
 */
export function createSSEClient({
  url,
  headers,
  signal,
  onMessage,
  onClose,
  onError,
  onOpen,
  handleEnd
}) {
  fetchEventSource(url, {
    headers: {
      Authorization: localStorage.getItem('authorization') || '',
	  ...headers
    },
    openWhenHidden: true, // 处理标签切换，sse 重新触发
    signal,
    onmessage: (ev) => {
      if (ev.event === 'close') {
        // if (onClose) onClose(ev)
        handleEnd();
        return
      }
      try {
        const parsed = JSON.parse(ev.data)
        if (onMessage) onMessage(parsed)
      } catch (error) {
        // if (onError) onError(error)
		    console.error('SSE数据解析错误:', error)
      }
    },
    onclose: (close) => {
      console.log('SSE连接关闭')
      if (onClose) onClose(close)
    },
    onerror: (error) => {
      console.error('SSE连接错误:', error)
      if (onError) onError(error)
    },
    onopen: () => {
	  console.log('SSE连接成功')
      if (onOpen) onOpen()
    },
  })
}
