<template>
  <div class="index" :id="forceData.nodes.length">
    <!-- 调试信息显示 -->
    <div style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; z-index: 9999; border-radius: 5px;" v-if="devMode">
      <div>节点数量: {{ forceData.nodes.length }}</div>
      <div>待渲染: {{ pendingNodes.length }}</div>
      <div>正在渲染: {{ isRendering ? '是' : '否' }}</div>
      <div>总接收: {{ totalNodesReceived }}</div>
    </div>

    <force-star
        ref="forceStar"
        :forceData="forceData"
        :bondList="bondList">
    </force-star>
    <div class="logo-box" @click="handleToHomePage">
      <div class="logo-before-B">
        <div>B</div>
        <div class="logo-after-B">B</div>
      </div>
    </div>
    <transition name="fade">
      <div
        v-if="!isNotFirstSearch"
        style="
          color: white;
          transform: translate(-50%, -50%);
          position: absolute;
          top: 35%;
          left: 50%;
          text-align: center;
        "
      >
        <div
          style="font-size: 51px; color: rgb(154, 182, 200); font-weight: 700"
        >
          Bookor Brain
        </div>
        <div style="font-size: 26px; font-weight: 600">联想 洞见 创见</div>
      </div>
    </transition>
    <div
      :style="{
        position: 'absolute',
        top: isNotFirstSearch ? (isMobile ? '5px' : '10px') : '50%',
        left: '50%',
        transition: 'all 0.5s ease',
        transform: isNotFirstSearch
          ? 'translateX(-50%)'
          : 'translate(-50%, -50%)',
      }"
    >
      <IndexSearch :forceData="forceData" :bondList="bondList"/>
    </div>
    <el-popover
    placement="top-start"
    width="600"
    trigger="hover"
    >
    <div class="category-list">
      <el-tag v-for="item in tagList" :key="item.classify_id" @click="getCategory(item.classify_id, item.type)">{{ item.classify_name }}</el-tag>
    </div>
    <div class="category-container" slot="reference">
      <img src="https://bookor-application.oss-cn-beijing.aliyuncs.com/category.png"/>
    </div>
  </el-popover>
    <drop-welt />
    <div ref="cubeInfo" class="cube-info"></div>
  </div>
</template>

<script>
import request from '@/http/request'
import DropWelt from '@/components/dropwelt/index'
import ForceStar from '@/components/force-star/index'
import IndexSearch from '@/components/index-search/index'
import { mapState } from 'vuex'
import { getRandomColor, pointConvertNode, lineConvertLink } from '../components/force-star/utils'
import { createSSEClient } from '@/utils/sseclient'
import { calculateBrainPositions } from '@/utils/brainPosition'

export default {
  name: 'Index',
  components: {
    ForceStar,
    DropWelt,
    IndexSearch
  },
  data () {
    return {
      devMode: true,
      sseIsEnd: false, // 数据获取是否结束
      forceData: {
        nodes: [],
        links: []
      },
      bondList: [],
      tagList: [],
      offX: 0,
      isMobile: false, // 移动端适配
      // SSE分批渲染相关
      sseController: null,
      batchSize: 2000, // 每批渲染的节点数量
      renderDelay: 100, // 每批之间的延迟(ms)
      totalNodesReceived: 0,
      isRendering: false,
      brainPositionIndex: 0, // 大脑位置索引
      pendingNodes: [] // 待渲染的节点队列
    }
  },
  computed: {
    scrollWrapper () {
      return this.$refs.scrollContainer.$refs.wrap
    },
    ...mapState('indexsearch', {
      isNotFirstSearch: (state) => state.isNotFirstSearch
    })
  },

  async created () {
      // this.forceData = window.__forceData
    this.sseData();
    const finishedSplash = sessionStorage.getItem('finishedSplash')
    if (finishedSplash && finishedSplash === 'true') {
      this.$store.commit('SETISSHOWSPLASH', false)
    } else {
      setTimeout(() => {
        this.$store.commit('SETISSHOWSPLASH', true)
      }, 13000)
    }
  },
    methods: {
      // 处理 graph lines
      async handleConnect() {
        const linksRes = await request('/api/Nebula/keywordsLinesAll', {})
        const links = linksRes
          .map((item) => ({
          source: item.keywords_id_front,
          target: item.keywords_id_behind,
          color: '#33ffff',
          // width: 2,
          // opacity: 1
        }))
        this.$set(this.forceData, 'links', links)
          this.$nextTick(() => {
            if (this.$refs.forceStar) {
                this.$refs.forceStar.updateGraphDynamic()
            }
          })
      },

      sseData() {
        const sseController = new AbortController()
        this.sseController = sseController;
        createSSEClient({
          url: '/api/Nebula/keywordsListAll',
          signal: this.sseController.signal,
          handleEnd: () => {
            // TODO: 渲染完成后通知子组件节点渲染完毕， 开始进行连线
            this.sseIsEnd = true;
            this.handleConnect();
          },
          onOpen: () => {
            this.resetData();
          },
          onMessage: async (parsed) => {
            if (parsed?.length > 0) {
              console.log(parsed, 'parsed')
              this.totalNodesReceived += parsed.length
              this.pendingNodes.push(...parsed)
              if (!this.isRendering) {
                await this.startBatchRendering()
              }
            }
          },
          onClose: () => {
            if (sseController) {
              sseController.abort()
            }
            this.sseController = null
          },
        })
      },

      // 开始分批渲染
      async startBatchRendering() {
        if (this.isRendering) return

        this.isRendering = true

        while (this.pendingNodes.length > 0) {
          // 取出一批节点
          const batch = this.pendingNodes.splice(0, this.batchSize)

          // 为这批节点计算大脑形状位置
          const processedNodes = calculateBrainPositions(batch, this.brainPositionIndex)
          this.brainPositionIndex += batch.length

          // 直接添加到现有数组中（不重新创建数组）
          this.forceData.nodes.push(...processedNodes)

          // 通过事件通知子组件更新图形
          this.$nextTick(() => {
            if (this.$refs.forceStar) {
              this.$refs.forceStar.updateGraphDynamic(processedNodes)
            }
          })

          if (this.pendingNodes.length > 0) {
            await new Promise(resolve => setTimeout(resolve, this.renderDelay))
          }
        }

        this.isRendering = false
      },

      handleToHomePage() {
        this.$router.push('/')
      },
      getLinks(nodes, list) {
        // 目前展示不满一页的数据
        const listMap = new Map()
        const newList = []
        // 接口返回重复的数据，只能前端去过滤
        list.forEach((item) => {
          if (!listMap.has(item.mix_keywords_lines_id)) {
            listMap.set(item.mix_keywords_lines_id)
            newList.push(item)
          }
        })
        this.bondList = newList
        let points = []
        let pointLinks = []
        for (let i = 0; i < newList.length; i++) {
          const item = newList[i]
          const nodes = pointConvertNode(item.point_list, item.title[0])
          const links = lineConvertLink(nodes, item.line_list)
          points = [...points, ...nodes]
          pointLinks = [...pointLinks, ...links]
        }
        // 使用Vue.set来确保响应式更新
        this.$set(this.forceData, 'nodes', [...nodes, ...points])
        this.$set(this.forceData, 'links', pointLinks)
                this.$refs.forceStar.updateGraphDynamic()
      },
      getCategoryList() {
        request('/api/Nebula/keywordsCategoryList', {}).then((result) => {
          this.tagList = result
        })
      },
      getCategory(categoryId, common) {
        // terminate sse
        if (this.sseController) {
          this.sseController.abort();
        }
        // 使用Vue.set清空数组
        this.$set(this.forceData, 'nodes', [])
        request('/api/Nebula/getKeywordsByCategory', {
          category_id: categoryId,
          type: common
        }).then((result) => {
          const list = result.point_data
          if (!list.length) {
            this.$message.info('暂无词条')
            return
          }
          const nodes = []
          for (let i = 0; i < list.length; i += 1) {
            const item = list[i]
            nodes.push({
              id: Date.now() + Math.random() * 1000000,
              user: item.keywords,
              color: getRandomColor(),
              bookId: item.book_keywords_id,
              nodeId: item.member_keywords_id
            })
          }
          this.getLinks(nodes, result.line_data)
        })
      },
    resetData() {
      this.$set(this.forceData, 'nodes', [])
      this.$set(this.forceData, 'links', [])
    }

  },
  mounted () {
    this.getCategoryList()
  },
  beforeDestroy() {
    // 清理SSE连接
    if (this.sseController) {
      this.sseController.abort()
    }
    // 清理待渲染队列
    this.pendingNodes = []
    this.isRendering = false
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/scss/index.scss";
@import "@/assets/scss/global.scss";
.index-mask {
  @include mask;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 3s ease-in-out;
}
.fade-enter,
.fade-leave-to {
  transition: all 0.5s ease-out;
  opacity: 0;
}
.category-container{
  position: fixed;
  bottom: 100px;
  left: 40px;
  img{
    position: relative;
    width: 30px;
    height: 30px;
    cursor: pointer;
  }
}
.category-list{
  width: 540px;
  display:  flex;
  flex-wrap: wrap;
  gap: 8px;
  .el-tag{
    cursor: pointer;
  }
}
</style>
