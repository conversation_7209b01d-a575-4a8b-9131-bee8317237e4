@mixin center-both {
    display: flex;
    justify-content: center;
    align-items: center;
}

@mixin center-x {
    display: flex;
    justify-content: center;
}

@mixin center-y {
    display: flex;
    align-items: center;
}

@mixin flex-wrap {
    display: flex;
    flex-wrap: wrap;
}

@mixin flex-row {
    display: flex;
    flex-direction: row;
}

@mixin flex-column {
    display: flex;
    flex-direction: column;
}

@mixin flex-even {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

@mixin flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

@mixin flex-ccc {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

@mixin flex-grow($value) {
    flex-grow: $value;
}

@mixin radius-shadow {
    border-radius: 10px;
    box-shadow: 1px 2px 1px rgba(0, 0, 0, 0.5);
}

@mixin zindex1 {
    z-index: 1
}

@mixin no-scrollbar {
    &::-webkit-scrollbar {
        display: none;
    }
}

@mixin mask {
    position: absolute;
    width: 100vw;
    height: 100vh;
    top: 0;
    // background-color: rgba(0, 0, 0, 0.3);
}
ul li, ol li{
    list-style: none;
}
