
import { getChildNodePositions } from './util'
class NodeEvent {
  constructor (graph) {
    this.isNodeDown = false
    this.graph = graph
  }

  init () {
    this.graph.on('node:click', ({ node }) => {
      const nodeList = this.graph.getNodes()
      const childList = nodeList.filter((item) => {
        if (item?.data?.id) {
          return item?.data?.id?.includes(node.id) && item?.data?.id !== node.id
        } else {
          return item?.id?.includes(node.id) && item?.id !== node.id
        }
      })
      if (childList.length) {
        const visible = !childList[0].visible
        for (const item of childList) {
          item.setVisible(visible)
        }
      }
    })
    this.graph.on('node:mousedown', ({ node }) => {
      if (node.getData()?.isGroup) {
        return
      }
      if (!this.isNodeDown) {
        this.isNodeDown = true
      }
    })
    this.graph.on('node:mousemove', ({ node }) => {
      if (this.isNodeDown && node.zIndex === 1) {
        node.setZIndex(10)
      }
    })

    this.graph.on('node:mouseup', ({ node }) => {
      if (this.isNodeDown) {
        node.setZIndex(1)
        // TODO 子元素位置重新调整
        // this.updateNodePosition(node)
      }
      this.isNodeDown = false
    })
  }

  updateNodePosition (node) {
    const nodeList = this.graph.getNodes()
    const childList = nodeList.filter(item => item.id.includes(node.id) && item.id !== node.id)
    const arr = getChildNodePositions(node.getBBox(), childList.length)
    for (let i = 0; i < childList.length; i += 1) {
      const item = childList[i]
      const currentNode = this.graph.getCellById((item.id).toString())
      currentNode.setPosition({ x: arr[i].x, y: arr[i].y })
    }
  }
}

export default NodeEvent
