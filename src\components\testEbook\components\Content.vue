<template>
  <transition name="slide-right">
    <div class="content">
      <div class="content-wrapper" v-if="bookAvailable">
        <div
          class="content-item"
          v-for="(item, index) in navigation.toc"
          :key="index"
          @click="jumpTo(item.href)"
        >
          <span class="text">{{ item.label }}</span>
        </div>
      </div>
      <div class="empty" v-else>加载中...</div>
    </div>
  </transition>
</template>
<script>
export default {
  props: {
    ifShowContent: Boolean,
    navigation: Object,
    bookAvailable: Boolean
  },
  methods: {
    jumpTo (target) {
      this.$emit('jumpTo', target)
    }
  }
}
</script>

<style scoped lang="scss">
@import "../styles/global";

.content {
  position: absolute;
    top: 40px;
    left: -12vw;
    z-index: 11;
    width: 100%;
    height: 100%;
    background: white;
  .content-wrapper {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-shadow: 1px 0px 3px rgba($color: #000000, $alpha: 0.5);
    &::-webkit-scrollbar {
      display: none;
    }
    .content-item {
      padding: px2rem(20) px2rem(15);
      border-bottom: px2rem(1) solid #f4f4f4;
      cursor: pointer;
      .text {
        // font-size: px2rem(14);
        font-size: 14px;
        color: #333;
      }
    }
  }
  .empty {
    width: 100%;
    height: 100%;
    @include center;
    font-size: px2rem(16);
    color: #333;
  }
}

@media (max-width: 600px) {
  .content {
    top: 85px;
    width: 33%;
    left: -18vw;
  }
}
</style>
