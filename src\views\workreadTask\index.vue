<template>
  <div>
    <div slot="header" class="headerBig">
      <div
        style="margin-left: 30px; white-space: nowrap; display: inline-block"
      >
        <el-button @click="addTask" type="success" round style="display: inline"
          >添加工作任务</el-button
        >
        <!-- <el-button @click="addBook" type="success" round style="display: inline"
          >添加读书计划</el-button
        > -->
        <el-tooltip effect="dark" content="点击跳转到此刻" placement="bottom">
          <el-button
            type="danger"
            style="display: inline; margin-left: 10px"
            @click="goNow"
            round
            >此刻</el-button
          ></el-tooltip
        >
      </div>
      <!-- <div style="margin-left: 50px; margin-top: 15px"> -->
      <div
        style="
          margin-left: 50px;
          margin-top: 30px;
          white-space: nowrap;
          display: inline-block;
        "
      >
        <el-form inline>
          <el-form-item class="item" label="请选择 :">
            <el-radio-group v-model="modeRadio">
              <el-radio :label="false">仅显示进度</el-radio>
              <el-radio :label="true">显示任务与进度</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <div
        style="
          margin-left: 50px;
          margin-top: 15px;
          white-space: nowrap;
          display: inline-block;
        "
      >
        <el-form inline>
          <el-form-item class="item" label="时间缩放">
            <el-slider
              v-model="scale"
              :min="2"
              :max="24"
              style="width: 130px"
            ></el-slider>
          </el-form-item>
        </el-form>
      </div>
      <div
        style="
          margin-left: 50px;
          margin-top: 15px;
          white-space: nowrap;
          display: inline-block;
        "
      >
        <el-form inline>
          <el-form-item class="item" label="高度缩放">
            <el-slider
              v-model="height"
              :min="30"
              :max="100"
              style="width: 130px"
            ></el-slider>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 第一个表格 -->
    <gantt-elastic
      ref="elastic"
      :tasks="tasks"
      :options="options"
      :dynamicStyle="dynamicStyle"
      style="margin-left: 30px; margin-right: 30px; width: auto"
    >
      <component
        v-if="components.footer"
        :is="components.footer"
        slot="footer"
      ></component>

      <template v-slot:operation="row">
        <el-tooltip effect="dark" content="查看任务详情" placement="bottom">
          <el-button
            icon="el-icon-tickets"
            size="mini"
            circle
            @click="openInfo(row)"
          >
          </el-button>
        </el-tooltip>
        <el-button
          icon="el-icon-edit"
          type="primary"
          size="mini"
          @click="onUpdate(row)"
          circle
        ></el-button>
        <el-button
          icon="el-icon-delete"
          type="danger"
          size="mini"
          @click="onDel(row)"
          circle
        ></el-button>
        <!-- <h>hihihi</h> -->
      </template>

      <template slot="label" slot-scope="{ task }">
        <div>{{ task.label }}</div>
      </template>
    </gantt-elastic>

    <!-- 第二个表格 -->
    <gantt-elastic
      ref="elastic2"
      :tasks="tasks2"
      :options="options2"
      :dynamicStyle="dynamicStyle2"
      style="
        margin-left: 30px;
        margin-right: 30px;
        width: auto;
        margin-top: 60px;
        margin-bottom: 30px;
      "
    >
      <template v-slot:operation="row">
        <el-tooltip effect="dark" content="查看任务详情" placement="bottom">
          <el-button
            icon="el-icon-tickets"
            size="mini"
            @click="openInfo2(row)"
            circle
          ></el-button
        ></el-tooltip>
        <el-button
          icon="el-icon-edit"
          size="mini"
          type="primary"
          @click="onUpdate2(row)"
          circle
        ></el-button>
        <el-button
          icon="el-icon-delete"
          type="danger"
          size="mini"
          @click="onDel2(row)"
          circle
        ></el-button>
        <!-- <h>hihihi</h> -->
      </template>

      <template slot="label" slot-scope="{ task }">
        <div>{{ task.label }}</div>
      </template>
    </gantt-elastic>

    <!-- 工作计划表的对话框 -->
    <el-dialog title="工作任务计划" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="任务名称" prop="label">
          <el-input
            v-model="form.label"
            placeholder="请简要描述工作任务"
          ></el-input>
        </el-form-item>
        <el-form-item label="时间范围" prop="timeArr">
          <el-date-picker
            style="width: 100%"
            v-model="form.timeArr"
            type="datetimerange"
            placeholder="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="父节点ID" prop="fatherID">
          <el-input
            :disabled="flag"
            v-model="form.fatherID"
            placeholder="fID"
          ></el-input>
        </el-form-item>
        <el-form-item label="心得体会" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            rows="4"
            placeholder="请输入心得体会......"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 查看工作计划表任务信息对话框 -->
    <el-dialog title="详细信息" :visible.sync="dialogFormVisible3">
      <el-form ref="form3" :model="form3" label-width="100px">
        <el-form-item label="任务名称" prop="label">
          <el-input readonly="true" v-model="form3.label"></el-input>
        </el-form-item>
        <el-form-item label="时间范围" prop="timeArr">
          <el-date-picker
            readonly="true"
            style="width: 100%"
            v-model="form3.timeArr"
            type="datetimerange"
            placeholder="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="任务时长" prop="taskduration">
          <el-input readonly="true" v-model="form3.taskduration"></el-input>
        </el-form-item>
        <el-form-item label="心得体会" prop="content">
          <el-input
            type="textarea"
            rows="4"
            readonly="true"
            v-model="form3.content"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 阅读计划表对话框 -->
    <el-dialog title="阅读计划" :visible.sync="dialogFormVisible2">
      <el-form ref="form2" :model="form2" :rules="rules" label-width="120px">
        <el-form-item label="书名" prop="label">
          <el-input
            :disabled="flag"
            v-model="form2.label"
            placeholder="请输入书籍名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="阅读计划" prop="timeArr">
          <el-date-picker
            style="width: 100%"
            v-model="form2.timeArr"
            type="datetimerange"
            placeholder="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="父节点ID" prop="fatherID">
          <el-input
            :disabled="flag"
            v-model="form2.fatherID"
            placeholder="fID"
          ></el-input>
        </el-form-item>
        <el-form-item label="心得体会" prop="content">
          <el-input
            v-model="form2.content"
            type="textarea"
            rows="4"
            placeholder="请输入心得体会......"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible2 = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit2">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 查看阅读表任务信息对话框 -->
    <el-dialog title="详细信息" :visible.sync="dialogFormVisible4">
      <el-form ref="form4" :model="form4" label-width="100px">
        <el-form-item label="书籍全称" prop="label">
          <el-input readonly="true" v-model="form4.label"></el-input>
        </el-form-item>
        <el-form-item label="阅读时间" prop="timeArr">
          <el-date-picker
            readonly="true"
            style="width: 100%"
            v-model="form4.timeArr"
            type="datetimerange"
            placeholder="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="阅读时长" prop="taskduration">
          <el-input readonly="true" v-model="form4.taskduration"></el-input>
        </el-form-item>
        <el-form-item label="心得体会" prop="content">
          <el-input
            type="textarea"
            rows="4"
            readonly="true"
            v-model="form4.content"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import GanttElastic from './gantt-elastic-h/src/GanttElastic.vue'
import dayjs from 'dayjs'
import { mapState } from 'vuex'
import request from '@/http/request'
var duration = require('dayjs/plugin/duration')
dayjs.extend(duration)

export default {
  name: 'GanttElasticStandalone',
  components: {
    'gantt-elastic': GanttElastic
  },
  props: ['header', 'footer'],
  data () {
    return {
      dialogFormVisible: false,
      dialogFormVisible2: false,
      dialogFormVisible3: false,
      dialogFormVisible4: false,
      form: {
        id: '',
        label: '',
        fatherID: 0,
        timeArr: [],
        content: ''
      },
      form2: {
        id: '',
        label: '',
        fatherID: 0,
        timeArr: [],
        content: ''
      },
      form3: {
        id: '',
        label: '',
        taskduration: '',
        timeArr: [],
        content: ''
      },
      form4: {
        id: '',
        label: '',
        taskduration: '',
        timeArr: [],
        content: ''
      },
      rules: {
        label: [
          { required: true, message: '不能为空', trigger: ['blur', 'change'] }
        ],
        fatherID: [
          { required: true, message: '不能为空', trigger: ['blur', 'change'] }
        ],
        timeArr: [
          { required: true, message: '不能为空', trigger: ['blur', 'change'] }
        ]
      },
      // modeRadio: "",
      lastId: 1,
      flag: false,
      TaskReadContent: '',
      // localScale: 10,
      components: {},
      tasks: [
        // {
        //   id: 1,
        //   label: "任务1",
        //   start: "2023-08-11 03:37",
        //   duration:
        //     2 * 24 * 60 * 60 * 1000 + 5 * 60 * 60 * 1000 + 40 * 60 * 1000,
        //   type: "task",
        //   collapsed: false,
        //   style: {
        //     base: {
        //       fill: "#106ebe",
        //       stroke: "#106ebe",
        //     },
        //   },
        // },
        // {
        //   id: 2,
        //   label: "任务1-1",
        //   parentId: 1,
        //   start: "2023-08-17 18:39",
        //   duration: 4 * 24 * 60 * 60 * 1000 + 6 * 60 * 1000,
        //   type: "task",
        //   collapsed: false,
        // },
      ],
      // 配置项
      options: {
        taskMapping: {
          // progress: "percent",
        },
        title: {
          // label: "Your project title as html (link or whatever...)",
          html: true
        },
        times: {
          timeZoom: 9
        },
        row: {
          height: 30
        },
        chart: {
          // grid: {
          //   display: true,
          // },
          // text: {
          //   display: true,
          // },
          // expander: {
          //   display: true,
          // },
        },
        taskList: {
          display: true,
          expander: {
            straight: true
          },
          columns: [
            {
              id: 1,
              label: 'ID',
              value: 'id',
              width: 40
            },
            {
              id: 2,
              label: '工作任务',
              value: 'label',
              width: 110,
              expander: true,
              slot: 'label'
            },
            {
              id: 6,
              label: '操作',
              value: 'operation',
              slot: 'operation',
              // html: true,
              width: 130,
              style: {
                'task-list-header-label': {
                  'text-align': 'center',
                  width: '100%'
                },
                'task-list-item-value-container': {
                  'text-align': 'center',
                  width: '100%'
                }
              }
            }
          ]
        },
        // 把甘特图的表头设置成中文
        locale: {
          weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
          months: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月'
          ]
        }
      },
      dynamicStyle: {
        // 任务顶栏字体
        'task-list-header-label': {
          'font-weight': 'bold',
          'font-size': '14px',
          color: '#ffffff'
        },
        'task-list-header-column': {
          'border-left': '1px solid #00000050',
          'box-sizing': 'border-box',
          display: 'flex',
          background: '#71afe5',
          'border-color': '#71afe5'
        },
        // 任务条目
        // "task-list": { background: "#d5dce8", "border-color": "#000000" },

        // 日历
        calendar: {
          width: '100%',
          background: '#7c3b4a',
          display: 'block'
        },
        // 日历刻度
        'calendar-row-text': {
          color: '#ffffff',
          'font-size': '13px',
          'font-weight': 'bold',
          display: 'inline-block',
          position: 'relative'
        },
        // 网格go now 线
        'grid-line-time': { stroke: '#f75c4c', 'stroke-width': 2 },
        // 网格的字
        'chart-row-text': {
          background: 'none',
          'border-radius': '10px',
          'font-weight': 'normal',
          color: '#7f7f7f',
          height: '100%',
          display: 'inline-block'
        }
      },
      tasks2: [
        // {
        //   id: 1,
        //   label: "水浒传",
        //   start: null,
        //   duration: 0,
        //   type: "task",
        //   collapsed: false,
        // },
        // {
        //   id: 2,
        //   label: "第一章",
        //   start: "2023-08-12 03:37",
        //   duration: 1 * 24 * 60 * 60 * 1000 + 5 * 60 * 60 * 1000,
        //   type: "task",
        //   collapsed: false,
        //   parentId: 1,
        //   style: {
        //     base: {
        //       fill: "#ed7d31",
        //       stroke: "#ed7d31",
        //     },
        //   },
        // },
        // {
        //   id: 3,
        //   label: "红楼梦",
        //   start: "2023-08-18 03:37",
        //   duration: 1 * 24 * 60 * 60 * 1000 + 5 * 60 * 60 * 1000,
        //   type: "task",
        //   collapsed: false,
        //   parentId: 2,
        //   style: {
        //     base: {
        //       fill: "#ed7d31",
        //       stroke: "#ed7d31",
        //     },
        //   },
        // },
      ],
      // 配置项
      options2: {
        times: {
          timeZoom: 9
        },
        row: {
          height: 30
        },
        taskList: {
          display: true,
          expander: {
            straight: true
          },
          columns: [
            {
              id: 1,
              label: 'ID',
              value: 'id',
              width: 40
            },
            {
              id: 2,
              label: '书名',
              value: 'label',
              width: 110,
              expander: true,
              slot: 'label'
            },
            {
              id: 6,
              label: '操作',
              value: 'operation',
              slot: 'operation',
              width: 130,
              style: {
                'task-list-header-label': {
                  'text-align': 'center',
                  width: '100%'
                },
                'task-list-item-value-container': {
                  'text-align': 'center',
                  width: '100%'
                }
              }
            }
          ]
        },
        // 把甘特图的表头设置成中文
        locale: {
          weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
          months: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月'
          ]
        }
      },
      dynamicStyle2: {
        // 任务顶栏字体
        'task-list-header-label': {
          'font-weight': 'bold',
          'font-size': '14px',
          color: '#8e671a'
        },
        // 任务栏顶部栏颜色
        'task-list-header-column': {
          'border-left': '1px solid #00000050',
          'box-sizing': 'border-box',
          display: 'flex',
          background: '#f6be98',
          'border-color': '#f6be98'
        },
        // 日历
        calendar: {
          width: '100%',
          background: '#7c3b4a',
          display: 'block'
        },
        // 日历刻度
        'calendar-row-text': {
          color: '#ffffff',
          'font-size': '13px',
          'font-weight': 'bold',
          display: 'inline-block',
          position: 'relative'
        },
        // 网格go now 线
        'grid-line-time': { stroke: '#f75c4c', 'stroke-width': 2 },
        // 网格上的字
        'chart-row-text': {
          background: 'none',
          'border-radius': '10px',
          'font-weight': 'normal',
          color: '#7f7f7f',
          height: '100%',
          display: 'inline-block'
        }
      }
    }
  },

  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      curBookNoteId: (state) => state.curBookNoteId
    }),
    modeRadio: {
      get () {
        return this.options.taskList.display
      },
      set (val) {
        this.options.taskList.display = val
        this.options2.taskList.display = val
      }
    },
    // y轴方向伸缩
    height: {
      get () {
        return this.options.row.height
      },
      set (value) {
        // elastic.$emit("row-height-change", Number(value));
        this.options.row.height = Number(value)
        this.options2.row.height = Number(value)
        this.$refs.elastic.onRowHeightChange(Number(value))
        this.$refs.elastic2.onRowHeightChange(Number(value))
      }
    },

    // x轴方向伸缩
    scale: {
      get () {
        return this.options.times.timeZoom
      },
      set (value) {
        this.options.times.timeZoom = Number(value)
        this.options2.times.timeZoom = Number(value)
        // elastic.$emit("times-timeZoom-change", Number(value));
        this.$refs.elastic.onTimeZoomChange(Number(value))
        this.$refs.elastic2.onTimeZoomChange(Number(value))
      }
    }
  },

  mounted () {
    this.getTasks()
    this.getBooks()
  },
  created () {},
  methods: {
    getAllTasks (tasks) {
      for (var i = tasks.length - 1; i >= 0; i--) {
        let pid = null
        if (tasks[i].pid != 0) {
          pid = tasks[i].pid
        }
        // 赋值给 tasks
        this.tasks.push({
          id: tasks[i].task_id,
          label: tasks[i].task_name,
          start: tasks[i].start_time,
          end: tasks[i].end_time,
          duration:
            dayjs(tasks[i].end_time).valueOf() -
            dayjs(tasks[i].start_time).valueOf(),
          parentId: pid,
          collapsed: false,
          type: 'task',
          style: {
            base: {
              fill: '#005a9e',
              stroke: '#005a9e'
            }
          }
        })
        if (tasks[i].children != null) {
          this.getAllTasks(tasks[i].children)
        }
      }
    },
    async getTasks () {
      this.tasks = []
      request('/api/Gantttask/getGanttTaskList', {}).then((result) => {
        this.getAllTasks(result) // 获取所有任务
      })
    },

    // 工作任务表
    // 查看任务信息
    async openInfo (row) {
      Object.keys(this.form3).forEach((item) => {
        if (item === 'timeArr') {
          this.form3[item] = [row.task.startTime, row.task.endTime]
        } else {
          this.form3[item] = row.task[item]
        }
        this.form3.taskduration =
          Math.floor(row.task.duration / 24 / 60 / 60 / 1000) +
          '天' +
          (Math.floor(row.task.duration / 60 / 60 / 1000) % 24) +
          '时' +
          (Math.floor(row.task.duration / 60 / 1000) % 60) +
          '分'
      })

      // 获取content
      request('/api/Gantttask/getGanttTaskList', {}).then((result) => {
        this.getTaskContent(result, this.form3.id) // 获取所有任务
        this.form3.content = this.TaskReadContent
      })
      this.dialogFormVisible3 = true
    },
    // 删除
    onDel (row) {
      const { task } = row
      const ids = [task.id, ...task.allChildren]
      this.$confirm(`确认删除以下任务[${ids.toString()}]为吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          request('/api/Gantttask/deleteGanttTask', {
            task_id: row.task.id
          })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getTasks()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 添加任务按钮
    addTask () {
      Object.assign(this.form, this.$options.data().form)
      // 表示添加
      this.flag = false
      // 打开对话框
      this.dialogFormVisible = true
    },

    // 查找对应的content
    getTaskContent (tasks, id) {
      for (var i = tasks.length - 1; i >= 0; i--) {
        if (tasks[i].task_id == id) {
          this.TaskReadContent = tasks[i].content
          break
        } else {
          if (tasks[i].children != null) {
            this.getTaskContent(tasks[i].children, id)
          }
        }
      }
    },
    // 打开修改信息界面
    async onUpdate (row) {
      Object.keys(this.form).forEach((item) => {
        if (item === 'timeArr') {
          this.form[item] = [row.task.startTime, row.task.endTime]
        } else {
          this.form[item] = row.task[item]
        }
        if (row.task.parentId == null) {
          this.form.fatherID = 0
        } else {
          this.form.fatherID = row.task.parentId
        }
      })
      // 获取content
      const result = request('/api/Gantttask/getGanttTaskList', {})
      this.getTaskContent(result, this.form.id) // 获取所有任务
      this.form.content = this.TaskReadContent
      this.flag = true
      this.dialogFormVisible = true
    },
    // 添加任务对话框的确定按钮
    async onSubmit (row) {
      // 计算时间差
      const time = this.form.timeArr[1] - this.form.timeArr[0]
      // 如果是添加按钮
      if (!this.flag) {
        if (this.form.fatherID != 0) {
          // 如果是子节点
          const id = Number(this.form.fatherID)
          if (!this.tasks.some((item) => item.id === id)) {
            this.$message.warning('没有找到ID为' + id + '的父节点')
            return
          }
        }
        request('/api/Gantttask/addGanttTask', {
          task_name: this.form.label,
          start_time: dayjs(this.form.timeArr[0]).format('YYYY-MM-DD HH:mm'),
          duration:
            Math.floor(time / 24 / 60 / 60 / 1000) +
            '天' +
            (Math.floor(time / 60 / 60 / 1000) % 24) +
            '时' +
            (Math.floor(time / 60 / 1000) % 60) +
            '分',
          end_time: dayjs(this.form.timeArr[1]).format('YYYY-MM-DD HH:mm'),
          pid: Number(this.form.fatherID)
        })
        this.$message.success('添加成功')
      } else {
        // 如果是编辑按钮
        request('/api/Gantttask/updateGanttTask', {
          task_id: this.form.id,
          task_name: this.form.label,
          start_time: dayjs(this.form.timeArr[0]).format('YYYY-MM-DD HH:mm'),
          duration:
            Math.floor(time / 24 / 60 / 60 / 1000) +
            '天' +
            (Math.floor(time / 60 / 60 / 1000) % 24) +
            '时' +
            (Math.floor(time / 60 / 1000) % 60) +
            '分',
          end_time: dayjs(this.form.timeArr[1]).format('YYYY-MM-DD HH:mm'),
          pid: Number(this.form.fatherID),
          content: this.form.content
        })
        this.$message.success('修改成功')
      }

      this.getTasks()
      // 关闭对话框
      this.dialogFormVisible = false
    },
    getAllBooks (tasks) {
      for (var i = tasks.length - 1; i >= 0; i--) {
        let pid = null
        if (tasks[i].pid != 0) {
          pid = tasks[i].pid
        }
        // 赋值给 tasks
        this.tasks2.push({
          id: tasks[i].plan_id,
          label: tasks[i].book_name,
          start: tasks[i].start_time,
          end: tasks[i].end_time,
          duration:
            dayjs(tasks[i].end_time).valueOf() -
            dayjs(tasks[i].start_time).valueOf(),
          parentId: pid,
          collapsed: false,
          type: 'task',
          style: {
            base: {
              fill: '#ed7d31',
              stroke: '#ed7d31'
            }
          }
        })
        if (tasks[i].children != null) {
          this.getAllBooks(tasks[i].children)
        }
      }
    },

    async getBooks () {
      this.tasks2 = []
      request('/api/Ganttreadplan/getGanttReadplanList', {}).then((result) => {
        this.getAllBooks(result) // 获取所有任务
      })
    },

    // 查找对应的content
    getBookContent (tasks, id) {
      for (var i = tasks.length - 1; i >= 0; i--) {
        if (tasks[i].plan_id == id) {
          this.TaskReadContent = tasks[i].content
          break
        } else {
          if (tasks[i].children != null) {
            this.getBookContent(tasks[i].children, id)
          }
        }
      }
    },
    // 查看阅读信息
    async openInfo2 (row) {
      Object.keys(this.form4).forEach((item) => {
        if (item === 'timeArr') {
          this.form4[item] = [row.task.startTime, row.task.endTime]
        } else {
          this.form4[item] = row.task[item]
        }
        this.form4.taskduration =
          Math.floor(row.task.duration / 24 / 60 / 60 / 1000) +
          '天' +
          (Math.floor(row.task.duration / 60 / 60 / 1000) % 24) +
          '时' +
          (Math.floor(row.task.duration / 60 / 1000) % 60) +
          '分'
      })
      // 获取content
      request('/api/Ganttreadplan/getGanttReadplanList', {}).then((result) => {
        this.getBookContent(result, this.form4.id) // 获取所有任务
        this.form4.content = this.TaskReadContent
      })

      this.dialogFormVisible4 = true
    },
    // 修改
    async onUpdate2 (row) {
      this.flag = true
      Object.keys(this.form2).forEach((item) => {
        if (item === 'timeArr') {
          this.form2[item] = [row.task.startTime, row.task.endTime]
        } else {
          this.form2[item] = row.task[item]
        }
        if (row.task.parentId == null) {
          this.form2.fatherID = 0
        } else {
          this.form2.fatherID = row.task.parentId
        }
      })
      // 获取content
      request('/api/Ganttreadplan/getGanttReadplanList', {}).then((result) => {
        this.getBookContent(result, this.form2.id) // 获取所有任务
        this.form2.content = this.TaskReadContent
      })
      this.dialogFormVisible2 = true
    },

    // 删除
    onDel2 (row) {
      const { task } = row
      const ids = [task.id, ...task.allChildren]
      this.$confirm(`确认删除以下任务[${ids.toString()}]为吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          request('/api/Ganttreadplan/deleteGanttReadplan',
            {
              book_name: row.task.label
            }
          )
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getBooks()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 阅读计划对话框确认按钮
    async onSubmit2 (row) {
      // 计算时间差
      const time = this.form2.timeArr[1] - this.form2.timeArr[0]
      // 如果是添加按钮
      if (this.flag) {
        // 如果是编辑按钮
        request('/api/Ganttreadplan/updateGanttReadplan', {
          book_name: this.form2.label,
          start_time: dayjs(this.form2.timeArr[0]).format('YYYY-MM-DD HH:mm'),
          duration:
            Math.floor(time / 24 / 60 / 60 / 1000) +
            '天' +
            (Math.floor(time / 60 / 60 / 1000) % 24) +
            '时' +
            (Math.floor(time / 60 / 1000) % 60) +
            '分',
          end_time: dayjs(this.form2.timeArr[1]).format('YYYY-MM-DD HH:mm'),
          pid: Number(this.form2.fatherID),
          content: this.form2.content
        })
        this.$message.success('修改成功')
      }
      this.getBooks()
      // 关闭对话框
      this.dialogFormVisible2 = false
    },
    // 跳转到此刻
    goNow (type) {
      this.$refs.elastic.goCurrentTime()
      this.$refs.elastic2.goCurrentTime()
    }
  }
}
</script>

<style>
.headerBig {
  margin-bottom: 30px;
  background-color: #ededed;
}

.item .el-form-item__label {
  color: #606266;
  font-weight: bold;
  font-size: 16px;
}
.total {
  /* background-color: black; */
}
/* 滑动条颜色 */
.el-slider__bar {
  background-color: #7c3b4a;
}
/* 滑块颜色 */
.el-slider__button {
  border: 2px solid #7c3b4a;
}

/* 滑动轨颜色 */
.el-slider__runway {
  background-color: #ffffff;
}

/* 单选按钮颜色 */
.el-radio__input.is-checked + .el-radio__label {
  color: #7c3b4a !important;
  font-weight: bold;
}
.el-radio__input.is-checked .el-radio__inner {
  background: #7c3b4a !important;
  border-color: #7c3b4a !important;
}
</style>
