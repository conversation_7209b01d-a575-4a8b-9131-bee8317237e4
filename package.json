{"name": "bookor", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --open --mode development --max-old-space-size=4096", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "deploy": "node publish.js", "publish": "npm run build && npm run deploy", "postinstall": "patch-package"}, "dependencies": {"@antv/g6": "^5.0.42", "@antv/x6": "^2.11.3", "@antv/x6-plugin-clipboard": "2.1.6", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.2", "@antv/x6-plugin-selection": "^2.2.1", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-transform": "^2.1.8", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@microsoft/fetch-event-source": "^2.0.1", "@types/three": "^0.152.1", "@vue-office/docx": "^1.3.0", "@xmldom/xmldom": "^0.8.10", "3d-force-graph": "^1.73.0", "ali-oss": "^6.22.0", "animate.css": "^4.1.1", "async-validator": "^4.2.5", "axios": "^1.5.0", "clipboard": "^2.0.11", "core-js": "^3.32.1", "d3": "^7.8.5", "dayjs": "^1.11.9", "docx-preview": "^0.1.18", "ebook-convert": "^2.0.1", "echarts": "^5.4.3", "element-tiptap": "1.27.1", "element-ui": "^2.15.14", "epubjs": "^0.3.93", "force-graph": "^1.43.5", "fuse.js": "^6.5.3", "jquery": "^3.7.1", "jsplumb": "^2.15.6", "jszip": "^3.10.1", "loadsh": "^0.0.4", "lodash": "^4.17.21", "mammoth": "^1.6.0", "markdown-it": "^14.1.0", "moment": "^2.29.4", "particles.js": "^2.0.0", "pdfjs-dist": "^2.5.207", "relation-graph": "^2.0.27", "resize-observer-polyfill": "^1.5.1", "simple-mind-map": "^0.12.1", "swiper": "^5.4.5", "three": "^0.170.0", "tiptap": "1.32.2", "tween": "^0.9.0", "uuid": "^9.0.0", "video.js": "^8.5.2", "vue": "2.7.16", "vue-axios": "^3.5.2", "vue-demi": "^0.14.6", "vue-draggable-resizable": "^2.3.0", "vue-dragscroll": "^3.0.1", "vue-loader": "^15.9.8", "vue-pdf": "^4.3.0", "vue-router": "^3.6.5", "vue-style-loader": "^4.1.3", "vue-template-compiler": "2.7.16", "vuex": "^3.4.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@types/uuid": "^9.0.3", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "css-loader": "5.2.7", "eslint": "^6.7.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "patch-package": "^8.0.0", "sass": "1.32.13", "sass-loader": "10.4.1", "terser-webpack-plugin": "^4.2.3", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "webpack": "^4.47.0", "worker-loader": "^3.0.8"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "overrides": {"three-render-objects": "1.39.1", "prosemirror-tables": "1.7.1"}}