
<template>
    <div>
      <div  class="switch-box-bottom" v-show="searchList.length">
        <div
            v-for="(item, index) in searchList"
            :key="index"
            class="local-box"
          >
            <div class="title"  @click="
                chooseLocalUrl(item.paper_id, item.paper_title)
              ">
              {{ item.paper_title }}
            </div>
            <img
              src="@/assets/editor/close.png"
              alt=""
              @click="deleteLocalUrl(item.paper_id)"
            />
        </div>
      </div>
      <div class="switch-box-bottom"  v-show="!searchList.length" v-infinite-scroll="load">
        <div
          v-for="(item, index) in fileList"
          :key="index"
          class="local-box"
        >
          <div class="title"  @click="
              chooseLocalUrl(item.paper_id, item.paper_title)
            ">
            {{ item.paper_title }}
          </div>
          <img
            src="@/assets/editor/close.png"
            alt=""
            @click="deleteLocalUrl(item.paper_id)"
          />
        </div>
        <p v-if="!isMore" class="more-text">没有更多了...</p>
        </div>
    </div>
</template>

<script>
// import axios from '@/http'
import request from '@/http/request'
import { EventBus } from '@/main.js'
import { MessageBox } from 'element-ui'
export default {
  data () {
    return {
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      fileList: [],
      page: 1,
      size: 10,
      isMore: true,
      searchList: []
    }
  },
  props: ['searchInput'],

  created () {
    EventBus.$on('refreshFileFileList', this.refreshFileFileList)
  },

  watch: {
    searchInput (val) {
      // if (val) {
      //   request('/api/Paper/getPaperKeywordsSearch', {
      //     keywords: val
      //   }).then((res) => {
      //   // console.log('res', res)
      //     this.searchList = result
      //   })
      // } else {
      //   this.searchList = []
      // }

      const value = this.searchInput
      if (!value) {
        this.searchList = []
        return
      }
      const res = this.fileList
      const source = []
      const reg = new RegExp(value, 'i')
      res.forEach((item, index) => {
        if (reg.test(item.paper_title)) {
          source.push(item)
        }
      })
      this.searchList = source
    }
  },
  methods: {
    refreshFileFileList () {
      this.page = 1
      this.apiGetFileData()
    },
    // 处理本地文件数组
    chooseLocalUrl (id, title) {
      request('/api/Paper/getPaperInfo', {
        paper_id: id
      }).then((result) => {
        const url = result.paper_url
        const dotIndex = url.lastIndexOf('.')
        const extension = url.slice(dotIndex + 1)
        if (extension === 'pdf') {
          this.$router.push({
            name: 'pdf',
            query: {
              paperId: id,
              title: title,
              url
            }
          })
        } else if (extension === 'epub') {
          this.$router.push({
            name: 'epub',
            query: {
              paperId: id,
              title: title,
              url
            }
          })
        } else if (extension === 'docx') {
          this.$router.push({
            name: 'docx',
            query: {
              paperId: id,
              title: title,
              url
            }
          })
        } else {
          this.$router.push({
            name: 'note',
            query: {
              paperId: id
            }
          })
        }
        const data = {
          id: id,
          url: url,
          type: '',
          title: title
        }
        this.$emit('getLocalUrlData', data)
      })
    },
    // 删除文件或者论文
    apiDeletePaper (paperId) {
      const params = {
        paper_id: paperId
      }
      request('/api/Paper/deletePaper', params).then((res) => {
        this.fileList = this.fileList.filter(
          (result) => result.paper_id !== paperId
        )
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: '论文删除失败',
          type: 'error',
          duration: 1000
        })
      })
    },
    deleteLocalUrl (id) {
      // 弹出确认对话框
      MessageBox.confirm('确定要删除这个文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 创建新数组，不包含要删除的项目
          this.apiDeletePaper(id)
        })
        .catch(() => {
          // 用户点击了“取消”按钮，什么也不做
        })
    },
    // 获取本地上传文件和word
    apiGetFileData () {
      const params = {
        page: this.page,
        limit: this.size,
        paper_type: 'others'
      }
      if (!this.userinfo?.userid) {
        return
      }
      request('/api/Paper/getPaperList', params).then((result) => {
        const list = result
        if (this.page === 1) {
          this.fileList = list
        } else if (list && list.length > 0) {
          this.fileList = [...this.fileList, ...list]
        } else {
          this.isMore = false
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
        this.page += 1
      }).catch(() => {
        this.$message({
          message: '未获取到数据，请重试',
          type: 'error',
          duration: 1000
        })
      })
    },
    load () {
      if (this.isMore) {
        this.apiGetFileData()
      }
    }
  },
  mounted () {
    this.apiGetFileData()
  }
}
</script>
<style lang="scss">
  .switch-box-bottom {
    height: 350px;
    background-color: #eeeeee;
    padding: 0px 5px;
    overflow-y: auto;

    .switch-box-bottom-content-else {
      text-align: center;
      margin-top: 40px;
      color: grey;
      font-size: 14px;
    }

    .entryListItem {
      background-color: #8faadc;
      padding: 4px;
      margin: 2px 4px;
      border-radius: 5px;
      text-align: center;
      color: white;
      cursor: pointer;
    }
  }
  .more-text{
    text-align: center;
    font-size: 12px;
    color: #333;
    padding: 5px 0;
  }
</style>
