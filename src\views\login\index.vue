<template>
  <div class="register" id="registerBox">
    <div class="mainBox">
      <div class="register_right_imgs">
        <div v-show="showLogin">
          <!-- 登录方式 -->
          <div style="display: flex; justify-content: center">
            <p
              id="password_login"
              :class="
                showLoginForm ? 'password_login_active' : 'password_login'
              "
              @click="toggleLoginMethod"
            >
              密码登录
            </p>
            <p
              id="account_login"
              :class="showLoginForm ? 'account_login' : 'account_login_active'"
              @click="toggleLoginMethod"
            >
              微信登录
            </p>
          </div>
          <form action="#" id="login_form" v-show="showLoginForm">
            <input
              class="login_phone_number_input"
              type="text"
              name="phone_number"
              placeholder="手机号/邮箱"
              v-model="username"
              style=""
            />
            <input
              class="login_phone_number_input"
              type="password"
              name="password"
              placeholder="密码"
              v-model="password"
            />
            <!-- 登录按钮 --><!-- 忘记密码、注册 -->
            <div style="display: flex; align-items: center">
              <div class="right_register" @click="handleAccountSecurity">
                忘记密码
              </div>
              <div id="login_btn" class="login_btn" @click="handleLogin">
                登录
              </div>
              <div
                v-if="isShowRegBtn"
                class="right_register"
                style="color: #0063b9"
                @click="handleRegister"
              >
                注册账户
              </div>
            </div>
          </form>
          <!-- 二维码 -->
          <div v-show="!showLoginForm" class="login-QRcode">
            <div id="login_container"></div>
          </div>
        </div>
        <!-- 找回密码-账号安全 -->
        <div v-show="showAccountSecurity" class="account_security_box">
          <div class="account_security_title_box">
            <img src="@/assets/login/back.png" alt="" @click="backToLogin" />
            <p class="account_security_title">账号安全</p>
          </div>
          <p class="account_security_line"></p>
          <div>
            <div class="account_security_phone_box">
              <input
                class="account_security_phone_number"
                type="text"
                placeholder="手机号"
                maxlength="11"
                v-model="securityPhoneNumber"
              />
            </div>

            <input
              class="account_security_phone_number"
              type="text"
              placeholder="输入验证码"
              v-model="securityCapture"
              maxlength="6"
            />

            <div class="account_security_box_bottom">
              <input
                class="send_sms"
                type="button"
                :value="smsButtonText"
                @click="getPhoneCaptcha"
              />
              <button class="next_step" @click="changePassword">下一步</button>
            </div>
          </div>
        </div>
        <!-- 找回密码：修改密码 -->
        <div v-show="showChangePassword" class="account_security_change_box">
          <div class="account_security_title_box">
            <img src="@/assets/login/back.png" alt="" @click="backToLogin" />
            <p class="account_security_title">账号密码-修改密码</p>
          </div>

          <p class="account_security_line"></p>
          <div class="account_security_change_box_bottom">
            <input
              class="input_new_password"
              type="password"
              placeholder="输入新密码"
              v-model="securityNewPassword"
            />
            <input
              class="input_new_password"
              type="password"
              placeholder="确认新密码"
              v-model="securityCheckNewPassword"
            />

            <button class="next_step" @click="confirmPassword">确认修改</button>
          </div>
        </div>
        <!-- 注册 -->
        <div v-show="showRegister">
          <register
            :show-login="showLogin"
            :show-register="showRegister"
            @updateShown="updateShown"
            @toLogin="toLogin"
          ></register>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@/router';
import store from '@/store';
import axios from 'axios';

import Register from '@/components/login/register';
export default {
  data () {
    return {
      registerBg: '',
      username: '',
      password: '',
      showLoginForm: true, // 切换账号密码登录/二维码登录
      showLogin: true, // 默认显示登录页面
      showAccountSecurity: false,
      showChangePassword: false,
      showRegister: false,
      options: [
        {
          value: '86',
          label: '中国大陆+86'
        },
        {
          value: '852',
          label: '中国香港+852'
        },
        {
          value: '853',
          label: '中国澳门+853'
        },
        {
          value: '886',
          label: '中国台湾+886'
        }
      ],
      value: '86',

      securityPhoneNumber: '',
      securityCapture: '',
      sms_time: 60,
      start_interval: null,
      isCountingDown: false,
      securityNewPassword: '',
      securityCheckNewPassword: '',

      count: 0,

      isShowRegBtn: true,
      dialogTableVisible: false
    }
  },
  components: {
    Register // 注册组件
  },
  props: ['type'],
  computed: {
    smsButtonText () {
      if (this.isCountingDown) {
        return `${this.sms_time}S`
      }
      return '获取验证码'
    }
  },
  created () {},
  mounted () {
    this.getwxCode()
    const code = this.getQueryString('code')

    // 获取背景图片
    this.getBackgroundImg()

    if (!code) {
      const url = this.getQueryString('url')
      if (url) {
        window.localStorage.setItem('login_url', url)
      }
    }
    if (this.type === 1) {
      this.showRegister = true
      this.showLogin = false
    }
  },
  methods: {
    getQueryString (name) {
      // 获取url中的参数
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
      const r = window.location.search.substr(1).match(reg)
      if (r != null) return unescape(r[2])
      return null
    },
    openVision () {
      this.dialogTableVisible = true
    },
    closeVision () {
      document.querySelector('#visionDom').pause()
    },
    getBackgroundImg () {
      let imgUrl = localStorage.getItem('registerBg')
      if (imgUrl) {
        this.registerBg = imgUrl
        return
      }

      axios
        .get('/api/config/adminConfigList', { params: { type: 'image' } })
        .then((res) => {
          res.data.result.forEach((element) => {
            if (element.id === 47) {
              imgUrl = element.value
              localStorage.setItem('registerBg', imgUrl)
              this.registerBg = imgUrl
            }
          })
        })
    },
    // 切换登录方式
    toggleLoginMethod () {
      this.showLoginForm = !this.showLoginForm
    },
    handleLogin () {
      if (!this.username.trim() || !this.password.trim()) {
        this.$message.error({
          message: '手机号/邮箱或者密码不能为空',
          duration: 1500
        })
        return
      }
      const params = {
        username: this.username,
        password: this.password
      }

      this.apiPostLogin(params)
    },

    apiPostLogin (params) {
      fetch('https://api.bookor.com.cn/index.php/mobile/Login/index', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      })
        .then((response) => {
          if (response.ok) {
            return response.json()
          }
          throw new Error('Network response was not ok.')
        })
        .then(async (data) => {
          if (data.code === 200) {
            this.$message({
              message: '登录成功',
              type: 'success',
              duration: 1000
            })
            localStorage.setItem('userinfo', JSON.stringify(data.result))
            localStorage.setItem('authorization', data.result.key)
            // 调用 getUserInfoMore，并等待其完成
            await store.dispatch('user/getUserInfoMore')
            // 调用 getUserInfoEnterprise，并等待其完成
            await store.dispatch('user/getUserInfoEnterprise')
            this.$store.commit('user/SET_USERINFO')
            // 进入index页
            if (this.$route.name === 'draft') {
              this.$emit('loginSuccess')
              const id = this.$route.query.id
              router.push({
                name: 'draft',
                query: {
                  id
                }
              })
            } else {
              router.push('/index')
            }
          } else {
            this.$message.error(data.message)
          }
        })
        .catch((error) => {
          console.error('There was a problem with the network request:', error)
        })
    },
    // 获得登录二维码
    getwxCode () {
      // eslint-disable-next-line no-undef, no-new
      new WxLogin({
        id: 'login_container',
        appid: 'wxc99f27666acff8db',
        scope: 'snsapi_login',
        redirect_uri: encodeURI('https://www.bookor.com.cn/'),
        state: '',
        style: 'black',
        href: 'https://oss.bookor.com.cn/static/css/qrcode.css',
        self_redirect: false
      })
    },

    handleAccountSecurity () {
      this.showAccountSecurity = true
      this.showLogin = false
    },

    backToLogin () {
      if (this.showAccountSecurity) {
        this.showAccountSecurity = false
        this.showLogin = true
        this.showChangePassword = false
      } else if (this.showChangePassword) {
        this.showAccountSecurity = true
        this.showLogin = false
        this.showChangePassword = false
      }
    },

    // 验证手机号+获得验证码
    getPhoneCaptcha () {
      const phone = this.securityPhoneNumber.trim()
      if (!phone) {
        this.$message({
          message: '手机号不能为空',
          type: 'error',
          duration: 1000
        })
      } else if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(phone)) {
        this.$message({
          message: '手机号格式不正确',
          type: 'error',
          duration: 1000
        })
      } else {
        this.apiGetPhoneCaptcha(phone)
      }
    },

    apiGetPhoneCaptcha (phone) {
      const params = {
        phone: phone,
        type: 3 // 1.为注册 2.为绑定 3.为找回登录密码 4.获取旧手机验证码 5.获取新手机验证码 6.找回支付密码
      }
      fetch(
        'https://api.bookor.com.cn/index.php/mobile/Connect/get_sms_captcha',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        }
      )
        .then((response) => {
          if (response.ok) {
            return response.json()
          }
          throw new Error('Network response was not ok.')
        })
        .then((data) => {
          if (data.code === 200) {
            if (!this.isCountingDown) {
              this.isCountingDown = true
              this.start_interval = setInterval(() => {
                this.sms_time--
                if (this.sms_time <= 0) {
                  clearInterval(this.start_interval)
                  this.isCountingDown = false
                  this.sms_time = 60
                }
              }, 1000)
            }
          } else {
            this.$message({
              message: data.message,
              type: 'error',
              duration: 1000
            })
          }
        })
        .catch((error) => {
          console.error('There was a problem with the network request:', error)
        })
    },

    // 下一步，验证验证码
    changePassword () {
      const phone = this.securityPhoneNumber.trim()
      const capture = this.securityCapture.trim()
      if (!phone) {
        this.$message({
          message: '手机号不能为空',
          type: 'error',
          duration: 1000
        })
      } else if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(phone)) {
        this.$message({
          message: '手机号格式不正确',
          type: 'error',
          duration: 1000
        })
      } else if (!capture) {
        this.$message({
          message: '验证码不能为空',
          type: 'error',
          duration: 1000
        })
      } else {
        // 判断验证码是否正确
        this.apiCheckPhoneCaptcha(phone, capture)
      }
    },

    apiCheckPhoneCaptcha (phone, capture) {
      const params = {
        phone: phone,
        captcha: capture,
        type: 3 // 1.为注册 2.为绑定 3.为找回登录密码 4.获取旧手机验证码 5.获取新手机验证码 6.找回支付密码
      }
      fetch(
        'https://api.bookor.com.cn/index.php/mobile/Connect/check_phone_captcha',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        }
      )
        .then((response) => {
          if (response.ok) {
            return response.json()
          }
          throw new Error('Network response was not ok.')
        })
        .then((data) => {
          if (data.code === 200) {
            this.showAccountSecurity = false
            this.showLogin = false
            this.showChangePassword = true
          } else {
            this.$message({
              message: data.message,
              type: 'error',
              duration: 1000
            })
          }
        })
        .catch((error) => {
          console.error('There was a problem with the network request:', error)
        })
    },

    // 确认修改密码
    confirmPassword () {
      const newPwd = this.securityNewPassword.trim()
      const checkPwd = this.securityCheckNewPassword.trim()
      if (!newPwd) {
        this.$message({
          message: '新密码不能为空',
          type: 'error',
          duration: 1000
        })
      } else if (
        !/^\S*([a-zA-Z]+\S*[0-9]+|[0-9]+\S*[a-zA-Z]+)\S*$/.test(newPwd)
      ) {
        this.$message({
          message: '密码格式不正确！(必须包含:数字和字母组合,8-20位)',
          type: 'error',
          duration: 1000
        })
      } else if (newPwd !== checkPwd) {
        this.$message({
          message: '确认密码和新密码不一致!',
          type: 'error',
          duration: 1000
        })
      } else {
        this.apiChangePassword(newPwd, checkPwd)
      }
    },
    apiChangePassword (newPwd, checkPwd) {
      const params = {
        phone: this.securityPhoneNumber,
        password: newPwd,
        repassword: checkPwd
      }
      fetch('https://api.bookor.com.cn/index.php/mobile/login/forgetPassword', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      })
        .then((response) => {
          if (response.ok) {
            return response.json()
          }
          throw new Error('Network response was not ok.')
        })
        .then((data) => {
          if (data.code === 200) {
            this.$message({
              message: '修改成功，请使用新密码登录',
              type: 'success',
              duration: 1000
            })
            setTimeout(() => {
              this.showLogin = true
              this.showAccountSecurity = false
              this.showChangePassword = false

              this.securityCapture = ''
              this.securityPhoneNumber = ''
              this.securityNewPassword = ''
              this.securityCheckNewPassword = ''
            }, 1500)
          } else {
            this.$message({
              message: data.message,
              type: 'error',
              duration: 1000
            })
          }
        })
        .catch((error) => {
          console.error('There was a problem with the network request:', error)
        })
    },

    // 切换到注册
    handleRegister () {
      this.showRegister = true
      this.showLogin = false
    },
    toLogin () {
      this.showRegister = false
      this.showLogin = true
    },

    // 返回到登录页
    updateShown (payload) {
      this.showLogin = payload.showLogin
      this.showRegister = payload.showRegister
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./personal.css";
@import "@/assets/scss/login/login.scss";
@import "@/assets/scss/global.scss";
#login_form {
  @include flex-ccc;
  .login_phone_number_input {
    @include gray-input;
  }

  .login_btn {
    @include black-btn;
  }

  .login_btn:hover {
    @include gray-btn;
  }
}

.account_security_box {
  .account_security_phone_number {
    @include gray-input;
  }

  .send_sms {
    @include gray-btn;
  }
  .next_step {
    @include black-btn;
  }
  .next_step:hover {
    @include gray-btn;
  }

  .account_security_box_bottom {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
}

.account_security_change_box {
  .input_new_password {
    @include gray-input;
  }
  .next_step {
    @include black-btn;
  }
  .account_security_change_box_bottom {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
  }
}
.login-QRcode {
  // margin-left: 40px;
  height: 300px;
}
.video-con{
  width: 100%;
  display: flex;
  justify-content: center;
  video{
    width: 100%;
  }
}
.vision-dialog{
  background-color: #000;
  width: 90%;
  box-shadow: 0 0 10px 4px rgba(255, 255, 255, 1);
}
</style>
