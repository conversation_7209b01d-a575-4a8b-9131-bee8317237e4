import { Bold as OriginalBold } from 'element-tiptap'
import CustomBoldMenuButton from './MenuButton'
import { fetchEventSource } from '@microsoft/fetch-event-source'
const ctrl = new AbortController()
let startPosition = 0

const addDocAi = (fileContent, addTextAfterDelay) => {
  const messages = [
    {
      role: 'system',
      content: '你是 Kimi，由 Moonshot AI 提供的人工智能助手，你是一个文章帮写高手。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。你擅长帮助用户理解文章，并帮写一段，帮写不要超过30个汉字和引文。'
    },
    { role: 'system', content: fileContent },
    { role: 'user', content: '以上是这篇文章的内容，需要你根据以上内容，帮忙续写一段文字，尽量简短，不可以30个汉字和引文' }
  ].filter(Boolean)
  fetchEventSource('/ai/stream-response', {
    method: 'POST',
    mode: 'cors',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      messages: messages
    }),
    signal: ctrl.signal,
    onmessage: (event) => {
      const message = JSON.parse(event.data)
      if (message.status === 2) {
        ctrl.abort()
        // const content = this.msgList[this.msgList.length - 1].content
      } else {
        setTimeout(() => {
          addTextAfterDelay(message.value)
        }, 100)
      }
    },
    onclose: () => {
      ctrl.abort()
    },
    onerror: () => {
      ctrl.abort()
    }
  })
}
export default class Ai extends OriginalBold {
  menuBtnView ({ isActive, editor }) {
    return {
      component: CustomBoldMenuButton,
      componentProps: {
        isActive: isActive.bold()
      },
      componentEvents: {
        click: () => {
          const { state } = editor.view
          const { $from } = state.selection
          startPosition = $from.end()
          const slice = state.doc.slice(0, $from.end())

          let textContent = ''
          slice.content.forEach(node => {
            if (node?.textContent) {
              textContent += node.textContent
            }
          })

          const addTextAfterDelay = (text) => {
            const { state, dispatch } = editor.view
            // 创建一个新的事务
            const textNode = state.schema.text(text)
            const tr = state.tr.insert(startPosition, textNode)
            // 分派事务给编辑器
            dispatch(tr)
            // 更新光标位置
            startPosition += text.length
          }
          addDocAi(textContent, addTextAfterDelay)
          // addDocAi(textContent)
        }
      }
    }
  }
}
