<template>
  <div @click.stop>
    <!-- 打分标准 -->
    <div class="book-score-info-box" v-if="scoreType == 'info'">
      <div v-for="(item, index) in bookScoreInfo" :key="index">
        <span style="font-weight: bold">{{ item.type }}</span
        >:{{ item.desc }}
      </div>
    </div>
    <!-- 评分单项详情 -->
    <div class="book-detail-score-num-desc-modal" v-if="scoreType == 'score'">
      <div class="bdsnd-inner">
        <div class="bdsnd-left">
          <div class="bdsnd-num">
            {{
              bookScoreInitData != null
                ? bookScoreInitData.total_average_score
                : ""
            }}
          </div>
          <div class="bdsnd-num-desc">综合评分</div>
        </div>
        <div class="bdsnd-right">
          <div
            v-for="(item, index) in bookScoreDetailData"
            :key="index"
            style="display: flex; align-items: center"
          >
            <StarRate
              bg="white"
              size="large"
              :readonly="true"
              :class="`star-rating-box-${item.code}`"
              :scoreValue="bookScoreInitData[item.code]"
            />
            <div class="bdsnd-score-type-name">{{ item.type }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import StarRate from '@/components/public/star-rate.vue'
export default {
  data () {
    return {
      bookScoreInfo: [
        {
          type: '专业性',
          desc: '具专业知识的基础性/精深度'
        },
        {
          type: '原创性',
          desc: '具创新的核心概念/独特视角'
        },
        {
          type: '逻辑性',
          desc: '论证严谨/结构清晰/例证丰富'
        },
        {
          type: '启发性',
          desc: '能激发反思/具重要历史地位'
        },
        {
          type: '文雅感',
          desc: '行文流畅/文字优美/翻译准确'
        }
      ],
      bookScoreDetailData: [
        {
          type: '专业性',
          code: 'professionalism'
        },
        {
          type: '原创性',
          code: 'individualization'
        },
        {
          type: '逻辑性',
          code: 'logicality'
        },
        {
          type: '启发性',
          code: 'heuristic'
        },
        {
          type: '文雅感',
          code: 'legibility'
        }
      ]
    }
  },
  components: {
    StarRate
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      bookScoreInitData: (state) => state.scoreData,
      scoreType: (state) => state.scoreType
    })
  }
}
</script>

<style lang="scss" scoped>
.book-score-info-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #e4c4ab;
  width: 235px;
  position: absolute;
  bottom: 244px;
  padding: 5px 11px;
  left: 121px;
  font-size: 14px;
}

.book-detail-score-num-desc-modal {
  position: absolute;
  left: 0%;
  // padding: 10px 5px;
  background-color: #2c3e50;
  // border-radius: 5px;
  z-index: 3;
  width: auto;
  font-size: 14px;
  bottom: 244px;
  left: 120px;
}

.bookScoreNum {
  color: #e23636;
  font-weight: 600;
}
.book-detail-score-num-wrap {
  font-size: 16px;
  padding-top: 10px;
  position: relative;
  display: inline-block;
}

.bookNoScore {
  color: #a9a9a9;
  font-size: 15px;
  font-weight: normal;
}
.bdsnd-inner {
  width: 100%;
  display: flex;
}
.bdsnd-left {
  min-width: 60px;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}
.bdsnd-num {
  font-size: 30px;
  color: #d16060;
}
.bdsnd-num-desc {
  color: #fff;
  height: 28px;
  line-height: 28px;
}
.bdsnd-right .bdsnd-score-type-name {
  color: #ffc919;
  margin-left: 8px;
  height: 28px;
  line-height: 28px;
  min-width: 55px;
}
.book-score-data-wrap {
  padding-top: 5px;
  padding-left: 10px;
}
</style>
