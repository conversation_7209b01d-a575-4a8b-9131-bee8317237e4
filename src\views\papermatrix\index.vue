<template>
  <div class="container" @click="closeClick">
    <div class="image-container">
      <img src="@/assets/img/background.png" alt="" />
    </div>

    <div class="search-box">
      <el-autocomplete
          class="inline-input"
          v-model="keyword"
          :fetch-suggestions="querySearch"
          placeholder="请输入内容"
          :trigger-on-focus="false"
          @select="handleSelect"
          :clearable="true"
        >
        <!-- @keyup.enter.native="changeSearchKeyWord()" -->
        <template #prefix> <img class="icon" src="@/assets/img/icon_search.png" /></template>
      </el-autocomplete>
    </div>

    <div class="paper-chart" ref="paperChart">
      <div class="triangle"></div>
      <div class="no-scroll-bar paper-chart-content">
        <div v-for="year in sortedYears" :key="year" style="display: flex">
          <div class="paper-chart-year">{{ year }}</div>
          <div class="paper-chart-dot">
            <el-tooltip v-for="(value, index) in chartData[year]" :key="index" class="item" effect="dark" :content="value.paper_name || value.bibliography_content" placement="top">
              <span class="dot" :class="{
              selected:
                selectedPaperId === value.paper_id ||
                (selectedBibliographyNum === value.bibliography_num &&
                  selectedBibliographyNumPaperId === value.paper_id) ||
                value.hasKeyword,
              selectedBibliography:
                selectedPaperId === value.paper_id && value.bibliography_num,
            }"
              @click.stop="handleDotClick(value)"></span>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="circle"></div>
    </div>

    <!-- 分类信息 -->
    <div class="category-list no-scroll-bar" v-if="categoryList" v-slide-scroll>
      <div v-for="(item, index) in categoryList" :key="index" class="category-item"
        :class="curClassify == item.classify_id ? 'category-item-active' : ''">
        <div @click="handleClassify(item.classify_id)">
          {{ item.classify_name }}
        </div>
      </div>
    </div>

    <!-- 论文内容 -->
    <div v-if="paperDetails" class="paper-details" @click.stop>
      <div class="paper-details-title">
        <span v-html="paperDetails.paper_name" @click="clickPaperTitle(paperDetails)"></span>
      </div>
      <div class="paper-details-doi">
        <span class="paper-details-doi-title">DOI:</span>
        <span v-if="isHttpLink">
          <a :href="paperDetails.paper_link" target="_blank" style="text-decoration: none;color: #ff3905;">{{
            paperDetails.identification_number }}</a>
        </span>
        <span v-else class="paper-details-doi-link">
          <a :href="paperDetails.paper_link" target="_blank" style="text-decoration: none;color: #ff3905;">{{
            paperDetails.identification_number }}</a>
        </span>
      </div>
      <div class="paper-details-author no-scroll-bar">
        <span v-html="paperDetails.paper_author"></span>
      </div>
      <!-- 导读 -->
      <div class="paperIntroduction" v-if="paperIntroductionList.length > 0">
        <div class="paperIntroduction-left">导读</div>
        <div class="paperIntroduction-right no-scroll-bar">
          <div class="paperIntroduction-right-item" v-for="(item, index) in paperIntroductionList" :key="index"
            @click="clickPaperIntroductionList(item)">
            {{ item.title }}
          </div>
        </div>
      </div>
      <!-- 摘要 -->
      <div>
        <div class="paper-details-subtitle">Abstract</div>
        <div class="paper-details-abstract no-scroll-bar" v-if="paperDetails.paper_abstract">
          {{ paperDetails.paper_abstract }}
        </div>
        <div v-else class="paper-details-abstract no-scroll-bar">暂无摘要</div>
      </div>
      <div>
        <div class="paper-details-subtitle">References</div>
        <div class="paper-details-reference no-scroll-bar" v-if="paperDetails.bibliography">
          <div v-for="(item, index) in paperDetails.bibliography" :key="index">
            <span style="color: #4387cf; margin-right: 3px; cursor: pointer"
              @click.stop="clickBibliography(item, index)">[{{ item.bibliography.match(/^\d+/)[0] }}]</span>
            <span>{{ item.bibliography.replace(/^\d+\./, "") }}</span>
          </div>
        </div>
        <div v-else class="paper-details-abstract no-scroll-bar">暂无引用</div>
      </div>

    </div>
    <!-- 按钮 -->
    <drop-welt />
  </div>
</template>

<script lang="js">
import DropWelt from '@/components/dropwelt/index';
import request from '@/http/request';
import { Loading } from 'element-ui';

export default {
  components: {
    DropWelt
  },
  data () {
    return {
      keyword: '',
      dataPage: 1,
      dataLimit: 30,
      paperPage: 1,
      paperLimit: 30,

      chartData: {},

      tooltipLeft: null,
      tooltipTop: null,
      selectedPaperId: -1, // 选中的论文id
      selectedBibliographyNum: -1, // 选中的参考文献num(这个编号需要关联paperid,不是唯一的)
      selectedBibliographyNumPaperId: -1, // 和参考文献关联的
      paperDetails: null,

      isHttpLink: false,

      // 分类
      categoryList: [],
      curClassify: -1,

      // 年份
      maxYear: null,
      minYear: null,

      // 搜索防抖
      timerId: null,

      // 对比搜索的完整数组
      allData: null,

      // 导读数组
      paperIntroductionList: []
    }
  },
  methods: {
    handleSelect (item) {
      this.paperDetails = item
      // 系统文档
    },
    querySearch (val, cb) {
      if (this.keyword) {
        request('/api/Paper/getApiPaperList', {
          search: this.keyword,
          page: 1,
          limit: 10
        }).then((result) => {
          const list = result.map((item) => {
            return {
              ...item,
              title: item.paper_name,
              paper_url: item.url,
              value: item.paper_name,
              id: item.paper_id,
              type: item.type,
              url: item.url
            }
          })
          cb(list)
        })
      }
    },
    // 获取数据
    async getData (classifyId) {
      this.showLoading()
      const params = {
        page: this.dataPage,
        limit: this.dataLimit,
        page2: this.paperPage,
        limit2: this.paperLimit
      }

      if (classifyId) {
        params.classify_id = classifyId
      }
      if (this.keyword) {
        params.search = this.keyword
      }
      try {
        const [responsetPaperList, responseBibliographyList] =
          await Promise.all([
            request('/api/Paper/getPaperUploadList', params),
            request('/api/Paper/getPaperBibliographyList', {
              ...params
            })
          ])
        if (responsetPaperList) {
          this.hideLoading()
          // 合并两个数据的结果
          const concatData = this.mergeData(
            responsetPaperList,
            responseBibliographyList
          )
          // 补空
          this.maxYear = new Date().getFullYear()
          this.minYear = 1800

          const newData = {}
          for (let year = this.minYear; year <= this.maxYear; year++) {
            newData[year] = []
          }
          // 合并两个对象中的数据
          for (const key in newData) {
            if (key in concatData && concatData[key] instanceof Array) {
            // 如果两个对象中都有相同键名，则将其对应的值合并为一个数组
              newData[key] = [...concatData[key], ...newData[key]]
            }
          }
          this.chartData = newData
          if (!classifyId && !this.keyword) {
            this.allData = JSON.parse(JSON.stringify(newData))
          }
        } else {
          this.hideLoading()
          this.$message({
            message: '请求失败',
            type: 'error',
            duration: 1000
          })
        }
      } catch (error) {
        console.error(error)
        // 处理错误
      }
    },

    // 合并对象
    mergeData (data1, data2) {
      // 创建一个空对象作为合并结果
      const mergedData = {}

      // 合并 data1
      for (const key in data1) {
        // 直接将键值赋给 mergedData
        mergedData[key] = mergedData[key]
          ? mergedData[key].concat(data1[key])
          : data1[key]
      }

      // 合并 data2
      for (const key in data2) {
        // 直接将键值赋给 mergedData
        mergedData[key] = mergedData[key]
          ? mergedData[key].concat(data2[key])
          : data2[key]
      }

      return mergedData
    },
    // 获得分类信息
    async getCategoryList () {
      request('api/Paper/getCategoryList').then((result) => {
        this.categoryList = result
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 获取论文具体信息
    async getPaperDetails (paperId) {
      try {
        request('/api/Paper/getPaperUploadInfo', {
          paper_id: paperId
        }).then((result) => {
          this.paperDetails = result
          this.paperDetails.title = result.paper_name
          this.processPaperDetails(this.paperDetails)
          // 处理参考文献
          const bibliography = this.paperDetails.bibliography
          if (!bibliography?.length) {
            this.paperDetails.bibliography = null
          }
          // 处理doi
          let link = this.paperDetails.paper_link
          if (link?.startsWith('doi:')) {
            link = link.replace('doi:', '')
          }
          if (link?.startsWith('http://') || link?.startsWith('https://')) {
            this.isHttpLink = true
          } else {
            this.isHttpLink = false
          }
          this.paperDetails.paper_link = link
        }).catch(() => {
          this.$message.error('获取论文详情失败，请稍后重试')
        })
      } catch (error) {
        this.$message.error('获取论文详情失败，请稍后重试')
      }
    },

    // 参考文献的details
    handleBibliographyDetails (value) {
      const reference = value.bibliography_content || value.bibliography
      // 提取作者名字
      const { authorName, articleTitle } =
        this.extractAuthorAndTitle(reference)
      this.paperDetails = {
        paper_name: articleTitle,
        paper_author: authorName
      }
      this.processPaperDetails(this.paperDetails)
    },

    // 处理高亮的标题和作者名
    processPaperDetails (paperDetails) {
      // 对paper_name属性进行处理
      paperDetails.paper_name = this.highlight(
        paperDetails.paper_name,
        this.keyword
      )
      paperDetails.paper_author = this.highlight(
        paperDetails.paper_author,
        this.keyword
      )
    },
    highlight (value, keyword) {
      const regex = new RegExp(keyword, 'gi')
      if (value != null) {
        return value.replace(
          regex,
          '<span style="background-color: yellow;">$&</span>'
        )
      }
    },

    // 取消点击
    closeClick (event) {
      // const isDot = event.target.classList.contains('dot')
      // if ((!isDot && this.selectedPaperId !== -1) ||
      //   this.selectedBibliographyNum !== -1) {
      //   this.selectedPaperId = -1
      //   this.selectedBibliographyNum = -1
      //   this.paperDetails = null
      // }
      this.paperDetails = null
    },

    // 点击圆点
    handleDotClick (value) {
      this.selectedBibliographyNum = -1
      this.selectedPaperId = -1
      // 如果有参考文献id，则表示点击的是参考文献
      if (value.bibliography_num) {
        this.selectedBibliographyNum = value.bibliography_num
        this.selectedBibliographyNumPaperId = value.paper_id
        this.handleBibliographyDetails(value)
      } else {
        this.selectedPaperId = value.paper_id
        this.getPaperDetails(value.paper_id)
      }

      this.getPaperIntroduction(value.paper_id)
    },

    // 拆分文章标题和作者名
    extractAuthorAndTitle (bibliographyContent) {
      let authorName, articleTitle
      // 移除句首的序号
      const removeSerialNumber = bibliographyContent.replace(/^\d+\.\s*/, '')

      // 提取作者名
      const authorNameRegex = /\d+\.\s(.*?)\s\(/
      const authorNameMatch = bibliographyContent.match(authorNameRegex)
      authorName = authorNameMatch ? authorNameMatch[1] : ''

      // 提取文章标题
      // const articleTitleRegex = /\(\d+\)\.\s(.*?)\./;
      const articleTitleRegex = /\(\d+\)(.*)/
      const articleTitleMatch = removeSerialNumber.match(articleTitleRegex)
      // 如果年份在末尾的话，获取到的长度<=3
      if (articleTitleMatch && articleTitleMatch[1].length > 3) {
        articleTitle = articleTitleMatch[1]
      } else if (articleTitleMatch && articleTitleMatch[1].length <= 3) {
        const regex = /,(.*)/
        const match = removeSerialNumber.match(regex)
        if (match) {
          articleTitle = match[1]
        }
        // 第一个逗号之前的内容作为作者
        const regexAuthor = /([^,]+),/
        const matchAuthor = removeSerialNumber.match(regexAuthor)
        if (match) {
          authorName = matchAuthor[1]
        }
      } else {
        articleTitle = removeSerialNumber
      }

      // 匹配 空格al.空格 之后的内容，如有，作为标题
      const match = bibliographyContent.match(/(?<= al\. ).*/)
      if (match) {
        articleTitle = match[0]
      }

      // 匹配双引号，如有，双引号前面的表示作者，双引号内的表示标题
      // 提取双引号之前的内容
      const regexBeforeQuotes = /(.*?)(?=")/
      const beforeQuotesContent = removeSerialNumber.match(regexBeforeQuotes)
      if (beforeQuotesContent) {
        authorName = beforeQuotesContent[1]
      }
      // 提取双引号中的内容
      const regexQuotes = /"(.*?)"/g
      const quotesContents = removeSerialNumber
        .match(regexQuotes)
        ?.map((match) => match.slice(1, -1))
      if (quotesContents) {
        articleTitle = quotesContents
      }
      return { authorName, articleTitle }
    },

    // 点击详情里的参考文献
    clickBibliography (item, index) {
      this.selectedPaperId = -1
      this.selectedBibliographyNumPaperId = item.paper_id
      this.selectedBibliographyNum = index + 1
      this.handleBibliographyDetails(item)
    },

    // 选中分类
    handleClassify (value) {
      if (this.curClassify === value) {
        this.curClassify = -1
        this.getData()
      } else {
        this.curClassify = value
        this.getData(value)
      }
    },

    // 搜索
    changeSearchKeyWord () {
      this.selectedPaperId = -1
      this.selectedBibliographyNum = -1
      this.classify_id = -1

      this.chartData = this.allData
      this.getData()
    },
    // 清空搜索内容
    clearSearchBox () {
      this.keyword = ''
      this.chartData = this.allData
    },

    // 转换为word时的loading
    showLoading () {
      this.loadingInstance = Loading.service({
        fullscreen: true, // 是否全屏遮罩
        text: '加载中...', // 显示的文本
        background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
      })
    },
    hideLoading () {
      this.loadingInstance.close() // 关闭 Loading 实例
    },

    // 获取导读文章
    async getPaperIntroduction (paperId) {
      const params = {
        paper_id: paperId
      }
      const result = await request('/api/Paper/getPaperIntroduction', params)
      this.paperIntroductionList = result
    },

    // 点击导读文章
    clickPaperIntroductionList (item) {
      //
      this.apiCollectArticle(item)
      this.$router.push({
        path: '/editor',
        query: { articleId: item }
      })
    },

    // 收藏导读文章
    apiCollectArticle (item) {
      const params = {
        article_id: item.id,
        collect_member_id: item.member_id
      }
      request('/api/Paper/collectArticle', params).then((result) => {
        this.$message({
          message: '收藏文章成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // // 点击文章标题，如有url，跳转显示
    clickPaperTitle (paperData) {
      if (paperData?.type === 'own') {
        request('/api/paper/addPaper', {
          paper_title: paperData.title,
          paper_url: paperData.paper_url,
          paper_type: 'paper'
        }).then((result) => {
          this.$router.push({
            name: 'pdf',
            query: {
              url: result.paper_url,
              title: paperData.title,
              id: result.paper_id
            }
          })
        })
      } else if (paperData.paper_url) {
        if (!paperData.url) {
          return this.$message.error('文档地址为空')
        }
        const el = document.createElement('a')
        el.href = paperData.paper_url
        el.target = '_blank'
        el.click()
      } else {
        this.$message({
          message: '暂无pdf文档',
          type: 'error',
          duration: 1000
        })
      }
    }
  },

  computed: {
    sortedYears () {
      return Object.keys(this.chartData).sort((a, b) => b - a) // 对年份进行降序排序
    }
  },
  mounted () {
    this.getData()
    this.getCategoryList()
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: black;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
}
.el-popper{
  background-color: #494949;
  border: none;
}
.el-autocomplete-suggestion.is-loading li:hover{
  background-color: #494949;
}
.el-input__inner{
  height: 36px;
  color: #fff;
}
.el-popper[x-placement^=bottom] .popper__arrow{
  border-bottom-color: #494949;
}
.el-popper[x-placement^=bottom] .popper__arrow::after {
  border-bottom-color: #494949;
}
.el-autocomplete-suggestion__list li{
  color: #fff;
}
.el-autocomplete-suggestion li:hover {
    background-color: #000;
}
.image-container {
  position: relative;
  width: 100vw;
  max-height: 100%;

  img {
    width: 100vw;
    opacity: 0.8;
    height: auto;
  }
}

.search-box {
  position: fixed;
  // left: 300px;
  top: 25px;
  display: flex;
  align-items: center;
  height: 28px;
  padding: 0 6px;
  // background: rgb(255, 242, 204);
  // border-radius: 3px;
  // box-shadow: 0 0 1px 2px #494949;
  // margin-left: 30px;
  transform: translateY(5px);
  z-index: 10;
  .inline-input{
    width: 420px;
  }
  .el-input__inner{
    background-color: #000;
    box-shadow: 0 0 1px 2px #494949;
    border: none
  }
  .icon {
    width: 22px;
    height: 22px;
    position: absolute;
    top: 8px;

    &:last-of-type {
      cursor: pointer;
    }
  }

  .input {
    height: 24px;
    line-height: 24px;
    border: none;
    background: transparent;
    padding: 0 10px;

    &:focus {
      outline: none;
    }

    color: white;
  }
}

.paper-chart {
  // display: flex;
  position: absolute;
  width: 80vw;
  bottom: 9vh;
  color: white;
  display: flex;
  flex-direction: column;

  .paper-chart-content {
    // height: 60vh;
    height: 77vh;
    overflow: scroll;
  }

  .paper-chart-year {
    color: #c00000;
    font-size: 14px;
    display: inline-block;
    margin-right: 8px;
    border-right: 1px dashed #4472c4;
    width: 45px;
    // height: 3vh;
  }

  .paper-chart-dot {
    // overflow: scroll;
    width: 60vw;
  }

  .triangle {
    top: -10px;
    left: 40px;
    position: absolute;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 10px solid #4472c4;
  }

  .circle {
    width: 8px;
    height: 8px;
    left: 41px;
    bottom: -10px;
    position: absolute;
    border-radius: 50%;
    background-color: #4472c4;
  }

  .dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #fff2cc;
    margin-right: 2px;
    position: relative;
    cursor: pointer;
  }

  .selected {
    background-color: #f17723 !important;
  }

  .selectedBibliography {
    background-color: #00b0f0 !important;
  }

  .tooltip {
    position: absolute;
    padding: 2px 4px;
    height: auto;
    width: auto;
    max-width: 220px;
    max-height: 40px;
    font-size: 12px;
    color: black;
    display: flow-root;
    white-space: nowrap;
    background-color: white;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.paper-details {
  position: absolute;
  right: 4vw;
  top: 3vw;
  padding: 10px;
  background-color: white;
  width: 28vw;
  max-height: 90vh;
  font-size: 14px;

  .paper-details-title {
    font-size: 21px;
    font-family: math;
    word-break: break-all;
    cursor: pointer;
  }

  .paper-details-doi {
    margin-top: 10px;

    .paper-details-doi-title {
      color: #7b788a;
    }

    .paper-details-doi-link {
      // text-decoration: none;
      // color: inherit;
      word-wrap: break-word;
      color: #ff3905;
    }
  }

  .paper-details-author {
    color: #4387cf;
    font-weight: 600;
    // margin: 10px 0;
    max-height: 4vh;
    overflow-y: scroll;
  }

  .paper-details-subtitle {
    font-weight: 600;
    margin: 3px 0;
  }

  .paper-details-abstract {
    max-height: 25vh;
    overflow-y: scroll;
  }

  .paper-details-reference {
    max-height: 25vh;
    overflow-y: scroll;
  }

  // 导读
  .paperIntroduction {
    display: flex;
    align-items: center;

    .paperIntroduction-left {
      color: white;
      background-color: #2c76b6;
      width: 30px;
      padding: 2px 5px;
      border-radius: 5px;
      height: 20px;
      margin-right: 5px;
    }

    .paperIntroduction-right {
      width: 335px;
      max-height: 50px;
      overflow-y: scroll;
    }

    .paperIntroduction-right-item {
      cursor: pointer;
      font-weight: 600;
      white-space: nowrap;
      /* 禁止换行 */
      overflow: hidden;
      /* 隐藏溢出部分 */
      text-overflow: ellipsis;
      /* 显示省略号 */
    }
  }
}

.category-list {
  display: flex;
  /* color: white; */
  position: fixed;
  bottom: 2vh;
  left: 28vh;
  width: 80vw;
  overflow-x: scroll;

  .category-item {
    color: #0070c0;
    margin-right: 10px;
    border: 1px solid;
    padding: 0px 5px;
    font-size: 15px;
    border-radius: 5px;
    cursor: pointer;
    white-space: nowrap;
  }

  .category-item-active {
    background-color: #2a75b5;
    color: white;
    border: 1px solid #2a75b5;
  }
}

@media (max-width: 600px) {
  .category-list {
    bottom: 25px;
    left: 5px;
    width: 100vw;
  }

  .paper-details {
    width: 50vw;
    max-height: 83vh;
    top: 20vw;
    z-index: 10;
  }

  .paper-details-abstract {
    max-height: 20vh !important;
    overflow-y: scroll !important;
  }

  .paper-details-reference {
    max-height: 20vh !important;
    overflow-y: scroll !important;
  }
}

.no-scroll-bar::-webkit-scrollbar {
  display: none;
}
</style>
