<template>
  <div class="monitor-report">
    <div class="office">
      <MonitorOffice ref="monitorOffice" :option="option"></MonitorOffice>
      <div class="mask-title" ref="maskTitle">
        <div @click.prevent="downloadFile">
          <img
            src="./img/download.png"
            alt=""
            style="
              position: absolute;
              top: 2px;
              width: 20px;
              left: 97px;
              cursor: pointer;
            "
          />
          <a :href="fileUrl"></a>
        </div>
      </div>
      <div class="mask" ref="mask"></div>
      <div class="mask-left" ref="maskLeft"></div>
    </div>
  </div>
</template>

<script>
import MonitorOffice from './only-office'
import request from '@/http/request'
export default {
  components: {
    MonitorOffice
  },
  props: {
    url: {
      type: String,
      default: ''
    },
    paper_id: {
      type: Number
    },
    title: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      // office 配置参数
      option: {
        url: this.url,
        isEdit: false,
        fileType: 'docx',
        title: this.title,
        editUrl:
          'https://api.bookor.com.cn/api/paper/index?paper_id=' + this.paper_id
      },
      fileUrl: '',
      fileTitle: ''
    }
  },

  watch: {
    url (newUrl) {
      this.option.url = newUrl
    },
    paper_id () {
      this.option.editUrl =
        'https://api.bookor.com.cn/api/paper/index?paper_id=' + this.paper_id
    },
    title (newVal) {
      this.option.title = newVal
    }
  },
  methods: {
    getWordMes () {
      request('/api/Paper/getPaperInfo', {
        paper_id: this.paper_id
      })
        .then((result) => {
          this.fileTitle = result.paper_title
          this.fileUrl = result.paper_url
        })
    },
    downloadFile () {
      this.getWordMes()
      if (!(this.fileTitle && this.fileUrl)) {

      } else {
        // 创建隐藏的<a>元素
        const link = document.createElement('a')
        link.href = this.fileUrl // 使用 this.fileUrl 获取文件的完整路径
        link.download = this.fileTitle // 指定下载的文件名
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    }
  },
  mounted () {
    // 延迟5秒后显示 mask 和 mask-left 盒子
    setTimeout(() => {
      this.$refs.mask.style.display = 'block'
      this.$refs.maskLeft.style.display = 'block'
      this.$refs.maskTitle.style.display = 'block'
    }, 6000)
    this.getWordMes()
  },
  beforeUnmount () {
    // 销毁 only-office 组件
    this.$refs.monitorOffice.$destroy()
  }
}
</script>
<style lang="scss" scoped>
.monitor-report {
  .office {
    height: 93vh;
    position: relative;
    .mask-title {
      display: none;
      position: absolute;
      top: 0px;
      left: 6px;
      height: 4.5vh;
      // height: 26px;
      line-height: 26px;
      // width: 100px;
      width: 9vw;
      color: white;
      font-size: 13px;
      background-color: #446995;
    }
    .mask {
      display: none;
      position: absolute;
      top: 28px;
      left: 0px;
      height: 32px;
      width: 100%;
      background-color: #446995;
    }
    .mask-left {
      display: none;
      position: absolute;
      top: 147px;
      left: 0px;
      height: 29px;
      width: 39px;
      background-color: #f1f1f1;
    }
  }
}
@media (max-width: 700px) {
  .monitor-report .office .mask {
    display: none !important;
  }
  .monitor-report .office .mask-left {
    display: none;
  }
}
</style>
