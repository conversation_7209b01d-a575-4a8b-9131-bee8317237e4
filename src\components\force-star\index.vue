<template>
  <div>
    <div id="3d-graph" @click="handleClick"></div>
    <div class="book-container" id="book-container">
      <h3 v-if="lineTitle" class="keyword-title">{{ lineTitle.title }}</h3>
      <book
        :isShowInfo="isShowInfo"
        :currentBookId="currentBookId"
        :currentKeyword="currentKeyword"
        :currentNodeId="currentNodeId"
        @showBookDetail="showBookDetail"
      />
      <book
        v-for="item in currentBondList"
        :key="item?.book_keywords_id || item.member_keywords_id"
        :isShowInfo="isShowInfo"
        :currentBookId="item.book_keywords_id"
        :currentKeyword="item.keywords"
        :currentNodeId="item.member_keywords_id"
        @showBookDetail="showBookDetail"
      />
    </div>
    <BookDetail :book_id="choosedBookId" :key="choosedBookId" />
    <BookDetailsPopup />
    <!-- <MindMap /> -->
    <div class="node-active" v-show="activeLabel.text" :style="getStyle">
      <p>{{activeLabel.text}}</p>
    </div>
  </div>
</template>

<script lang="js">
import * as THREE from 'three'
import ForceGraph3D from '3d-force-graph'
import { getBondNode, getBondTitle } from './utils'
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass'
import book from './components/book/index.vue'
// import MindMap from '@/components/mind-map/index.vue'
import BookDetailsPopup from '@/components/nebula-sub/book-details-popup.vue'
import BookDetail from '@/components/nebula-sub/book-details.vue'
import { EventBus } from '@/main.js'
import { mapState, mapMutations } from 'vuex'

let Graph = null
let isRotationActive = true

let isDragging = false // 是否正在拖动
let startX = 0 // 鼠标按下时的X坐标
let scrollLeft = 0 // 当前滚动的距离
let bloomPass = null

const dashedMaterial = new THREE.LineDashedMaterial({
  color: '#fff',
  dashSize: 1,
  gapSize: 2,
  linewidth: 5,
  scale: 1
})
export default {
  data: function () {
    return {
      activeLabel: {
        text: '',
        x: 0,
        y: 0
      },
      lineTitle: null,
      isShowInfo: false,
      currentKeyword: '',
      currentBookId: '',
      currentNodeId: '',
      currentBondList: [], // 当前关联列表
      lastNodeCount: 0 // 用于跟踪节点数量变化
    }
  },
  computed: {
    ...mapState('book', ['choosedBookId', 'isShowBookDetail']),
    ...mapState('indexsearch', {
      choosedSearchItem: (state) => state.chooseSearchItem
    }),
    getStyle () {
      return {
        left: `${this.activeLabel.x}px`,
        top: `${this.activeLabel.y}px`
      }
    }
  },
  props: ['forceData', 'bondList'],
  watch: {
    choosedBookId (newVal) {
      // 在这里处理 choosedBookId 的变化
      this.setShowDetails()
    },
    isShowBookDetail (newVal) {
      if (newVal === 'false') {
        // 解除锁定
        this.isLocked = false
      }
    },
    choosedSearchItem (value) {
      this.isLocked = false
      this.chooseSearchItem(value)
    },
  },
  components: {
    book,
    // MindMap,
    BookDetailsPopup,
    BookDetail
  },
  methods: {
    ...mapMutations({
      setChosenBookId: 'book/SET_CHOSED_BOOK_ID',
      setShowDetails: 'book/showDetails'
    }),
    handleClick () {
      this.currentBookId = null
      this.currentNodeId = null
      // this.isShowInfo = false
    },

    updateGraphDynamic() {
      // if (!Graph || !newNodes || newNodes.length === 0) {
      //   console.error('force-star: updateGraphDynamic 条件不满足')
      //   return
      // }

      Graph.graphData(this.forceData);
    },

    // 为新节点添加出现动画
    animateNewNodes(newNodes) {
      if (!newNodes || newNodes.length === 0) return

      // 为新节点添加渐现效果
      newNodes.forEach((node, index) => {
        setTimeout(() => {
          if (Graph) {
            // 暂时高亮新节点
            const originalColor = node.color
            node.color = '#ffff00' // 黄色高亮

            // 1秒后恢复原色
            setTimeout(() => {
              node.color = originalColor
              // 使用refresh()方法更新视觉效果
              Graph.refresh()
            }, 0)
          }
        }, index * 50) // 每个节点延迟50ms出现
      })
    },
    nodeAnimation (node) {
      this.activeLabel.text = ''
      // bloomPass.strength = 0
      const distance = 200
      const distRatio = 1 + distance / Math.hypot(node.x, node.y, node.z)
      const newPos = node.x || node.y || node.z
        ? { x: node.x * distRatio, y: node.y * distRatio, z: node.z * distRatio }
        : { x: 0, y: 0, z: distance } // special case if node is in (0,0,0)
      Graph.cameraPosition(
        newPos, // new position
        node, // lookAt ({ x, y, z })
        1000 // ms transition duration
      )
      setTimeout(() => {
        // bloomPass.strength = 2
        this.activeLabel = {
          text: node.user,
          x: window.innerWidth / 2 - (node.user.length * 8),
          y: (window.innerHeight / 2) - 50
        }
      }, 1100)
    },
    async init () {
      const elem = document.getElementById('3d-graph')
      // console.log('初始化3D图形，节点数:', this.forceData?.nodes?.length || 0)
      Graph = ForceGraph3D({
        antialias: true
      })(elem)
        .graphData({
          nodes: this.forceData?.nodes || [],
          links: this.forceData?.links || []
        })
        .backgroundColor('#000')
        .nodeLabel(node => node.user)
        .nodeId('bookId')
        // Use the nodeSize property to set varying node sizes
        .nodeRelSize(0.6) // Base size multiplier
        .nodeVal(node => node.nodeSize ? node.nodeSize * 3 : 1) // Use custom node sizes
        .enableNodeDrag(true)
        .cooldownTicks(0)
        // Use each node's color
        // .nodeColor(node => node.color || '#21cfc9')
        // Make links more visible
        .linkWidth(link => link.width || 0.3)
        .linkOpacity(0.8)
        .linkColor(link => link.color || '#21cfc988')
        // .linkAutoColorBy('group')
			  // .nodeAutoColorBy('group')
        .linkCurvature(0.25) // 设置连接线的弯曲程度，0 是直线，越大越弯
        .linkCurveRotation(Math.PI / 2) // 控制弯曲方向（旋转角度）
        // .nodeThreeObject(node => {
        //   // Vary the size based on node's size property
        //   const size = node.nodeSize ? node.nodeSize * 0.6 : 0.6;

        //   // Create a glowing sphere for each node
        //   const sphereGeometry = new THREE.SphereGeometry(size);
        //   const sphereMaterial = new THREE.MeshBasicMaterial({
        //     color: node.color || '#21cfc9',
        //     transparent: false,
        //     opacity: 1.0
        //   });
        //   return new THREE.Mesh(sphereGeometry, sphereMaterial);
        // })

        // Set initial camera position to view the 3D brain structure optimally
        // Position at an angle to show brain outline
        .onNodeClick(node => {
          // 高亮当前节点
          // const originalColor = node.color
          // node.color = '#ffff00' // 高亮为黄色
          // Graph.refresh()

          if (node.titleId) {
            this.lineTitle = {
              title: node.title,
              id: node.titleId
            }
          } else {
            this.lineTitle = null
          }
          // bloomPass.strength = 0
          const currentBond = getBondNode(this.bondList, node)
          if (currentBond) {
            this.currentBondList = currentBond.point_list.filter(item => item.keywords !== node.user)
          } else {
            this.currentBondList = []
          }
          this.nodeAnimation(node)
          this.isShowInfo = true
          this.currentBookId = node.bookId
          this.currentNodeId = node.nodeId
          this.currentKeyword = node.user
        })

      // Set initial camera view
      Graph.cameraPosition({ x: 600, y: 0, z: 400 });
        // return

      // 动画 - Optimize rotation to showcase brain outline
      // let angle = 0
      // const distance = 1000 // Further back to see the whole brain
      // setInterval(() => {
      //   if (isRotationActive) {
      //     // Rotate to highlight the brain outline
      //     Graph.cameraPosition({
      //       // Slower rotation around brain to clearly see the outline
      //       x: distance * Math.sin(angle * 0.6),
      //       y: 200 + 150 * Math.sin(angle * 0.3), // Gentle up/down to see top and bottom
      //       z: distance * Math.cos(angle * 0.6)
      //     })
      //     angle += Math.PI / 400 // Slower rotation
      //   }
      // }, 100)

      // setTimeout(() => {
      //   isRotationActive = false
      // }, 8000) // Longer time to show full outline

      // NOTE: 由于数据点过多， 此方案已经不再适用
      // bloomPass = new UnrealBloomPass()
      // bloomPass.strength = 2 // 发光强度
      // bloomPass.radius = 1 // 表示泛光散发的半径
      // bloomPass.threshold = 0 // 表示产生泛光的强度阀值，如果照在屋里上的光照强度大于该值就会产生泛光
      // Graph.postProcessingComposer().addPass(bloomPass)
    },
    // 清除页面上的截面标签
    clearObjectsLabels () {
      // 移除之前所有的标识符元素
      // isShowInfo = false;
      const visibleMarkerList =
        document.getElementsByClassName('visible-marker')
      while (visibleMarkerList.length > 0) {
        visibleMarkerList[0].parentNode.removeChild(visibleMarkerList[0])
      }
    },
    clearShow () {
      this.isShowInfo = false
      this.isShowInfoArrayLocked = false
      this.isShowInfoArray = false
      this.entryids = {
        book_keywords_id: '',
        member_keywords_id: ''
      }
      this.isShowEntryAddClassify = false
    },
    showBookDetail (id) {
      if (id) {
        this.setChosenBookId(id)
        const previousLabel = document.getElementById('object-label')
        if (previousLabel) {
          previousLabel.remove()
        }
        this.keyword = ''
        this.clearObjectsLabels()
        this.clearShow()
      } else {
        this.$message({
          message: '暂无对应图书',
          type: 'error',
          duration: 1000
        })
      }
    }
  },
  // async beforeCreate() {
  //    await getData();
  // },
  async mounted () {
    await this.init()
    console.log('force-star: 组件已挂载，Graph初始化完成')

    // Fix node positions after graph initialization
    // setTimeout(() => {
    //   const { nodes } = Graph.graphData();
    //   nodes.forEach(node => {
    //     if (node.x && node.y && node.z) {
    //       node.fx = node.x;
    //       node.fy = node.y;
    //       node.fz = node.z;
    //     }
    //   });
    //   Graph.refresh(); // Refresh the graph to apply fixed positions
    // }, 100);

    const container = document.getElementById('book-container')
    container.addEventListener('mousedown', function (event) {
      isDragging = true
      startX = event.clientX
      scrollLeft = container.scrollLeft
    })
    container.addEventListener('mousemove', function (event) {
      if (!isDragging) return

      var distance = event.clientX - startX
      container.scrollLeft = scrollLeft - distance
    })

    container.addEventListener('mouseup', function (event) {
      isDragging = false
    })

    EventBus.$on('openDetail', (item) => {
      this.isShowInfo = true
      const { nodes } = Graph.graphData()
      let node = null
      if (item.type && item.type === 'KEYWORD_TITLE') {
        const currentBond = getBondTitle(this.bondList, item)
        const first = currentBond.point_list[0]
        item.user = first.keywords
        this.currentBondList = currentBond.point_list.filter(child => child.keywords !== item.user)
        node = nodes.find(i => {
          if (first.book_keywords_id) {
            return i.bookId === first.book_keywords_id
          } else {
            return i.nodeId === first.member_keywords_id
          }
        })
        this.lineTitle = {
          title: currentBond.title[0].lines_title,
          id: currentBond.title[0].mix_keywords_lines_id
        }
      } else {
        node = nodes.find(i => {
          if (item.bookId) {
            return i.bookId === (item.bookId)
          } else {
            return i.nodeId === item.nodeId
          }
        })
        const currentBond = getBondNode(this.bondList, node)
        if (currentBond) {
          this.currentBondList = currentBond.point_list.filter(child => child.keywords !== item.user)
        } else {
          this.currentBondList = []
        }
        if (node.titleId) {
          this.lineTitle = {
            title: node.title,
            id: node.titleId
          }
        } else {
          this.lineTitle = null
        }
      }
      this.currentKeyword = item.user
      this.currentBookId = node.bookId
      this.currentNodeId = node.nodeId
      this.currentKeyword = node.user
      this.nodeAnimation(node)
    })
  }
}

</script>

<style lang="scss">
.book-container{
   position: absolute;
   bottom: 15px;
    /* z-index: 9999999; */
    bottom: 0;
    display: flex;
    max-width: 100%;
    overflow-x: scroll;
    padding-top: 30px;
    .keyword-title{
      position: absolute;
      color: #fff;
      top: 0;
      left: 12px;
      z-index: 9;
    }
}
.node-label{
  color: rgb(234, 178, 4);
  background-color: black;
  border: 1px solid rgb(154, 182, 200);
  padding: 2px 4px;
}
.node-active{
  position: absolute;
  display: inline-block;
  padding: 2px 4px;
  color: rgb(234, 178, 4);
  border: 1px solid rgb(154, 182, 200);
}
</style>
