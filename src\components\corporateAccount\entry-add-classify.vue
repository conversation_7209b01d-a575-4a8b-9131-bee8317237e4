<template>
  <div class="container">
    <div class="bookor_tag_modal">
      <div class="bookor_tag_modal_mask"></div>
      <div class="bookor_tag_modal_content" v-if="type != 'book'">
        <div class="bookor_tag_modal_handle_group">
          <div class="bookor_tag_modal_handle_select">
            {{ classifyTitle }}分类:
          </div>
          <!--  @input="bindNewTagKeyInput" -->
          <input class="bookor_tag_modal_add_input" v-model="newTagValue" />
          <div
            class="bookor_tag_modal_add_confirm"
            @click="handleConfirmAddNewTag"
          >
            新增
          </div>
        </div>
      </div>
      <div class="bookor_tag_modal_content" v-if="type == 'book'">
        <div class="bookor_tag_modal_handle_group">
          <input
            class="bookor_tag_modal_add_input custom-placeholder"
            style="width: 120px"
            v-model="newTagValue"
            @input="bindBookKeyInput"
            placeholder="搜索已上架图书"
          />
        </div>
        <!-- 提示 -->
        <div v-if="isShowSearchCue">
          <div class="bookor_tag_modal_tag_list no-scroll-bar">
            <div
              class="bookor_tag_modal_tag_list_item"
              v-for="(item, index) in source"
              :key="index"
            >
              <div
                style="width: 100%; cursor: pointer"
                @click.stop="handleSelectBook(item.goods_book_id)"
              >
                {{ item.book_name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import axios from '@/http'
import { mapState } from 'vuex'
import request from '@/http/request'
export default {
  data () {
    return {
      newTagValue: '', // 用户输入的搜索词
      isShowSearchCue: false, // 提示盒子
      source: [], // 搜索后结果
      currentEditTagList: []
    }
  },
  props: {
    pid: {
      type: Number
    },
    classifyTitle: {
      type: String
    },
    type: {
      type: String,
      default: 'classify'
    },
    category_id: {
      type: Number
    }
  },
  computed: {
    ...mapState('corporateAccountClassify', {
      bookClassifyList: (state) => state.bookClassifyList,
      sellBookList: (state) => state.sellBookList
    })
  },
  methods: {
    // 新增分类接口
    // 点击新增按钮
    handleConfirmAddNewTag () {
      // this.addNewTag(this.newTagValue);
      this.addClassify()
    },

    // 新增分类api
    async addClassify () {
      const params = {
        name: this.newTagValue
      }
      if (this.pid) {
        params.pid = this.pid
      }
      request('/api/BookCategory/addCategory', params).then((res) => {
        this.$store.dispatch('corporateAccountClassify/getBookClassifyList')
        this.$emit('closeClassify')
        this.$message({
          message: '添加分类成功',
          type: 'success',
          duration: 1000
        })
      }).catch((res) => {
        this.$message({
          message: res.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 获取已上架图书提示
    bindBookKeyInput () {
      const value = this.newTagValue
      // 数据源
      const res = this.sellBookList
      const source = []
      const reg = new RegExp(value, 'i')
      res.forEach((item, index) => {
        if (reg.test(item.book_name)) {
          source.push(item)
        }
      })
      this.source = source
      this.isShowSearchCue = true
    },

    // 选中某本书,关联某个分类
    async handleSelectBook (id) {
      const params = {

        goods_book_id: id,
        category_id: this.category_id
      }

      request(
        '/api/BookCategory/addGoodsBookCategory',
        params
      ).then((result) => {
        this.$message({
          message: '图书分类成功',
          type: 'success',
          duration: 1000
        })
        this.$emit('closeClassify')
        this.$store.dispatch(
          'corporateAccountClassify/getGoodsBookByCategory',
          this.category_id
        )
      }).catch((res) => {
        this.$message({
          message: res.message,
          type: 'error',
          duration: 1000
        })
      })
    }
  },
  mounted () {}
}
</script>

<style lang="scss" scoped>
.container {
  background-color: white;
  width: 130px;
  border-radius: 5px;
  padding: 0 3px;
  .bookor_tag_modal_item {
    font-size: 14px;
    margin-right: 5px;
    color: #4caf50;
    padding: 0 5px;
    display: inline-block;
    border-radius: 5px;
    background-color: #f1f1f1;
    margin-bottom: 5px;
    font-weight: normal;
  }

  .bookor_tag_modal_handle_group {
    display: flex;
    justify-content: space-between;
    padding: 10px 0 0;
    align-items: center;
  }

  .bookor_tag_modal_handle_select {
    font-size: 12px;
    color: #91a4b9;
  }

  .bookor_tag_modal_handle_add {
    color: #36ade2;
    font-size: 15px;
  }

  .bookor_tag_modal_add_input_wrap {
    display: flex;
  }

  .bookor_tag_modal_add_input {
    // width: 50%;
    width: 38px;
    margin-right: 5px;
    border-radius: 5px;
    color: #000;
    padding: 4px;
    border: 1px solid #dcdcdc;
    font-size: 13px;
    &::placeholder {
      color: #5086d1;
    }
  }

  .bookor_tag_modal_add_confirm {
    color: #fff;
    width: 25px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #60c1ec;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
  }

  .bookor_tag_modal_tag_list {
    max-height: 70px;
    overflow-y: scroll;
    background-color: #f1f1f1;
    /* width: 51%; */
    padding: 0 5px 5px;
    z-index: 100;
    box-sizing: border-box;
    border-radius: 5px;
    margin: auto;
    /* position:absolute;
 top: 280rpx;
    right: 130rpx; */
  }

  .bookor_tag_modal_tag_list_item {
    font-size: 14px;
    line-height: 20px;
    border-bottom: 1px solid #dcdcdc;
    padding: 5px 0;
    color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .bookor_tag_modal_list_del_btn {
    width: 25px;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }

  .bookor_tag_modal_list_del_icon {
    width: 13px;
  }
}

.no-scroll-bar::-webkit-scrollbar {
  display: none;
}
</style>
