import ElementTiptapPlugin from 'element-tiptap'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

import 'animate.css'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './assets/icon-font/iconfont.css'

// import DirectivesPlugin from './directives/directives.plugin'

// Vue.use(DirectivesPlugin)
Vue.prototype.$video = videojs

export const EventBus = new Vue()
Vue.prototype.$bus = EventBus
Vue.use(ElementUI)
Vue.use(ElementTiptapPlugin)

Vue.config.productionTip = false

// �? Vue 应用程序启动时执行获取身份信息的操作
if (localStorage.getItem('userinfo')) {
  store.dispatch('user/getUserInfoMore')
  store.dispatch('user/getUserInfoEnterprise')
}
new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')
