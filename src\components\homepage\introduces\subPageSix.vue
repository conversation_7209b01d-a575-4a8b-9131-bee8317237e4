<template>
<div class="section">
    <div class="page-box">
        <div class="title-part1">什么是元知识</div>
        <div class="content-box-part1">
            <div class="content-title-part1">
                <span
                    style="color: #a78bfb">元知识</span><span>——原著的最小知识单元，是反映作者思想和原创性的载体。元知识之间严密的推导关系和逻辑联系能支撑作者的整个思想架构。包括以下四个方面：
                </span>
            </div>
            <div class="content-inside">
                <div class="content-inside-item">
                    <span class="content-sub-title-part1">核心概念</span>
                    <div class="content-text">
                        论述所涉及到的专业概念、理论及主张。它构成全书的框架和主旨。
                    </div>
                </div>
                <div class="content-inside-item">
                    <span class="content-sub-title-part1">理论起承
                    </span>
                    <div class="content-text">
                        作者的思想起源和发展脉络，涉及其他理论、概念或理论。
                    </div>
                </div>
                <div class="content-inside-item">
                    <span class="content-sub-title-part1">证据与论据
                    </span>
                    <div class="content-text">
                        作者为支撑观点或论证过程而采用的数据、案例和信息等。
                    </div>
                </div>
                <div class="content-inside-item">
                    <span class="content-sub-title-part1">结论主张
                    </span>
                    <div class="content-text">
                        作者的结论性意见。能代表其立场和对所探讨问题的最终看法。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</template>
<style lang="scss" scoped>
.title-part1 {
    color: white;
    font-size: 26px;
    font-weight: 600;
    text-align: center;
    margin-top: 5%;
}
.content-title-part1{
  text-align: center;
  color: #fff;
  width: 60vw;
  margin: 50px auto;
}
.content-inside{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  width: 1000px;
  margin: 6% auto;
  gap: 60px;
  .content-inside-item{
    flex: 1;
  }
  .content-sub-title-part1{
    color: #40dbff; font-size: 25px;
  }
  .content-text{
    color: white;
    font-weight: 600;
    font-size: 17px;
    margin-top: 10px;
    text-align: left;
  }
}
@media only screen and (max-width: 420px) {
  .content-title-part1{
    width: 80vw;
  }
  .content-inside{
    width: 80vw;
    flex-direction: column;
    gap: 20px;
    .content-inside-item{
      text-align: center;
    }
    .content-sub-title-part1{
      text-align: center;
    }
  }
}
</style>
