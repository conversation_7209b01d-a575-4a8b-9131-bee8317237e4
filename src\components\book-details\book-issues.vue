<template>
  <div style="padding-top: 5px; height: 200px" @click.stop="closeInputBox">
    <div
      class="issuesList-item"
      v-for="(item, index) in topicList"
      :key="index"
      @click="showDeleteDialog(item)"
    >
      {{ item.topics_content }}
    </div>
    <div>
      <div v-if="!showInput" class="add-btn" @click.stop="showInput = true">
        +
      </div>
      <input
        v-else
        class="input-box"
        type="text"
        v-model="inputValue"
        @keyup.enter="addBooktopics"
        @input="handleInput"
        @click.stop="stopEventBubble"
      />
      <!-- @blur="showInput = false" -->
      <div
        v-if="searchList.length > 0"
        class="searchList-box"
        @scroll="handleScrollSearchList"
      >
        <div
          v-for="(item, index) in searchList"
          :key="index"
          class="searchList-item"
          @click.stop="chooseBookTopics($event, item.topics_content)"
        >
          {{ item.topics_content }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import request from '../../http/request'
import { MessageBox } from 'element-ui'
export default {
  data () {
    return {
      showInput: false,
      inputValue: '',
      topicList: [],
      // data: [],
      echartsData: {},
      // 搜索
      searchPage: 1,
      searchLimit: 10,
      searchList: [],
      userinfo: JSON.parse(localStorage.getItem('userinfo'))
    }
  },
  props: {
    bookName: {
      type: String
    },
    bookImg: {
      type: String
    }
  },
  watch: {
    bookName: {
      handler (newValue, oldValue) {
        this.apiGetBookstopics()
      },
      immediate: true // 设置 immediate 为 true，可以在 watcher 创建后立即执行一次回调函数，以处理初始值的情况
    },
    bookImg: {
      handler (newValue, oldValue) {
        this.apiGetBookstopics()
      },
      immediate: true
    }
  },
  inject: ['book_id'],
  methods: {
    // 初始化的数据,本书的议题
    async apiGetBookstopics () {
      const params = {
        book_id: this.book_id,
        page: 1,
        limit: 100
      }
      const result = await request('/api/Booktopics/booktopicsList', params)
      this.topicList = result
      for (let i = 0; i < this.topicList.length; i++) {
        const topic = this.topicList[i]
        const books = await this.fetchTopicListData(topic)
        this.topicList[i].topicBooks = books
      }
      this.handleDataForEcharts(this.topicList)
    },
    // 获取议题对应的图书信息
    async fetchTopicListData (topic) {
      const params = {
        topics_content: topic.topics_content,
        book_id: topic.book_id
      }
      const result = await request('/api/Booktopics/topicsBookList', params)
      return result
    },

    // 拼接数据
    handleDataForEcharts (array) {
      if (this.bookName) {
        const data = []
        const links = []
        // Add root node
        const rootNode = {
          id: 'root',
          name: '《' + this.bookName + '》',
          category: 0,
          book_cover: this.bookImg
        }
        data.push(rootNode)

        array.forEach((obj, index) => {
          const nodeId = Date.now() + Math.random()
          const node = {
            id: `node${nodeId}`,
            name: obj.topics_content,
            category: 1
          }
          data.push(node)
          links.push({ source: 'root', target: `node${nodeId}`, value: 1 })

          if (obj?.topicBooks?.length > 0) {
            obj.topicBooks.forEach((book) => {
              const bookId = Date.now() + Math.random() // 获取当前时间戳作为替代索引
              const bookNode = {
                id: `book${bookId}`,
                name: '《' + book.book_name + '》',
                category: 2,
                book_id: book.book_id // 添加 book_id 属性
              }
              data.push(bookNode)
              links.push({
                source: `node${nodeId}`,
                target: `book${bookId}`,
                value: 1
              })
            })
          }
        })
        this.echartsData = {
          data,
          links
        }
        if (array.length > 0) {
          this.$store.commit('book/SET_ECHARTS_DATA', this.echartsData)
          this.$store.commit('book/showBookTopic')
        } else {
          // 无议题时不画图
          this.$store.commit('book/SET_ECHARTS_DATA', null)
        }
      }
    },
    // api新增议题
    async apiAddBooktopics (topicsContent) {
      const params = {
        book_id: this.book_id,
        topics_content: topicsContent
      }
      await request('/api/Booktopics/addBooktopics', params)
      this.apiGetBookstopics()
      this.$message({
        message: '新增成功',
        type: 'success',
        duration: 1000
      })
      this.closeInputBox()
    },
    addBooktopics () {
      this.apiAddBooktopics(this.inputValue)
    },
    stopEventBubble (event) {
      event.stopPropagation()
    },
    // 关闭input框
    closeInputBox () {
      this.showInput = false
      this.inputValue = ''
      this.searchList = []
    },
    // 处理输入
    async handleInput () {
      this.searchList = []
      const input = this.inputValue.trim()
      if (input.length > 0) {
        await this.apiSearchTopics(input)
      }
    },
    // 搜索
    async apiSearchTopics (input) {
      const params = {
        search: input,
        page: this.searchPage,
        limit: this.searchLimit
      }
      request('/api/Booktopics/searchBooktopicsList', params).then((result) => {
        const newList = result
        if (newList.length > 0) {
          // 过滤content相同的结果
          const cache = []
          const filteredList = newList.filter((item) => {
            if (cache.includes(item.topics_content)) {
              return false // 从数组中删除当前元素
            } else {
              cache.push(item.topics_content) // 将当前元素的 topics_content 加入缓存数组
              return true // 保留当前元素在数组中
            }
          })

          this.searchList = this.searchList.concat(filteredList)
        } else {
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 选中
    chooseBookTopics (event, topicsContent) {
      event.stopPropagation()
      this.inputValue = topicsContent
      this.apiAddBooktopics(this.inputValue)
    },

    // 滚动加载
    async handleScrollSearchList (event) {
      // scrollHandler(event, this.pageNote, this.apiGetNote);
      const elem = event.target
      if (elem.scrollTop + elem.clientHeight >= elem.scrollHeight) {
        this.searchPage++
        await this.apiSearchTopics(this.inputValue)
      }
    },

    // 选中已有的标签
    showDeleteDialog (item) {
      if (item.member_id !== this.userinfo.userid) {
        return
      }
      // 调用MessageBox.confirm方法显示弹窗
      MessageBox.confirm('是否删除该议题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.apiDeleteMyBookTopic(item)
        })
    },

    // 删除议题
    async apiDeleteMyBookTopic (item) {
      const params = {
        topics_id: item.topics_id
      }
      request('/api/Booktopics/deleteBooktopics', params).then((result) => {
        this.apiGetBookstopics()
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    }
  },
  mounted () {
    this.apiGetBookstopics()
  },
  beforeDestroy () {
    this.echartsData = {}
    this.$store.commit('book/SET_ECHARTS_DATA', this.echartsData)
  }
}
</script>
<style lang="scss" scoped>
.issuesList-item {
  display: inline-block;
  padding: 3px 8px;
  background-color: #2e72b5;
  border-radius: 5px;
  font-size: 12px;
  color: white;
  margin: 4px 4px 0px 0;
  font-weight: bold;
  cursor: pointer;
}
.add-btn {
  border: 2px solid #28619a;
  // display: inline-block;
  width: 15px;
  height: 17px;
  text-align: center;
  border-radius: 4px;
  line-height: 15px;
  color: white;
  margin-top: 5px;
  cursor: pointer;
}

.input-box {
  border: 2px solid #28619a;
  border-radius: 4px;
  padding: 2px;
  width: 210px;
  display: block;
  margin-top: 5px;
  height: 17px;
}

.searchList-box {
  background-color: white;
  width: 210px;
  padding: 2px;
  margin: 0 2px;
  font-size: 14px;
  max-height: 90px;
  overflow: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
}
.searchList-item:hover {
  color: white;
  background-color: #2e72b5;
}
</style>
