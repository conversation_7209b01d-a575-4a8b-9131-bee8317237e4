<template>
  <div class="historyFlashBox">
    <!-- 关闭历史记录弹窗 -->
    <i class="el-icon-circle-close" @click="setHistoryFlashBoo(false)"></i>
    <div style="margin-top: 7%" v-loading="loading">
      <div class="swiper-container-history">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="item in swiperList" :key="item.content_id">
            <div class="swiper-slide-imgBox">
              <img style="height: 100%" :src="item.content_img ? item.content_img : item.image" />
            </div>

            <div class="inverted_current">
              <img style="height: 100%" :src="item.content_img ? item.content_img : item.image" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="history-prop" @click="close" v-show="isShow">
      <div class="popover">
        <div class="video">
          <!-- <img :src="item.img" alt="" style="height: 100%; width: 100%;"> -->
          <el-image style="width: 100%; height: 100%" :src="historyItem.content_img
            ? historyItem.content_img
            : historyItem.image
            " :fit="'cover'"></el-image>
          <audio :src="historyItem.content_music" controls v-show="historyItem.content_music" :data-id="`audio11`"
            @play="handlePlay(`audio11`)" autoplay muted></audio>
          <!-- <audio controls v-show="item.multi_media>
                        <source :src="item.multi_media" type="audio/mpeg" />
                    </audio> -->
        </div>
        <div class="pTitle">
          <div class="text">
            {{
              historyItem.content_title
              ? historyItem.content_title
              : historyItem.title
            }}
          </div>
          <div class="btn">
            <!-- 没有更多数据 -->
            <!-- <div class="noMore">没有更多数据</div> -->
          </div>
        </div>
        <div class="content">
          <div class="summary">
            <!-- 内容提要： -->
          </div>
          <div v-loading="detailsLoading" v-html="contentInfoItem.content_details" class="html_box"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

import Swiper from 'swiper'
import 'swiper/css/swiper.min.css'
let vm = null // vue 对象
export default {
  props: {
    setHistoryFlashBoo: {
      type: Function
    },
    item: {
      type: Object
    }
  },

  data () {
    return {
      isShow: false,
      loading: true,
      detailsLoading: true,
      swiperList: [],
      swiper_: null,
      currentIndex: 0, // 当前展示的真实索引
      historyItem: {}, // 当前历史记录点击的item
      contentInfoItem: {}, // 文化快讯item

      formatTypeMap: {
        information: 'news', // 文化快讯
        aggregate_2: 'aggregate' // 专题书单
      }
    }
  },
  methods: {
    close () {
      this.isShow = false
      this.handlePlay(null)
    },
    // 同时播放只存在一个音视频
    handlePlay (id) {
      this.videoElement = document.getElementsByTagName('audio')
      Array.from(this.videoElement).forEach((item) => {
        if (item.dataset.id == id) {
          item.play()
        } else {
          item.pause()
        }
      })
    },
    // 鼠标移入停止swiper
    stopWiper () {
      this.swiper_?.autoplay?.stop()
    },
    checkItem (item, index) {
      this.isShow = true
      this.historyItem = item
      this.currentIndex = index
    },
    // 获取历史记录数据
    async getContentData (type) {
      this.loading = true
      await axios
        .post('/api/content/getContentList', {
          page: 1,
          limit: 10,
          type: type
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.swiperList = res.data.result
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 精选书评
    async getWonderfulData () {
      this.loading = true

      await axios
        .post('/api/books/getCarefullyChosenComment', {
          page: 1,
          limit: 10
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.swiperList = res.data.result.list
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    async getContentInfoWithKeyWords () {
      this.detailsLoading = true
      await axios
        .post('/api/content/getContentInfoWithKeyWords', {
          content_id: this.historyItem.content_id
        })
        .then((res) => {
          this.contentInfoItem = res.data.result
        }).finally(() => {
          this.detailsLoading = false
        })
    }
  },
  created () {
    vm = this
  },
  async mounted () {
    if (this.item?.type == 'wonderful' || this.item?.type == 'foreign') {
      // 精选书评
      await this.getWonderfulData()
    } else {
      await this.getContentData(this.formatTypeMap[this.item?.type])
    }

    // 轮播图
    this.swiper_ = new Swiper('.swiper-container-history', {
      loop: true, // 无限循环
      slideToClickedSlide: true, // 点击切换至响应的item
      slidesPerView: 'auto', // 展示几个子元素显示
      autoplay: {
        delay: 1700, // 切换时间间隔
        stopOnLastSlide: false, // 切换到最后一张停止
        disableOnInteraction: true, // 用户操作之后停止轮播
        pauseOnMouseEnter: true // 鼠标至于上方停止轮播
      },
      speed: 800, // 每次切换的时间长短
      spaceBetween: 30, // 子元素间隔距离
      effect: 'coverflow', // 切换效果
      centeredSlides: true, // item默认居中
      coverflowEffect: {
        rotate: 0,
        stretch: 10,
        depth: 60,
        modifier: 2,
        slideShadows: true
      },
      on: {
        click: function (event) {
          vm.stopWiper()
          const src = event.target.currentSrc

          // vm.stopWiper();
          if (src) {
            if (vm.item?.type == 'wonderful' || vm.item?.type == 'foreign') {
              vm.swiperList.forEach((item, index) => {
                if (src.indexOf(item.image) != -1) {
                  vm.checkItem(item, index) // 执行想要执行的方法
                }
              })
            } else {
              vm.swiperList.forEach((item, index) => {
                if (src.indexOf(item.content_img) != -1) {
                  vm.checkItem(item, index) // 执行想要执行的方法
                }
              })
            }

            // 获取文化快讯详情
            vm.getContentInfoWithKeyWords()
          }
        }
      }
    })
  }
}
</script>

<style scoped lang="scss">
.historyFlashBox {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background-color: #000;
  z-index: 99;

  // display: flex;
  // align-items: center;
  .history-prop {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .popover {
    background-color: rgb(245, 245, 245);
    // width: 345px;
    // height: 410px;
    width: 30vw;
    height: 70vh;
    // border: 1px solid #00f;
    // 滚动条
    overflow-y: scroll;

    /* 隐藏标准的滚动条 */
    &::-webkit-scrollbar {
      width: 0;
    }

    &::-webkit-scrollbar {
      width: 0;
    }

    /* 隐藏 IE 和 Edge 浏览器的滚动条 */
    &::-ms-scrollbar {
      width: 0;
    }

    .video {
      width: 100%;
      height: 140px;
      background-color: #0f0f;
      position: relative;
      top: 0;
      display: flex;
      justify-content: center;

      .el-image {
        position: absolute;
      }

      audio {
        // display: block !important;
        width: 90%;
        height: 20px;
        position: absolute;
        bottom: 0;
        // left: 0;
      }
    }

    .pTitle {
      padding: 20px 15px;

      .text {
        font-size: 19px;
        font-weight: 600;
      }

      .btn {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
        // .noMore{
        //     position: fixed;
        //     top: 0;
        //     bottom: 0;
        //     left: 0;
        //     right: 0;
        //     width: 200px;
        //     height: 200px;
        //     background-color: #000;

        // }
      }
    }

    .content {
      padding: 0 10px;

      >ul {
        margin-top: 15px;
        list-style: square;

        li {
          margin-left: 15px;
          letter-spacing: 1px;
          margin-bottom: 7px;
        }
      }
    }
  }

  .el-icon-circle-close {
    position: absolute;
    font-size: 30px;
    right: 5%;
    top: 5%;
    color: #fff;
    cursor: pointer;
  }

  .swiper-slide {
    width: 460px;
    height: 50vh;
    position: relative;

    .swiper-slide-imgBox {
      width: 460px;
      height: 50vh;
      background-color: #fff;
      overflow: hidden;
    }

    &.swiper-slide-active {
      .inverted_current {
        opacity: 1;
      }
    }

    .inverted_current {
      position: absolute;
      overflow: hidden;
      width: 460px;
      height: 50vh;
      opacity: 0;
      background-color: #fff;
      transition: opacity 1.7s ease;
      transform: rotatex(180deg) translatey(97%);
      transform-origin: 50% 100%;

      // &.current {
      //     opacity: 1;
      // }

      &::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: linear-gradient(#000, 82%, transparent);
      }
    }

  }
}
</style>
