import { Bold as OriginalBold } from 'element-tiptap'
import CustomBoldMenuButton from './MenuButton'
// import { TextSelection } from 'prosemirror-state'

export default class Bold extends OriginalBold {
  menuBtnView ({ isActive, commands, focus, editor }) {
    return {
      component: CustomBoldMenuButton,
      componentProps: {
        isActive: isActive.bold()
      },
      componentEvents: {
        click: () => {
          const { state, dispatch } = editor.view
          const { $from, $to } = state.selection
          const doc = state.doc
          const prefix = '【1-】'
          const tr = state.tr
          let total = 0

          doc.nodesBetween($from.pos, $to.pos, (node, pos) => {
            if (node.isText) {
              const textNode = state.schema.text(prefix)
              // 在每个文本节点的开始处插入前缀
              tr.insert(pos + total, textNode)
              total += 4
            }
            return true // 继续遍历
          })

          dispatch(tr)
        }
      }
    }
  }
}
