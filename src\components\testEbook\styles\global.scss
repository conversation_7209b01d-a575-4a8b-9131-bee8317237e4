@import 'reset';

// 1rem = fontSize px
// 1px = (1 / fontSize)rem
$fontSize: 37.5;

@function px2rem($px) {
  @return ($px / $fontSize)+rem;
}

@mixin center() {
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  color: #333;
  // font-size: px2rem(20);
  font-size: 14px;
}

.slide-down-enter,
.slide-down-leave-to {
  transform: translate3d(0, -100%, 0)
}

.slide-down-enter-to,
.slide-down-leave,
.slide-up-enter-to,
.slide-up-leave {
  transform: translate3d(0, 0, 0)
}

.slide-down-enter-active,
.slide-down-leave-active,
.slide-up-enter-active,
.slide-up-leave-active,
.fade-enter-active,
.fade-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all .3s linear;
}

.slide-up-enter,
.slide-up-leave-to {
  transform: translate3d(0, px2rem(108), 0)
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave {
  opacity: 1;
}

.slide-right-enter,
.slide-right-leave-to {
  transform: translate3d(-100%, 0, 0);
}