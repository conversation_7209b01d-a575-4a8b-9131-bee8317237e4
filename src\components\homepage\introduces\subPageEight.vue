
<template>
 <div class="section">
    <div class="page-box">
        <div class="title-part1">实现学习的良性循环</div>
        <div class="arc-box">
            <div class="arc-container">
                <img src="../img/arc.png" class="arcarrow" />
            </div>
            <div class="arc-title one">元知识</div>
            <div class="arc-title two">文本</div>
            <div class="arc-title thr">高效工具</div>
            <div class="arc-content four">总结提炼</div>
            <div class="arc-content five">加速阅读</div>
            <div class="arc-content six">共享</div>
        </div>
    </div>
</div>
</template>
<style lang="scss" scoped>
.title-part1 {
  color: white;
  font-size: 30px;
  font-weight: 600;
  text-align: center;
  margin-top: 5%;
}
.arc-box {
  width: 400px;
  height: 400px;
  margin: 0 45vw;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .arcarrow {
    width: 450px;
  }
}
.arc-box{
  margin-top: 20%;
  margin-left: 48%;
}
.arc-title {
  color: white;
  font-size: 23px;
  position: absolute;
  width: 100px;
}

.arc-content {
  color: #d9d9d9;
  font-size: 18px;
  position: absolute;
  width: 40px;
}
.one {
  top: 31%;
  left: 90%;
}
.two{
  top: 31%;
  left: 11%;
}
.thr{
  top: 83%;
  left: 48%;
}
.four{
  top: 16%;
  left: 49%;
}
.five{
  top: 57%;
  left: 31%;
}
.six{
  top: 58%;
  left: 77%;
}

@media only screen and (max-width: 420px) {
  .arc-box{
    margin: 0;
    width: auto;
    height: auto;
    transform: none;
    .arcarrow{
      width: 100%;
    }
  }
  .one{
    top: 27%;
    left: 81%;
  }
  .two{
    top: 27%;
    left: 5%;
  }
  .thr{
    top: 43%;
    left: 40%;
  }
  .four{
    top: 14%;
    left: 44%;
  }
  .five{
    top: 30%;
    left: 31%;
  }
  .six{
    top: 32%;
    left: 64%;
  }
}
</style>
