<template>
  <div @click.stop style="justify-content: center; display: flex">
    <graph-echart :echartsData="echartsData" />
  </div>
</template>
<script>
import { GraphEchart } from '@/components/Echarts'
import { mapState } from 'vuex'
export default {
  data () {
    return {}
  },
  components: { GraphEchart },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      echartsData: (state) => state.echartsData
    })
  },
  watch: {
    echartsData: function (newValue, oldValue) {
      // 在这里执行变量改变时的逻辑
    }
  }
}
</script>
<style lang="scss" scoped>
.edit-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}
</style>
