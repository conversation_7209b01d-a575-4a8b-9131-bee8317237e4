<template>
  <div class="container" @click.stop>
    <div class="bookor_tag_modal">
      <div class="bookor_tag_modal_mask"></div>
      <div class="bookor_tag_modal_content">
        <div
          class="bookor_tag_modal_handle_select"
          style="margin-bottom: 5px; color: black; padding: 0 2px"
        >
          已选择：
        </div>
        <div
          v-for="(item, index) in currentEditTagList"
          :key="index"
          class="bookor_tag_modal_item"
          style="
            font-size: 24rpx;
            padding: 10rpx 10rpx;
            margin-left: 0;
            margin-right: 10rpx;
          "
          @click="handleDeleteSelectTag(item)"
        >
          #{{ item.classify_name }}
        </div>

        <div class="bookor_tag_modal_handle_group">
          <div class="bookor_tag_modal_handle_select">词条分类:</div>
          <input
            class="bookor_tag_modal_add_input"
            v-model="newTagValue"
            @input="bindNewTagKeyInput"
            placeholder=""
          />
          <div
            class="bookor_tag_modal_add_confirm"
            @click="handleConfirmAddNewTag"
          >
            新增
          </div>
        </div>
        <!-- 提示 -->
        <div v-if="isShowSearchCue">
          <div class="bookor_tag_modal_tag_list no-scroll-bar">
            <div
              class="bookor_tag_modal_tag_list_item"
              v-for="(item, index) in source"
              :key="index"
            >
              <div
                style="width: 100%; cursor: pointer"
                @click.stop="
                  handleSelectMyKeyword(item.classify_id, item.classify_name)
                "
              >
                {{ item.classify_name }}
              </div>

              <div
                class="bookor_tag_modal_list_del_btn"
                @click.stop="deleteTag(item.classify_id, item.classify_name)"
              >
                <img
                  class="bookor_tag_modal_list_del_icon"
                  mode="widthFix"
                  src="https://oss.bookor.com.cn/static/img/mini_programs/images/deletegray.png"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import axios from '@/http'
import request from '@/http/request'
import { mapState } from 'vuex'
import { MessageBox } from 'element-ui'
export default {
  data () {
    return {
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      newTagValue: '', // 用户输入的搜索词
      isShowSearchCue: false, // 提示盒子
      source: [], // 搜索后结果
      currentEditTagList: []
    }
  },
  props: {
    entryids: {
      type: Object
    }
  },
  computed: {
    ...mapState('entryclassify', {
      myMemberKeywordsTagList: (state) => state.myMemberKeywordsTagList
    })
  },

  watch: {
    newTagValue (newValue) {
    },
    myMemberKeywordsTagList () {},
    entryids: {
      handler (newValue) {
        this.apiMemberKeywordsClassify(newValue)
      },
      immediate: true, // 在初始化时立即执行回调函数
      deep: true
    }
  },
  methods: {
    // 点击新增按钮
    handleConfirmAddNewTag () {
      // this.addNewTag(this.newTagValue);
      this.addClassify()
    },

    // 新增api
    async addClassify () {
      const params = {
        classify_name: this.newTagValue
      }
      request('/api/Keywords/addClassify', params).then((result) => {
        this.$store.dispatch('entryclassify/getMemberKeywordsTagList')

        // 刷新当前词条的source
        this.source = []
        this.newTagValue = ''

        // 和当前的词条关联
        this.handleSelectMyKeyword()
        this.$message({
          message: '添加分类成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 获取词条分类提示
    bindNewTagKeyInput () {
      const value = this.newTagValue
      // 数据源
      const res = this.myMemberKeywordsTagList
      const source = []
      const reg = new RegExp(value, 'i')
      res.forEach((item, index) => {
        if (reg.test(item.classify_name)) {
          source.push(item)
        }
      })
      this.source = source
      this.isShowSearchCue = true
    },

    // 选中某个提示的词条
    handleSelectMyKeyword (id, tag) {
      // let currentEditTagList = this.currentEditTagList;
      // let isAdded = false;
      // for (let i = 0; i < currentEditTagList.length; i++) {
      //   if (id == currentEditTagList[i].id) {
      //     isAdded = true;
      //   }
      // }
      // if (isAdded == true) {
      //   this.isShowKeywordsTagList = false;
      // }

      // currentEditTagList.push({
      //   id: id,
      //   classify_name: tag,
      // });

      // this.currentEditTagList = currentEditTagList;
      // this.isShowKeywordsTagList = false;
      // this.newTagValue = "";
      // this.isShowSearchCue = false;

      // 提交关联到数据库里
      this.apiKeywordsToClassify(id)
    },
    // 提交关联到数据库里
    async apiKeywordsToClassify (id) {
      const params = {
        classify_id: id,
        member_keywords_id: this.entryids.member_keywords_id,
        book_keywords_id: this.entryids.book_keywords_id
      }
      request('/api/Keywords/keywordsToClassify', params).then((result) => {
        this.apiMemberKeywordsClassify(this.entryids)
        this.isShowKeywordsTagList = false
        this.newTagValue = ''
        this.isShowSearchCue = false
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 点击已选择的词条（删除某个关联）
    handleDeleteSelectTag (item) {
      // 调用MessageBox.confirm方法显示弹窗
      MessageBox.confirm('是否删除该分类?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 用户点击确定按钮时触发的回调函数
          this.apiKeywordsDisClassify(item)
        })
    },

    // 取消词条和分类的关联
    async apiKeywordsDisClassify (item) {
      const params = {
        classify_id: item.classify_id,
        member_keywords_id: this.entryids.member_keywords_id,
        book_keywords_id: this.entryids.book_keywords_id
      }
      await request('/api/Keywords/keywordsDisClassify', params)
    },

    // 删除自己的某个词条分类
    deleteTag (id, tag) {
      MessageBox.confirm('是否删除该分类？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 用户点击确定按钮时触发的回调函数
          // this.apiDeleteMyBookTopic(item);
          this.apiDeleteMemberKeywordsTag(id)
        })
    },
    async apiDeleteMemberKeywordsTag (id) {
      const params = {
        classify_id: id
      }
      request('/api/Keywords/deleteClassify', params).then((result) => {
        this.$store.dispatch('entryclassify/getMemberKeywordsTagList')

        // 刷新当前词条的source
        this.source = this.source.filter((item) => item.classify_id !== id)
        this.newTagValue = ''
        this.$message({
          message: '删除分类成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 获取某个词条已选择的词条分类list
    async apiMemberKeywordsClassify (entryids) {
      const params = {
        member_keywords_id: entryids.member_keywords_id,
        book_keywords_id: entryids.book_keywords_id
      }
      request(
        '/api/Keywords/memberKeywordsClassify',
        params
      ).then((result) => {
        this.currentEditTagList = result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: white;
  width: 205px;
  border-radius: 5px;
  padding: 0 3px;
  .bookor_tag_modal_item {
    font-size: 14px;
    margin-right: 5px;
    color: #4caf50;
    padding: 0 5px;
    display: inline-block;
    border-radius: 5px;
    background-color: #f1f1f1;
    margin-bottom: 5px;
    font-weight: normal;
  }

  .bookor_tag_modal_handle_group {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    align-items: center;
  }

  .bookor_tag_modal_handle_select {
    font-size: 15px;
    color: #91a4b9;
  }

  .bookor_tag_modal_handle_add {
    color: #36ade2;
    font-size: 15px;
  }

  .bookor_tag_modal_add_input_wrap {
    display: flex;
  }

  .bookor_tag_modal_add_input {
    // width: 50%;
    width: 70px;
    margin-right: 5px;
    border-radius: 5px;
    color: #000;
    padding: 4px;
    border: 1px solid #dcdcdc;
    font-size: 14px;
  }

  .bookor_tag_modal_add_confirm {
    color: #fff;
    width: 50px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #60c1ec;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
  }

  .bookor_tag_modal_tag_list {
    max-height: 50px;
    overflow-y: scroll;
    /* background: rgb(241 241 241); */
    /* width: 51%; */
    padding: 5px;
    z-index: 100;
    box-sizing: border-box;
    border-radius: 5px;
    margin: auto;
    /* position:absolute;
 top: 280rpx;
    right: 130rpx; */
  }

  .bookor_tag_modal_tag_list_item {
    font-size: 14px;
    line-height: 20px;
    border-bottom: 1px solid #dcdcdc;
    padding: 5px 0;
    color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .bookor_tag_modal_list_del_btn {
    width: 25px;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }

  .bookor_tag_modal_list_del_icon {
    width: 13px;
  }
}

.no-scroll-bar::-webkit-scrollbar {
  display: none;
}
</style>
