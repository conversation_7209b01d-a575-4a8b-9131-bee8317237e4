<template>
  <div id="container">
    <div id="circle">
      <div class="dot"></div>
    </div>
  </div>
</template>

<script>
import * as THREE from "three";

export default {
  mounted() {
    // 初始化场景、灯光、相机、渲染器
    const scene = new THREE.Scene();
    scene.background = new THREE.Color("#ffffff"); // 设置背景色为白色
    const camera = new THREE.PerspectiveCamera(
      60,
      window.innerWidth / window.innerHeight,
      1,
      1000
    );
    camera.position.z = 500;
    const light = new THREE.PointLight(0xffffff, 1);
    light.position.set(100, 100, 100);
    scene.add(light);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.getElementById("container").appendChild(renderer.domElement);

    // 创建圆形对象并设置颜色和大小
    const circleArray = [];
    const colors = ["#535353", "#4c4c4c", "#383838"];
    for (let i = 0; i < 300; i++) {
      const size = Math.random() * 2 + 2;
      const circleGeometry = new THREE.CircleGeometry(size, 32);
      const circleMaterial = new THREE.MeshBasicMaterial({
        color: colors[Math.floor(Math.random() * colors.length)],
        opacity: Math.random(),
        transparent: true,
      });
      const circleMesh = new THREE.Mesh(circleGeometry, circleMaterial);
      circleMesh.position.x = 0;
      circleMesh.position.y = 0;
      circleMesh.position.z = 0;

      // 设置移动速度及比例
      let speed = Math.random();
      let speedX, speedY, speedZ;
      if (speed < 0.3) {
        speedX = (Math.random() - 0.5) * 0.4;
        speedY = (Math.random() - 0.5) * 0.4;
        speedZ = (Math.random() - 0.5) * 0.4;
      } else if (speed < 0.8) {
        speedX = (Math.random() - 0.5) * 0.6;
        speedY = (Math.random() - 0.5) * 0.6;
        speedZ = (Math.random() - 0.5) * 0.6;
      } else {
        speedX = (Math.random() - 0.5) * 1;
        speedY = (Math.random() - 0.5) * 1;
        speedZ = (Math.random() - 0.5) * 1;
      }

      let radius = Math.random() * 170 + 10; // 修改半径范围，使小点在圆形区域内
      let theta = Math.random() * Math.PI * 2;
      circleMesh.position.x = radius * Math.cos(theta);
      circleMesh.position.y = radius * Math.sin(theta);
      circleMesh.speed = { x: speedX, y: speedY, z: speedZ };
      circleArray.push(circleMesh);
      scene.add(circleMesh);
    }

    // 创建旁边的圆形对象并设置颜色和大小
    const circleArray2 = [];
    for (let i = 0; i < 600; i++) {
      const size = Math.random() * 2 + 1;
      const circleGeometry = new THREE.CircleGeometry(size, 32);
      const circleMaterial = new THREE.MeshBasicMaterial({
        color: colors[Math.floor(Math.random() * colors.length)],
        opacity: Math.random(),
        transparent: true,
      });
      const circleMesh = new THREE.Mesh(circleGeometry, circleMaterial);
      circleMesh.position.x = (Math.random() - 0.5) * 600;
      circleMesh.position.y = (Math.random() - 0.5) * 600;
      circleMesh.position.z = (Math.random() - 0.5) * 200;
      circleMesh.speed = {
        x: (Math.random() - 0.5) * 2,
        y: (Math.random() - 0.5) * 2,
        z: (Math.random() - 0.5) * 2,
      };
      circleArray2.push(circleMesh);
      scene.add(circleMesh);
    }

    // 创建中心圆形区域并设置样式
    const circleDiv = document.getElementById("circle");
    circleDiv.style.width = "350px"; // 修改中心圆形的半径为350px
    circleDiv.style.height = "350px";
    circleDiv.style.borderRadius = "50%";
    circleDiv.style.background = "transparent";
    circleDiv.style.position = "absolute";
    circleDiv.style.top = "calc(50% - 175px)";
    circleDiv.style.left = "calc(50% - 175px)";
    circleDiv.style.zIndex = "9999";

    // 创建中心圆点并设置样式
    const dotDiv = document.querySelector(".dot");
    dotDiv.style.width = "4px";
    dotDiv.style.height = "4px";
    dotDiv.style.borderRadius = "50%";
    dotDiv.style.background = "#000000";
    dotDiv.style.position = "absolute";
    dotDiv.style.top = "calc(50% - 2px)";
    dotDiv.style.left = "calc(50% - 2px)";

    function animate() {
      requestAnimationFrame(animate);
      circleArray.forEach((circle) => {
        circle.position.x += circle.speed.x;
        circle.position.y += circle.speed.y;
        circle.position.z += circle.speed.z;

        // 判断小点是否超出圆形区域，是则反弹
        const radius = circle.geometry.parameters.radius;
        const distanceToCenter = Math.sqrt(
          Math.pow(circle.position.x, 2) + Math.pow(circle.position.y, 2)
        );
        if (distanceToCenter > 175 - radius && distanceToCenter < 175) {
          const ratio = 1 - (distanceToCenter - (175 - radius)) / radius;
          circle.position.x *= ratio;
          circle.position.y *= ratio;
          circle.speed.x *= -ratio;
          circle.speed.y *= -ratio;
        }

        // 判断小点是否碰到边界，是则反弹
        if (
          circle.position.x < -300 + radius ||
          circle.position.x > 300 - radius
        ) {
          circle.speed.x = -circle.speed.x;
        }
        if (
          circle.position.y < -300 + radius ||
          circle.position.y > 300 - radius
        ) {
          circle.speed.y = -circle.speed.y;
        }
        if (circle.position.z < -100 || circle.position.z > 100) {
          circle.speed.z = -circle.speed.z;
        }
      });

      circleArray2.forEach((circle) => {
        circle.position.x += circle.speed.x;
        circle.position.y += circle.speed.y;
        circle.position.z += circle.speed.z;

        if (
          circle.position.x < -600 ||
          circle.position.x > 600 ||
          circle.position.y < -600 ||
          circle.position.y > 600 ||
          circle.position.z < -200 ||
          circle.position.z > 200
        ) {
          circle.position.x = (Math.random() - 0.5) * 600;
          circle.position.y = (Math.random() - 0.5) * 600;
          circle.position.z = (Math.random() - 0.5) * 200;
        }
      });
      renderer.render(scene, camera);
    }
    animate();

    // 动态调整相机位置和渲染器尺寸
    function onWindowResize() {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    }

    window.addEventListener("resize", onWindowResize);
  },
};
</script>

<style>
#container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#circle {
  width: 350px;
  height: 350px;
  border-radius: 50%;
  background: transparent;
  position: absolute;
  top: calc(50% - 175px);
  left: calc(50% - 175px);
  z-index: 9999;
}

.dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #000000;
  position: absolute;
  top: calc(50% - 2px);
  left: calc(50% - 2px);
}
</style>
