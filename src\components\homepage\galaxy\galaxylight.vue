<template>
  <canvas id="galaxy"></canvas>
</template>

<script>
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { Star } from "./star.js";
export default {
  name: "ThreeTest",
  data() {
    return {
      camera: null,
      scene: null,
      renderer: null,
      mesh: null,
      controls: null,
      orbit: null,

      moveCameraInterval: null,
    };
  },
  methods: {
    init: function () {
      this.scene = new THREE.Scene();

      this.stars = this.generateObject(1500, (pos) => new Star(pos));
      this.stars.forEach((star) => {
        star.toThreeObject(this.scene);
      });

      this.camera = new THREE.PerspectiveCamera(
        60,
        window.innerWidth / window.innerHeight,
        0.1,
        5000000
      );
      this.camera.position.set(0, 100, 100);
      this.camera.up.set(0, 0, 1);
      this.camera.lookAt(0, 0, 0);

      this.renderer = new THREE.WebGLRenderer({
        canvas: document.getElementById("galaxy"),
        antialias: true,
      });
      this.renderer.setSize(window.innerWidth, window.innerHeight);
      // this.renderer.setClearColor(0xffffff, 1);

      // this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      // this.controls.autoRotate = false;

      this.orbit = new OrbitControls(this.camera, this.renderer.domElement);
      this.orbit.enableDamping = true; // an animation loop is required when either damping or auto-rotation are enabled
      this.orbit.dampingFactor = 0.05;
      this.orbit.screenSpacePanning = false;
      this.orbit.minDistance = 10; // 设置最小距离
      this.orbit.maxDistance = 600; // 设置最大距离
      this.orbit.maxPolarAngle = Math.PI / 2 - Math.PI / 360;
      // this.controls.minDistance = 10; // 相机到目标点的最小距离为 10
      // this.controls.maxDistance = 100; // 相机到目标点的最大距离为 100
    },
    render() {
      this.orbit.update();
      this.renderer.render(this.scene, this.camera);

      this.stars.forEach((star) => {
        star.updateScale(this.camera);
      });
      requestAnimationFrame(this.render);
    },

    generateObject(numStars, generator) {
      let objects = [];
      for (let i = 0; i < numStars; i++) {
        let pos = new THREE.Vector3(
          this.gaussianRandom(0, 300),
          this.gaussianRandom(0, 300),
          this.gaussianRandom(0, 40)
        );
        let obj = generator(pos); // 传入 idx
        objects.push(obj);
      }

      return objects;
    },
    gaussianRandom(mean = 0, stdev = 1) {
      let u = 1 - Math.random();
      let v = Math.random();
      let z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);

      return z * stdev + mean;
    },
    onClick() {
      clearInterval(this.moveCameraInterval);
    },
  },
  mounted() {
    this.init();
    const duration = 3000;
    const startPosition = new THREE.Vector3(0, 0, -700); // 初始位置
    const endPosition = this.camera.position.clone(); // 目标位置
    let currentTime = 0;

    this.moveCameraInterval = setInterval(() => {
      currentTime += 16;
      const progress = currentTime / duration;
      this.camera.position.lerpVectors(startPosition, endPosition, progress);
      this.camera.lookAt(0, 0, 0);
      if (progress >= 1) {
        clearInterval(this.moveCameraInterval);
      }
    }, 16);
    this.render();
    document
      .getElementById("galaxy")
      .addEventListener("click", this.onClick, false);
  },
};
</script>
