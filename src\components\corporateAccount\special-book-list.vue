<template>
  <div class="container" @click.stop>
    <div class="desc">
      <div class="item-title">
        <div class="txt">{{ data.title }}</div>
      </div>
      <div class="showimage">
        <img :src="data.image" />
      </div>
      <div
        class="booklistDetailRichText no-scroll-bar"
        v-html="data.describe"
      ></div>
      <div class="bookBox">
        <div
          style="overflow-x: scroll; display: flex"
          class="no-scroll-bar"
          v-slide-scroll
        >
          <div class="bookList">
            <!-- bind:tap="wantBook" data-id="{{bookItem.goods_book_id}}" -->
            <div
              class="bookItem"
              v-for="(bookItem, bookIndex) in data.goods_book_list"
              :key="bookIndex"
            >
              <img
                style="width: 100%; height: 100%; object-fit: inherit"
                :src="bookItem.book_image"
              />

              <div class="bookName">{{ bookItem.book_name }}</div>
              <div class="tool">
                <!-- <div class="time">{{ bookItem.book_addtime }}</div> -->
                <!-- <div class="cart">
                  <image
                    mode="widthFix"
                    src="https://oss.bookor.com.cn/static/img/mini_programs/images/cart.png"
                  />
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    data: {
      type: Object
    }
  },
  watch: {
    data: {
      handler (newValue) {
      },
      immediate: true
    }
  },
  data () {
    return {}
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    })
  },
  methods: {},
  mounted () {}
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
.container {
  width: 380px;
  height: 500px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 15px 12px;

  .item-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 16px;
    color: #000;
    font-weight: bold;
    // padding: 30px 10px 0 10px;
  }

  .item-title .txt {
    max-width: 500px;
    font-size: 18px;
  }

  .btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding-right: 30px;
  }

  .btn .editBtn {
    color: #f5ecc1;
    padding: 0 6px;
    height: 40px;
    background: #929292;
    line-height: 1;
    margin-left: 30px;
    font-size: 26px;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .showimage {
    margin: 10px 0;
    width: 100%;
    height: 120px;
  }

  .showimage img {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    object-fit: cover;
  }
  .booklistDetailRichText {
    max-height: 150px;
    overflow-y: scroll;
  }
  .bookBox {
    margin-top: 20px;
  }

  .bookBox .bookList {
    // padding: 20px 0;
    // padding-left: 30px;
    // display: -webkit-box;
    // display: -ms-flexbox;
    display: flex;
    height: 160px;
  }

  .bookBox .bookList .bookItem {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 100px;
    height: 145px;
    border-radius: 5px;
    padding-bottom: 10px;
    margin-right: 10px;
    overflow: hidden;
    // -webkit-box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 3px 3px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  // .bookBox .bookList .bookItem img {
  //   width: 100%;
  //   height: 100px;
  //   display: block;
  // }
  .bookBox .bookList .bookItem .bookName {
    // font-size: 26px;
    // color: #000;
    // display: -webkit-box;
    // height: 74px;
    // overflow: hidden;
    // -o-text-overflow: ellipsis;
    // text-overflow: ellipsis;
    // word-wrap: break-word;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // padding: 0 4px;
    // line-height: 1.4;
    font-size: 13px;
    /* color: #000; */
    display: -webkit-box;
    height: 36px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    padding: 0 4px;
    line-height: 1.4;
    position: absolute;
    bottom: 0;
    background-color: white;
    width: 100%;
    padding: 0 2px;
  }

  .bookBox .bookList .bookItem .tool {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-top: 10px;
    padding: 0 10px;
  }

  .bookBox .bookList .bookItem .tool .time {
    font-size: 24px;
    color: #000;
    margin-top: 10px;
  }

  .bookBox .bookList .bookItem .tool .cart {
    width: 50px;
    height: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background: #f53d40;
    border-radius: 50%;
  }
  .bookBox .bookList .bookItem .tool .cart image {
    width: 30px;
    height: 30px;
    display: block;
  }
}

.no-scroll-bar::-webkit-scrollbar {
  display: none;
}
</style>
