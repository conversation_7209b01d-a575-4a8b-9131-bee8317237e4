// 实现一些公共的方法
const nodeColor = [0xffcc6f, 0xffd2a1, 0xfff4ea, 0xf8f7ff, 0xcad7ff, 0xaabfff]
export const getRandomColor = () => {
  const num = Math.floor(Math.random() * 6)
  return nodeColor[num] // 生成一个0到5的随机整数
}
export const pointConvertNode = (list, titleObj) => {
  const arr = []
  for (let i = 0; i < list.length; i += 1) {
    const item = list[i]
    const obj = {
      id: parseInt(Math.random() * 100000000000000000),
      color: getRandomColor(),
      user: item.keywords,
      bookId: item.book_keywords_id,
      nodeId: item.member_keywords_id,
      title: titleObj?.lines_title,
      titleId: titleObj?.mix_keywords_lines_id
    }
    arr.push(obj)
  }
  return arr
}

export const lineConvertLink = (nodes, list) => {
  const links = []
  for (let i = 0; i < list.length; i += 1) {
    const link = list[i]
    const source = nodes.find(item => item.user === link.keywords_front)
    const target = nodes.find(item => item.user === link.keywords_behind)
    if (source && target) {
      const linkObj = {
        source: source.id,
        target: target.id,
        size: link.lines_code !== 3 ? 8 : 0,
        lines_code: link.lines_code
      }
      links.push(linkObj)
    }
  }
  return links
}

// 寻找关系链中有没有当前node

export const getBondNode = (bondList, node) => {
  for (let i = 0; i < bondList.length; i += 1) {
    const item = bondList[i]
    const idx = item.point_list.findIndex((child) => child.keywords === node.user)
    if (idx !== -1) {
      return item
    }
  }
  return null
}

export const getBondTitle = (bondList, node) => {
  for (let i = 0; i < bondList.length; i += 1) {
    const item = bondList[i]
    if (item.title && item.title.length === 1) {
      const title = item.title[0]
      if (title.mix_keywords_lines_id === node.id) {
        return item
      }
    }
  }
  return null
}
