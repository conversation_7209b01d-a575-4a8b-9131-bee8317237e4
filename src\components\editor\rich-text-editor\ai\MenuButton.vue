<template>
  <el-tooltip content="AI 助手" placement="bottom">
    <img class="func-img" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/ai%20%281%29.png" @click="onClick" />
  </el-tooltip>

</template>

<script>
export default {
  name: 'BoldMenuButton',

  props: ['isActive'],

  methods: {
    onClick (e) {
      this.$emit('click', (text) => {
        this.$parent.$parent.$parent.$emit('openAi', text)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.func-img{
  width: 18px;
  height: 15px;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 10px;
}
</style>
