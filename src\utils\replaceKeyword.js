
const extractDataLabel = (str) => {
  const regex = /<enrty-suggestion.*?data-label="(.*?)"/
  const match = str.match(regex)
  if (match && match[1]) {
    return match[1]
  }
  return null
}
const extractDataId = (str) => {
  const regex = /<enrty-suggestion.*?data-id="(.*?)"/
  const match = str.match(regex)
  if (match && match[1]) {
    return match[1]
  }
  return null
}
const extractDataDescr = (str) => {
  const regex = /<enrty-suggestion[^>]*?data-descriptions="([^"]+)"/
  const match = str.match(regex)
  if (match && match[1]) {
    return match[1]
  }
  return null
}

const replaceKeyword = (str, keyword) => {
  const regex = /<enrty-suggestion.*?<\/enrty-suggestion>/gs
  let replacedStr = str.replace(regex, (match) => {
    const id = extractDataId(match)
    const label = extractDataLabel(match)
    const descr = extractDataDescr(match) || ''
    const arr = descr.split(/\\n?\\r/) || []
    const info = []
    if (arr.length > 1) {
      arr.forEach((item) => {
        const regex = /bookinfo_(.*?)?_(\d+)/
        const match = regex.exec(item)
        if (match) {
          const content = item.replace(/bookinfo_(.*?)?_(\d+)/g, '')
          info.push({
            bookName: `《${match[1]}》`,
            bookId: match[2],
            content
          })
        }
      })
    } else {
      const item = arr[0]
      const regex = /bookinfo_(.*?)?_(\d+)/
      const match = regex.exec(item)
      if (match) {
        const content = item.replace(/bookinfo_(.*?)?_(\d+)/g, '')
        info.push({
          bookName: `《${match[1]}》`,
          bookId: match[2],
          content
        })
      }
    }
    const replacement = `<a class="keyword-tag" tag="${id}" descr="${descr}" info='${JSON.stringify(info)}'>${label}</a>`
    return replacement
  })
  if (keyword) {
    const reg = new RegExp(keyword, 'gi')
    replacedStr = replacedStr.replace(reg, `<a class="text-keyword">${keyword}</a>`)
  }
  return replacedStr
}

export {
  replaceKeyword
}
