html,
body {
    margin: 0;
    height: 100%;
    overflow: hidden;
    /* 禁止滚动条出现 */
}

.register {
    width: 100vw;
    height: 90vh;
    position: relative;
    padding-top: 10vh;
    display: flex;
    justify-content: center;
    /* 让子元素水平居中 */
    align-items: center;
    /* 让子元素垂直居中 */
    background-size: cover;
    background-position: center center;
}

/* 注册左边 */
/*.register_left {
     background-image: url('../img/bg.png');
         background-color: #47ADDE; 这行之前注释的
    background-repeat: no-repeat;
    background-size: 600px 500px;
    background-position: center center;
    width: 60%;
    height: 800px;
    display: inline-block;
    position: relative;
}*/

/* 注册右边 */
/* .register_right {
    display: inline-block;
    float: right;
    background-color: #47ADDE; 
    width: 40%;
    height: 800px;
}*/

/* 右边图片最外框 */
.register_right_imgs {
    /* width: 30vw;
    height: 50vh; */
    /* box-shadow: 0px 0px 10px 6px rgba(0, 0, 0, 0.17); */
    /* border-radius: 4px;
    padding: 30px 60px; */
    padding: 30px;
    border: 1px solid #afabab;
}

/* 线 
.boder_line {
    display: inline-block;
    width: 30vw;
    height: 1px;
    border-bottom: 2px solid #d9d9d9;
    margin-bottom: 15px;
}

.boder_line_active {
    border-bottom: 3px solid black;
    display: inline-block;
    width: 30vw;
    height: 1px;
    margin-bottom: 15px;
}

.border_bottom_line_active {
    display: inline-block;
    width: 7.5vw;
    border-bottom: 3px solid black;
    position: relative;
    bottom: 13px;
}

.border_bottom_line {
    display: inline-block;
    width: 7.5vw;
    border-bottom: 3px solid #d9d9d9;
    position: relative;
    bottom: 13px;
}*/


/* 标题 */
.password_login_active {
    font-size: 16px;
    font-weight: 800;
    display: inline-block;
    position: relative;
    /* right: 50px; */
    margin-bottom: 10px;
    cursor: pointer;
    margin-right: 5px;
    color: black;
}

.password_login {
    font-size: 14px;
    font-weight: 500;
    display: inline-block;
    position: relative;
    /* right: 50px; */
    margin-bottom: 10px;
    cursor: pointer;
    margin-right: 5px;
    color: #607078;
}

.account_login {
    font-size: 14px;
    color: #607078;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 10px;
    cursor: pointer;
    padding-left: 5vw;
}

.account_login_active {
    color: black;
    font-size: 16px;
    font-weight: 800;
    display: inline-block;
    margin-bottom: 10px;
    cursor: pointer;
    padding-left: 5vw;

}

/* 忘记密码、注册 */
.right_register {
    font-size: 14px;
    color: #656667;
    cursor: pointer;
    margin: 0 15px;
}

/* 线 */
/* .black_border {
    margin-left: 5px;
    border-left: 1px solid;
    padding: 0 5px;
} */

/* 账号安全 */
/* #account_security {
    display: none;
    width: 450px;
    height: 480px;
    box-shadow: 0px 0px 10px 6px rgba(0, 0, 0, 0.17);
    position: absolute;
    background: rgba(255, 255, 255, .2);
    right: 0;
    bottom: 0;
    border-radius: 4px;
} */

.account_security_title_box {
    display: flex;
}

.account_security_title_box img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    cursor: pointer;
}

.account_security_title {
    font-size: 16px;
    font-weight: 600;
}

/* 账户安全下边的下划线 */
.account_security_line {
    /* width: 30vw; */
    height: 1px;
    border-bottom: 2px solid #d9d9d9;
    margin-top: 10px;
    /* margin-right: 15px; */
    position: relative;
    /* left: 10px; */
}


.account_security_phone_box {
    display: flex;
    margin: 25px 0 20px;
}

/* 下拉选框样式 */
.el-select {
    width: 140px;
}

.account_security_phone_number {
    width: 150px;
    height: 40px;
    border: none;
    background-color: #eeeeee;
    padding: 0 0 0 10px;
    border-radius: 4px;
    display: block;
}

/* 下一步按钮 */
/* .next_step {
    display: block;
    font-size: 17px;
    width: 300px;
    height: 35px;
    color: #9D9D9D;
    background-color: #CCCCCC;
    border: none;
    border-radius: 5px;
    margin-top: 20px;
    position: relative;
    cursor: pointer;
}

.next_step:hover {
    background-color: #83cbff;
    color: white;
} */

/* 账户安全-修改密码 */

/* 账户安全-找回密码 */
#find_password {
    display: none;
    width: 450px;
    height: 480px;
    box-shadow: 0px 0px 10px 6px rgba(0, 0, 0, 0.17);
    background: rgba(255, 255, 255, .2);
    position: absolute;
    right: 0;
    bottom: 0;
    border-radius: 4px;
}

#find_password .find_password_title {
    font-size: 18px;
    font-weight: 600;
    margin-top: 40px;
    margin-right: 150px;
}

#find_password .find_password_line {
    width: 320px;
    height: 1px;
    border-bottom: 2px solid #A2D5EE;
    margin-top: 10px;
    margin-right: 15px;
    position: relative;
    left: 17px;
}

/* 提示语 
#find_password .find_password_prompt {
    font-size: 18px;
    color: #EBF5FA;
    position: relative;
    top: 90px;
    left: 10px;
}*/

/* 马上登录按钮 
#find_password_login_btn {
    width: 320px;
    height: 40px;
    font-size: 17px;
    background-color: #CCCCCC;
    border: none;
    border-radius: 5px;
    color: #9F9F9F;
    position: relative;
    left: 10px;
    top: 150px;
    cursor: pointer;
}*/

/* 注册 */
#register,
#third_party_account_login {
    display: none;
    width: 450px;
    height: 480px;
    box-shadow: 0px 0px 10px 6px rgba(0, 0, 0, 0.17);
    position: absolute;
    background: rgba(255, 255, 255, .2);
    right: 0;
    bottom: 0;
    border-radius: 4px;
}

/* 发送验证码 */
.register_send_sms {
    display: inline-block;
    width: 160px;
    height: 35px;
    text-align: center;
    background-color: #83CBFF;
    color: white;
    border: none;
    border-radius: 5px;
    /* position: absolute;
    top: 170px;
    right: 50px; */
    cursor: pointer;

}

.register_send_sms_text {
    text-align: center;
    font-size: 16px;
    position: relative;
    top: 8px;
}


.register_show_blackborder {
    display: inline-block;
    width: 1px;
    height: 33px;
    border-right: 1.2px solid black;
    position: relative;
    top: 42px;
    right: 132px;

}

/* 注册按钮 */
#register_btn {
    width: 300px;
    height: 35px;
    color: #999999;
    background-color: #CCCCCC;
    border: none;
    border-radius: 5px;
    /* position: absolute;
    top: 360px;
    left: 65px; */
    cursor: pointer;
}

#register_btn:hover {
    color: white;
    background-color: #83CBFF;
}

/* 注册成功页面样式 */
#register_success {
    display: none;
    width: 450px;
    height: 480px;
    box-shadow: 0px 0px 10px 6px rgba(0, 0, 0, 0.17);

    position: absolute;
    right: 0;
    bottom: 0;
    border-radius: 4px;
}

#register_success .register_success_title {
    font-size: 18px;
    font-weight: 600;
    margin-top: 40px;
    margin-right: 250px;
}

#register_success .register_success_line {
    width: 320px;
    height: 1px;
    border-bottom: 2px solid #A2D5EE;
    margin-top: 10px;
    margin-right: 15px;
    position: relative;
    left: 17px;
}

/* 提示语 */
#register_success .register_success_prompt {
    font-size: 18px;
    color: #EBF5FA;
    position: relative;
    top: 90px;
    left: 10px;
}

/* 马上登录按钮 */
#register_success_login_btn {
    width: 320px;
    height: 40px;
    font-size: 18px;
    background-color: #CCCCCC;
    border: none;
    border-radius: 5px;
    color: #9F9F9F;
    position: relative;
    left: 10px;
    top: 150px;
    cursor: pointer;
}

/* 第三方账户登录页面样式 */
#password_login_2 {
    position: relative;
    top: 30px;
    right: 60px;
    color: #607078;
    font-size: 18px;
    font-weight: 500;
    display: inline-block;
    cursor: pointer;
}

#account_login_2 {
    font-size: 18px;
    font-weight: 800;
    display: inline-block;
    cursor: pointer;
    position: relative;
    top: 30px;
    right: 35px;
}

#account_phone_2 {
    font-size: 18px;
    font-weight: 800;
    display: inline-block;
    cursor: pointer;
    position: relative;
    top: 30px;
    right: 35px;
}

.border_buttom_line2 {
    display: inline-block;
    width: 126px;
    border-bottom: 3px solid black;
    position: relative;
    bottom: 9px;
    left: 2px;
}

.boder_line_2 {
    display: inline-block;
    width: 320px;
    height: 1px;
    border-bottom: 2px solid #A2D5EE;
    position: relative;
    left: 50px;
    top: 32px;
}

.third_party_account_login_show_slectwords {
    display: inline-block;
    width: 75px;
    height: 35px;
    line-height: 35px;
    position: relative;
    top: 65px;
    right: 272px;
    background: #EEEEEE;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.third_party_account_login_small_xiala_arrow {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-top: 1px solid black;
    border-right: 1px solid black;
    transform: rotateZ(135deg);
    -webkit-transform: rotateZ(135deg);
    position: relative;
    top: 61.5px;
    right: 293px;
    cursor: pointer;
}

.third_party_account_login_show_blackborder {
    display: inline-block;
    width: 1px;
    height: 28px;
    border-right: 1.2px solid black;
    position: relative;
    top: 73px;
    right: 290px;
}

#third_party_account_login_phone_area {
    width: 120px;
    position: absolute;
    bottom: 250px;
    left: 60px;
    box-shadow: 0px 0px 3px 1px lightgray;
    background: #FFFFFF;
    box-sizing: border-box;
    z-index: 1000;
    display: none;
}

#third_party_account_login_phone_area li {
    width: 100%;
    height: 25px;
    line-height: 25px;
    text-align: left;
    padding-left: 13px;
    cursor: pointer;
}

#third_party_account_login_phone_area li:hover {
    color: #83CBFF;
}

/* 绑定成功弹框样式 */
.bind_success .layui-layer-content {
    box-shadow: 0px 0px 6px 3px rgba(0, 0, 0, 0.17);
    text-align: center;
}

.other_register_send_sms {
    display: inline-block;
    width: 160px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background-color: #83CBFF;
    color: white;
    border: none;
    border-radius: 5px;
    position: relative;
    left: 5px;
    cursor: pointer;
    top: 12px;
}

.other_register_send_sms_text {
    text-align: center;
    position: relative;
    top: 0px;
}


.center_top_input_style {
    background-color: white !important;
    padding-left: 50px !important;
}

.center_top_input_style_add {
    background-color: #FFFFFF !important;
}

.other_register_send_sms {
    margin-top: 10px;
}

.weixin_qrcod {
    position: absolute;
    background-color: #fff;
    width: 340px;
    height: 344px;
    top: -4px;
    right: 54px;
    z-index: 99;
}

.disanfangfugai {
    width: 345px;
    height: 370px;
    background-color: #fff;
    position: absolute;
    right: 437px;
    top: 247px;

}

.input_sms {
    display: inline-block;
    width: 160px;
    height: 40px;
    background-color: #eeeeee;
    border: none;
    margin-top: 5px;
    border-radius: 4px;
    text-align: center;
    position: relative;
}

/* .send_sms {
    display: inline-block;
    width: 140px;
    height: 40px;
    text-align: center;
    background-color: #83cbff;
    color: white;
    border: none;
    margin-top: 5px;
    border-radius: 4px;
    position: relative;
    cursor: pointer;
} */

.mainBox {
    /* width: 500px;
    height: 480px; */
    /* position: absolute; */
    /* top: 100px;
    left: 50%;
    transform: translate(-50%); */
}

body,
html {
    height: 100%;
}