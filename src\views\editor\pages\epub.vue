
<template>
  <div class="pub-content">
      <el-button  class="change-to-word" type="info" size="small"  @click="changeToWord">转为WORD</el-button>
      <div
        class="edit-padding-box"
        @wheel="handleWheel"
      >
        <div class="ebook">
          <div style="width: 70vw; height: 93vh">
            <div id="read"></div>
          </div>
          <menu-bar
            :bookAvailable="bookAvailable"
            @onPageChange="onPageChange"
            @prevPage="prevPage"
            @nextPage="nextPage"
            :currentPage="currentPage"
            @jumpTo="jumpTo"
            :navigation="navigation"
            :totalPages="totalPages"
            ref="menuBar"
          >
          </menu-bar>
          <div @wheel="handleWheel">
            <img
              class="epub-scroll-btn epub-arrow-up"
              src="../img/arrow.png"
              alt=""
              @click="prevPage"
            />
            <img
              class="epub-scroll-btn epub-arrow-up epub-arrow-down"
              src="../img/arrow.png"
              alt=""
              @click="nextPage"
            />
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import ePub from 'epubjs'
import request from '@/http/request'
import { Loading } from 'element-ui'
import MenuBar from '@/components/testEbook/components/MenuBar.vue'
export default {
  data () {
    return {
      pdfUrl: '',
      book: null,
      leftExpanded: true,
      bookAvailable: false,
      currentPage: 1,
      rendition: null,
      totalPages: 0,
      title: '',
      navigation: {}
    }
  },
  components: {
    MenuBar // epub组件
  },
  methods: {
    // 转换为word时的loading
    showLoading () {
      this.loadingInstance = Loading.service({
        fullscreen: true, // 是否全屏遮罩
        text: '加载中...', // 显示的文本
        background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
      })
    },
    hideLoading () {
      this.loadingInstance.close() // 关闭 Loading 实例
    },
    // 转换为word
    async changeToWord () {
      this.showLoading()
      request('api/Paper/aliyunPdfToWord', {
        fileUrl: this.pdfUrl
      }).then((result) => {
        if (result.body.Code === 'Success') {
          const wordId = result.body.Data.Id
          this.pool(wordId, this.title)
        } else {
          this.$message({
            message: '转换失败',
            type: 'error',
            duration: 1000
          })
        }
        this.hideLoading()
      })
    },
    prevPage () {
      if (this.rendition && this.totalPages) {
        this.isShowNotePop = false
        this.rendition.prev()
        this.currentPage-- // 向前翻页时更新 currentPage
        if (this.currentPage <= 1) {
          this.currentPage = 1
        }
      }
    },
    nextPage () {
      if (this.rendition && this.totalPages) {
        try {
          this.isShowNotePop = false
          this.rendition.next()
          this.currentPage++ // 向后翻页时更新 currentPage
          if (this.currentPage >= this.totalPages) {
            this.currentPage = this.totalPages
          }
        } catch (error) {
          alert('出错，请重试或检查 EPUB 文件格式。')
        }
      }
    },

    jumpTo (href) {
      this.rendition.display(href)
    },

    onPageChange (pageNumber) {
      const percentage = pageNumber / this.totalPages
      const location = this.locations.cfiFromPercentage(percentage)
      this.rendition.display(location)
      this.currentPage = pageNumber // 跳转页码时更新 currentPage
    },

    handleWheel (event) {
      if (this.totalPages) {
        this.isScrolling = true

        if (event.deltaY < 0) {
          this.prevPage()
        } else if (event.deltaY > 0) {
          this.nextPage()
        }

        setTimeout(() => {
          this.isScrolling = false
        }, 200) // 设置节流时间间隔为200毫秒
      }
    },
    // 读取epub信息并挂载
    // 读取epub信息并挂载
    async readEpubMes (file) {
      this.book = ePub(file)
      await this.book.ready
      if (this.book !== null && typeof this.book !== 'undefined') {
        this.rendition = this.book.renderTo('read', {
          flow: 'paginated',
          width: '100%',
          height: '93vh',
          spread: 'none' // 设置为单页显示
        })

        this.rendition.on('rendered', async () => {
          const cfiList = this.uniqueEntryPositions
          cfiList.forEach((cfi) => {
            const marker = document.createElement('span')
            marker.style.textDecoration = 'underline' // 设置下划线
            marker.style.textDecorationStyle = 'dashed' // 设置下划线样式
            marker.style.textDecorationColor = '#ffa500' // 设置下划线颜色
            marker.style.textDecorationOffset = '0.2em' // 设置下划线的偏移量为0.2em
            marker.style.lineHeight = '1.5' // 设置行高为1.5倍
            marker.classList.add('my-marker')

            const range = this.rendition.getRange(cfi.entry_position)
            if (range) {
              range.surroundContents(marker)

              // 创建并添加红色背景的div元素
              const div = document.createElement('div')
              div.innerText = cfi.keywords
              div.style.backgroundColor = 'rgba(165, 165, 249, 0.5)'
              div.style.display = 'inline'
              div.style.padding = '2px 4px'
              div.style.borderRadius = '10px'
              marker.parentNode.insertBefore(div, marker.nextSibling)
            }
          })
        })

        this.rendition.display()

        // 添加选中事件监听器
        this.rendition.on('selected', (cfiRange, contents) => {
          this.isShowNotePop = false
          // 获取选中范围
          const range = contents.window.getSelection().getRangeAt(0)
          if (!range.toString().trim()) {
            this.isShowNotePop = false
          } else {
            const rect = range.getBoundingClientRect()
            let selectionLeft = rect.left
            const pageWidth = window.innerWidth * 0.6

            if (selectionLeft > pageWidth) {
              selectionLeft =
                selectionLeft -
                pageWidth * Math.floor(selectionLeft / pageWidth)
            }

            const relativeTop = range.getBoundingClientRect().top
            const relativeLeft = selectionLeft

            this.notePopTop = relativeTop
            this.notePopLeft = relativeLeft
            this.isShowNotePop = true

            this.selectText = range.toString()

            this.entry_position = cfiRange
            this.chapterInformation = this.chapterInformation_bookTitle
            for (let i = 0; i < contents.content.children.length; i++) {
              const element = contents.content.children[i]
              if (
                element.tagName.toLowerCase().startsWith('h1') ||
                element.tagName.toLowerCase().startsWith('h2')
              ) {
                this.chapterInformation =
                  this.chapterInformation_bookTitle + ' ' + element.textContent
                return
              }
            }
          }
        })
        // 获取locations对象 epubjs的钩子函数实现
        this.book.ready
          .then(() => {
            this.navigation = this.book.navigation

            return this.book.locations.generate()
          })
          .then((result) => {
            this.locations = this.book.locations
            this.bookAvailable = true
            // 获取总页数
            this.totalPages = this.locations.length()
          })
      }
    },

    // 卸载当前展示的epub文件
    destroyEpub () {
      if (this.book) {
        this.rendition.destroy()
        this.book = null
        this.locations = null
        this.navigation = null
        this.totalPages = 0
        this.currentPage = 1
        this.isShowNotePop = false
      }
    }
  },
  async mounted () {
    this.pdfUrl = this.$route.query.url
    this.title = this.$route.query.title
    await this.readEpubMes(this.pdfUrl)
  }
}
</script>

<style lang="scss" scoped>
.edit-padding-box{
  display: flex;
  height: 93vh;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-top: 10px;
}
.pub-content{
  position: relative;
}
.change-to-word{
  position: absolute;
  top: 10px;
  right: -120px
}
.close-btn{
  width: 20px;
  height: 20px;
  img{
    width: 20px;
    height: 20px;
  }
}
</style>
