{"version": 3, "sources": ["webpack://GanttElastic/webpack/bootstrap", "webpack://GanttElastic/./node_modules/dayjs/dayjs.min.js", "webpack://GanttElastic/./node_modules/css-loader/lib/css-base.js", "webpack://GanttElastic/./node_modules/vue-style-loader/lib/listToStyles.js", "webpack://GanttElastic/./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack://GanttElastic/./src/components/Chart/Text.vue?483e", "webpack://GanttElastic/./src/components/Chart/ProgressBar.vue?534b", "webpack://GanttElastic/./src/components/Chart/Chart.vue?f7f3", "webpack://GanttElastic/./src/GanttElastic.vue?8d77", "webpack://GanttElastic/external \"Vue\"", "webpack://GanttElastic/./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js", "webpack://GanttElastic/./src/components/Chart/Text.vue?a818", "webpack://GanttElastic/./src/components/Chart/Text.vue?f86e", "webpack://GanttElastic/./src/components/Chart/ProgressBar.vue?82a9", "webpack://GanttElastic/./src/components/Chart/ProgressBar.vue?2007", "webpack://GanttElastic/./src/components/Chart/Chart.vue?7176", "webpack://GanttElastic/./src/components/Chart/Chart.vue?60b1", "webpack://GanttElastic/(webpack)/buildin/global.js", "webpack://GanttElastic/./src/GanttElastic.vue?4a7c", "webpack://GanttElastic/./src/GanttElastic.vue?58f6", "webpack://GanttElastic/./src/GanttElastic.vue?e261", "webpack://GanttElastic/./src/components/MainView.vue?ff89", "webpack://GanttElastic/./src/components/TaskList/TaskList.vue?ac45", "webpack://GanttElastic/./src/components/TaskList/TaskListHeader.vue?9cf6", "webpack://GanttElastic/./src/components/Expander.vue?e88b", "webpack://GanttElastic/src/components/Expander.vue", "webpack://GanttElastic/./src/components/Expander.vue?0898", "webpack://GanttElastic/./node_modules/vue-loader/lib/runtime/componentNormalizer.js", "webpack://GanttElastic/./src/components/Expander.vue", "webpack://GanttElastic/src/components/TaskList/TaskListHeader.vue", "webpack://GanttElastic/./src/components/TaskList/TaskListHeader.vue?6353", "webpack://GanttElastic/./src/components/TaskList/TaskListHeader.vue", "webpack://GanttElastic/./src/components/TaskList/TaskListItem.vue?2ebc", "webpack://GanttElastic/./src/components/TaskList/ItemColumn.vue?875e", "webpack://GanttElastic/./src/components/TaskList/slot.js", "webpack://GanttElastic/src/components/TaskList/ItemColumn.vue", "webpack://GanttElastic/./src/components/TaskList/ItemColumn.vue?8814", "webpack://GanttElastic/./src/components/TaskList/ItemColumn.vue", "webpack://GanttElastic/src/components/TaskList/TaskListItem.vue", "webpack://GanttElastic/./src/components/TaskList/TaskListItem.vue?64f8", "webpack://GanttElastic/./src/components/TaskList/TaskListItem.vue", "webpack://GanttElastic/src/components/TaskList/TaskList.vue", "webpack://GanttElastic/./src/components/TaskList/TaskList.vue?7354", "webpack://GanttElastic/./src/components/TaskList/TaskList.vue", "webpack://GanttElastic/./src/components/Chart/Chart.vue?580c", "webpack://GanttElastic/./src/components/Chart/Grid.vue?c206", "webpack://GanttElastic/src/components/Chart/Grid.vue", "webpack://GanttElastic/./src/components/Chart/Grid.vue?7761", "webpack://GanttElastic/./src/components/Chart/Grid.vue", "webpack://GanttElastic/./src/components/Chart/DaysHighlight.vue?73fd", "webpack://GanttElastic/src/components/Chart/DaysHighlight.vue", "webpack://GanttElastic/./src/components/Chart/DaysHighlight.vue?7e33", "webpack://GanttElastic/./src/components/Chart/DaysHighlight.vue", "webpack://GanttElastic/./src/components/Calendar/Calendar.vue?29d8", "webpack://GanttElastic/./src/components/Calendar/CalendarRow.vue?6e4c", "webpack://GanttElastic/src/components/Calendar/CalendarRow.vue", "webpack://GanttElastic/./src/components/Calendar/CalendarRow.vue?255b", "webpack://GanttElastic/./src/components/Calendar/CalendarRow.vue", "webpack://GanttElastic/src/components/Calendar/Calendar.vue", "webpack://GanttElastic/./src/components/Calendar/Calendar.vue?40d5", "webpack://GanttElastic/./src/components/Calendar/Calendar.vue", "webpack://GanttElastic/./src/components/Chart/DependencyLines.vue?f91c", "webpack://GanttElastic/src/components/Chart/DependencyLines.vue", "webpack://GanttElastic/./src/components/Chart/DependencyLines.vue?7164", "webpack://GanttElastic/./src/components/Chart/DependencyLines.vue", "webpack://GanttElastic/./src/components/Chart/Row/Task.vue?c34f", "webpack://GanttElastic/./src/components/Chart/Text.vue?5298", "webpack://GanttElastic/src/components/Chart/Text.vue", "webpack://GanttElastic/./src/components/Chart/Text.vue?17b1", "webpack://GanttElastic/./src/components/Chart/Text.vue", "webpack://GanttElastic/./src/components/Chart/ProgressBar.vue?26a1", "webpack://GanttElastic/src/components/Chart/ProgressBar.vue", "webpack://GanttElastic/./src/components/Chart/ProgressBar.vue?9aab", "webpack://GanttElastic/./src/components/Chart/ProgressBar.vue", "webpack://GanttElastic/./src/components/Chart/Row/Task.mixin.js", "webpack://GanttElastic/src/components/Chart/Row/Task.vue", "webpack://GanttElastic/./src/components/Chart/Row/Task.vue?8687", "webpack://GanttElastic/./src/components/Chart/Row/Task.vue", "webpack://GanttElastic/./src/components/Chart/Row/Milestone.vue?6820", "webpack://GanttElastic/src/components/Chart/Row/Milestone.vue", "webpack://GanttElastic/./src/components/Chart/Row/Milestone.vue?9239", "webpack://GanttElastic/./src/components/Chart/Row/Milestone.vue", "webpack://GanttElastic/./src/components/Chart/Row/Project.vue?055d", "webpack://GanttElastic/src/components/Chart/Row/Project.vue", "webpack://GanttElastic/./src/components/Chart/Row/Project.vue?54c9", "webpack://GanttElastic/./src/components/Chart/Row/Project.vue", "webpack://GanttElastic/src/components/Chart/Chart.vue", "webpack://GanttElastic/./src/components/Chart/Chart.vue?7f9c", "webpack://GanttElastic/./src/components/Chart/Chart.vue", "webpack://GanttElastic/src/components/MainView.vue", "webpack://GanttElastic/./src/components/MainView.vue?f5b3", "webpack://GanttElastic/./src/components/MainView.vue", "webpack://GanttElastic/./src/style.js", "webpack://GanttElastic/src/GanttElastic.vue", "webpack://GanttElastic/./src/GanttElastic.vue?ca2a", "webpack://GanttElastic/./src/GanttElastic.vue"], "names": [], "mappings": ";;QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;AClFA,eAAe,KAAoD,oBAAoB,SAA+G,CAAC,kBAAkB,aAAa,wJAAwJ,EAAE,UAAU,IAAI,WAAW,IAAI,YAAY,IAAI,QAAQ,IAAI,QAAQ,IAAI,iCAAiC,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,uNAAuN,oCAAoC,4CAA4C,mBAAmB,gBAAgB,yDAAyD,IAAI,kBAAkB,6DAA6D,+CAA+C,mBAAmB,mCAAmC,8GAA8G,mCAAmC,eAAe,yCAAyC,eAAe,OAAO,yCAAyC,kDAAkD,eAAe,mBAAmB,aAAa,OAAO,kBAAkB,sBAAsB,qBAAqB,MAAM,eAAe,uBAAuB,sBAAsB,4BAA4B,mBAAmB,iCAAiC,KAAK,aAAa,WAAW,4BAA4B,iBAAiB,yBAAyB,8BAA8B,0CAA0C,KAAK,8BAA8B,YAAY,8CAA8C,GAAG,iBAAiB,cAAc,0CAA0C,kBAAkB,2BAA2B,oBAAoB,qBAAqB,iCAAiC,0BAA0B,wCAAwC,uCAAuC,iBAAiB,MAAM,6CAA6C,0HAA0H,mBAAmB,mBAAmB,aAAa,mBAAmB,cAAc,oLAAoL,qBAAqB,SAAS,sBAAsB,gCAAgC,wBAAwB,WAAW,4CAA4C,yBAAyB,4BAA4B,0BAA0B,0BAA0B,sBAAsB,oCAAoC,mBAAmB,sCAAsC,sBAAsB,yBAAyB,yBAAyB,kDAAkD,wDAAwD,sBAAsB,iBAAiB,uFAAuF,0DAA0D,UAAU,gCAAgC,gCAAgC,yDAAyD,0BAA0B,oCAAoC,+BAA+B,+BAA+B,oCAAoC,6BAA6B,qBAAqB,0BAA0B,sBAAsB,iDAAiD,yKAAyK,iBAAiB,4BAA4B,0EAA0E,sBAAsB,wBAAwB,qBAAqB,8BAA8B,mBAAmB,sBAAsB,qBAAqB,aAAa,YAAY,2BAA2B,WAAW,gDAAgD,sCAAsC,sCAAsC,qBAAqB,qBAAqB,WAAW,uDAAuD,mBAAmB,0BAA0B,wBAAwB,sBAAsB,4BAA4B,2CAA2C,sHAAsH,0CAA0C,eAAe,2BAA2B,+BAA+B,qBAAqB,2BAA2B,IAAI,kZAAkZ,kCAAkC,kCAAkC,GAAG,wBAAwB,sDAAsD,wBAAwB,kFAAkF,cAAc,6GAA6G,0BAA0B,wBAAwB,sBAAsB,kBAAkB,wBAAwB,qBAAqB,+BAA+B,qBAAqB,oBAAoB,yBAAyB,qBAAqB,gCAAgC,qBAAqB,8CAA8C,0BAA0B,6BAA6B,uBAAuB,6BAA6B,GAAG,iBAAiB,qHAAqH,oBAAoB,6BAA6B,0BAA0B,kCAAkC,2CAA2C,gBAAgB,wBAAwB,GAAG,G;;;;;;ACA3gN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,mCAAmC,gBAAgB;AACnD,IAAI;AACJ;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,gBAAgB,iBAAiB;AACjC;AACA;AACA;AACA;AACA,YAAY,oBAAoB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,oDAAoD,cAAc;;AAElE;AACA;;;;;;;;;;;;;;;AC3EA;AACA;AACA;AACA;AACe;AACf;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,wBAAwB;AAC3D,KAAK;AACL;AACA;AACA;AACA;AACA;;;AC1BA;AACA;AACA;AACA;AACA;;AAEyC;;AAEzC;;AAEA;AACA;AACA;AACA;AACA,UAAU,iBAAiB;AAC3B;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEe;AACf;;AAEA;;AAEA,eAAe,YAAY;AAC3B;;AAEA;AACA;AACA,mBAAmB,mBAAmB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,YAAY;AAC3B;AACA,KAAK;AACL;AACA;AACA,mBAAmB,sBAAsB;AACzC;AACA;AACA,uBAAuB,2BAA2B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB,mBAAmB;AACpC;AACA;AACA;AACA;AACA,qBAAqB,2BAA2B;AAChD;AACA;AACA,YAAY,uBAAuB;AACnC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;;;;;;AC7NA;;AAEA;AACA,cAAc,mBAAO,CAAC,EAAuS;AAC7T;AACA,4CAA4C,QAAS;AACrD;AACA;AACA,UAAU,mBAAO,CAAC,CAAgE;AAClF,+CAA+C;AAC/C;AACA,GAAG,KAAU,EAAE,E;;;;;;ACXf;;AAEA;AACA,cAAc,mBAAO,CAAC,EAAmP;AACzQ;AACA,4CAA4C,QAAS;AACrD;AACA;AACA,UAAU,mBAAO,CAAC,CAAgE;AAClF,+CAA+C;AAC/C;AACA,GAAG,KAAU,EAAE,E;;;;;;ACXf;;AAEA;AACA,cAAc,mBAAO,CAAC,EAA4R;AAClT;AACA,4CAA4C,QAAS;AACrD;AACA;AACA,UAAU,mBAAO,CAAC,CAAgE;AAClF,+CAA+C;AAC/C;AACA,GAAG,KAAU,EAAE,E;;;;;;ACXf;;AAEA;AACA,cAAc,mBAAO,CAAC,EAAkO;AACxP;AACA,4CAA4C,QAAS;AACrD;AACA;AACA,UAAU,mBAAO,CAAC,CAA0D;AAC5E,+CAA+C;AAC/C;AACA,GAAG,KAAU,EAAE,E;;;;;;ACXf,gC;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,aAAa;AAC5B,eAAe,EAAE;AACjB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,mBAAmB,SAAS;AAC5B,mBAAmB,EAAE;AACrB,qBAAqB;AACrB;AACA;AACA,iCAAiC,YAAY;AAC7C,mDAAmD,gBAAgB;AACnE;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,gCAAgC,6BAA6B,EAAE,aAAa;AAC5G,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kBAAkB;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kBAAkB;AACjC,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,qDAAqD,mCAAmC,EAAE;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gBAAgB;AAC/B,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA;AACA,6CAA6C,gBAAgB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,WAAW,UAAU;AACrB,aAAa;AACb;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,WAAW,oBAAoB;AAC/B,aAAa,OAAO;AACpB;AACA;AACA;AACA;AACA,6CAA6C,yBAAyB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,mBAAmB;AAC9B;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,YAAY;AACvB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,kCAAkC,iEAAiE;AACnG;AACA;AACA;AACA;AACA,8BAA8B;AAC9B,8CAA8C;AAC9C,CAAC;AACD;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,YAAY;AACvB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;AACA,YAAY;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,YAAY;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,2CAA2C;AAC7E;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA,eAAe,uBAAuB;AACtC;AACA,eAAe,yBAAyB;AACxC;AACA,eAAe,eAAe;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,uBAAuB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAEc,8DAAK,EAAC;;;;;;;;;AC/5BrB;AAAA;AAAA;;;;;;;ACAA,2BAA2B,mBAAO,CAAC,CAAkD;AACrF;;;AAGA;AACA,cAAc,QAAS,mDAAmD,oBAAoB;;AAE9F;;;;;;;;ACPA;AAAA;AAAA;;;;;;;ACAA,2BAA2B,mBAAO,CAAC,CAAkD;AACrF;;;AAGA;AACA,cAAc,QAAS,2DAA2D,qBAAqB,GAAG,sFAAsF,2DAA2D,GAAG;;AAE9P;;;;;;;;ACPA;AAAA;AAAA;;;;;;;ACAA,2BAA2B,mBAAO,CAAC,CAAkD;AACrF;;;AAGA;AACA,cAAc,QAAS,uEAAuE,iBAAiB,2EAA2E,eAAe;;AAEzM;;;;;;;ACPA;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;ACnBA;AAAA;AAAA;;;;;;;ACAA,2BAA2B,mBAAO,CAAC,CAA4C;AAC/E;;;AAGA;AACA,cAAc,QAAS,2DAA2D,2BAA2B,GAAG,gCAAgC,kBAAkB,GAAG,iCAAiC,mBAAmB,GAAG,6EAA6E,oBAAoB,oBAAoB,GAAG,qBAAqB,gBAAgB,GAAG,uBAAuB,mBAAmB,GAAG,kFAAkF,qBAAqB,oBAAoB,GAAG,wDAAwD,sCAAsC,GAAG,+CAA+C,uCAAuC,GAAG,sDAAsD,iCAAiC,GAAG,uGAAuG,uBAAuB,iCAAiC,GAAG,6FAA6F,uBAAuB,GAAG;;AAEplC;;;;;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,uBAAuB,kBAAkB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA,IAAI,4CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,iCAAiC;AAC/C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,0BAA0B,iCAAiC;AAC3D,uBAAuB,iDAAiD;AACxE,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,2CAA2C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,mBAAmB,+BAA+B;AAClD,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,eAAe,iCAAiC;AAChD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,IAAI,qDAAe;AACnB,4CAAM;;;;;;ACtJN,IAAI,4CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,iCAAiC;AACnD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA,wBAAwB,aAAa;AACrC,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qDAAe;AACnB,4CAAM;;;;;;AC1DN,IAAI,kDAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,uCAAuC;AAC1D,4BAA4B,oCAAoC;AAChE,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,sBAAsB,iBAAiB;AACvC,mBAAmB,8BAA8B;AACjD,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,sBAAsB,iBAAiB;AACvC;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI,2DAAe;AACnB,kDAAM;;;;;;AC9GN,IAAI,4CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,sBAAsB,oDAAoD;AAC1E;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qDAAe;AACnB,4CAAM;;;;;;;AC5BS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACvIyH,CAAgB,gHAAG,EAAC,C;;ACA/I;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AC/FuF;AAC3B;AACL;;;AAGvD;AAC0F;AAC1F,gBAAgB,kBAAU;AAC1B,EAAE,2CAAM;AACR,EAAE,4CAAM;AACR,EAAE,qDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,8D;;;ACqEgC;AAChC;AACf;AACA;AACA,IAAI,0BAAgB;AACpB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACvLkI,CAAgB,0HAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAG7D;AAC6F;AAC7F,IAAI,wBAAS,GAAG,kBAAU;AAC1B,EAAE,+CAAM;AACR,EAAE,kDAAM;AACR,EAAE,2DAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,2EAAS,Q;;AClBxB,IAAI,gDAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,sCAAsC;AACpD,KAAK;AACL;AACA;AACA;AACA,SAAS,0BAA0B,iCAAiC,EAAE;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI,yDAAe;AACnB,gDAAM;;;;;;AC/BN,IAAI,8CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,gCAAgC,qCAAqC;AACrE,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,+BAA+B,+BAA+B;AAC9D,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,uDAAe;AACnB,8CAAM;;;;;;ACrGS;AACf;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH,CAAC,EAAC;;;;ACcgC;AACnB;AACf;AACA;AACA;AACA,gBAAgB,kBAAY;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;AC1K8H,CAAgB,kHAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAGzD;AAC6F;AAC7F,IAAI,oBAAS,GAAG,kBAAU;AAC1B,EAAE,2CAAM;AACR,EAAE,8CAAM;AACR,EAAE,uDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,mEAAS,Q;;;ACGuB;AACL;;AAE3B;AACf;AACA;AACA,IAAI,0BAAgB;AACpB,IAAI,sBAAU;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACxCgI,CAAgB,sHAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAG3D;AAC6F;AAC7F,IAAI,sBAAS,GAAG,kBAAU;AAC1B,EAAE,6CAAM;AACR,EAAE,gDAAM;AACR,EAAE,yDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,uEAAS,Q;;;ACkB0B;AACJ;AAC/B;AACf;AACA;AACA,IAAI,8BAAc;AAClB,IAAI,0BAAY;AAChB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACzD4H,CAAgB,8GAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAGvD;AAC6F;AAC7F,IAAI,kBAAS,GAAG,kBAAU;AAC1B,EAAE,yCAAM;AACR,EAAE,4CAAM;AACR,EAAE,qDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,+DAAS,Q;;AClBxB,IAAI,yCAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,6BAA6B;AAC3C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,mDAAmD;AAC7E,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,uCAAuC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,+BAA+B;AAC/D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,yCAAyC;AAC7E,oCAAoC,qCAAqC;AACzE,2BAA2B;AAC3B;AACA;AACA;AACA,sCAAsC,aAAa;AACnD,6BAA6B;AAC7B;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,kDAAe;AACnB,yCAAM;;;;;;ACxIN,IAAI,wCAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,0CAA0C;AACxD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,kBAAkB,kCAAkC;AACpD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,sBAAsB,4CAA4C;AAClE,sBAAsB,qDAAqD;AAC3E,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,sBAAsB,0CAA0C;AAChE,sBAAsB,qDAAqD;AAC3E,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA,oBAAoB,sCAAsC;AAC1D;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iDAAe;AACnB,wCAAM;;;;;;;ACPS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;AC1KwH,CAAgB,mGAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAGnD;AAC6F;AAC7F,IAAI,cAAS,GAAG,kBAAU;AAC1B,EAAE,kCAAM;AACR,EAAE,wCAAM;AACR,EAAE,iDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,uDAAS,Q;;AClBxB,IAAI,iDAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,sDAAsD;AACxE,SAAS;AACT;AACA;AACA;AACA;AACA,oBAAoB,iDAAiD;AACrE;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA,IAAI,0DAAe;AACnB,iDAAM;;;;;;;ACCoB;AACX;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,mBAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,mBAAK;AACzE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;AC5EiI,CAAgB,qHAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAG5D;AAC6F;AAC7F,IAAI,uBAAS,GAAG,kBAAU;AAC1B,EAAE,2CAAM;AACR,EAAE,iDAAM;AACR,EAAE,0DAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,yEAAS,Q;;AClBxB,IAAI,4CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA,wBAAwB,0CAA0C;AAClE,eAAe;AACf;AACA;AACA;AACA;AACA,wBAAwB,sCAAsC;AAC9D,eAAe;AACf;AACA;AACA;AACA;AACA,wBAAwB,wCAAwC;AAChE,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qDAAe;AACnB,4CAAM;;;;;;AC/CN,IAAI,+CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI,wDAAe;AACnB,+CAAM;;;;;;;ACjBS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;AC3G+H,CAAgB,oHAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAG1D;AAC6F;AAC7F,IAAI,qBAAS,GAAG,kBAAU;AAC1B,EAAE,4CAAM;AACR,EAAE,+CAAM;AACR,EAAE,wDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,qEAAS,Q;;;ACaE;AACkB;;AAE7B;AACf;AACA;AACA;AACA,IAAI,wBAAW;AACf;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,mBAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,mBAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAK;AAC7B;AACA;AACA;AACA,wBAAwB,mBAAK;AAC7B;AACA,sBAAsB,mBAAK;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAK;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACzV4H,CAAgB,8GAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAGvD;AAC6F;AAC7F,IAAI,kBAAS,GAAG,kBAAU;AAC1B,EAAE,yCAAM;AACR,EAAE,4CAAM;AACR,EAAE,qDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,+DAAS,Q;;AClBxB,IAAI,mDAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,wDAAwD;AACtE,cAAc,gDAAgD;AAC9D,KAAK;AACL;AACA;AACA;AACA,SAAS,uBAAuB,aAAa,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,oBAAoB,uCAAuC;AAC3D,WAAW;AACX,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI,4DAAe;AACnB,mDAAM;;;;;;;ACAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;AC1HmI,CAAgB,yHAAG,EAAC,C;;ACA3D;AAC3B;AACL;;;AAG9D;AAC6F;AAC7F,IAAI,yBAAS,GAAG,kBAAU;AAC1B,EAAE,6CAAM;AACR,EAAE,mDAAM;AACR,EAAE,4DAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,6EAAS,Q;;AClBxB,IAAI,wCAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,SAAS,iBAAiB,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,4BAA4B,eAAe,GAAG,gBAAgB;AAC9D;AACA,WAAW;AACX;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA,4BAA4B,SAAS,qBAAqB,EAAE;AAC5D,6BAA6B,SAAS,wBAAwB,EAAE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,oBAAoB,oDAAoD;AACxE,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iDAAe;AACnB,wCAAM;;;;;;ACtLN,IAAI,oDAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,8CAA8C;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,SAAS,SAAS,uDAAuD,EAAE;AAC3E;AACA;AACA;AACA;AACA;AACA,sBAAsB,sCAAsC;AAC5D,sBAAsB,wCAAwC;AAC9D,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,+BAA+B,oCAAoC;AACnE,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,6DAAe;AACnB,oDAAM;;;;;;;ACjBS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACzGwH,CAAgB,mGAAG,EAAC,C;;;;;ACA/C;AACvC;AACL;AAC2C;;;AAG9F;AAC6F;AAC7F,IAAI,cAAS,GAAG,kBAAU;AAC1B,EAAE,kCAAM;AACR,EAAE,oDAAM;AACR,EAAE,6DAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,uDAAS,Q;;ACnBxB,IAAI,+CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,oBAAoB,8CAA8C;AAClE,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,sBAAsB,uBAAuB;AAC7C;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,IAAI,wDAAe;AACnB,+CAAM;;;;;;;ACnBS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACzM+H,CAAgB,iHAAG,EAAC,C;;;;;ACA3D;AAC3B;AACL;AAC8B;;;AAGxF;AAC6F;AAC7F,IAAI,qBAAS,GAAG,kBAAU;AAC1B,EAAE,yCAAM;AACR,EAAE,+CAAM;AACR,EAAE,wDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,qEAAS,Q;;ACnBxB;AACA;AACA;AACA;AACA;AACA;;AAEe;AACf;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,oBAAoB,WAAW,GAAG,YAAY;AAC9C,KAAK;;AAEL;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,0BAA0B,YAAY,GAAG,YAAY;AACrD,KAAK;;AAEL;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa,eAAe;AAC5B,cAAc;AACd,KAAK;;AAEL;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,aAAa,eAAe;AAC5B,cAAc;AACd,KAAK;;AAEL;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,eAAe,OAAO;AACtB,eAAe,MAAM;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,wCAAwC,UAAU;AAClD,wCAAwC,eAAe,GAAG,UAAU;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,eAAe,oDAAoD;AACnE,wBAAwB,eAAe;AACvC,qCAAqC,6CAA6C;AAClF;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH,CAAC,EAAC;;;;ACpHkC;AACS;AACH;AACF;AACzB;AACf;AACA;AACA;AACA,IAAI,eAAS;AACb,IAAI,wBAAW;AACf,IAAI,kBAAQ;AACZ;AACA;AACA;AACA,WAAW,UAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,CAAC,EAAC;;;AC/I2H,CAAgB,iGAAG,EAAC,C;;ACA9D;AAC3B;AACL;;;AAGnD;AACgG;AAChG,IAAI,cAAS,GAAG,kBAAU;AAC1B,EAAE,gCAAM;AACR,EAAE,wCAAM;AACR,EAAE,iDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,uDAAS,Q;;AClBxB,IAAI,6CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,SAAS,iBAAiB,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,4BAA4B,eAAe,GAAG,gBAAgB;AAC9D;AACA,WAAW;AACX;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA,4BAA4B,SAAS,qBAAqB,EAAE;AAC5D,6BAA6B,SAAS,wBAAwB,EAAE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,oBAAoB,wBAAwB;AAC5C,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,sDAAe;AACnB,6CAAM;;;;;;;AChF8B;AACS;AACH;AACF;AACzB;AACf;AACA;AACA;AACA,IAAI,eAAS;AACb,IAAI,wBAAW;AACf,IAAI,kBAAQ;AACZ;AACA;AACA;AACA,WAAW,UAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACtJgI,CAAgB,2GAAG,EAAC,C;;ACA9D;AAC3B;AACL;;;AAGxD;AACgG;AAChG,IAAI,mBAAS,GAAG,kBAAU;AAC1B,EAAE,qCAAM;AACR,EAAE,6CAAM;AACR,EAAE,sDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,iEAAS,Q;;AClBxB,IAAI,2CAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,SAAS,iBAAiB,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,4BAA4B,eAAe,GAAG,gBAAgB;AAC9D;AACA,WAAW;AACX;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA,4BAA4B,SAAS,qBAAqB,EAAE;AAC5D,0BAA0B,SAAS,mBAAmB,EAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,oBAAoB,mBAAmB;AACvC,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oDAAe;AACnB,2CAAM;;;;;;;AChF8B;AACS;AACH;AACF;AACzB;AACf;AACA;AACA;AACA,IAAI,eAAS;AACb,IAAI,wBAAW;AACf,IAAI,kBAAQ;AACZ;AACA;AACA;AACA,WAAW,UAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACpK8H,CAAgB,uGAAG,EAAC,C;;ACA9D;AAC3B;AACL;;;AAGtD;AACgG;AAChG,IAAI,iBAAS,GAAG,kBAAU;AAC1B,EAAE,mCAAM;AACR,EAAE,2CAAM;AACR,EAAE,oDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,6DAAS,Q;;;AC8DM;AACkB;AACA;AACI;AAClB;AACU;AACJ;AACzB;AACf;AACA;AACA;AACA,IAAI,UAAI;AACR,IAAI,gCAAe;AACnB,IAAI,kBAAQ;AACZ,IAAI,UAAI;AACR,IAAI,oBAAS;AACb,IAAI,gBAAO;AACX,IAAI,4BAAa;AACjB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACvLyH,CAAgB,qGAAG,EAAC,C;;;;;ACA3D;AAC3B;AACL;AAC+B;;;AAGnF;AAC6F;AAC7F,IAAI,eAAS,GAAG,kBAAU;AAC1B,EAAE,mCAAM;AACR,EAAE,yCAAM;AACR,EAAE,kDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,yDAAS,Q;;;AC+EuB;AACT;;AAEtC;;AAEe;AACf;AACA;AACA,IAAI,kBAAQ;AACZ,IAAI,YAAK;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAC;;;ACrSyH,CAAgB,gHAAG,EAAC,C;;ACAxD;AAC3B;AACL;;;AAGvD;AAC0F;AAC1F,IAAI,kBAAS,GAAG,kBAAU;AAC1B,EAAE,2CAAM;AACR,EAAE,4CAAM;AACR,EAAE,qDAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,+DAAS,Q;;AClBxB;AACA;AACA;AACA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL,6BAA6B;AAC7B,2BAA2B;AAC3B;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL,kCAAkC;AAClC,gCAAgC;AAChC,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,wCAAwC;AACxC,qCAAqC,yBAAyB;AAC9D,sCAAsC,yBAAyB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,kCAAkC;AAClC,gCAAgC;AAChC,iCAAiC;AACjC,2BAA2B;AAC3B,kBAAkB,oDAAoD;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,iCAAiC,qBAAqB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,oBAAoB;AACpB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,oBAAoB;AACpB;AACA;AACA,KAAK;AACL,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,sCAAsC;AACtC,sCAAsC;AACtC,2BAA2B;AAC3B,+BAA+B;AAC/B,uBAAuB;AACvB;AACA;AACA;AACA;AACA,KAAK;AACL,mCAAmC;AACnC,2BAA2B;AAC3B,mCAAmC;AACnC,qCAAqC;AACrC,6BAA6B;AAC7B,qCAAqC;AACrC,gCAAgC;AAChC,wBAAwB;AACxB,gCAAgC;AAChC,wCAAwC;AACxC,gCAAgC;AAChC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL,wCAAwC;AACxC;AACA;AACA;AACA;AACA,KAAK;AACL,gCAAgC;AAChC;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;;;;;;;AChS8B;AACJ;AACuB;AACf;AACoB;;AAEtD;AACA,cAAc,sBAAW;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,kBAAQ;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mBAAK;AAChC;AACA;AACA,yBAAyB,mBAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,mBAAK;AACX,MAAM,mBAAK;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,mBAAK;AAC3D,uDAAuD,mBAAK;AAC5D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,mBAAK;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,2CAA2C,mBAAK;AAChD;AACA;AACA;AACA;AACA;AACA,0CAA0C,mBAAK;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,mBAAK;AAC1B,0BAA0B,mBAAK;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,mBAAK;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAK;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAK;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,mBAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAK;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,mBAAK;AAChD;AACA;AACA;AACA;AACA;AACA,0CAA0C,mBAAK;AAC/C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oCAAoC,oCAAc;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,qFAAY,EAAC;;;ACprDgG,CAAgB,iHAAG,EAAC,C;;;;;ACArD;AAC3B;AACL;AAC8B;;;AAGzF;AACuF;AACvF,IAAI,sBAAS,GAAG,kBAAU;AAC1B,EAAE,wCAAM;AACR,EAAE,MAAM;AACR,EAAE,eAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,4GAAS,Q", "file": "GanttElastic.common.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 18);\n", "!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs=e()}(this,(function(){\"use strict\";var t=1e3,e=6e4,n=36e5,r=\"millisecond\",i=\"second\",s=\"minute\",u=\"hour\",a=\"day\",o=\"week\",f=\"month\",h=\"quarter\",c=\"year\",d=\"date\",l=\"Invalid Date\",$=/^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[Tt\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/,y=/\\[([^\\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,M={name:\"en\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),ordinal:function(t){var e=[\"th\",\"st\",\"nd\",\"rd\"],n=t%100;return\"[\"+t+(e[(n-20)%10]||e[n]||e[0])+\"]\"}},m=function(t,e,n){var r=String(t);return!r||r.length>=e?t:\"\"+Array(e+1-r.length).join(n)+t},v={s:m,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),r=Math.floor(n/60),i=n%60;return(e<=0?\"+\":\"-\")+m(r,2,\"0\")+\":\"+m(i,2,\"0\")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var r=12*(n.year()-e.year())+(n.month()-e.month()),i=e.clone().add(r,f),s=n-i<0,u=e.clone().add(r+(s?-1:1),f);return+(-(r+(n-i)/(s?i-u:u-i))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:f,y:c,w:o,d:a,D:d,h:u,m:s,s:i,ms:r,Q:h}[t]||String(t||\"\").toLowerCase().replace(/s$/,\"\")},u:function(t){return void 0===t}},g=\"en\",D={};D[g]=M;var p=function(t){return t instanceof _},S=function t(e,n,r){var i;if(!e)return g;if(\"string\"==typeof e){var s=e.toLowerCase();D[s]&&(i=s),n&&(D[s]=n,i=s);var u=e.split(\"-\");if(!i&&u.length>1)return t(u[0])}else{var a=e.name;D[a]=e,i=a}return!r&&i&&(g=i),i||!r&&g},w=function(t,e){if(p(t))return t.clone();var n=\"object\"==typeof e?e:{};return n.date=t,n.args=arguments,new _(n)},O=v;O.l=S,O.i=p,O.w=function(t,e){return w(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var _=function(){function M(t){this.$L=S(t.locale,null,!0),this.parse(t)}var m=M.prototype;return m.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(O.u(e))return new Date;if(e instanceof Date)return new Date(e);if(\"string\"==typeof e&&!/Z$/i.test(e)){var r=e.match($);if(r){var i=r[2]-1||0,s=(r[7]||\"0\").substring(0,3);return n?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},m.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},m.$utils=function(){return O},m.isValid=function(){return!(this.$d.toString()===l)},m.isSame=function(t,e){var n=w(t);return this.startOf(e)<=n&&n<=this.endOf(e)},m.isAfter=function(t,e){return w(t)<this.startOf(e)},m.isBefore=function(t,e){return this.endOf(e)<w(t)},m.$g=function(t,e,n){return O.u(t)?this[e]:this.set(n,t)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(t,e){var n=this,r=!!O.u(e)||e,h=O.p(t),l=function(t,e){var i=O.w(n.$u?Date.UTC(n.$y,e,t):new Date(n.$y,e,t),n);return r?i:i.endOf(a)},$=function(t,e){return O.w(n.toDate()[t].apply(n.toDate(\"s\"),(r?[0,0,0,0]:[23,59,59,999]).slice(e)),n)},y=this.$W,M=this.$M,m=this.$D,v=\"set\"+(this.$u?\"UTC\":\"\");switch(h){case c:return r?l(1,0):l(31,11);case f:return r?l(1,M):l(0,M+1);case o:var g=this.$locale().weekStart||0,D=(y<g?y+7:y)-g;return l(r?m-D:m+(6-D),M);case a:case d:return $(v+\"Hours\",0);case u:return $(v+\"Minutes\",1);case s:return $(v+\"Seconds\",2);case i:return $(v+\"Milliseconds\",3);default:return this.clone()}},m.endOf=function(t){return this.startOf(t,!1)},m.$set=function(t,e){var n,o=O.p(t),h=\"set\"+(this.$u?\"UTC\":\"\"),l=(n={},n[a]=h+\"Date\",n[d]=h+\"Date\",n[f]=h+\"Month\",n[c]=h+\"FullYear\",n[u]=h+\"Hours\",n[s]=h+\"Minutes\",n[i]=h+\"Seconds\",n[r]=h+\"Milliseconds\",n)[o],$=o===a?this.$D+(e-this.$W):e;if(o===f||o===c){var y=this.clone().set(d,1);y.$d[l]($),y.init(),this.$d=y.set(d,Math.min(this.$D,y.daysInMonth())).$d}else l&&this.$d[l]($);return this.init(),this},m.set=function(t,e){return this.clone().$set(t,e)},m.get=function(t){return this[O.p(t)]()},m.add=function(r,h){var d,l=this;r=Number(r);var $=O.p(h),y=function(t){var e=w(l);return O.w(e.date(e.date()+Math.round(t*r)),l)};if($===f)return this.set(f,this.$M+r);if($===c)return this.set(c,this.$y+r);if($===a)return y(1);if($===o)return y(7);var M=(d={},d[s]=e,d[u]=n,d[i]=t,d)[$]||1,m=this.$d.getTime()+r*M;return O.w(m,this)},m.subtract=function(t,e){return this.add(-1*t,e)},m.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||l;var r=t||\"YYYY-MM-DDTHH:mm:ssZ\",i=O.z(this),s=this.$H,u=this.$m,a=this.$M,o=n.weekdays,f=n.months,h=function(t,n,i,s){return t&&(t[n]||t(e,r))||i[n].slice(0,s)},c=function(t){return O.s(s%12||12,t,\"0\")},d=n.meridiem||function(t,e,n){var r=t<12?\"AM\":\"PM\";return n?r.toLowerCase():r},$={YY:String(this.$y).slice(-2),YYYY:this.$y,M:a+1,MM:O.s(a+1,2,\"0\"),MMM:h(n.monthsShort,a,f,3),MMMM:h(f,a),D:this.$D,DD:O.s(this.$D,2,\"0\"),d:String(this.$W),dd:h(n.weekdaysMin,this.$W,o,2),ddd:h(n.weekdaysShort,this.$W,o,3),dddd:o[this.$W],H:String(s),HH:O.s(s,2,\"0\"),h:c(1),hh:c(2),a:d(s,u,!0),A:d(s,u,!1),m:String(u),mm:O.s(u,2,\"0\"),s:String(this.$s),ss:O.s(this.$s,2,\"0\"),SSS:O.s(this.$ms,3,\"0\"),Z:i};return r.replace(y,(function(t,e){return e||$[t]||i.replace(\":\",\"\")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(r,d,l){var $,y=O.p(d),M=w(r),m=(M.utcOffset()-this.utcOffset())*e,v=this-M,g=O.m(this,M);return g=($={},$[c]=g/12,$[f]=g,$[h]=g/3,$[o]=(v-m)/6048e5,$[a]=(v-m)/864e5,$[u]=v/n,$[s]=v/e,$[i]=v/t,$)[y]||v,l?g:O.a(g)},m.daysInMonth=function(){return this.endOf(f).$D},m.$locale=function(){return D[this.$L]},m.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),r=S(t,e,!0);return r&&(n.$L=r),n},m.clone=function(){return O.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},M}(),T=_.prototype;return w.prototype=T,[[\"$ms\",r],[\"$s\",i],[\"$m\",s],[\"$H\",u],[\"$W\",a],[\"$M\",f],[\"$y\",c],[\"$D\",d]].forEach((function(t){T[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),w.extend=function(t,e){return t.$i||(t(e,_,w),t.$i=!0),w},w.locale=S,w.isDayjs=p,w.unix=function(t){return w(1e3*t)},w.en=D[g],w.Ls=D,w.p={},w}));", "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n// css base code, injected by the css-loader\nmodule.exports = function(useSourceMap) {\n\tvar list = [];\n\n\t// return the list of modules as css string\n\tlist.toString = function toString() {\n\t\treturn this.map(function (item) {\n\t\t\tvar content = cssWithMappingToString(item, useSourceMap);\n\t\t\tif(item[2]) {\n\t\t\t\treturn \"@media \" + item[2] + \"{\" + content + \"}\";\n\t\t\t} else {\n\t\t\t\treturn content;\n\t\t\t}\n\t\t}).join(\"\");\n\t};\n\n\t// import a list of modules into the list\n\tlist.i = function(modules, mediaQuery) {\n\t\tif(typeof modules === \"string\")\n\t\t\tmodules = [[null, modules, \"\"]];\n\t\tvar alreadyImportedModules = {};\n\t\tfor(var i = 0; i < this.length; i++) {\n\t\t\tvar id = this[i][0];\n\t\t\tif(typeof id === \"number\")\n\t\t\t\talreadyImportedModules[id] = true;\n\t\t}\n\t\tfor(i = 0; i < modules.length; i++) {\n\t\t\tvar item = modules[i];\n\t\t\t// skip already imported module\n\t\t\t// this implementation is not 100% perfect for weird media query combinations\n\t\t\t//  when a module is imported multiple times with different media queries.\n\t\t\t//  I hope this will never occur (Hey this way we have smaller bundles)\n\t\t\tif(typeof item[0] !== \"number\" || !alreadyImportedModules[item[0]]) {\n\t\t\t\tif(mediaQuery && !item[2]) {\n\t\t\t\t\titem[2] = mediaQuery;\n\t\t\t\t} else if(mediaQuery) {\n\t\t\t\t\titem[2] = \"(\" + item[2] + \") and (\" + mediaQuery + \")\";\n\t\t\t\t}\n\t\t\t\tlist.push(item);\n\t\t\t}\n\t\t}\n\t};\n\treturn list;\n};\n\nfunction cssWithMappingToString(item, useSourceMap) {\n\tvar content = item[1] || '';\n\tvar cssMapping = item[3];\n\tif (!cssMapping) {\n\t\treturn content;\n\t}\n\n\tif (useSourceMap && typeof btoa === 'function') {\n\t\tvar sourceMapping = toComment(cssMapping);\n\t\tvar sourceURLs = cssMapping.sources.map(function (source) {\n\t\t\treturn '/*# sourceURL=' + cssMapping.sourceRoot + source + ' */'\n\t\t});\n\n\t\treturn [content].concat(sourceURLs).concat([sourceMapping]).join('\\n');\n\t}\n\n\treturn [content].join('\\n');\n}\n\n// Adapted from convert-source-map (MIT)\nfunction toComment(sourceMap) {\n\t// eslint-disable-next-line no-undef\n\tvar base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n\tvar data = 'sourceMappingURL=data:application/json;charset=utf-8;base64,' + base64;\n\n\treturn '/*# ' + data + ' */';\n}\n", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Text.vue?vue&type=style&index=0&id=26409f00&prod&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"5aa1ccbc\", content, false, {});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Text.vue?vue&type=style&index=0&id=26409f00&prod&lang=scss&scoped=true&\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Text.vue?vue&type=style&index=0&id=26409f00&prod&lang=scss&scoped=true&\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=style&index=0&id=91139344&prod&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"ff27e1be\", content, false, {});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=style&index=0&id=91139344&prod&lang=css&\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=style&index=0&id=91139344&prod&lang=css&\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chart.vue?vue&type=style&index=0&id=23688fcb&prod&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"ef3c1e08\", content, false, {});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chart.vue?vue&type=style&index=0&id=23688fcb&prod&lang=scss&\", function() {\n     var newContent = require(\"!!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chart.vue?vue&type=style&index=0&id=23688fcb&prod&lang=scss&\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/index.js!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/vue-loader/lib/index.js??vue-loader-options!./GanttElastic.vue?vue&type=style&index=0&id=1ca074be&prod&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"1af3dc42\", content, false, {});\n// Hot Module Replacement\nif(module.hot) {\n // When the styles change, update the <style> tags\n if(!content.locals) {\n   module.hot.accept(\"!!../node_modules/css-loader/index.js!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/vue-loader/lib/index.js??vue-loader-options!./GanttElastic.vue?vue&type=style&index=0&id=1ca074be&prod&lang=css&\", function() {\n     var newContent = require(\"!!../node_modules/css-loader/index.js!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/vue-loader/lib/index.js??vue-loader-options!./GanttElastic.vue?vue&type=style&index=0&id=1ca074be&prod&lang=css&\");\n     if(newContent.__esModule) newContent = newContent.default;\n     if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n     update(newContent);\n   });\n }\n // When the module is disposed, remove the <style> tags\n module.hot.dispose(function() { update(); });\n}", "module.exports = require(\"Vue\");", "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n", "export * from \"-!../../../node_modules/vue-style-loader/index.js!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Text.vue?vue&type=style&index=0&id=26409f00&prod&lang=scss&scoped=true&\"", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".gantt-elastic__chart-row-text[data-v-26409f00]{pointer-events:none}\", \"\"]);\n\n// exports\n", "export * from \"-!../../../node_modules/vue-style-loader/index.js!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=style&index=0&id=91139344&prod&lang=css&\"", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \"\\n.gantt-elastic__chart-row-progress-bar-outline:hover {\\n  cursor: w-resize;\\n}\\n.gantt-elastic__chart-row-bar:hover .gantt-elastic__chart-row-progress-bar-outline {\\n  stroke-width: calc(var(--stroke-width) * 2) !important;\\n}\\n\", \"\"]);\n\n// exports\n", "export * from \"-!../../../node_modules/vue-style-loader/index.js!../../../node_modules/css-loader/index.js!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/sass-loader/dist/cjs.js!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chart.vue?vue&type=style&index=0&id=23688fcb&prod&lang=scss&\"", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".gantt-elastic__chart-row-bar-wrapper .gantt-elastic__chart-row-bar{overflow:visible}.gantt-elastic__chart-row-bar-wrapper .gantt-elastic__chart-row-bar-circle{cursor:pointer}\", \"\"]);\n\n// exports\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "export * from \"-!../node_modules/vue-style-loader/index.js!../node_modules/css-loader/index.js!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/vue-loader/lib/index.js??vue-loader-options!./GanttElastic.vue?vue&type=style&index=0&id=1ca074be&prod&lang=css&\"", "exports = module.exports = require(\"../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \"\\n[class^='gantt-elastic'],\\n[class*=' gantt-elastic'] {\\n  box-sizing: border-box;\\n}\\n.gantt-elastic:focus-visible {\\n  outline: none;\\n}\\n.gantt-elastic__main-view svg {\\n  display: block;\\n}\\n.gantt-elastic__grid-horizontal-line,\\n.gantt-elastic__grid-vertical-line {\\n  stroke: #a0a0a0;\\n  stroke-width: 1;\\n}\\nforeignObject > * {\\n  margin: 0px;\\n}\\n.gantt-elastic .p-2 {\\n  padding: 10rem;\\n}\\n.gantt-elastic__main-view-main-container,\\n.gantt-elastic__main-view-container {\\n  overflow: hidden;\\n  max-width: 100%;\\n}\\n.gantt-elastic__task-list-header-column:last-of-type {\\n  border-right: 1px solid #00000050;\\n}\\n.gantt-elastic__task-list-item:last-of-type {\\n  border-bottom: 1px solid #00000050;\\n}\\n.gantt-elastic__task-list-item-value-wrapper:hover {\\n  overflow: visible !important;\\n}\\n.gantt-elastic__task-list-item-value-wrapper:hover > .gantt-elastic__task-list-item-value-container {\\n  position: relative;\\n  overflow: visible !important;\\n}\\n.gantt-elastic__task-list-item-value-wrapper:hover > .gantt-elastic__task-list-item-value {\\n  position: absolute;\\n}\\n\", \"\"]);\n\n// exports\n", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c,\n    _setup = _vm._self._setupProxy\n  return _c(\n    \"div\",\n    {\n      staticClass: \"gantt-elastic\",\n      staticStyle: { width: \"100%\" },\n      attrs: { tabindex: \"-1\" },\n      on: {\n        keydown: _vm.onKeyDown,\n        keyup: _vm.onKeyUp,\n        mousewheel: _vm.onMousewheel,\n      },\n    },\n    [\n      _vm._t(\"header\"),\n      _vm._v(\" \"),\n      _c(\"main-view\", { ref: \"mainView\" }),\n      _vm._v(\" \"),\n      _vm._t(\"footer\"),\n    ],\n    2\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"gantt-elastic__main-view\",\n      style: { ..._vm.root.style[\"main-view\"] },\n    },\n    [\n      _c(\n        \"div\",\n        {\n          staticClass: \"gantt-elastic__main-container-wrapper\",\n          style: {\n            ..._vm.root.style[\"main-container-wrapper\"],\n            height: _vm.root.state.options.height + \"px\",\n          },\n        },\n        [\n          _c(\n            \"div\",\n            {\n              ref: \"mainView\",\n              staticClass: \"gantt-elastic__main-container\",\n              style: {\n                ..._vm.root.style[\"main-container\"],\n                width: _vm.root.state.options.clientWidth + \"px\",\n                height: _vm.root.state.options.height + \"px\",\n              },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"gantt-elastic__container\",\n                  style: { ..._vm.root.style[\"container\"] },\n                  on: { mousemove: _vm.mouseMove, mouseup: _vm.mouseUp },\n                },\n                [\n                  _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.root.state.options.taskList.display,\n                          expression: \"root.state.options.taskList.display\",\n                        },\n                      ],\n                      ref: \"taskList\",\n                      staticClass: \"gantt-elastic__task-list-container\",\n                      style: {\n                        ..._vm.root.style[\"task-list-container\"],\n                        width:\n                          _vm.root.state.options.taskList.finalWidth + \"px\",\n                        height: _vm.root.state.options.height + \"px\",\n                      },\n                    },\n                    [_c(\"task-list\")],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"div\",\n                    {\n                      ref: \"chartContainer\",\n                      staticClass: \"gantt-elastic__main-view-container\",\n                      style: { ..._vm.root.style[\"main-view-container\"] },\n                      on: {\n                        mousedown: _vm.chartMouseDown,\n                        touchstart: _vm.chartMouseDown,\n                        mouseup: _vm.chartMouseUp,\n                        touchend: _vm.chartMouseUp,\n                        mousemove: function ($event) {\n                          $event.preventDefault()\n                          return _vm.chartMouseMove.apply(null, arguments)\n                        },\n                        touchmove: function ($event) {\n                          $event.preventDefault()\n                          return _vm.chartMouseMove.apply(null, arguments)\n                        },\n                        wheel: function ($event) {\n                          $event.preventDefault()\n                          return _vm.chartWheel.apply(null, arguments)\n                        },\n                      },\n                    },\n                    [_c(\"chart\")],\n                    1\n                  ),\n                ]\n              ),\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              ref: \"chartScrollContainerVertical\",\n              staticClass:\n                \"gantt-elastic__chart-scroll-container gantt-elastic__chart-scroll-container--vertical\",\n              style: {\n                ..._vm.root.style[\"chart-scroll-container\"],\n                ..._vm.root.style[\"chart-scroll-container--vertical\"],\n                ..._vm.verticalStyle,\n              },\n              on: { scroll: _vm.onVerticalScroll },\n            },\n            [\n              _c(\"div\", {\n                staticClass: \"gantt-elastic__chart-scroll--vertical\",\n                style: {\n                  width: \"1px\",\n                  height: _vm.root.state.options.allVisibleTasksHeight + \"px\",\n                },\n              }),\n            ]\n          ),\n        ]\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        {\n          ref: \"chartScrollContainerHorizontal\",\n          staticClass:\n            \"gantt-elastic__chart-scroll-container gantt-elastic__chart-scroll-container--horizontal\",\n          style: {\n            ..._vm.root.style[\"chart-scroll-container\"],\n            ..._vm.root.style[\"chart-scroll-container--horizontal\"],\n            marginLeft: _vm.getMarginLeft,\n          },\n          on: { scroll: _vm.onHorizontalScroll },\n        },\n        [\n          _c(\"div\", {\n            staticClass: \"gantt-elastic__chart-scroll--horizontal\",\n            style: {\n              height: \"1px\",\n              width: _vm.root.state.options.width + \"px\",\n            },\n          }),\n        ]\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      directives: [\n        {\n          name: \"show\",\n          rawName: \"v-show\",\n          value: _vm.root.state.options.taskList.display,\n          expression: \"root.state.options.taskList.display\",\n        },\n      ],\n      ref: \"taskListWrapper\",\n      staticClass: \"gantt-elastic__task-list-wrapper\",\n      style: {\n        ..._vm.root.style[\"task-list-wrapper\"],\n        width: \"100%\",\n        height: \"100%\",\n      },\n    },\n    [\n      _c(\n        \"div\",\n        {\n          ref: \"taskList\",\n          staticClass: \"gantt-elastic__task-list\",\n          style: { ..._vm.root.style[\"task-list\"] },\n        },\n        [\n          _c(\"task-list-header\"),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              ref: \"taskListItems\",\n              staticClass: \"gantt-elastic__task-list-items\",\n              style: {\n                ..._vm.root.style[\"task-list-items\"],\n                height: _vm.root.state.options.rowsHeight + \"px\",\n              },\n            },\n            _vm._l(_vm.root.visibleTasks, function (task) {\n              return _c(\"task-list-item\", {\n                key: task.id,\n                attrs: { task: task },\n              })\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"gantt-elastic__task-list-header\",\n      style: {\n        ..._vm.root.style[\"task-list-header\"],\n        height: `${_vm.root.state.options.calendar.height}px`,\n        \"margin-bottom\": `${_vm.root.state.options.calendar.gap}px`,\n      },\n    },\n    _vm._l(_vm.root.getTaskListColumns, function (column) {\n      return _c(\n        \"div\",\n        {\n          key: column._id,\n          staticClass: \"gantt-elastic__task-list-header-column\",\n          style: {\n            ..._vm.root.style[\"task-list-header-column\"],\n            ...column.style[\"task-list-header-column\"],\n            ..._vm.getStyle(column),\n          },\n        },\n        [\n          column.expander\n            ? _c(\"task-list-expander\", {\n                attrs: {\n                  tasks: _vm.collapsible,\n                  options: _vm.root.state.options.taskList.expander,\n                },\n              })\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              staticClass: \"gantt-elastic__task-list-header-label\",\n              style: {\n                ..._vm.root.style[\"task-list-header-label\"],\n                ...column.style[\"task-list-header-label\"],\n              },\n              attrs: { column: column },\n              on: { mouseup: _vm.resizerMouseUp },\n            },\n            [_vm._v(\"\\n      \" + _vm._s(column.label) + \"\\n    \")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              staticClass: \"gantt-elastic__task-list-header-resizer-wrapper\",\n              style: {\n                ..._vm.root.style[\"task-list-header-resizer-wrapper\"],\n                ...column.style[\"task-list-header-resizer-wrapper\"],\n              },\n              attrs: { column: column },\n              on: {\n                mousedown: function ($event) {\n                  return _vm.resizerMouseDown($event, column)\n                },\n              },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"gantt-elastic__task-list-header-resizer\",\n                  style: {\n                    ..._vm.root.style[\"task-list-header-resizer\"],\n                    ...column.style[\"task-list-header-resizer\"],\n                  },\n                },\n                [\n                  _c(\"div\", {\n                    staticClass: \"gantt-elastic__task-list-header-resizer-dot\",\n                    style: {\n                      ..._vm.root.style[\"task-list-header-resizer-dot\"],\n                      ...column.style[\"task-list-header-resizer-dot\"],\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"div\", {\n                    staticClass: \"gantt-elastic__task-list-header-resizer-dot\",\n                    style: {\n                      ..._vm.root.style[\"task-list-header-resizer-dot\"],\n                      ...column.style[\"task-list-header-resizer-dot\"],\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"div\", {\n                    staticClass: \"gantt-elastic__task-list-header-resizer-dot\",\n                    style: {\n                      ..._vm.root.style[\"task-list-header-resizer-dot\"],\n                      ...column.style[\"task-list-header-resizer-dot\"],\n                    },\n                  }),\n                ]\n              ),\n            ]\n          ),\n        ],\n        1\n      )\n    }),\n    0\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      class: _vm.getClassPrefix() + \"-wrapper\",\n      style: {\n        ..._vm.root.style[_vm.getClassPrefix(false) + \"-wrapper\"],\n        ..._vm.style,\n      },\n    },\n    [\n      _vm.allChildren.length\n        ? _c(\n            \"svg\",\n            {\n              class: _vm.getClassPrefix() + \"-content\",\n              style: {\n                ..._vm.root.style[_vm.getClassPrefix(false) + \"-content\"],\n              },\n              attrs: { width: _vm.options.size, height: _vm.options.size },\n              on: {\n                click: function ($event) {\n                  $event.stopPropagation()\n                  return _vm.toggle.apply(null, arguments)\n                },\n              },\n            },\n            [\n              _c(\"rect\", {\n                class: _vm.getClassPrefix() + \"-border\",\n                style: {\n                  ..._vm.root.style[_vm.getClassPrefix(false) + \"-border\"],\n                  ..._vm.borderStyle,\n                },\n                attrs: {\n                  x: _vm.border,\n                  y: _vm.border,\n                  width: _vm.options.size - _vm.border * 2,\n                  height: _vm.options.size - _vm.border * 2,\n                  rx: \"2\",\n                  ry: \"2\",\n                },\n              }),\n              _vm._v(\" \"),\n              _vm.allChildren.length\n                ? _c(\"line\", {\n                    class: _vm.getClassPrefix() + \"-line\",\n                    style: {\n                      ..._vm.root.style[_vm.getClassPrefix(false) + \"-line\"],\n                    },\n                    attrs: {\n                      x1: _vm.lineOffset,\n                      y1: _vm.options.size / 2,\n                      x2: _vm.options.size - _vm.lineOffset,\n                      y2: _vm.options.size / 2,\n                    },\n                  })\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.collapsed\n                ? _c(\"line\", {\n                    class: _vm.getClassPrefix() + \"-line\",\n                    style: {\n                      ..._vm.root.style[_vm.getClassPrefix(false) + \"-line\"],\n                    },\n                    attrs: {\n                      x1: _vm.options.size / 2,\n                      y1: _vm.lineOffset,\n                      x2: _vm.options.size / 2,\n                      y2: _vm.options.size - _vm.lineOffset,\n                    },\n                  })\n                : _vm._e(),\n            ]\n          )\n        : _vm._e(),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview Expander component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div :class=\"getClassPrefix() + '-wrapper'\" :style=\"{ ...root.style[getClassPrefix(false) + '-wrapper'], ...style }\">\n    <svg\n      :class=\"getClassPrefix() + '-content'\"\n      :style=\"{ ...root.style[getClassPrefix(false) + '-content'] }\"\n      :width=\"options.size\"\n      :height=\"options.size\"\n      v-if=\"allChildren.length\"\n      @click.stop=\"toggle\"\n    >\n      <rect\n        :class=\"getClassPrefix() + '-border'\"\n        :style=\"{\n          ...root.style[getClassPrefix(false) + '-border'],\n          ...borderStyle,\n        }\"\n        :x=\"border\"\n        :y=\"border\"\n        :width=\"options.size - border * 2\"\n        :height=\"options.size - border * 2\"\n        rx=\"2\"\n        ry=\"2\"\n      ></rect>\n      <line\n        :class=\"getClassPrefix() + '-line'\"\n        :style=\"{ ...root.style[getClassPrefix(false) + '-line'] }\"\n        v-if=\"allChildren.length\"\n        :x1=\"lineOffset\"\n        :y1=\"options.size / 2\"\n        :x2=\"options.size - lineOffset\"\n        :y2=\"options.size / 2\"\n      ></line>\n      <line\n        :class=\"getClassPrefix() + '-line'\"\n        :style=\"{ ...root.style[getClassPrefix(false) + '-line'] }\"\n        v-if=\"collapsed\"\n        :x1=\"options.size / 2\"\n        :y1=\"lineOffset\"\n        :x2=\"options.size / 2\"\n        :y2=\"options.size - lineOffset\"\n      ></line>\n    </svg>\n  </div>\n</template>\n\n<script>\nexport default {\n  // eslint-disable-next-line vue/multi-word-component-names\n  name: 'Expander',\n  inject: ['root'],\n  props: ['tasks', 'options', 'type'],\n  data() {\n    const border = 0.5;\n    return {\n      border,\n      borderStyle: {\n        'stroke-width': border,\n      },\n      lineOffset: 5,\n    };\n  },\n  computed: {\n    style() {\n      if (this.type !== 'taskList') {\n        return {};\n      }\n      const margin = this.root.state.options.taskList.expander.margin;\n      const padding = this.tasks[0].parents.length * this.root.state.options.taskList.expander.padding;\n      return {\n        'padding-left': padding + margin + 'px',\n        margin: 'auto 0',\n      };\n    },\n    /**\n     * Get all tasks\n     *\n     * @returns {array}\n     */\n    allChildren() {\n      const children = [];\n      this.tasks.forEach((task) => {\n        task.allChildren.forEach((childId) => {\n          children.push(childId);\n        });\n      });\n      return children;\n    },\n    /**\n     * Is current expander collapsed?\n     *\n     * @returns {boolean}\n     */\n    collapsed() {\n      if (this.tasks.length === 0) {\n        return false;\n      }\n      let collapsed = 0;\n      for (let i = 0, len = this.tasks.length; i < len; i++) {\n        if (this.tasks[i].collapsed) {\n          collapsed++;\n        }\n      }\n      return collapsed === this.tasks.length;\n    },\n  },\n  methods: {\n    /**\n     * Get specific class prefix\n     *\n     * @returns {string}\n     */\n    getClassPrefix(full = true) {\n      return `${full ? 'gantt-elastic__' : ''}${this.options.type}-expander`;\n    },\n    /**\n     * Toggle expander\n     */\n    toggle() {\n      if (this.tasks.length === 0) {\n        return;\n      }\n      const collapsed = !this.collapsed;\n      this.tasks.forEach((task) => {\n        task.collapsed = collapsed;\n      });\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Expander.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Expander.vue?vue&type=script&lang=js&\"", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent(\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier /* server only */,\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options =\n    typeof scriptExports === 'function' ? scriptExports.options : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) {\n    // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () {\n          injectStyles.call(\n            this,\n            (options.functional ? this.parent : this).$root.$options.shadowRoot\n          )\n        }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functional component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection(h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing ? [].concat(existing, hook) : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "import { render, staticRenderFns } from \"./Expander.vue?vue&type=template&id=011d6d92&\"\nimport script from \"./Expander.vue?vue&type=script&lang=js&\"\nexport * from \"./Expander.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<!--\n/**\n * @fileoverview TaskListHeader component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div\n    class=\"gantt-elastic__task-list-header\"\n    :style=\"{\n      ...root.style['task-list-header'],\n      height: `${root.state.options.calendar.height}px`,\n      'margin-bottom': `${root.state.options.calendar.gap}px`,\n    }\"\n  >\n    <div\n      class=\"gantt-elastic__task-list-header-column\"\n      :style=\"{\n        ...root.style['task-list-header-column'],\n        ...column.style['task-list-header-column'],\n        ...getStyle(column),\n      }\"\n      v-for=\"column in root.getTaskListColumns\"\n      :key=\"column._id\"\n    >\n      <task-list-expander\n        v-if=\"column.expander\"\n        :tasks=\"collapsible\"\n        :options=\"root.state.options.taskList.expander\"\n      ></task-list-expander>\n      <div\n        class=\"gantt-elastic__task-list-header-label\"\n        :style=\"{\n          ...root.style['task-list-header-label'],\n          ...column.style['task-list-header-label'],\n        }\"\n        :column=\"column\"\n        @mouseup=\"resizerMouseUp\"\n      >\n        {{ column.label }}\n      </div>\n      <div\n        class=\"gantt-elastic__task-list-header-resizer-wrapper\"\n        :style=\"{\n          ...root.style['task-list-header-resizer-wrapper'],\n          ...column.style['task-list-header-resizer-wrapper'],\n        }\"\n        :column=\"column\"\n        @mousedown=\"resizerMouseDown($event, column)\"\n      >\n        <div\n          class=\"gantt-elastic__task-list-header-resizer\"\n          :style=\"{\n            ...root.style['task-list-header-resizer'],\n            ...column.style['task-list-header-resizer'],\n          }\"\n        >\n          <div\n            class=\"gantt-elastic__task-list-header-resizer-dot\"\n            :style=\"{\n              ...root.style['task-list-header-resizer-dot'],\n              ...column.style['task-list-header-resizer-dot'],\n            }\"\n          ></div>\n          <div\n            class=\"gantt-elastic__task-list-header-resizer-dot\"\n            :style=\"{\n              ...root.style['task-list-header-resizer-dot'],\n              ...column.style['task-list-header-resizer-dot'],\n            }\"\n          ></div>\n          <div\n            class=\"gantt-elastic__task-list-header-resizer-dot\"\n            :style=\"{\n              ...root.style['task-list-header-resizer-dot'],\n              ...column.style['task-list-header-resizer-dot'],\n            }\"\n          ></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TaskListExpander from '../Expander.vue';\nexport default {\n  name: 'TaskListHeader',\n  components: {\n    TaskListExpander,\n  },\n\n  inject: ['root'],\n\n  data() {\n    return {\n      resizer: {\n        moving: false,\n        x: 0,\n      },\n    };\n  },\n\n  computed: {\n    /**\n     * Is this row collapsible?\n     *\n     * @returns {bool}\n     */\n    collapsible() {\n      return this.root.state.tasks.filter((task) => task.allChildren.length > 0);\n    },\n  },\n\n  methods: {\n    /**\n     * Get style\n     *\n     * @returns {object}\n     */\n    getStyle(column) {\n      return {\n        width: column.finalWidth + 'px',\n      };\n    },\n    /**\n     * Resizer mouse down event handler\n     */\n    resizerMouseDown(event, column) {\n      if (!this.resizer.moving) {\n        this.resizer.moving = column;\n        this.resizer.x = event.clientX;\n        this.resizer.initialWidth = column.width;\n        this.root.$emit('taskList-column-width-change-start', this.resizer.moving);\n      }\n    },\n\n    /**\n     * Resizer mouse move event handler\n     */\n    resizerMouseMove(event) {\n      if (this.resizer.moving) {\n        const lastWidth = this.resizer.moving.width;\n        this.resizer.moving.width = this.resizer.initialWidth + event.clientX - this.resizer.x;\n        if (this.resizer.moving.width < this.root.state.options.taskList.minWidth) {\n          this.resizer.moving.width = this.root.state.options.taskList.minWidth;\n        }\n        if (lastWidth !== this.resizer.moving.width) {\n          this.root.$emit('taskList-column-width-change', this.resizer.moving);\n        }\n      }\n    },\n\n    /**\n     * Resizer mouse up event handler\n     */\n    resizerMouseUp(event) {\n      if (this.resizer.moving) {\n        this.root.$emit('taskList-column-width-change-stop', this.resizer.moving);\n        this.resizer.moving = false;\n      }\n    },\n  },\n\n  /**\n   * Created\n   */\n  created() {\n    this.mouseUpListener = document.addEventListener('mouseup', this.resizerMouseUp.bind(this));\n    this.mouseMoveListener = document.addEventListener('mousemove', this.resizerMouseMove.bind(this));\n    this.root.$on('main-view-mousemove', this.resizerMouseMove);\n    this.root.$on('main-view-mouseup', this.resizerMouseUp);\n  },\n\n  /**\n   * Before destroy event - clear all event listeners\n   */\n  beforeDestroy() {\n    document.removeEventListener('mouseup', this.resizerMouseUp);\n    document.removeEventListener('mousemove', this.resizerMouseMove);\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskListHeader.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskListHeader.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TaskListHeader.vue?vue&type=template&id=387823c6&\"\nimport script from \"./TaskListHeader.vue?vue&type=script&lang=js&\"\nexport * from \"./TaskListHeader.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"gantt-elastic__task-list-item\",\n      style: { ..._vm.root.style[\"task-list-item\"] },\n    },\n    _vm._l(_vm.columns, function (column) {\n      return _c(\n        \"item-column\",\n        { key: column._id, attrs: { column: column, task: _vm.task } },\n        [\n          column.expander\n            ? _c(\"task-list-expander\", {\n                attrs: {\n                  tasks: [_vm.task],\n                  options: _vm.root.state.options.taskList.expander,\n                  type: \"taskList\",\n                },\n              })\n            : _vm._e(),\n        ],\n        1\n      )\n    }),\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"gantt-elastic__task-list-item-column\",\n      style: _vm.itemColumnStyle,\n    },\n    [\n      _c(\n        \"div\",\n        {\n          staticClass: \"gantt-elastic__task-list-item-value-wrapper\",\n          style: _vm.wrapperStyle,\n          on: {\n            click: function ($event) {\n              return _vm.emitEvent(\"click\", $event)\n            },\n            dblclick: function ($event) {\n              return _vm.emitEvent(\"dblclick\", $event)\n            },\n            mouseenter: function ($event) {\n              return _vm.emitEvent(\"mouseenter\", $event)\n            },\n            mouseover: function ($event) {\n              return _vm.emitEvent(\"mouseover\", $event)\n            },\n            mouseout: function ($event) {\n              return _vm.emitEvent(\"mouseout\", $event)\n            },\n            mousemove: function ($event) {\n              return _vm.emitEvent(\"mousemove\", $event)\n            },\n            mousedown: function ($event) {\n              return _vm.emitEvent(\"mousedown\", $event)\n            },\n            mouseup: function ($event) {\n              return _vm.emitEvent(\"mouseup\", $event)\n            },\n            mousewheel: function ($event) {\n              return _vm.emitEvent(\"mousewheel\", $event)\n            },\n            touchstart: function ($event) {\n              return _vm.emitEvent(\"touchstart\", $event)\n            },\n            touchmove: function ($event) {\n              return _vm.emitEvent(\"touchmove\", $event)\n            },\n            touchend: function ($event) {\n              return _vm.emitEvent(\"touchend\", $event)\n            },\n          },\n        },\n        [\n          _vm._t(\"default\"),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              staticClass: \"gantt-elastic__task-list-item-value-container\",\n              style: _vm.containerStyle,\n            },\n            [\n              _vm.slot\n                ? _c(\n                    \"div\",\n                    {\n                      staticClass: \"gantt-elastic__task-list-item-value\",\n                      style: _vm.valueStyle,\n                    },\n                    [\n                      _c(\"taskListSlot\", {\n                        attrs: { column: _vm.column, task: _vm.task },\n                      }),\n                    ],\n                    1\n                  )\n                : !_vm.html\n                ? _c(\n                    \"div\",\n                    {\n                      staticClass: \"gantt-elastic__task-list-item-value\",\n                      style: _vm.valueStyle,\n                    },\n                    [_vm._v(\"\\n        \" + _vm._s(_vm.value) + \"\\n      \")]\n                  )\n                : _c(\"div\", {\n                    staticClass: \"gantt-elastic__task-list-item-value\",\n                    style: _vm.valueStyle,\n                    domProps: { innerHTML: _vm._s(_vm.value) },\n                  }),\n            ]\n          ),\n        ],\n        2\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "export default {\n  name: 'taskListSlot',\n  functional: true,\n  inject: ['root'],\n\n  props: {\n    column: {\n      type: Object,\n      default: null,\n    },\n\n    task: {\n      type: Object,\n      default: null,\n    },\n  },\n\n  render: (h, ctx) => {\n    return h(\n      'div',\n      {},\n      ctx.injections.root.$scopedSlots[ctx.props.column.slot]({\n        column: ctx.props.column,\n        task: ctx.props.task,\n      })\n    );\n  },\n};\n", "<!--\n/**\n * @fileoverview ItemColumn component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div class=\"gantt-elastic__task-list-item-column\" :style=\"itemColumnStyle\">\n    <div\n      class=\"gantt-elastic__task-list-item-value-wrapper\"\n      :style=\"wrapperStyle\"\n      @click=\"emitEvent('click', $event)\"\n      @dblclick=\"emitEvent('dblclick', $event)\"\n      @mouseenter=\"emitEvent('mouseenter', $event)\"\n      @mouseover=\"emitEvent('mouseover', $event)\"\n      @mouseout=\"emitEvent('mouseout', $event)\"\n      @mousemove=\"emitEvent('mousemove', $event)\"\n      @mousedown=\"emitEvent('mousedown', $event)\"\n      @mouseup=\"emitEvent('mouseup', $event)\"\n      @mousewheel=\"emitEvent('mousewheel', $event)\"\n      @touchstart=\"emitEvent('touchstart', $event)\"\n      @touchmove=\"emitEvent('touchmove', $event)\"\n      @touchend=\"emitEvent('touchend', $event)\"\n    >\n      <slot></slot>\n      <div class=\"gantt-elastic__task-list-item-value-container\" :style=\"containerStyle\">\n        <div v-if=\"slot\" class=\"gantt-elastic__task-list-item-value\" :style=\"valueStyle\">\n          <taskListSlot :column=\"column\" :task=\"task\" />\n        </div>\n        <div v-else-if=\"!html\" class=\"gantt-elastic__task-list-item-value\" :style=\"valueStyle\">\n          {{ value }}\n        </div>\n        <div v-else class=\"gantt-elastic__task-list-item-value\" :style=\"valueStyle\" v-html=\"value\"></div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport taskListSlot from './slot';\nexport default {\n  name: 'ItemColumn',\n  inject: ['root'],\n  props: ['column', 'task'],\n  components: { taskListSlot },\n  data() {\n    return {};\n  },\n  mounted() {},\n  methods: {\n    /**\n     * Emit event\n     *\n     * @param {String} eventName\n     * @param {Event} event\n     */\n    emitEvent(eventName, event) {\n      if (typeof this.column.events !== 'undefined' && typeof this.column.events[eventName] === 'function') {\n        this.column.events[eventName]({\n          event,\n          data: this.task,\n          column: this.column,\n        });\n      }\n\n      /**\n       * 改动：\n       * 新增无任务类型的基础事件\n       */\n\n      const eventObj = {\n        event,\n        data: this.task,\n        column: this.column,\n      };\n\n      const eventEmitFun = () => {\n        this.root.$emit(`task-list-${eventName}`, eventObj);\n        this.root.$emit(`task-list-${this.task.type}-${eventName}`, eventObj);\n      };\n\n      switch (eventName) {\n        // 消除单机事件和双击事件同时使用带来的副作用\n        case 'click':\n        case 'dblclick':\n          clearTimeout(this._eventTriggerTime);\n          this._eventTriggerTime = setTimeout(() => {\n            eventEmitFun();\n          }, 300);\n\n          if (eventName === 'dblclick') {\n            clearTimeout(this._eventTriggerTime);\n            eventEmitFun();\n          }\n          break;\n        default:\n          eventEmitFun();\n          break;\n      }\n    },\n  },\n  computed: {\n    /**\n     * Should we display html or just text?\n     *\n     * @returns {boolean}\n     */\n    html() {\n      if (typeof this.column.html !== 'undefined' && this.column.html === true) {\n        return true;\n      }\n      return false;\n    },\n\n    /**\n     * Should we display slot or just text?\n     *\n     * @returns {boolean}\n     */\n    slot() {\n      if (!this.html && typeof this.column.slot === 'string' && this.column.slot !== '') {\n        return true;\n      }\n      return false;\n    },\n\n    /**\n     * Get column value\n     *\n     * @returns {any|string}\n     */\n    value() {\n      if (typeof this.column.value === 'function') {\n        return this.column.value(this.task);\n      }\n      return this.task[this.column.value];\n    },\n\n    itemColumnStyle() {\n      return {\n        ...this.root.style['task-list-item-column'],\n        ...this.column.style['task-list-item-column'],\n        width: this.column.finalWidth + 'px',\n        height: this.column.height + 'px',\n      };\n    },\n\n    wrapperStyle() {\n      return {\n        ...this.root.style['task-list-item-value-wrapper'],\n        ...this.column.style['task-list-item-value-wrapper'],\n      };\n    },\n\n    containerStyle() {\n      return {\n        ...this.root.style['task-list-item-value-container'],\n        ...this.column.style['task-list-item-value-container'],\n      };\n    },\n\n    valueStyle() {\n      return {\n        ...this.root.style['task-list-item-value'],\n        ...this.column.style['task-list-item-value'],\n      };\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ItemColumn.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ItemColumn.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ItemColumn.vue?vue&type=template&id=7041fdd1&\"\nimport script from \"./ItemColumn.vue?vue&type=script&lang=js&\"\nexport * from \"./ItemColumn.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<!--\n/**\n * @fileoverview TaskListItem component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div class=\"gantt-elastic__task-list-item\" :style=\"{ ...root.style['task-list-item'] }\">\n    <item-column v-for=\"column in columns\" :key=\"column._id\" :column=\"column\" :task=\"task\">\n      <task-list-expander\n        v-if=\"column.expander\"\n        :tasks=\"[task]\"\n        :options=\"root.state.options.taskList.expander\"\n        type=\"taskList\"\n      ></task-list-expander>\n    </item-column>\n  </div>\n</template>\n<script>\nimport TaskListExpander from '../Expander.vue';\nimport ItemColumn from './ItemColumn.vue';\n\nexport default {\n  name: 'TaskListItem',\n  components: {\n    TaskListExpander,\n    ItemColumn,\n  },\n  inject: ['root'],\n  props: ['task'],\n  data() {\n    return {};\n  },\n  computed: {\n    columns() {\n      return this.root.state.options.taskList.columns;\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskListItem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskListItem.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TaskListItem.vue?vue&type=template&id=27273714&\"\nimport script from \"./TaskListItem.vue?vue&type=script&lang=js&\"\nexport * from \"./TaskListItem.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<!--\n/**\n * @fileoverview TaskList component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div\n    class=\"gantt-elastic__task-list-wrapper\"\n    ref=\"taskListWrapper\"\n    :style=\"{\n      ...root.style['task-list-wrapper'],\n      width: '100%',\n      height: '100%',\n    }\"\n    v-show=\"root.state.options.taskList.display\"\n  >\n    <div class=\"gantt-elastic__task-list\" :style=\"{ ...root.style['task-list'] }\" ref=\"taskList\">\n      <task-list-header></task-list-header>\n      <div\n        class=\"gantt-elastic__task-list-items\"\n        ref=\"taskListItems\"\n        :style=\"{\n          ...root.style['task-list-items'],\n          height: root.state.options.rowsHeight + 'px',\n        }\"\n      >\n        <task-list-item v-for=\"task in root.visibleTasks\" :key=\"task.id\" :task=\"task\"></task-list-item>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TaskListHeader from './TaskListHeader.vue';\nimport TaskListItem from './TaskListItem.vue';\nexport default {\n  name: 'TaskList',\n  components: {\n    TaskListHeader,\n    TaskListItem,\n  },\n  inject: ['root'],\n  data() {\n    return {};\n  },\n\n  /**\n   * Mounted\n   */\n  mounted() {\n    this.root.state.refs.taskListWrapper = this.$refs.taskListWrapper;\n    this.root.state.refs.taskList = this.$refs.taskList;\n    this.root.state.refs.taskListItems = this.$refs.taskListItems;\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TaskList.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TaskList.vue?vue&type=template&id=5e517f1a&\"\nimport script from \"./TaskList.vue?vue&type=script&lang=js&\"\nexport * from \"./TaskList.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      ref: \"chart\",\n      staticClass: \"gantt-elastic__chart\",\n      style: { ..._vm.root.style[\"chart\"] },\n    },\n    [\n      _c(\n        \"div\",\n        {\n          ref: \"chartCalendarContainer\",\n          staticClass: \"gantt-elastic__chart-calendar-container\",\n          style: {\n            ..._vm.root.style[\"chart-calendar-container\"],\n            height: _vm.root.state.options.calendar.height + \"px\",\n            \"margin-bottom\": _vm.root.state.options.calendar.gap + \"px\",\n          },\n        },\n        [_c(\"calendar\")],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        {\n          ref: \"chartGraphContainer\",\n          staticClass: \"gantt-elastic__chart-graph-container\",\n          style: {\n            ..._vm.root.style[\"chart-graph-container\"],\n            height:\n              _vm.root.state.options.height -\n              _vm.root.state.options.calendar.height +\n              \"px\",\n          },\n        },\n        [\n          _c(\n            \"div\",\n            {\n              style: {\n                ..._vm.root.style[\"chart-area\"],\n                width: _vm.root.state.options.width + \"px\",\n                height: _vm.root.state.options.rowsHeight + \"px\",\n              },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  ref: \"chartGraph\",\n                  staticClass: \"gantt-elastic__chart-graph\",\n                  style: { ..._vm.root.style[\"chart-graph\"], height: \"100%\" },\n                },\n                [\n                  _c(\n                    \"svg\",\n                    {\n                      ref: \"chartGraphSvg\",\n                      staticClass: \"gantt-elastic__chart-graph-svg\",\n                      style: { ..._vm.root.style[\"chart-graph-svg\"] },\n                      attrs: {\n                        x: \"0\",\n                        y: \"0\",\n                        width: _vm.root.state.options.width + \"px\",\n                        height:\n                          _vm.root.state.options.allVisibleTasksHeight + \"px\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                      },\n                    },\n                    [\n                      _c(\"days-highlight\"),\n                      _vm._v(\" \"),\n                      _c(\"grid\"),\n                      _vm._v(\" \"),\n                      _c(\"dependency-lines\", {\n                        attrs: { tasks: _vm.root.visibleTasks },\n                      }),\n                      _vm._v(\" \"),\n                      _vm._l(_vm.root.visibleTasks, function (task) {\n                        return _c(\n                          \"g\",\n                          {\n                            key: task.id,\n                            staticClass: \"gantt-elastic__chart-row-wrapper\",\n                            style: { ..._vm.root.style[\"chart-row-wrapper\"] },\n                            attrs: { task: task, \"data-taskid\": task.id },\n                          },\n                          [\n                            _c(task.type, {\n                              tag: \"component\",\n                              attrs: { task: task },\n                            }),\n                          ],\n                          1\n                        )\n                      }),\n                      _vm._v(\" \"),\n                      _c(\n                        \"line\",\n                        _vm._b(\n                          {\n                            directives: [\n                              {\n                                name: \"show\",\n                                rawName: \"v-show\",\n                                value: _vm.connectingLine.show,\n                                expression: \"connectingLine.show\",\n                              },\n                            ],\n                            staticStyle: {\n                              stroke: \"rgba(0, 119, 192, 0.5)\",\n                              \"stroke-width\": \"4\",\n                            },\n                          },\n                          \"line\",\n                          _vm.getContLine,\n                          false\n                        )\n                      ),\n                    ],\n                    2\n                  ),\n                ]\n              ),\n            ]\n          ),\n        ]\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"svg\",\n    {\n      ref: \"chart\",\n      staticClass: \"gantt-elastic__grid-lines-wrapper\",\n      style: { ..._vm.root.style[\"grid-lines-wrapper\"] },\n      attrs: {\n        x: \"0\",\n        y: \"0\",\n        width: _vm.root.state.options.width,\n        height: _vm.root.state.options.allVisibleTasksHeight,\n        xmlns: \"http://www.w3.org/2000/svg\",\n      },\n    },\n    [\n      _c(\n        \"g\",\n        {\n          staticClass: \"gantt-elastic__grid-lines\",\n          style: { ..._vm.root.style[\"grid-lines\"] },\n        },\n        [\n          _vm._l(_vm.horizontalLines, function (line) {\n            return _c(\"line\", {\n              key: line.key,\n              staticClass: \"gantt-elastic__grid-line-horizontal\",\n              style: { ..._vm.root.style[\"grid-line-horizontal\"] },\n              attrs: { x1: line.x1, y1: line.y1, x2: line.x2, y2: line.y2 },\n            })\n          }),\n          _vm._v(\" \"),\n          _vm._l(_vm.verticalLines, function (line) {\n            return _c(\"line\", {\n              key: line.key,\n              staticClass: \"gantt-elastic__grid-line-vertical\",\n              style: { ..._vm.root.style[\"grid-line-vertical\"] },\n              attrs: { x1: line.x1, y1: line.y1, x2: line.x2, y2: line.y2 },\n            })\n          }),\n          _vm._v(\" \"),\n          _c(\"line\", {\n            staticClass: \"gantt-elastic__grid-line-time\",\n            style: { ..._vm.root.style[\"grid-line-time\"] },\n            attrs: {\n              x1: _vm.timeLinePosition.x,\n              y1: _vm.timeLinePosition.y1,\n              x2: _vm.timeLinePosition.x,\n              y2: _vm.timeLinePosition.y2,\n            },\n          }),\n        ],\n        2\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview Grid component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <svg\n    class=\"gantt-elastic__grid-lines-wrapper\"\n    :style=\"{ ...root.style['grid-lines-wrapper'] }\"\n    ref=\"chart\"\n    x=\"0\"\n    y=\"0\"\n    :width=\"root.state.options.width\"\n    :height=\"root.state.options.allVisibleTasksHeight\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g class=\"gantt-elastic__grid-lines\" :style=\"{ ...root.style['grid-lines'] }\">\n      <line\n        class=\"gantt-elastic__grid-line-horizontal\"\n        :style=\"{ ...root.style['grid-line-horizontal'] }\"\n        v-for=\"line in horizontalLines\"\n        :key=\"line.key\"\n        :x1=\"line.x1\"\n        :y1=\"line.y1\"\n        :x2=\"line.x2\"\n        :y2=\"line.y2\"\n      ></line>\n      <line\n        class=\"gantt-elastic__grid-line-vertical\"\n        :style=\"{ ...root.style['grid-line-vertical'] }\"\n        v-for=\"line in verticalLines\"\n        :key=\"line.key\"\n        :x1=\"line.x1\"\n        :y1=\"line.y1\"\n        :x2=\"line.x2\"\n        :y2=\"line.y2\"\n      ></line>\n      <line\n        class=\"gantt-elastic__grid-line-time\"\n        :style=\"{ ...root.style['grid-line-time'] }\"\n        :x1=\"timeLinePosition.x\"\n        :y1=\"timeLinePosition.y1\"\n        :x2=\"timeLinePosition.x\"\n        :y2=\"timeLinePosition.y2\"\n      ></line>\n    </g>\n  </svg>\n</template>\n\n<script>\nexport default {\n  // eslint-disable-next-line vue/multi-word-component-names\n  name: 'Grid',\n  inject: ['root'],\n  data() {\n    return {};\n  },\n  /**\n   * Created\n   */\n  created() {\n    this.root.$on('recenterPosition', this.recenterPosition);\n  },\n\n  /**\n   * Mounted\n   */\n  mounted() {\n    this.$nextTick(() => {\n      this.$nextTick(() => {\n        // because of stupid slider :/\n        this.root.scrollToTime(this.timeLinePosition.time);\n      });\n    });\n  },\n\n  methods: {\n    /**\n     * Recenter position - go to current time line\n     */\n    recenterPosition() {\n      this.root.scrollToTime(this.timeLinePosition.time);\n    },\n  },\n\n  computed: {\n    /**\n     * Generate vertical lines of the grid\n     *\n     * @returns {array}\n     */\n    verticalLines() {\n      let lines = [];\n      const state = this.root.state;\n      state.options.times.steps.forEach((step) => {\n        if (this.root.isInsideViewPort(step.offset.px, 1)) {\n          lines.push({\n            key: step.time,\n            x1: step.offset.px,\n            y1: 0,\n            x2: step.offset.px,\n            y2:\n              state.tasks.length * (state.options.row.height + state.options.chart.grid.horizontal.gap * 2) +\n              this.root.style['grid-line-vertical']['stroke-width'],\n          });\n        }\n      });\n      return lines;\n    },\n\n    /**\n     * Generate horizontal lines of the grid\n     *\n     * @returns {array}\n     */\n    horizontalLines() {\n      let lines = [];\n      const state = this.root.state.options;\n      let tasks = this.root.visibleTasks;\n      for (let index = 0, len = tasks.length; index <= len; index++) {\n        const y =\n          index * (state.row.height + state.chart.grid.horizontal.gap * 2) +\n          this.root.style['grid-line-vertical']['stroke-width'] / 2;\n        lines.push({\n          key: 'hl' + index,\n          x1: 0,\n          y1: y,\n          x2: '100%',\n          y2: y,\n        });\n      }\n      return lines;\n    },\n\n    /**\n     * Check if specified line is inside viewport (visible)\n     *\n     * @returns {function}\n     */\n    inViewPort() {\n      return (line) => {\n        const state = this.root.state.options;\n        return line.x1 >= state.scroll.chart.left && line.x1 <= state.scroll.chart.right;\n      };\n    },\n\n    /**\n     * Get current time line position\n     *\n     * @returns {object}\n     */\n    timeLinePosition() {\n      const d = new Date();\n      const current = d.getTime();\n      const currentOffset = this.root.timeToPixelOffsetX(current);\n      const timeLine = {\n        x: 0,\n        y1: 0,\n        y2: '100%',\n        dateTime: '',\n        time: current,\n      };\n      timeLine.x = currentOffset;\n      timeLine.dateTime = d.toLocaleDateString();\n      return timeLine;\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Grid.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Grid.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Grid.vue?vue&type=template&id=7eff4a24&\"\nimport script from \"./Grid.vue?vue&type=script&lang=js&\"\nexport * from \"./Grid.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.showWorkingDays\n    ? _c(\n        \"g\",\n        {\n          staticClass: \"gantt-elastic__chart-days-highlight-container\",\n          style: { ..._vm.root.style[\"chart-days-highlight-container\"] },\n        },\n        _vm._l(_vm.workingDays, function (day) {\n          return _c(\"rect\", {\n            key: _vm.getKey(day),\n            staticClass: \"gantt-elastic__chart-days-highlight-rect\",\n            style: { ..._vm.root.style[\"chart-days-highlight-rect\"] },\n            attrs: {\n              x: day.offset.px,\n              y: \"0\",\n              width: day.width.px,\n              height: \"100%\",\n            },\n          })\n        }),\n        0\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview Days highlight component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n\n<template>\n  <g\n    class=\"gantt-elastic__chart-days-highlight-container\"\n    :style=\"{ ...root.style['chart-days-highlight-container'] }\"\n    v-if=\"showWorkingDays\"\n  >\n    <rect\n      class=\"gantt-elastic__chart-days-highlight-rect\"\n      v-for=\"day in workingDays\"\n      :key=\"getKey(day)\"\n      :x=\"day.offset.px\"\n      y=\"0\"\n      :width=\"day.width.px\"\n      height=\"100%\"\n      :style=\"{ ...root.style['chart-days-highlight-rect'] }\"\n    ></rect>\n  </g>\n</template>\n\n<script>\nimport dayjs from 'dayjs';\nexport default {\n  name: 'DaysHighlight',\n  inject: ['root'],\n  data() {\n    return {};\n  },\n  methods: {\n    /**\n     * Get key\n     *\n     * @param {object} day\n     * @returns {string} key ideintifier for loop\n     */\n    getKey(day) {\n      return dayjs(day.time).format('YYYY-MM-DD');\n    },\n  },\n  computed: {\n    /**\n     * Get working days\n     *\n     * @returns {array}\n     */\n    workingDays() {\n      return this.root.state.options.times.steps.filter((step) => {\n        return this.root.state.options.calendar.workingDays.indexOf(dayjs(step.time).day()) === -1;\n      });\n    },\n\n    /**\n     * Show working days?\n     *\n     * @returns {bool}\n     */\n    showWorkingDays() {\n      const calendar = this.root.state.options.calendar;\n      if (\n        typeof calendar.workingDays !== 'undefined' &&\n        Array.isArray(calendar.workingDays) &&\n        calendar.workingDays.length\n      ) {\n        return true;\n      }\n      return false;\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DaysHighlight.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DaysHighlight.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./DaysHighlight.vue?vue&type=template&id=5caa0270&\"\nimport script from \"./DaysHighlight.vue?vue&type=script&lang=js&\"\nexport * from \"./DaysHighlight.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"gantt-elastic__calendar-wrapper\",\n      style: {\n        ..._vm.root.style[\"calendar-wrapper\"],\n        width: _vm.root.state.options.width + \"px\",\n      },\n    },\n    [\n      _c(\n        \"div\",\n        {\n          staticClass: \"gantt-elastic__calendar\",\n          style: {\n            ..._vm.root.style[\"calendar\"],\n            width: _vm.root.state.options.width + \"px\",\n          },\n        },\n        [\n          _vm.root.state.options.calendar.month.display\n            ? _c(\"calendar-row\", {\n                attrs: { items: _vm.dates.months, which: \"month\" },\n              })\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.root.state.options.calendar.day.display\n            ? _c(\"calendar-row\", {\n                attrs: { items: _vm.dates.days, which: \"day\" },\n              })\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.root.state.options.calendar.hour.display\n            ? _c(\"calendar-row\", {\n                attrs: { items: _vm.dates.hours, which: \"hour\" },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      class:\n        \"gantt-elastic__calendar-row gantt-elastic__calendar-row--\" + _vm.which,\n      style: _vm.rowStyle,\n    },\n    _vm._l(_vm.items, function (item, itemIndex) {\n      return _c(\n        \"div\",\n        {\n          key: item.key,\n          class:\n            \"gantt-elastic__calendar-row-rect gantt-elastic__calendar-row-rect--\" +\n            _vm.which,\n          style: _vm.rectStyle,\n        },\n        _vm._l(item.children, function (child, childIndex) {\n          return _c(\n            \"div\",\n            {\n              key: child.key,\n              class:\n                \"gantt-elastic__calendar-row-rect-child gantt-elastic__calendar-row-rect-child--\" +\n                _vm.which,\n              style: _vm.rectChildStyle[itemIndex][childIndex],\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  class:\n                    \"gantt-elastic__calendar-row-text gantt-elastic__calendar-row-text--\" +\n                    _vm.which,\n                  style: _vm.textStyle(child),\n                },\n                [_vm._v(\"\\n        \" + _vm._s(child.label) + \"\\n      \")]\n              ),\n            ]\n          )\n        }),\n        0\n      )\n    }),\n    0\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview CalendarRow component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div :class=\"'gantt-elastic__calendar-row gantt-elastic__calendar-row--' + which\" :style=\"rowStyle\">\n    <div\n      v-for=\"(item, itemIndex) in items\"\n      :key=\"item.key\"\n      :class=\"'gantt-elastic__calendar-row-rect gantt-elastic__calendar-row-rect--' + which\"\n      :style=\"rectStyle\"\n    >\n      <div\n        :class=\"'gantt-elastic__calendar-row-rect-child gantt-elastic__calendar-row-rect-child--' + which\"\n        v-for=\"(child, childIndex) in item.children\"\n        :key=\"child.key\"\n        :style=\"rectChildStyle[itemIndex][childIndex]\"\n      >\n        <div\n          :class=\"'gantt-elastic__calendar-row-text gantt-elastic__calendar-row-text--' + which\"\n          :style=\"textStyle(child)\"\n        >\n          {{ child.label }}\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CalendarRow',\n  inject: ['root'],\n  props: ['items', 'which'],\n  data() {\n    return {};\n  },\n  methods: {\n    /**\n     * Get x position\n     *\n     * @returns {number}\n     */\n    getTextX(item) {\n      let x = item.x + item.width / 2 - item.textWidth / 2;\n      if (this.which === 'month' && this.root.isInsideViewPort(item.x, item.width, 0)) {\n        let scrollWidth = this.root.state.options.scroll.chart.right - this.root.state.options.scroll.chart.left;\n        x = this.root.state.options.scroll.chart.left + scrollWidth / 2 - item.textWidth / 2 + 2;\n        if (x + item.textWidth + 2 > item.x + item.width) {\n          x = item.x + item.width - item.textWidth - 2;\n        } else if (x < item.x) {\n          x = item.x + 2;\n        }\n      }\n      return x - item.x;\n    },\n  },\n  computed: {\n    rowStyle() {\n      return {\n        ...this.root.style['calendar-row'],\n        ...this.root.style['calendar-row--' + this.which],\n      };\n    },\n    rectStyle() {\n      return {\n        ...this.root.style['calendar-row-rect'],\n        ...this.root.style['calendar-row-rect--' + this.which],\n      };\n    },\n    rectChildStyle() {\n      const basicStyle = {\n        ...this.root.style['calendar-row-rect-child'],\n        ...this.root.style['calendar-row-rect-child--' + this.which],\n      };\n      const style = [];\n      for (let item of this.items) {\n        const childrenStyle = [];\n        for (let child of item.children) {\n          childrenStyle.push({\n            ...basicStyle,\n            width: child.width + 'px',\n            height: child.height + 'px',\n          });\n        }\n        style.push(childrenStyle);\n      }\n      return style;\n    },\n    textStyle() {\n      const basicStyle = {\n        ...this.root.style['calendar-row-text'],\n        ...this.root.style['calendar-row-text--' + this.which],\n      };\n      return (child) => {\n        const style = { ...basicStyle };\n        if (this.which === 'month') {\n          style.left = this.getTextX(child) + 'px';\n        }\n        return style;\n      };\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarRow.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarRow.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./CalendarRow.vue?vue&type=template&id=6698ecef&\"\nimport script from \"./CalendarRow.vue?vue&type=script&lang=js&\"\nexport * from \"./CalendarRow.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<!--\n/**\n * @fileoverview Calendar component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div\n    class=\"gantt-elastic__calendar-wrapper\"\n    :style=\"{\n      ...root.style['calendar-wrapper'],\n      width: root.state.options.width + 'px',\n    }\"\n  >\n    <div\n      class=\"gantt-elastic__calendar\"\n      :style=\"{\n        ...root.style['calendar'],\n        width: root.state.options.width + 'px',\n      }\"\n    >\n      <calendar-row :items=\"dates.months\" which=\"month\" v-if=\"root.state.options.calendar.month.display\"></calendar-row>\n      <calendar-row :items=\"dates.days\" which=\"day\" v-if=\"root.state.options.calendar.day.display\"></calendar-row>\n      <calendar-row :items=\"dates.hours\" which=\"hour\" v-if=\"root.state.options.calendar.hour.display\"></calendar-row>\n    </div>\n  </div>\n</template>\n\n<script>\nimport dayjs from 'dayjs';\nimport CalendarRow from './CalendarRow.vue';\n\nexport default {\n  // eslint-disable-next-line vue/multi-word-component-names\n  name: 'Calendar',\n  components: {\n    CalendarRow,\n  },\n  inject: ['root'],\n  data() {\n    return {};\n  },\n\n  methods: {\n    /**\n     * How many hours will fit?\n     *\n     * @returns {object}\n     */\n    howManyHoursFit(dayIndex) {\n      const stroke = 1;\n      const additionalSpace = stroke + 2;\n      let fullCellWidth = this.root.state.options.times.steps[dayIndex].width.px;\n      let formatNames = Object.keys(this.root.state.options.calendar.hour.format);\n      for (let hours = 24; hours > 1; hours = Math.ceil(hours / 2)) {\n        for (let formatName of formatNames) {\n          if (\n            (this.root.state.options.calendar.hour.maxWidths[formatName] + additionalSpace) * hours <= fullCellWidth &&\n            hours > 1\n          ) {\n            return {\n              count: hours,\n              type: formatName,\n            };\n          }\n        }\n      }\n      return {\n        count: 0,\n        type: '',\n      };\n    },\n\n    /**\n     * How many days will fit?\n     *\n     * @returns {object}\n     */\n    howManyDaysFit() {\n      const stroke = 1;\n      const additionalSpace = stroke + 2;\n      let fullWidth = this.root.state.options.width;\n      let formatNames = Object.keys(this.root.state.options.calendar.day.format);\n      for (let days = this.root.state.options.times.steps.length; days > 1; days = Math.ceil(days / 2)) {\n        for (let formatName of formatNames) {\n          if (\n            (this.root.state.options.calendar.day.maxWidths[formatName] + additionalSpace) * days <= fullWidth &&\n            days > 1\n          ) {\n            return {\n              count: days,\n              type: formatName,\n            };\n          }\n        }\n      }\n      return {\n        count: 0,\n        type: '',\n      };\n    },\n\n    /**\n     * How many months will fit?\n     *\n     * @returns {object}\n     */\n    howManyMonthsFit() {\n      const stroke = 1;\n      const additionalSpace = stroke + 2;\n      let fullWidth = this.root.state.options.width;\n      let formatNames = Object.keys(this.root.state.options.calendar.month.format);\n      let currentMonth = dayjs(this.root.state.options.times.firstTime);\n      let previousMonth = currentMonth.clone();\n      const lastTime = this.root.state.options.times.lastTime;\n      let monthsCount = this.root.monthsCount(\n        this.root.state.options.times.firstTime,\n        this.root.state.options.times.lastTime\n      );\n      if (monthsCount === 1) {\n        for (let formatName of formatNames) {\n          if (this.root.state.options.calendar.month.maxWidths[formatName] + additionalSpace <= fullWidth) {\n            return {\n              count: 1,\n              type: formatName,\n            };\n          }\n        }\n      }\n      for (let months = monthsCount; months > 1; months = Math.ceil(months / 2)) {\n        for (let formatName of formatNames) {\n          if (\n            (this.root.state.options.calendar.month.maxWidths[formatName] + additionalSpace) * months <= fullWidth &&\n            months > 1\n          ) {\n            return {\n              count: months,\n              type: formatName,\n            };\n          }\n        }\n      }\n      return {\n        count: 0,\n        type: formatNames[0],\n      };\n    },\n\n    /**\n     * Generate hours\n     *\n     * @returns {array}\n     */\n    generateHours() {\n      let allHours = [];\n      if (!this.root.state.options.calendar.hour.display) {\n        return allHours;\n      }\n      const steps = this.root.state.options.times.steps;\n      const localeName = this.root.state.options.locale.name;\n      for (let hourIndex = 0, len = steps.length; hourIndex < len; hourIndex++) {\n        const hoursCount = this.howManyHoursFit(hourIndex);\n        if (hoursCount.count === 0) {\n          continue;\n        }\n        const hours = { key: hourIndex + 'step', children: [] };\n        const hourStep = 24 / hoursCount.count;\n        const hourWidthPx = steps[hourIndex].width.px / hoursCount.count;\n        for (let i = 0, len = hoursCount.count; i < len; i++) {\n          const hour = i * hourStep;\n          let index = hourIndex;\n          if (hourIndex > 0) {\n            index = hourIndex - Math.floor(hourIndex / 24) * 24;\n          }\n          let textWidth = 0;\n          if (typeof this.root.state.options.calendar.hour.widths[index] !== 'undefined') {\n            textWidth = this.root.state.options.calendar.hour.widths[index][hoursCount.type];\n          }\n          let x = steps[hourIndex].offset.px + hourWidthPx * i;\n          hours.children.push({\n            index: hourIndex,\n            key: 'h' + i,\n            x,\n            y: this.root.state.options.calendar.day.height + this.root.state.options.calendar.month.height,\n            width: hourWidthPx,\n            textWidth,\n            height: this.root.state.options.calendar.hour.height,\n            label: this.root.state.options.calendar.hour.formatted[hoursCount.type][hour],\n          });\n        }\n        allHours.push(hours);\n      }\n      return allHours;\n    },\n\n    /**\n     * Generate days\n     *\n     * @returns {array}\n     */\n    generateDays() {\n      let days = [];\n      if (!this.root.state.options.calendar.day.display) {\n        return days;\n      }\n      const daysCount = this.howManyDaysFit();\n      if (daysCount.count === 0) {\n        return days;\n      }\n      const steps = this.root.state.options.times.steps;\n      const localeName = this.root.state.options.locale.name;\n      const dayStep = Math.ceil(steps.length / daysCount.count);\n      for (let dayIndex = 0, len = steps.length; dayIndex < len; dayIndex += dayStep) {\n        let dayWidthPx = 0;\n        // day could be shorter (daylight saving time) so join widths and divide\n        for (let currentStep = 0; currentStep < dayStep; currentStep++) {\n          if (typeof steps[dayIndex + currentStep] !== 'undefined') {\n            dayWidthPx += steps[dayIndex + currentStep].width.px;\n          }\n        }\n        const date = dayjs(steps[dayIndex].time);\n        let textWidth = 0;\n        if (typeof this.root.state.options.calendar.day.widths[dayIndex] !== 'undefined') {\n          textWidth = this.root.state.options.calendar.day.widths[dayIndex][daysCount.type];\n        }\n        let x = steps[dayIndex].offset.px;\n        days.push({\n          index: dayIndex,\n          key: steps[dayIndex].time + 'd',\n          x,\n          y: this.root.state.options.calendar.month.height,\n          width: dayWidthPx,\n          textWidth,\n          height: this.root.state.options.calendar.day.height,\n          label: this.root.state.options.calendar.day.format[daysCount.type](date.locale(localeName)),\n        });\n      }\n      return days.map((item) => ({\n        key: item.key,\n        children: [item],\n      }));\n    },\n\n    /**\n     * Generate months\n     *\n     * @returns {array}\n     */\n    generateMonths() {\n      let months = [];\n      if (!this.root.state.options.calendar.month.display) {\n        return months;\n      }\n      const monthsCount = this.howManyMonthsFit();\n      if (monthsCount.count === 0) {\n        return months;\n      }\n      const steps = this.root.state.options.times.steps;\n      const localeName = this.root.state.options.locale.name;\n      let formatNames = Object.keys(this.root.state.options.calendar.month.format);\n      let currentDate = dayjs(this.root.state.options.times.firstTime);\n      for (let monthIndex = 0; monthIndex < monthsCount.count; monthIndex++) {\n        let monthWidth = 0;\n        let monthOffset = Number.MAX_SAFE_INTEGER;\n        let finalDate = dayjs(currentDate).add(1, 'month').startOf('month');\n        if (finalDate.valueOf() > this.root.state.options.times.lastTime) {\n          finalDate = dayjs(this.root.state.options.times.lastTime);\n        }\n        // we must find first and last step to get the offsets / widths\n        for (let step = 0, len = this.root.state.options.times.steps.length; step < len; step++) {\n          let currentStep = this.root.state.options.times.steps[step];\n          if (currentStep.time >= currentDate.valueOf() && currentStep.time < finalDate.valueOf()) {\n            monthWidth += currentStep.width.px;\n            if (currentStep.offset.px < monthOffset) {\n              monthOffset = currentStep.offset.px;\n            }\n          }\n        }\n        let label = '';\n        let choosenFormatName;\n        for (let formatName of formatNames) {\n          if (this.root.state.options.calendar.month.maxWidths[formatName] + 2 <= monthWidth) {\n            label = this.root.state.options.calendar.month.format[formatName](currentDate.locale(localeName));\n            choosenFormatName = formatName;\n          }\n        }\n        let textWidth = 0;\n        if (typeof this.root.state.options.calendar.month.widths[monthIndex] !== 'undefined') {\n          textWidth = this.root.state.options.calendar.month.widths[monthIndex][choosenFormatName];\n        }\n        let x = monthOffset;\n        months.push({\n          index: monthIndex,\n          key: monthIndex + 'm',\n          x,\n          y: 0,\n          width: monthWidth,\n          textWidth,\n          choosenFormatName,\n          height: this.root.state.options.calendar.month.height,\n          label,\n        });\n        currentDate = currentDate.add(1, 'month').startOf('month');\n        if (currentDate.valueOf() > this.root.state.options.times.lastTime) {\n          currentDate = dayjs(this.root.state.options.times.lastTime);\n        }\n      }\n      return months.map((item) => ({\n        key: item.key,\n        children: [item],\n      }));\n    },\n\n    /**\n     * Sum all calendar rows height and return result\n     *\n     * @returns {int}\n     */\n    calculateCalendarDimensions({ hours, days, months }) {\n      let height = 0;\n      if (this.root.state.options.calendar.hour.display && hours.length > 0) {\n        height += this.root.state.options.calendar.hour.height;\n      }\n      if (this.root.state.options.calendar.day.display && days.length > 0) {\n        height += this.root.state.options.calendar.day.height;\n      }\n      if (this.root.state.options.calendar.month.display && months.length > 0) {\n        height += this.root.state.options.calendar.month.height;\n      }\n      this.root.state.options.calendar.height = height;\n    },\n  },\n\n  computed: {\n    dates() {\n      const hours = this.generateHours();\n      const days = this.generateDays();\n      const months = this.generateMonths();\n      const allDates = { hours, days, months };\n      this.calculateCalendarDimensions(allDates);\n      return allDates;\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Calendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Calendar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Calendar.vue?vue&type=template&id=6abc9ca1&\"\nimport script from \"./Calendar.vue?vue&type=script&lang=js&\"\nexport * from \"./Calendar.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"svg\",\n    {\n      staticClass: \"gantt-elastic__chart-dependency-lines-container\",\n      style: { ..._vm.root.style[\"chart-dependency-lines-container\"] },\n      attrs: { x: \"0\", y: \"0\", width: \"100%\", height: \"100%\" },\n    },\n    _vm._l(_vm.dependencyTasks, function (task) {\n      return _c(\n        \"g\",\n        { key: task.id, attrs: { task: task } },\n        _vm._l(task.dependencyLines, function (dependencyLine) {\n          return _c(\"path\", {\n            key: dependencyLine.id,\n            staticClass: \"gantt-elastic__chart-dependency-lines-path\",\n            style: {\n              ..._vm.root.style[\"chart-dependency-lines-path\"],\n              ...task.style[\"chart-dependency-lines-path\"],\n              ...task.style[\n                \"chart-dependency-lines-path-\" + dependencyLine.task_id\n              ],\n            },\n            attrs: { task: task, d: dependencyLine.points },\n          })\n        }),\n        0\n      )\n    }),\n    0\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview DependencyLines component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <svg\n    x=\"0\"\n    y=\"0\"\n    width=\"100%\"\n    height=\"100%\"\n    class=\"gantt-elastic__chart-dependency-lines-container\"\n    :style=\"{ ...root.style['chart-dependency-lines-container'] }\"\n  >\n    <g v-for=\"task in dependencyTasks\" :key=\"task.id\" :task=\"task\">\n      <path\n        class=\"gantt-elastic__chart-dependency-lines-path\"\n        :style=\"{\n          ...root.style['chart-dependency-lines-path'],\n          ...task.style['chart-dependency-lines-path'],\n          ...task.style['chart-dependency-lines-path-' + dependencyLine.task_id],\n        }\"\n        v-for=\"dependencyLine in task.dependencyLines\"\n        :key=\"dependencyLine.id\"\n        :task=\"task\"\n        :d=\"dependencyLine.points\"\n      ></path>\n    </g>\n  </svg>\n</template>\n\n<script>\nexport default {\n  name: 'DependencyLines',\n  inject: ['root'],\n  props: ['tasks'],\n  data() {\n    return {};\n  },\n  methods: {\n    /**\n     * Get path points\n     *\n     * @param {any} fromTaskId\n     * @param {any} toTaskId\n     * @returns {string}\n     */\n    getPoints(fromTaskId, toTaskId) {\n      const fromTask = this.root.getTask(fromTaskId);\n      const toTask = this.root.getTask(toTaskId);\n      if (\n        fromTask === null ||\n        toTask === null ||\n        !this.root.isTaskVisible(toTask) ||\n        !this.root.isTaskVisible(fromTask)\n      ) {\n        return null;\n      }\n      const startX = fromTask.x + fromTask.width;\n      const startY = fromTask.y + fromTask.height / 2;\n      const stopX = toTask.x;\n      const stopY = toTask.y + toTask.height / 2;\n      const distanceX = stopX - startX;\n      let distanceY;\n      let yMultiplier = 1;\n      if (stopY >= startY) {\n        distanceY = stopY - startY;\n      } else {\n        distanceY = startY - stopY;\n        yMultiplier = -1;\n      }\n      const offset = 10;\n      const roundness = 4;\n      const isBefore = distanceX <= offset + roundness;\n      let points = `M ${startX} ${startY}\n          L ${startX + offset},${startY} `;\n      if (isBefore) {\n        points += `Q ${startX + offset + roundness},${startY} ${startX + offset + roundness},${\n          startY + roundness * yMultiplier\n        }\n            L ${startX + offset + roundness},${startY + (distanceY * yMultiplier) / 2 - roundness * yMultiplier}\n            Q ${startX + offset + roundness},${startY + (distanceY * yMultiplier) / 2} ${startX + offset},${\n          startY + (distanceY * yMultiplier) / 2\n        }\n            L ${startX - offset + distanceX},${startY + (distanceY * yMultiplier) / 2}\n            Q ${startX - offset + distanceX - roundness},${startY + (distanceY * yMultiplier) / 2} ${\n          startX - offset + distanceX - roundness\n        },${startY + (distanceY * yMultiplier) / 2 + roundness * yMultiplier}\n            L ${startX - offset + distanceX - roundness},${stopY - roundness * yMultiplier}\n            Q ${startX - offset + distanceX - roundness},${stopY} ${startX - offset + distanceX},${stopY}\n            L ${stopX},${stopY}`;\n      } else {\n        points += `L ${startX + distanceX / 2 - roundness},${startY}\n            Q ${startX + distanceX / 2},${startY} ${startX + distanceX / 2},${startY + roundness * yMultiplier}\n            L ${startX + distanceX / 2},${stopY - roundness * yMultiplier}\n            Q ${startX + distanceX / 2},${stopY} ${startX + distanceX / 2 + roundness},${stopY}\n            L ${stopX},${stopY}`;\n      }\n      return points;\n    },\n  },\n  computed: {\n    /**\n     * Get tasks which are dependent on other tasks\n     *\n     * @returns {array}\n     */\n    dependencyTasks() {\n      return this.tasks\n        .filter((task) => typeof task.dependentOn !== 'undefined')\n        .map((task) => {\n          task.dependencyLines = task.dependentOn.map((id) => {\n            return { points: this.getPoints(id, task.id), task_id: id };\n          });\n          return task;\n        })\n        .filter((task) => task.dependencyLines.points !== null);\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DependencyLines.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DependencyLines.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./DependencyLines.vue?vue&type=template&id=2e0284d0&\"\nimport script from \"./DependencyLines.vue?vue&type=script&lang=js&\"\nexport * from \"./DependencyLines.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"g\",\n    {\n      staticClass:\n        \"gantt-elastic__chart-row-bar-wrapper gantt-elastic__chart-row-task-wrapper\",\n      style: {\n        ..._vm.root.style[\"chart-row-bar-wrapper\"],\n        ..._vm.root.style[\"chart-row-task-wrapper\"],\n        ..._vm.task.style[\"chart-row-bar-wrapper\"],\n      },\n    },\n    [\n      _vm.displayExpander\n        ? _c(\n            \"foreignObject\",\n            {\n              staticClass:\n                \"gantt-elastic__chart-expander gantt-elastic__chart-expander--task\",\n              style: {\n                ..._vm.root.style[\"chart-expander\"],\n                ..._vm.root.style[\"chart-expander--task\"],\n                ..._vm.task.style[\"chart-expander\"],\n              },\n              attrs: {\n                x:\n                  _vm.task.x -\n                  _vm.root.state.options.chart.expander.offset -\n                  _vm.root.state.options.chart.expander.size,\n                y:\n                  _vm.task.y +\n                  (_vm.root.state.options.row.height -\n                    _vm.root.state.options.chart.expander.size) /\n                    2,\n                width: _vm.root.state.options.chart.expander.size,\n                height: _vm.root.state.options.chart.expander.size,\n              },\n            },\n            [\n              _c(\"expander\", {\n                attrs: {\n                  tasks: [_vm.task],\n                  options: _vm.root.state.options.chart.expander,\n                  type: \"chart\",\n                },\n              }),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.root.state.options.chart.text.display\n        ? _c(\"chart-text\", { attrs: { task: _vm.task } })\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"svg\",\n        {\n          staticClass:\n            \"gantt-elastic__chart-row-bar gantt-elastic__chart-row-task\",\n          style: {\n            ..._vm.root.style[\"chart-row-bar\"],\n            ..._vm.root.style[\"chart-row-task\"],\n            ..._vm.task.style[\"chart-row-bar\"],\n          },\n          attrs: {\n            x: _vm.task.x,\n            y: _vm.task.y,\n            width: _vm.task.width,\n            height: _vm.task.height,\n            viewBox: `0 0 ${_vm.task.width} ${_vm.task.height}`,\n            xmlns: \"http://www.w3.org/2000/svg\",\n          },\n          on: {\n            click: function ($event) {\n              return _vm.emitEvent(\"click\", $event)\n            },\n            dblclick: function ($event) {\n              return _vm.emitEvent(\"dblclick\", $event)\n            },\n            mouseenter: function ($event) {\n              return _vm.emitEvent(\"mouseenter\", $event)\n            },\n            mouseleave: function ($event) {\n              return _vm.emitEvent(\"mouseleave\", $event)\n            },\n            mouseover: function ($event) {\n              return _vm.emitEvent(\"mouseover\", $event)\n            },\n            mouseout: function ($event) {\n              return _vm.emitEvent(\"mouseout\", $event)\n            },\n            mousemove: function ($event) {\n              return _vm.emitEvent(\"mousemove\", $event)\n            },\n            mousedown: function ($event) {\n              return _vm.emitEvent(\"mousedown\", $event)\n            },\n            mouseup: function ($event) {\n              return _vm.emitEvent(\"mouseup\", $event)\n            },\n            mousewheel: function ($event) {\n              return _vm.emitEvent(\"mousewheel\", $event)\n            },\n            touchstart: function ($event) {\n              return _vm.emitEvent(\"touchstart\", $event)\n            },\n            touchmove: function ($event) {\n              return _vm.emitEvent(\"touchmove\", $event)\n            },\n            touchend: function ($event) {\n              return _vm.emitEvent(\"touchend\", $event)\n            },\n          },\n        },\n        [\n          _c(\"defs\", [\n            _c(\"clipPath\", { attrs: { id: _vm.clipPathId } }, [\n              _c(\"polygon\", { attrs: { points: _vm.getPoints } }),\n            ]),\n          ]),\n          _vm._v(\" \"),\n          _c(\"polygon\", {\n            staticClass:\n              \"gantt-elastic__chart-row-bar-polygon gantt-elastic__chart-row-task-polygon\",\n            style: {\n              ..._vm.root.style[\"chart-row-bar-polygon\"],\n              ..._vm.root.style[\"chart-row-task-polygon\"],\n              ..._vm.task.style[\"base\"],\n              ..._vm.task.style[\"chart-row-bar-polygon\"],\n            },\n            attrs: { points: _vm.getPoints, \"data-taskid\": _vm.task.id },\n          }),\n          _vm._v(\" \"),\n          _c(\"circle\", {\n            staticClass: \"gantt-elastic__chart-row-bar-circle\",\n            style: _vm.getCircleStyle,\n            attrs: {\n              cx: _vm.getStartCircle.x,\n              cy: _vm.getStartCircle.y,\n              r: _vm.circle.r,\n            },\n            on: {\n              mousedown: function ($event) {\n                $event.stopPropagation()\n                return _vm.onMouseDown($event, _vm.getStartCircle)\n              },\n            },\n          }),\n          _vm._v(\" \"),\n          _c(\"circle\", {\n            staticClass: \"gantt-elastic__chart-row-bar-circle\",\n            style: _vm.getCircleStyle,\n            attrs: {\n              cx: _vm.getEndCircle.x,\n              cy: _vm.getEndCircle.y,\n              r: _vm.circle.r,\n            },\n            on: {\n              mousedown: function ($event) {\n                $event.stopPropagation()\n                return _vm.onMouseDown($event, _vm.getEndCircle)\n              },\n            },\n          }),\n          _vm._v(\" \"),\n          _c(\"progress-bar\", {\n            attrs: {\n              task: _vm.task,\n              \"clip-path\": \"url(#\" + _vm.clipPathId + \")\",\n            },\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"svg\",\n    {\n      staticClass: \"gantt-elastic__chart-row-text-wrapper\",\n      style: { ..._vm.root.style[\"chart-row-text-wrapper\"] },\n      attrs: {\n        x:\n          _vm.task.x +\n          _vm.task.width +\n          _vm.root.state.options.chart.text.offset,\n        y: _vm.task.y - _vm.root.state.options.chart.grid.horizontal.gap,\n        width: _vm.getWidth,\n        height: _vm.getHeight,\n      },\n    },\n    [\n      _c(\n        \"foreignObject\",\n        { attrs: { x: \"0\", y: \"0\", width: \"100%\", height: _vm.getHeight } },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"gantt-elastic__chart-row-text\",\n              style: { ..._vm.root.style[\"chart-row-text\"] },\n              attrs: { xmlns: \"http://www.w3.org/1999/xhtml\" },\n            },\n            [\n              !_vm.html\n                ? _c(\n                    \"div\",\n                    {\n                      staticClass:\n                        \"gantt-elastic__chart-row-text-content gantt-elastic__chart-row-text-content--text\",\n                      style: {\n                        ..._vm.root.style[\"chart-row-text-content\"],\n                        ..._vm.root.style[\"chart-row-text-content--text\"],\n                        ..._vm.contentStyle,\n                      },\n                    },\n                    [_c(\"div\", [_vm._v(_vm._s(_vm.task.label))])]\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.html\n                ? _c(\"div\", {\n                    staticClass:\n                      \"gantt-elastic__chart-row-text-content gantt-elastic__chart-row-text-content--html\",\n                    style: {\n                      ..._vm.root.style[\"chart-row-text-content\"],\n                      ..._vm.root.style[\"chart-row-text-content--html\"],\n                      ..._vm.contentStyle,\n                    },\n                    domProps: { innerHTML: _vm._s(_vm.task.label) },\n                  })\n                : _vm._e(),\n            ]\n          ),\n        ]\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview Text component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <svg\n    class=\"gantt-elastic__chart-row-text-wrapper\"\n    :style=\"{ ...root.style['chart-row-text-wrapper'] }\"\n    :x=\"task.x + task.width + root.state.options.chart.text.offset\"\n    :y=\"task.y - root.state.options.chart.grid.horizontal.gap\"\n    :width=\"getWidth\"\n    :height=\"getHeight\"\n  >\n    <foreignObject x=\"0\" y=\"0\" width=\"100%\" :height=\"getHeight\">\n      <div\n        xmlns=\"http://www.w3.org/1999/xhtml\"\n        class=\"gantt-elastic__chart-row-text\"\n        :style=\"{ ...root.style['chart-row-text'] }\"\n      >\n        <div\n          class=\"gantt-elastic__chart-row-text-content gantt-elastic__chart-row-text-content--text\"\n          :style=\"{\n            ...root.style['chart-row-text-content'],\n            ...root.style['chart-row-text-content--text'],\n            ...contentStyle,\n          }\"\n          v-if=\"!html\"\n        >\n          <div>{{ task.label }}</div>\n        </div>\n        <div\n          class=\"gantt-elastic__chart-row-text-content gantt-elastic__chart-row-text-content--html\"\n          :style=\"{\n            ...root.style['chart-row-text-content'],\n            ...root.style['chart-row-text-content--html'],\n            ...contentStyle,\n          }\"\n          v-if=\"html\"\n          v-html=\"task.label\"\n        ></div>\n      </div>\n    </foreignObject>\n  </svg>\n</template>\n\n<script>\nexport default {\n  name: 'ChartText',\n  inject: ['root'],\n  props: ['task'],\n  data() {\n    return {};\n  },\n  computed: {\n    /**\n     * Get width\n     *\n     * @returns {number}\n     */\n    getWidth() {\n      const textStyle = this.root.style['chart-row-text'];\n      // eslint-disable-next-line vue/no-side-effects-in-computed-properties\n      this.root.state.ctx.font = `${textStyle['font-weight']} ${textStyle['font-size']} ${textStyle['font-family']}`;\n      const textWidth = this.root.state.ctx.measureText(this.task.label).width;\n      return textWidth + this.root.state.options.chart.text.xPadding * 2;\n    },\n\n    /**\n     * Get height\n     *\n     * @returns {number}\n     */\n    getHeight() {\n      return this.task.height + this.root.state.options.chart.grid.horizontal.gap * 2;\n    },\n\n    /**\n     * Get content style\n     *\n     * @returns {object}\n     */\n    contentStyle() {\n      return { height: '100%', 'line-height': this.getHeight + 'px' };\n    },\n\n    /**\n     * Should we render text as html?\n     *\n     * @returns {boolean}\n     */\n    html() {\n      const cols = this.root.state.options.taskList.columns;\n      for (let i = 0, len = cols.length; i < len; i++) {\n        const col = cols[i];\n        if (col.value === 'label' && typeof col.html !== 'undefined' && col.html) {\n          return true;\n        }\n      }\n      return false;\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.gantt-elastic__chart-row-text {\n  pointer-events: none;\n}\n</style>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Text.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Text.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Text.vue?vue&type=template&id=26409f00&scoped=true&\"\nimport script from \"./Text.vue?vue&type=script&lang=js&\"\nexport * from \"./Text.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Text.vue?vue&type=style&index=0&id=26409f00&prod&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26409f00\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"g\",\n    {\n      staticClass: \"gantt-elastic__chart-row-progress-bar-wrapper\",\n      style: {\n        ..._vm.root.style[\"chart-row-progress-bar-wrapper\"],\n        ..._vm.task.style[\"chart-row-progress-bar-wrapper\"],\n      },\n    },\n    [\n      _c(\"defs\", [\n        _c(\n          \"pattern\",\n          {\n            attrs: {\n              id: \"diagonalHatch\",\n              width: _vm.root.state.options.chart.progress.width,\n              height: _vm.root.state.options.chart.progress.width,\n              patternTransform: \"rotate(45 0 0)\",\n              patternUnits: \"userSpaceOnUse\",\n            },\n          },\n          [\n            _c(\"line\", {\n              staticClass: \"chart-row-progress-bar-line\",\n              style: {\n                ..._vm.root.style[\"chart-row-progress-bar-line\"],\n                ..._vm.task.style[\"chart-row-progress-bar-line\"],\n              },\n              attrs: {\n                x1: \"0\",\n                y1: \"0\",\n                x2: \"0\",\n                y2: _vm.root.state.options.chart.progress.width,\n              },\n            }),\n          ]\n        ),\n      ]),\n      _vm._v(\" \"),\n      _vm.root.state.options.chart.progress.bar\n        ? _c(\"rect\", {\n            staticClass: \"gantt-elastic__chart-row-progress-bar-solid\",\n            style: {\n              ..._vm.root.style[\"chart-row-progress-bar-solid\"],\n              ..._vm.task.style[\"chart-row-progress-bar-solid\"],\n            },\n            attrs: { x: \"0\", y: \"0\", width: _vm.getProgressWidth },\n          })\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.root.state.options.chart.progress.pattern\n        ? _c(\"g\", [\n            _c(\"rect\", {\n              staticClass: \"gantt-elastic__chart-row-progress-bar-pattern\",\n              style: {\n                ..._vm.root.style[\"chart-row-progress-bar-pattern\"],\n                ..._vm.task.style[\"chart-row-progress-bar-pattern\"],\n              },\n              attrs: {\n                x: _vm.getProgressWidth,\n                y: \"0\",\n                width: 100 - _vm.task.progress + \"%\",\n                height: \"100%\",\n              },\n            }),\n            _vm._v(\" \"),\n            _c(\"path\", {\n              staticClass: \"gantt-elastic__chart-row-progress-bar-outline\",\n              style: {\n                ..._vm.root.style[\"chart-row-progress-bar-outline\"],\n                ..._vm.task.style[\"base\"],\n                ..._vm.task.style[\"chart-row-progress-bar-outline\"],\n                \"--stroke-width\": _vm.getOutlineStyle[\"stroke-width\"],\n              },\n              attrs: { d: _vm.getLinePoints },\n              on: {\n                mousedown: function ($event) {\n                  $event.stopPropagation()\n                  return _vm.resizerMouseDown.apply(null, arguments)\n                },\n                mouseup: function ($event) {\n                  $event.stopPropagation()\n                  return _vm.resizerMouseup.apply(null, arguments)\n                },\n              },\n            }),\n          ])\n        : _vm._e(),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview ProgressBar component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <g\n    class=\"gantt-elastic__chart-row-progress-bar-wrapper\"\n    :style=\"{\n      ...root.style['chart-row-progress-bar-wrapper'],\n      ...task.style['chart-row-progress-bar-wrapper'],\n    }\"\n  >\n    <defs>\n      <pattern\n        id=\"diagonalHatch\"\n        :width=\"root.state.options.chart.progress.width\"\n        :height=\"root.state.options.chart.progress.width\"\n        patternTransform=\"rotate(45 0 0)\"\n        patternUnits=\"userSpaceOnUse\"\n      >\n        <line\n          class=\"chart-row-progress-bar-line\"\n          :style=\"{\n            ...root.style['chart-row-progress-bar-line'],\n            ...task.style['chart-row-progress-bar-line'],\n          }\"\n          x1=\"0\"\n          y1=\"0\"\n          x2=\"0\"\n          :y2=\"root.state.options.chart.progress.width\"\n        />\n      </pattern>\n    </defs>\n    <rect\n      v-if=\"root.state.options.chart.progress.bar\"\n      class=\"gantt-elastic__chart-row-progress-bar-solid\"\n      :style=\"{\n        ...root.style['chart-row-progress-bar-solid'],\n        ...task.style['chart-row-progress-bar-solid'],\n      }\"\n      x=\"0\"\n      y=\"0\"\n      :width=\"getProgressWidth\"\n    ></rect>\n    <g v-if=\"root.state.options.chart.progress.pattern\">\n      <rect\n        class=\"gantt-elastic__chart-row-progress-bar-pattern\"\n        :style=\"{\n          ...root.style['chart-row-progress-bar-pattern'],\n          ...task.style['chart-row-progress-bar-pattern'],\n        }\"\n        :x=\"getProgressWidth\"\n        y=\"0\"\n        :width=\"100 - task.progress + '%'\"\n        height=\"100%\"\n      ></rect>\n      <path\n        class=\"gantt-elastic__chart-row-progress-bar-outline\"\n        :style=\"{\n          ...root.style['chart-row-progress-bar-outline'],\n          ...task.style['base'],\n          ...task.style['chart-row-progress-bar-outline'],\n          '--stroke-width': getOutlineStyle['stroke-width'],\n        }\"\n        :d=\"getLinePoints\"\n        @mousedown.stop=\"resizerMouseDown\"\n        @mouseup.stop=\"resizerMouseup\"\n      ></path>\n    </g>\n  </g>\n</template>\n\n<script>\nexport default {\n  name: 'ProgressBar',\n  inject: ['root'],\n  props: ['task'],\n  data() {\n    return {\n      resize: {\n        moving: false,\n        moveEvent: {\n          offsetX: 0,\n          clientX: 0,\n        },\n      },\n    };\n  },\n\n  computed: {\n    /**\n     * Get progress width\n     *\n     * @returns {string}\n     */\n    getProgressWidth() {\n      return this.task.progress + '%';\n    },\n\n    /**\n     * Get line points\n     *\n     * @returns {string}\n     */\n    getLinePoints() {\n      const start = (this.task.width / 100) * this.task.progress;\n      return `M ${start} 0 L ${start} ${this.task.height}`;\n    },\n\n    /**\n     * Get solid style\n     *\n     * @returns {object}\n     */\n    getSolidStyle() {\n      return Object.assign({}, this.root.state.options.chart.progress.styles.bar.solid, this.task.progressBarStyle.bar);\n    },\n\n    /**\n     * Get line style\n     *\n     * @returns {object}\n     */\n    getLineStyle() {\n      return Object.assign(\n        {},\n        {\n          stroke: this.root.state.options.row.styles.bar.stroke + 'a0',\n          'stroke-width': this.root.state.options.row.styles.bar['stroke-width'] / 2,\n        },\n        this.task.style\n      );\n    },\n\n    getOutlineStyle() {\n      return Object.assign(\n        {},\n        this.root.style['chart-row-progress-bar-outline'],\n        this.task.style['base'],\n        this.task.style['chart-row-progress-bar-outline']\n      );\n    },\n  },\n\n  watch: {\n    'resize.moving': {\n      handler(n) {\n        // 修改鼠标手势\n        document.body.style.cursor = n ? 'w-resize' : 'auto';\n      },\n    },\n  },\n\n  created() {\n    document.addEventListener('mousemove', this.resizerMouseMove.bind(this));\n    document.addEventListener('mouseup', this.resizerMouseup.bind(this));\n  },\n\n  beforeDestroy() {\n    document.removeEventListener('mouseover', this.resizerMouseMove);\n    document.removeEventListener('mouseup', this.resizerMouseup);\n  },\n\n  methods: {\n    resizerMouseDown(event) {\n      if (!this.resize.moving) {\n        this.resize.moving = true;\n        this.resize.x = event.clientX;\n      }\n    },\n\n    resizerMouseup(event) {\n      this.resize.moving = false;\n      const { moveEvent } = this.resize;\n      moveEvent.offsetX = 0;\n      moveEvent.clientX = 0;\n    },\n\n    resizerMouseMove(event) {\n      if (this.resize.moving) {\n        const { moveEvent } = this.resize;\n        if (!moveEvent.offsetX) {\n          moveEvent.offsetX = event.offsetX;\n          moveEvent.clientX = event.clientX;\n        }\n        const offsetX = moveEvent.offsetX + (event.clientX - (moveEvent.clientX || event.clientX));\n        const width = offsetX - this.task.x;\n\n        let progress = (width / this.task.width) * 100;\n        if (progress === 100) return;\n        if (progress === 0) return;\n        progress = progress >= 100 ? 100 : progress <= 0 ? 0 : progress;\n        progress = progress.toFixed(0);\n        this.root.updateTask(this.task.id, { progress });\n      }\n    },\n  },\n};\n</script>\n\n<style>\n.gantt-elastic__chart-row-progress-bar-outline:hover {\n  cursor: w-resize;\n}\n\n.gantt-elastic__chart-row-bar:hover .gantt-elastic__chart-row-progress-bar-outline {\n  stroke-width: calc(var(--stroke-width) * 2) !important;\n}\n</style>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ProgressBar.vue?vue&type=template&id=91139344&\"\nimport script from \"./ProgressBar.vue?vue&type=script&lang=js&\"\nexport * from \"./ProgressBar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ProgressBar.vue?vue&type=style&index=0&id=91139344&prod&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "/**\n * @fileoverview Task mixin\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n\nexport default {\n  data() {\n    return {\n      circleOffset: 5,\n\n      circle: {\n        show: false,\n        r: 6,\n        offset: 7,\n      },\n\n      connectLine: {\n        moving: false,\n        x1: 0,\n        y1: 0,\n        x2: 0,\n        y2: 0,\n      },\n    };\n  },\n  computed: {\n    /**\n     * Get view box\n     *\n     * @returns {string}\n     */\n    getViewBox() {\n      const task = this.task;\n      return `0 0 ${task.width} ${task.height}`;\n    },\n\n    /**\n     * Get group transform\n     *\n     * @returns {string}\n     */\n    getGroupTransform() {\n      return `translate(${this.task.x} ${this.task.y})`;\n    },\n\n    /**\n     * Should we display expander?\n     *\n     * @returns {boolean}\n     */\n    displayExpander() {\n      const expander = this.root.state.options.chart.expander;\n      return expander.display || (expander.displayIfTaskListHidden && !this.root.state.options.taskList.display);\n    },\n\n    /**\n     * get start circle position\n     *\n     * @returns {object}\n     */\n    getStartCircle() {\n      const { task, circle } = this;\n      return { x: 0 - circle.offset, y: task.height / 2 };\n    },\n\n    /**\n     * get start circle position\n     *\n     * @returns {object}\n     */\n    getEndCircle() {\n      const { task, circle } = this;\n      return { x: task.width + circle.offset, y: task.height / 2 };\n    },\n\n    /**\n     * get circle show\n     * @returns {boolean}\n     */\n    getCircleShow() {\n      return this.circle.show || this.connectLine.moving;\n    },\n\n    /**\n     * get circle style\n     * @returns {object}\n     */\n    getCircleStyle() {\n      return {\n        stroke: 'rgba(0, 119, 192, 1)',\n        fill: 'rgba(0, 119, 192, 1)',\n        opacity: this.getCircleShow ? '1' : '0',\n      };\n    },\n  },\n\n  mounted() {\n    document.addEventListener('mousemove', this.onMouseMove.bind(this));\n    document.addEventListener('mouseup', this.onMouseup.bind(this));\n  },\n\n  beforeDestroy() {\n    document.removeEventListener('mouseup', this.onMouseup);\n    document.removeEventListener('mousemove', this.onMouseMove);\n  },\n\n  methods: {\n    /**\n     * Emit event\n     *\n     * @param {string} eventName\n     * @param {Event} event\n     */\n    emitEvent(eventName, event) {\n      if (!this.root.state.options.scroll.scrolling) {\n        const eventObj = {\n          event,\n          data: this.task,\n        };\n\n        const eventEmitFun = () => {\n          this.root.$emit(`chart-task-${eventName}`, eventObj);\n          this.root.$emit(`chart-task-${this.task.type}-${eventName}`, eventObj);\n        };\n\n        switch (eventName) {\n          // 消除单机事件和双击事件同时使用带来的副作用\n          case 'click':\n          case 'dblclick':\n            clearTimeout(this._eventTriggerTime);\n            this._eventTriggerTime = setTimeout(() => {\n              eventEmitFun();\n            }, 300);\n\n            if (eventName === 'dblclick') {\n              clearTimeout(this._eventTriggerTime);\n              eventEmitFun();\n            }\n            break;\n\n          case 'mouseenter':\n            this.setCircleShow();\n            eventEmitFun();\n            break;\n\n          case 'mouseleave':\n            this.setCircleHidden();\n            eventEmitFun();\n            break;\n\n          default:\n            eventEmitFun();\n            break;\n        }\n      }\n    },\n\n    // 显示连接线锚点\n    setCircleShow() {\n      this.circle.show = true;\n    },\n\n    // 隐藏连接线锚点\n    setCircleHidden() {\n      this.circle.show = false;\n    },\n\n    // 鼠标按下事件\n    onMouseDown(event, params) {\n      if (!this.connectLine.moving) {\n        this.circle.show = true;\n        const task = this.task;\n        const { setConnectLine } = this.root.state.connectLine;\n        this.circle.show = true;\n        this.connectLine.moving = true;\n        this.connectLine.x1 = task.x + params.x;\n        this.connectLine.y1 = task.y + params.y;\n        setConnectLine({ startId: task.id });\n      }\n    },\n\n    // 鼠标松开事件\n    onMouseup(event) {\n      if (this.connectLine.moving) {\n        const endId = this.getTaskIdByNode(event.target);\n        const { getConnectingLine, setConnectLine, delConnectLine } = this.root.state.connectLine;\n        setConnectLine({ endId: endId });\n        this.root.updateTask(endId, { dependentOn: [getConnectingLine().startId] });\n        delConnectLine();\n        this.connectLine.moving = false;\n      }\n    },\n\n    // 鼠标移动事件\n    onMouseMove(event) {\n      if (this.connectLine.moving) {\n        const { setConnectLine } = this.root.state.connectLine;\n        const offset = event.offsetX > this.connectLine.x1 ? -2 : 2;\n        this.connectLine.x2 = event.offsetX + offset;\n        this.connectLine.y2 = event.offsetY + offset;\n        setConnectLine(this.connectLine);\n      }\n    },\n\n    // 递归 获取dom节点上的 taskID\n    getTaskIdByNode(node) {\n      const pNode = node.parentNode;\n      if (pNode && pNode.dataset) {\n        const { taskid } = pNode.dataset;\n        if (taskid) {\n          return taskid;\n        } else {\n          return this.getTaskIdByNode(pNode);\n        }\n      }\n    },\n  },\n};\n", "<!--\n/**\n * @fileoverview Task component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <g\n    class=\"gantt-elastic__chart-row-bar-wrapper gantt-elastic__chart-row-task-wrapper\"\n    :style=\"{\n      ...root.style['chart-row-bar-wrapper'],\n      ...root.style['chart-row-task-wrapper'],\n      ...task.style['chart-row-bar-wrapper'],\n    }\"\n  >\n    <foreignObject\n      class=\"gantt-elastic__chart-expander gantt-elastic__chart-expander--task\"\n      :style=\"{\n        ...root.style['chart-expander'],\n        ...root.style['chart-expander--task'],\n        ...task.style['chart-expander'],\n      }\"\n      :x=\"task.x - root.state.options.chart.expander.offset - root.state.options.chart.expander.size\"\n      :y=\"task.y + (root.state.options.row.height - root.state.options.chart.expander.size) / 2\"\n      :width=\"root.state.options.chart.expander.size\"\n      :height=\"root.state.options.chart.expander.size\"\n      v-if=\"displayExpander\"\n    >\n      <expander :tasks=\"[task]\" :options=\"root.state.options.chart.expander\" type=\"chart\"></expander>\n    </foreignObject>\n\n    <chart-text :task=\"task\" v-if=\"root.state.options.chart.text.display\"></chart-text>\n\n    <svg\n      class=\"gantt-elastic__chart-row-bar gantt-elastic__chart-row-task\"\n      :style=\"{\n        ...root.style['chart-row-bar'],\n        ...root.style['chart-row-task'],\n        ...task.style['chart-row-bar'],\n      }\"\n      :x=\"task.x\"\n      :y=\"task.y\"\n      :width=\"task.width\"\n      :height=\"task.height\"\n      :viewBox=\"`0 0 ${task.width} ${task.height}`\"\n      @click=\"emitEvent('click', $event)\"\n      @dblclick=\"emitEvent('dblclick', $event)\"\n      @mouseenter=\"emitEvent('mouseenter', $event)\"\n      @mouseleave=\"emitEvent('mouseleave', $event)\"\n      @mouseover=\"emitEvent('mouseover', $event)\"\n      @mouseout=\"emitEvent('mouseout', $event)\"\n      @mousemove=\"emitEvent('mousemove', $event)\"\n      @mousedown=\"emitEvent('mousedown', $event)\"\n      @mouseup=\"emitEvent('mouseup', $event)\"\n      @mousewheel=\"emitEvent('mousewheel', $event)\"\n      @touchstart=\"emitEvent('touchstart', $event)\"\n      @touchmove=\"emitEvent('touchmove', $event)\"\n      @touchend=\"emitEvent('touchend', $event)\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <defs>\n        <clipPath :id=\"clipPathId\">\n          <polygon :points=\"getPoints\"></polygon>\n        </clipPath>\n      </defs>\n      <polygon\n        class=\"gantt-elastic__chart-row-bar-polygon gantt-elastic__chart-row-task-polygon\"\n        :style=\"{\n          ...root.style['chart-row-bar-polygon'],\n          ...root.style['chart-row-task-polygon'],\n          ...task.style['base'],\n          ...task.style['chart-row-bar-polygon'],\n        }\"\n        :points=\"getPoints\"\n        :data-taskid=\"task.id\"\n      ></polygon>\n\n      <circle\n        class=\"gantt-elastic__chart-row-bar-circle\"\n        :cx=\"getStartCircle.x\"\n        :cy=\"getStartCircle.y\"\n        :r=\"circle.r\"\n        :style=\"getCircleStyle\"\n        @mousedown.stop=\"onMouseDown($event, getStartCircle)\"\n      ></circle>\n\n      <circle\n        class=\"gantt-elastic__chart-row-bar-circle\"\n        :cx=\"getEndCircle.x\"\n        :cy=\"getEndCircle.y\"\n        :r=\"circle.r\"\n        :style=\"getCircleStyle\"\n        @mousedown.stop=\"onMouseDown($event, getEndCircle)\"\n      ></circle>\n\n      <progress-bar :task=\"task\" :clip-path=\"'url(#' + clipPathId + ')'\"></progress-bar>\n    </svg>\n  </g>\n</template>\n\n<script>\nimport ChartText from '../Text.vue';\nimport ProgressBar from '../ProgressBar.vue';\nimport Expander from '../../Expander.vue';\nimport taskMixin from './Task.mixin.js';\nexport default {\n  // eslint-disable-next-line vue/multi-word-component-names\n  name: 'Task',\n  components: {\n    ChartText,\n    ProgressBar,\n    Expander,\n  },\n  inject: ['root'],\n  props: ['task'],\n  mixins: [taskMixin],\n  data() {\n    return {};\n  },\n  computed: {\n    /**\n     * Get clip path id\n     *\n     * @returns {string}\n     */\n    clipPathId() {\n      return 'gantt-elastic__task-clip-path-' + this.task.id;\n    },\n\n    /**\n     * Get points\n     *\n     * @returns {string}\n     */\n    getPoints() {\n      const task = this.task;\n      return `0,0 ${task.width},0 ${task.width},${task.height} 0,${task.height}`;\n    },\n  },\n\n  methods: {},\n};\n</script>\n", "import mod from \"-!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Task.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Task.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Task.vue?vue&type=template&id=6190d00a&\"\nimport script from \"./Task.vue?vue&type=script&lang=js&\"\nexport * from \"./Task.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"g\",\n    {\n      staticClass:\n        \"gantt-elastic__chart-row-bar-wrapper gantt-elastic__chart-row-milestone-wrapper\",\n      style: {\n        ..._vm.root.style[\"chart-row-bar-wrapper\"],\n        ..._vm.root.style[\"chart-row-milestone-wrapper\"],\n        ..._vm.task.style[\"chart-row-bar-wrapper\"],\n      },\n    },\n    [\n      _vm.displayExpander\n        ? _c(\n            \"foreignObject\",\n            {\n              staticClass:\n                \"gantt-elastic__chart-expander gantt-elastic__chart-expander--milestone\",\n              style: {\n                ..._vm.root.style[\"chart-expander\"],\n                ..._vm.root.style[\"chart-expander--milestone\"],\n                ..._vm.task.style[\"chart-expander\"],\n              },\n              attrs: {\n                x:\n                  _vm.task.x -\n                  _vm.root.state.options.chart.expander.offset -\n                  _vm.root.state.options.chart.expander.size,\n                y:\n                  _vm.task.y +\n                  (_vm.root.state.options.row.height -\n                    _vm.root.state.options.chart.expander.size) /\n                    2,\n                width: _vm.root.state.options.chart.expander.size,\n                height: _vm.root.state.options.chart.expander.size,\n              },\n            },\n            [\n              _c(\"expander\", {\n                attrs: {\n                  tasks: [_vm.task],\n                  options: _vm.root.state.options.chart.expander,\n                  type: \"chart\",\n                },\n              }),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.root.state.options.chart.text.display\n        ? _c(\"chart-text\", { attrs: { task: _vm.task } })\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"svg\",\n        {\n          staticClass:\n            \"gantt-elastic__chart-row-bar gantt-elastic__chart-row-milestone\",\n          style: {\n            ..._vm.root.style[\"chart-row-bar\"],\n            ..._vm.root.style[\"chart-row-milestone\"],\n            ..._vm.task.style[\"chart-row-bar\"],\n          },\n          attrs: {\n            x: _vm.task.x,\n            y: _vm.task.y,\n            width: _vm.task.width,\n            height: _vm.task.height,\n            viewBox: `0 0 ${_vm.task.width} ${_vm.task.height}`,\n            xmlns: \"http://www.w3.org/2000/svg\",\n          },\n          on: {\n            click: function ($event) {\n              return _vm.emitEvent(\"click\", $event)\n            },\n            dblclick: function ($event) {\n              return _vm.emitEvent(\"dblclick\", $event)\n            },\n            mouseenter: function ($event) {\n              return _vm.emitEvent(\"mouseenter\", $event)\n            },\n            mouseleave: function ($event) {\n              return _vm.emitEvent(\"mouseleave\", $event)\n            },\n            mouseover: function ($event) {\n              return _vm.emitEvent(\"mouseover\", $event)\n            },\n            mouseout: function ($event) {\n              return _vm.emitEvent(\"mouseout\", $event)\n            },\n            mousemove: function ($event) {\n              return _vm.emitEvent(\"mousemove\", $event)\n            },\n            mousedown: function ($event) {\n              return _vm.emitEvent(\"mousedown\", $event)\n            },\n            mouseup: function ($event) {\n              return _vm.emitEvent(\"mouseup\", $event)\n            },\n            mousewheel: function ($event) {\n              return _vm.emitEvent(\"mousewheel\", $event)\n            },\n            touchstart: function ($event) {\n              return _vm.emitEvent(\"touchstart\", $event)\n            },\n            touchmove: function ($event) {\n              return _vm.emitEvent(\"touchmove\", $event)\n            },\n            touchend: function ($event) {\n              return _vm.emitEvent(\"touchend\", $event)\n            },\n          },\n        },\n        [\n          _c(\"defs\", [\n            _c(\"clipPath\", { attrs: { id: _vm.clipPathId } }, [\n              _c(\"polygon\", { attrs: { points: _vm.getPoints } }),\n            ]),\n          ]),\n          _vm._v(\" \"),\n          _c(\"polygon\", {\n            staticClass:\n              \"gantt-elastic__chart-row-bar-polygon gantt-elastic__chart-row-milestone-polygon\",\n            style: {\n              ..._vm.root.style[\"chart-row-bar-polygon\"],\n              ..._vm.root.style[\"chart-row-milestone-polygon\"],\n              ..._vm.task.style[\"base\"],\n              ..._vm.task.style[\"chart-row-bar-polygon\"],\n            },\n            attrs: { points: _vm.getPoints },\n          }),\n          _vm._v(\" \"),\n          _c(\"circle\", {\n            staticClass: \"gantt-elastic__chart-row-bar-circle\",\n            style: _vm.getCircleStyle,\n            attrs: {\n              cx: _vm.getStartCircle.x,\n              cy: _vm.getStartCircle.y,\n              r: _vm.circle.r,\n            },\n            on: {\n              mousedown: function ($event) {\n                $event.stopPropagation()\n                return _vm.onMouseDown($event, _vm.getStartCircle)\n              },\n            },\n          }),\n          _vm._v(\" \"),\n          _c(\"circle\", {\n            staticClass: \"gantt-elastic__chart-row-bar-circle\",\n            style: _vm.getCircleStyle,\n            attrs: {\n              cx: _vm.getEndCircle.x,\n              cy: _vm.getEndCircle.y,\n              r: _vm.circle.r,\n            },\n            on: {\n              mousedown: function ($event) {\n                $event.stopPropagation()\n                return _vm.onMouseDown($event, _vm.getEndCircle)\n              },\n            },\n          }),\n          _vm._v(\" \"),\n          _c(\"progress-bar\", {\n            attrs: {\n              task: _vm.task,\n              \"clip-path\": \"url(#\" + _vm.clipPathId + \")\",\n            },\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview Milestone component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <g\n    class=\"gantt-elastic__chart-row-bar-wrapper gantt-elastic__chart-row-milestone-wrapper\"\n    :style=\"{\n      ...root.style['chart-row-bar-wrapper'],\n      ...root.style['chart-row-milestone-wrapper'],\n      ...task.style['chart-row-bar-wrapper'],\n    }\"\n  >\n    <foreignObject\n      class=\"gantt-elastic__chart-expander gantt-elastic__chart-expander--milestone\"\n      :style=\"{\n        ...root.style['chart-expander'],\n        ...root.style['chart-expander--milestone'],\n        ...task.style['chart-expander'],\n      }\"\n      :x=\"task.x - root.state.options.chart.expander.offset - root.state.options.chart.expander.size\"\n      :y=\"task.y + (root.state.options.row.height - root.state.options.chart.expander.size) / 2\"\n      :width=\"root.state.options.chart.expander.size\"\n      :height=\"root.state.options.chart.expander.size\"\n      v-if=\"displayExpander\"\n    >\n      <expander :tasks=\"[task]\" :options=\"root.state.options.chart.expander\" type=\"chart\"></expander>\n    </foreignObject>\n\n    <chart-text :task=\"task\" v-if=\"root.state.options.chart.text.display\"></chart-text>\n\n    <svg\n      class=\"gantt-elastic__chart-row-bar gantt-elastic__chart-row-milestone\"\n      :style=\"{\n        ...root.style['chart-row-bar'],\n        ...root.style['chart-row-milestone'],\n        ...task.style['chart-row-bar'],\n      }\"\n      :x=\"task.x\"\n      :y=\"task.y\"\n      :width=\"task.width\"\n      :height=\"task.height\"\n      :viewBox=\"`0 0 ${task.width} ${task.height}`\"\n      @click=\"emitEvent('click', $event)\"\n      @dblclick=\"emitEvent('dblclick', $event)\"\n      @mouseenter=\"emitEvent('mouseenter', $event)\"\n      @mouseleave=\"emitEvent('mouseleave', $event)\"\n      @mouseover=\"emitEvent('mouseover', $event)\"\n      @mouseout=\"emitEvent('mouseout', $event)\"\n      @mousemove=\"emitEvent('mousemove', $event)\"\n      @mousedown=\"emitEvent('mousedown', $event)\"\n      @mouseup=\"emitEvent('mouseup', $event)\"\n      @mousewheel=\"emitEvent('mousewheel', $event)\"\n      @touchstart=\"emitEvent('touchstart', $event)\"\n      @touchmove=\"emitEvent('touchmove', $event)\"\n      @touchend=\"emitEvent('touchend', $event)\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <defs>\n        <clipPath :id=\"clipPathId\">\n          <polygon :points=\"getPoints\"></polygon>\n        </clipPath>\n      </defs>\n      <polygon\n        class=\"gantt-elastic__chart-row-bar-polygon gantt-elastic__chart-row-milestone-polygon\"\n        :style=\"{\n          ...root.style['chart-row-bar-polygon'],\n          ...root.style['chart-row-milestone-polygon'],\n          ...task.style['base'],\n          ...task.style['chart-row-bar-polygon'],\n        }\"\n        :points=\"getPoints\"\n      ></polygon>\n\n      <circle\n        class=\"gantt-elastic__chart-row-bar-circle\"\n        :cx=\"getStartCircle.x\"\n        :cy=\"getStartCircle.y\"\n        :r=\"circle.r\"\n        :style=\"getCircleStyle\"\n        @mousedown.stop=\"onMouseDown($event, getStartCircle)\"\n      ></circle>\n\n      <circle\n        class=\"gantt-elastic__chart-row-bar-circle\"\n        :cx=\"getEndCircle.x\"\n        :cy=\"getEndCircle.y\"\n        :r=\"circle.r\"\n        :style=\"getCircleStyle\"\n        @mousedown.stop=\"onMouseDown($event, getEndCircle)\"\n      ></circle>\n\n      <progress-bar :task=\"task\" :clip-path=\"'url(#' + clipPathId + ')'\"></progress-bar>\n    </svg>\n  </g>\n</template>\n\n<script>\nimport ChartText from '../Text.vue';\nimport ProgressBar from '../ProgressBar.vue';\nimport Expander from '../../Expander.vue';\nimport taskMixin from './Task.mixin.js';\nexport default {\n  // eslint-disable-next-line vue/multi-word-component-names\n  name: 'Milestone',\n  components: {\n    ChartText,\n    ProgressBar,\n    Expander,\n  },\n  inject: ['root'],\n  props: ['task'],\n  mixins: [taskMixin],\n  data() {\n    return {};\n  },\n  computed: {\n    /**\n     * Get clip path id\n     *\n     * @returns {string}\n     */\n    clipPathId() {\n      return 'gantt-elastic__milestone-clip-path-' + this.task.id;\n    },\n\n    /**\n     * Get points\n     *\n     * @returns {string}\n     */\n    getPoints() {\n      const task = this.task;\n      const fifty = task.height / 2;\n      let offset = fifty;\n      if (task.width / 2 - offset < 0) {\n        offset = task.width / 2;\n      }\n      return `0,${fifty}\n        ${offset},0\n        ${task.width - offset},0\n        ${task.width},${fifty}\n        ${task.width - offset},${task.height}\n        ${offset},${task.height}`;\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Milestone.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Milestone.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Milestone.vue?vue&type=template&id=6b20ed77&\"\nimport script from \"./Milestone.vue?vue&type=script&lang=js&\"\nexport * from \"./Milestone.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"g\",\n    {\n      staticClass:\n        \"gantt-elastic__chart-row-bar-wrapper gantt-elastic__chart-row-project-wrapper\",\n      style: {\n        ..._vm.root.style[\"chart-row-bar-wrapper\"],\n        ..._vm.root.style[\"chart-row-project-wrapper\"],\n        ..._vm.task.style[\"chart-row-bar-wrapper\"],\n      },\n    },\n    [\n      _vm.displayExpander\n        ? _c(\n            \"foreignObject\",\n            {\n              staticClass:\n                \"gantt-elastic__chart-expander gantt-elastic__chart-expander--project\",\n              style: {\n                ..._vm.root.style[\"chart-expander\"],\n                ..._vm.root.style[\"chart-expander--project\"],\n                ..._vm.task.style[\"chart-expander\"],\n              },\n              attrs: {\n                x:\n                  _vm.task.x -\n                  _vm.root.state.options.chart.expander.offset -\n                  _vm.root.state.options.chart.expander.size,\n                y:\n                  _vm.task.y +\n                  (_vm.root.state.options.row.height -\n                    _vm.root.state.options.chart.expander.size) /\n                    2,\n                width: _vm.root.state.options.chart.expander.size,\n                height: _vm.root.state.options.chart.expander.size,\n              },\n            },\n            [\n              _c(\"expander\", {\n                attrs: {\n                  tasks: [_vm.task],\n                  options: _vm.root.state.options.chart.expander,\n                  type: \"chart\",\n                },\n              }),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.root.state.options.chart.text.display\n        ? _c(\"chart-text\", { attrs: { task: _vm.task } })\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"svg\",\n        {\n          staticClass:\n            \"gantt-elastic__chart-row-bar gantt-elastic__chart-row-project\",\n          style: {\n            ..._vm.root.style[\"chart-row-bar\"],\n            ..._vm.root.style[\"chart-row-project\"],\n            ..._vm.task.style[\"chart-row-bar\"],\n          },\n          attrs: {\n            x: _vm.task.x,\n            y: _vm.task.y,\n            width: _vm.task.width,\n            height: _vm.task.height,\n            viewBox: `0 0 ${_vm.task.width} ${_vm.task.height}`,\n            xmlns: \"http://www.w3.org/2000/svg\",\n          },\n          on: {\n            click: function ($event) {\n              return _vm.emitEvent(\"click\", $event)\n            },\n            dblclick: function ($event) {\n              return _vm.emitEvent(\"dblclick\", $event)\n            },\n            mouseenter: function ($event) {\n              return _vm.emitEvent(\"mouseenter\", $event)\n            },\n            mouseleave: function ($event) {\n              return _vm.emitEvent(\"mouseleave\", $event)\n            },\n            mouseover: function ($event) {\n              return _vm.emitEvent(\"mouseover\", $event)\n            },\n            mouseout: function ($event) {\n              return _vm.emitEvent(\"mouseout\", $event)\n            },\n            mousemove: function ($event) {\n              return _vm.emitEvent(\"mousemove\", $event)\n            },\n            mousedown: function ($event) {\n              return _vm.emitEvent(\"mousedown\", $event)\n            },\n            mouseup: function ($event) {\n              return _vm.emitEvent(\"mouseup\", $event)\n            },\n            mousewheel: function ($event) {\n              return _vm.emitEvent(\"mousewheel\", $event)\n            },\n            touchstart: function ($event) {\n              return _vm.emitEvent(\"touchstart\", $event)\n            },\n            touchmove: function ($event) {\n              return _vm.emitEvent(\"touchmove\", $event)\n            },\n            touchend: function ($event) {\n              return _vm.emitEvent(\"touchend\", $event)\n            },\n          },\n        },\n        [\n          _c(\"defs\", [\n            _c(\"clipPath\", { attrs: { id: _vm.clipPathId } }, [\n              _c(\"path\", { attrs: { d: _vm.getPoints } }),\n            ]),\n          ]),\n          _vm._v(\" \"),\n          _c(\"path\", {\n            staticClass:\n              \"gantt-elastic__chart-row-bar-polygon gantt-elastic__chart-row-project-polygon\",\n            style: {\n              ..._vm.root.style[\"chart-row-bar-polygon\"],\n              ..._vm.root.style[\"chart-row-project-polygon\"],\n              ..._vm.task.style[\"base\"],\n              ..._vm.task.style[\"chart-row-bar-polygon\"],\n            },\n            attrs: { d: _vm.getPoints },\n          }),\n          _vm._v(\" \"),\n          _c(\"circle\", {\n            staticClass: \"gantt-elastic__chart-row-bar-circle\",\n            style: _vm.getCircleStyle,\n            attrs: {\n              cx: _vm.getStartCircle.x,\n              cy: _vm.getStartCircle.y,\n              r: _vm.circle.r,\n            },\n            on: {\n              mousedown: function ($event) {\n                $event.stopPropagation()\n                return _vm.onMouseDown($event, _vm.getStartCircle)\n              },\n            },\n          }),\n          _vm._v(\" \"),\n          _c(\"circle\", {\n            staticClass: \"gantt-elastic__chart-row-bar-circle\",\n            style: _vm.getCircleStyle,\n            attrs: {\n              cx: _vm.getEndCircle.x,\n              cy: _vm.getEndCircle.y,\n              r: _vm.circle.r,\n            },\n            on: {\n              mousedown: function ($event) {\n                $event.stopPropagation()\n                return _vm.onMouseDown($event, _vm.getEndCircle)\n              },\n            },\n          }),\n          _vm._v(\" \"),\n          _c(\"progress-bar\", {\n            attrs: {\n              task: _vm.task,\n              \"clip-path\": \"url(#\" + _vm.clipPathId + \")\",\n            },\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "<!--\n/**\n * @fileoverview Project component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <g\n    class=\"gantt-elastic__chart-row-bar-wrapper gantt-elastic__chart-row-project-wrapper\"\n    :style=\"{\n      ...root.style['chart-row-bar-wrapper'],\n      ...root.style['chart-row-project-wrapper'],\n      ...task.style['chart-row-bar-wrapper'],\n    }\"\n  >\n    <foreignObject\n      class=\"gantt-elastic__chart-expander gantt-elastic__chart-expander--project\"\n      :style=\"{\n        ...root.style['chart-expander'],\n        ...root.style['chart-expander--project'],\n        ...task.style['chart-expander'],\n      }\"\n      :x=\"task.x - root.state.options.chart.expander.offset - root.state.options.chart.expander.size\"\n      :y=\"task.y + (root.state.options.row.height - root.state.options.chart.expander.size) / 2\"\n      :width=\"root.state.options.chart.expander.size\"\n      :height=\"root.state.options.chart.expander.size\"\n      v-if=\"displayExpander\"\n    >\n      <expander :tasks=\"[task]\" :options=\"root.state.options.chart.expander\" type=\"chart\"></expander>\n    </foreignObject>\n\n    <chart-text :task=\"task\" v-if=\"root.state.options.chart.text.display\"></chart-text>\n\n    <svg\n      class=\"gantt-elastic__chart-row-bar gantt-elastic__chart-row-project\"\n      :style=\"{\n        ...root.style['chart-row-bar'],\n        ...root.style['chart-row-project'],\n        ...task.style['chart-row-bar'],\n      }\"\n      :x=\"task.x\"\n      :y=\"task.y\"\n      :width=\"task.width\"\n      :height=\"task.height\"\n      :viewBox=\"`0 0 ${task.width} ${task.height}`\"\n      @click=\"emitEvent('click', $event)\"\n      @dblclick=\"emitEvent('dblclick', $event)\"\n      @mouseenter=\"emitEvent('mouseenter', $event)\"\n      @mouseleave=\"emitEvent('mouseleave', $event)\"\n      @mouseover=\"emitEvent('mouseover', $event)\"\n      @mouseout=\"emitEvent('mouseout', $event)\"\n      @mousemove=\"emitEvent('mousemove', $event)\"\n      @mousedown=\"emitEvent('mousedown', $event)\"\n      @mouseup=\"emitEvent('mouseup', $event)\"\n      @mousewheel=\"emitEvent('mousewheel', $event)\"\n      @touchstart=\"emitEvent('touchstart', $event)\"\n      @touchmove=\"emitEvent('touchmove', $event)\"\n      @touchend=\"emitEvent('touchend', $event)\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <defs>\n        <clipPath :id=\"clipPathId\">\n          <path :d=\"getPoints\"></path>\n        </clipPath>\n      </defs>\n      <path\n        class=\"gantt-elastic__chart-row-bar-polygon gantt-elastic__chart-row-project-polygon\"\n        :style=\"{\n          ...root.style['chart-row-bar-polygon'],\n          ...root.style['chart-row-project-polygon'],\n          ...task.style['base'],\n          ...task.style['chart-row-bar-polygon'],\n        }\"\n        :d=\"getPoints\"\n      ></path>\n\n      <circle\n        class=\"gantt-elastic__chart-row-bar-circle\"\n        :cx=\"getStartCircle.x\"\n        :cy=\"getStartCircle.y\"\n        :r=\"circle.r\"\n        :style=\"getCircleStyle\"\n        @mousedown.stop=\"onMouseDown($event, getStartCircle)\"\n      ></circle>\n\n      <circle\n        class=\"gantt-elastic__chart-row-bar-circle\"\n        :cx=\"getEndCircle.x\"\n        :cy=\"getEndCircle.y\"\n        :r=\"circle.r\"\n        :style=\"getCircleStyle\"\n        @mousedown.stop=\"onMouseDown($event, getEndCircle)\"\n      ></circle>\n\n      <progress-bar :task=\"task\" :clip-path=\"'url(#' + clipPathId + ')'\"></progress-bar>\n    </svg>\n  </g>\n</template>\n\n<script>\nimport ChartText from '../Text.vue';\nimport ProgressBar from '../ProgressBar.vue';\nimport Expander from '../../Expander.vue';\nimport taskMixin from './Task.mixin.js';\nexport default {\n  // eslint-disable-next-line vue/multi-word-component-names\n  name: 'Project',\n  components: {\n    ChartText,\n    ProgressBar,\n    Expander,\n  },\n  inject: ['root'],\n  props: ['task'],\n  mixins: [taskMixin],\n  data() {\n    return {};\n  },\n  computed: {\n    /**\n     * Get clip path id\n     *\n     * @returns {string}\n     */\n    clipPathId() {\n      return 'gantt-elastic__project-clip-path-' + this.task.id;\n    },\n\n    /**\n     * Get points\n     *\n     * @returns {string}\n     */\n    getPoints() {\n      const task = this.task;\n      const bottom = task.height - task.height / 4;\n      const corner = task.height / 6;\n      const smallCorner = task.height / 8;\n      return `M ${smallCorner},0\n                L ${task.width - smallCorner} 0\n                L ${task.width} ${smallCorner}\n                L ${task.width} ${bottom}\n                L ${task.width - corner} ${task.height}\n                L ${task.width - corner * 2} ${bottom}\n                L ${corner * 2} ${bottom}\n                L ${corner} ${task.height}\n                L 0 ${bottom}\n                L 0 ${smallCorner}\n                Z\n        `;\n    },\n\n    /**\n     * Should we display expander?\n     *\n     * @returns {boolean}\n     */\n    displayExpander() {\n      const expander = this.root.state.options.chart.expander;\n      return expander.display || (expander.displayIfTaskListHidden && !this.root.state.options.taskList.display);\n    },\n  },\n};\n</script>\n", "import mod from \"-!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Project.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Project.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Project.vue?vue&type=template&id=587a58d4&\"\nimport script from \"./Project.vue?vue&type=script&lang=js&\"\nexport * from \"./Project.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<!--\n/**\n * @fileoverview Chart component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div class=\"gantt-elastic__chart\" :style=\"{ ...root.style['chart'] }\" ref=\"chart\">\n    <div\n      class=\"gantt-elastic__chart-calendar-container\"\n      ref=\"chartCalendarContainer\"\n      :style=\"{\n        ...root.style['chart-calendar-container'],\n        height: root.state.options.calendar.height + 'px',\n        'margin-bottom': root.state.options.calendar.gap + 'px',\n      }\"\n    >\n      <calendar></calendar>\n    </div>\n    <div\n      class=\"gantt-elastic__chart-graph-container\"\n      ref=\"chartGraphContainer\"\n      :style=\"{\n        ...root.style['chart-graph-container'],\n        height: root.state.options.height - root.state.options.calendar.height + 'px',\n      }\"\n    >\n      <div\n        :style=\"{\n          ...root.style['chart-area'],\n          width: root.state.options.width + 'px',\n          height: root.state.options.rowsHeight + 'px',\n        }\"\n      >\n        <div\n          class=\"gantt-elastic__chart-graph\"\n          ref=\"chartGraph\"\n          :style=\"{ ...root.style['chart-graph'], height: '100%' }\"\n        >\n          <svg\n            class=\"gantt-elastic__chart-graph-svg\"\n            :style=\"{ ...root.style['chart-graph-svg'] }\"\n            ref=\"chartGraphSvg\"\n            x=\"0\"\n            y=\"0\"\n            :width=\"root.state.options.width + 'px'\"\n            :height=\"root.state.options.allVisibleTasksHeight + 'px'\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <days-highlight></days-highlight>\n            <grid></grid>\n            <dependency-lines :tasks=\"root.visibleTasks\"></dependency-lines>\n\n            <g\n              class=\"gantt-elastic__chart-row-wrapper\"\n              :style=\"{ ...root.style['chart-row-wrapper'] }\"\n              v-for=\"task in root.visibleTasks\"\n              :task=\"task\"\n              :key=\"task.id\"\n              :data-taskid=\"task.id\"\n            >\n              <component :task=\"task\" :is=\"task.type\"></component>\n            </g>\n\n            <!-- 拖拽连接线 -->\n            <line\n              v-show=\"connectingLine.show\"\n              v-bind=\"getContLine\"\n              style=\"stroke: rgba(0, 119, 192, 0.5); stroke-width: 4\"\n            ></line>\n          </svg>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport Grid from './Grid.vue';\nimport DaysHighlight from './DaysHighlight.vue';\nimport Calendar from '../Calendar/Calendar.vue';\nimport DependencyLines from './DependencyLines.vue';\nimport Task from './Row/Task.vue';\nimport Milestone from './Row/Milestone.vue';\nimport Project from './Row/Project.vue';\nexport default {\n  // eslint-disable-next-line vue/multi-word-component-names\n  name: 'Chart',\n  components: {\n    Grid,\n    DependencyLines,\n    Calendar,\n    Task,\n    Milestone,\n    Project,\n    DaysHighlight,\n  },\n  inject: ['root'],\n  data() {\n    return {\n      moving: false,\n\n      connectingLine: {\n        show: false,\n        startId: '',\n        endId: '',\n        x1: 0,\n        y1: 0,\n        x2: 0,\n        y2: 0,\n      },\n    };\n  },\n  /**\n   * Mounted\n   */\n  mounted() {\n    // document.addEventListener('mousemove', this.resizerMouseMove.bind(this));\n    // document.addEventListener('mouseup', this.resizerMouseup.bind(this), true);\n\n    this.root.state.refs.chart = this.$refs.chart;\n    this.root.state.refs.chartCalendarContainer = this.$refs.chartCalendarContainer;\n    this.root.state.refs.chartGraphContainer = this.$refs.chartGraphContainer;\n    this.root.state.refs.chartGraph = this.$refs.chartGraph;\n    this.root.state.refs.chartGraphSvg = this.$refs.chartGraphSvg;\n\n    this.root.state.connectLine = {\n      setConnectLine: this.setConnectLine,\n      getConnectingLine: this.getConnectingLine,\n      delConnectLine: this.delConnectLine,\n    };\n    // this.root.state.setConnectLine = this.setConnectLine;\n    // this.root.state.getConnectingLine = this.getConnectingLine;\n  },\n\n  beforeDestroy() {\n    // document.removeEventListener('mouseup', this.resizerMouseup);\n    // document.removeEventListener('mousemove', this.resizerMouseMove);\n  },\n\n  computed: {\n    /**\n     * Get view box\n     *\n     * @returns {string}\n     */\n    getViewBox() {\n      return `0 0 ${this.root.state.options.width} ${this.root.state.options.allVisibleTasksHeight}`;\n    },\n\n    getContLine() {\n      const { x1, y1, x2, y2 } = this.connectingLine;\n      return { x1, y1, x2, y2 };\n    },\n  },\n\n  methods: {\n    setConnectLine(params) {\n      this.connectingLine.show = true;\n      Object.keys(params).forEach((key) => {\n        this.$set(this.connectingLine, key, params[key]);\n      });\n    },\n\n    delConnectLine() {\n      this.connectingLine.show = false;\n\n      this.connectingLine = Object.assign(this.connectingLine, {\n        startId: '',\n        endId: '',\n        x1: 0,\n        y1: 0,\n        x2: 0,\n        y2: 0,\n      });\n    },\n\n    getConnectingLine() {\n      return this.connectingLine;\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.gantt-elastic__chart-row-bar-wrapper {\n  .gantt-elastic__chart-row-bar {\n    overflow: visible;\n  }\n\n  .gantt-elastic__chart-row-bar-circle {\n    cursor: pointer;\n  }\n\n  // &:hover .gantt-elastic__chart-row-bar-circle {\n  //   display: inline !important;\n  // }\n}\n</style>\n", "import mod from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Chart.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Chart.vue?vue&type=template&id=23688fcb&\"\nimport script from \"./Chart.vue?vue&type=script&lang=js&\"\nexport * from \"./Chart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Chart.vue?vue&type=style&index=0&id=23688fcb&prod&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<!--\n/**\n * @fileoverview MainView component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div class=\"gantt-elastic__main-view\" :style=\"{ ...root.style['main-view'] }\">\n    <div\n      class=\"gantt-elastic__main-container-wrapper\"\n      :style=\"{\n        ...root.style['main-container-wrapper'],\n        height: root.state.options.height + 'px',\n      }\"\n    >\n      <div\n        class=\"gantt-elastic__main-container\"\n        :style=\"{\n          ...root.style['main-container'],\n          width: root.state.options.clientWidth + 'px',\n          height: root.state.options.height + 'px',\n        }\"\n        ref=\"mainView\"\n      >\n        <div\n          class=\"gantt-elastic__container\"\n          :style=\"{ ...root.style['container'] }\"\n          @mousemove=\"mouseMove\"\n          @mouseup=\"mouseUp\"\n        >\n          <div\n            ref=\"taskList\"\n            class=\"gantt-elastic__task-list-container\"\n            :style=\"{\n              ...root.style['task-list-container'],\n              width: root.state.options.taskList.finalWidth + 'px',\n              height: root.state.options.height + 'px',\n            }\"\n            v-show=\"root.state.options.taskList.display\"\n          >\n            <task-list></task-list>\n          </div>\n          <div\n            class=\"gantt-elastic__main-view-container\"\n            :style=\"{ ...root.style['main-view-container'] }\"\n            ref=\"chartContainer\"\n            @mousedown=\"chartMouseDown\"\n            @touchstart=\"chartMouseDown\"\n            @mouseup=\"chartMouseUp\"\n            @touchend=\"chartMouseUp\"\n            @mousemove.prevent=\"chartMouseMove\"\n            @touchmove.prevent=\"chartMouseMove\"\n            @wheel.prevent=\"chartWheel\"\n          >\n            <chart></chart>\n          </div>\n        </div>\n      </div>\n      <div\n        class=\"gantt-elastic__chart-scroll-container gantt-elastic__chart-scroll-container--vertical\"\n        :style=\"{\n          ...root.style['chart-scroll-container'],\n          ...root.style['chart-scroll-container--vertical'],\n          ...verticalStyle,\n        }\"\n        ref=\"chartScrollContainerVertical\"\n        @scroll=\"onVerticalScroll\"\n      >\n        <div\n          class=\"gantt-elastic__chart-scroll--vertical\"\n          :style=\"{\n            width: '1px',\n            height: root.state.options.allVisibleTasksHeight + 'px',\n          }\"\n        ></div>\n      </div>\n    </div>\n    <div\n      class=\"gantt-elastic__chart-scroll-container gantt-elastic__chart-scroll-container--horizontal\"\n      :style=\"{\n        ...root.style['chart-scroll-container'],\n        ...root.style['chart-scroll-container--horizontal'],\n        marginLeft: getMarginLeft,\n      }\"\n      @scroll=\"onHorizontalScroll\"\n      ref=\"chartScrollContainerHorizontal\"\n    >\n      <div\n        class=\"gantt-elastic__chart-scroll--horizontal\"\n        :style=\"{ height: '1px', width: root.state.options.width + 'px' }\"\n      ></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TaskList from './TaskList/TaskList.vue';\nimport Chart from './Chart/Chart.vue';\n\nlet ignoreScrollEvents = false;\n\nexport default {\n  name: 'MainView',\n  components: {\n    TaskList,\n    Chart,\n  },\n  inject: ['root'],\n  data() {\n    return {\n      defs: '',\n      mousePos: {\n        x: 0,\n        y: 0,\n        movementX: 0,\n        movementY: 0,\n        lastX: 0,\n        lastY: 0,\n        positiveX: 0,\n        positiveY: 0,\n        currentX: 0,\n        currentY: 0,\n      },\n    };\n  },\n  /**\n   * Mounted\n   */\n  mounted() {\n    this.viewBoxWidth = this.$el.clientWidth;\n    this.root.state.refs.mainView = this.$refs.mainView;\n    this.root.state.refs.chartContainer = this.$refs.chartContainer;\n    this.root.state.refs.taskList = this.$refs.taskList;\n    this.root.state.refs.chartScrollContainerHorizontal = this.$refs.chartScrollContainerHorizontal;\n    this.root.state.refs.chartScrollContainerVertical = this.$refs.chartScrollContainerVertical;\n    document.addEventListener('mouseup', this.chartMouseUp.bind(this));\n    document.addEventListener('mousemove', this.chartMouseMove.bind(this));\n    document.addEventListener('touchmove', this.chartMouseMove.bind(this));\n    document.addEventListener('touchend', this.chartMouseUp.bind(this));\n  },\n  computed: {\n    /**\n     * Get margin left\n     *\n     * @returns {string}\n     */\n    getMarginLeft() {\n      if (!this.root.state.options.taskList.display) {\n        return '0px';\n      }\n      return this.root.state.options.taskList.finalWidth + 'px';\n    },\n\n    /**\n     * Get vertical style\n     *\n     * @returns {object}\n     */\n    verticalStyle() {\n      return {\n        width: this.root.state.options.scrollBarHeight + 'px',\n        height: this.root.state.options.rowsHeight + 'px',\n        'margin-top': this.root.state.options.calendar.height + this.root.state.options.calendar.gap + 'px',\n      };\n    },\n\n    /**\n     * Get view box\n     *\n     * @returns {string}\n     */\n    getViewBox() {\n      if (this.root.state.options.clientWidth) {\n        return `0 0 ${this.root.state.options.clientWidth - this.root.state.options.scrollBarHeight} ${\n          this.root.state.options.height\n        }`;\n      }\n      return `0 0 0 ${this.root.state.options.height}`;\n    },\n  },\n  methods: {\n    /**\n     * Emit event when mouse is moving inside main view\n     */\n    mouseMove(event) {\n      this.root.$emit('main-view-mousemove', event);\n    },\n\n    /**\n     * Emit mouseup event inside main view\n     */\n    mouseUp(event) {\n      this.root.$emit('main-view-mouseup', event);\n    },\n\n    /**\n     * Horizontal scroll event handler\n     */\n    onHorizontalScroll(ev) {\n      this.root.$emit('chart-scroll-horizontal', ev);\n    },\n\n    /**\n     * Vertical scroll event handler\n     */\n    onVerticalScroll(ev) {\n      this.root.$emit('chart-scroll-vertical', ev);\n    },\n\n    /**\n     * Mouse wheel event handler\n     */\n    chartWheel(ev) {\n      this.root.$emit('chart-wheel', ev);\n    },\n\n    /**\n     * Chart mousedown event handler\n     * Initiates drag scrolling mode\n     */\n    chartMouseDown(ev) {\n      if (typeof ev.touches !== 'undefined') {\n        this.mousePos.x = this.mousePos.lastX = ev.touches[0].screenX;\n        this.mousePos.y = this.mousePos.lastY = ev.touches[0].screenY;\n        this.mousePos.movementX = 0;\n        this.mousePos.movementY = 0;\n        this.mousePos.currentX = this.$refs.chartScrollContainerHorizontal.scrollLeft;\n        this.mousePos.currentY = this.$refs.chartScrollContainerVertical.scrollTop;\n      }\n      this.root.state.options.scroll.scrolling = true;\n    },\n\n    /**\n     * Chart mouseup event handler\n     * Deactivates drag scrolling mode\n     */\n    chartMouseUp(ev) {\n      this.root.state.options.scroll.scrolling = false;\n    },\n\n    /**\n     * Chart mousemove event handler\n     * When in drag scrolling mode this method calculate scroll movement\n     */\n    chartMouseMove(ev) {\n      if (this.root.state.options.scroll.scrolling) {\n        ev.preventDefault();\n        ev.stopImmediatePropagation();\n        ev.stopPropagation();\n        const touch = typeof ev.touches !== 'undefined';\n        let movementX, movementY;\n        if (touch) {\n          const screenX = ev.touches[0].screenX;\n          const screenY = ev.touches[0].screenY;\n          movementX = this.mousePos.x - screenX;\n          movementY = this.mousePos.y - screenY;\n          this.mousePos.lastX = screenX;\n          this.mousePos.lastY = screenY;\n        } else {\n          movementX = ev.movementX;\n          movementY = ev.movementY;\n        }\n        const horizontal = this.$refs.chartScrollContainerHorizontal;\n        const vertical = this.$refs.chartScrollContainerVertical;\n        let x = 0,\n          y = 0;\n        if (touch) {\n          x = this.mousePos.currentX + movementX * this.root.state.options.scroll.dragXMoveMultiplier;\n        } else {\n          x = horizontal.scrollLeft - movementX * this.root.state.options.scroll.dragXMoveMultiplier;\n        }\n        horizontal.scrollLeft = x;\n        if (touch) {\n          y = this.mousePos.currentY + movementY * this.root.state.options.scroll.dragYMoveMultiplier;\n        } else {\n          y = vertical.scrollTop - movementY * this.root.state.options.scroll.dragYMoveMultiplier;\n        }\n        vertical.scrollTop = y;\n      }\n    },\n  },\n\n  /**\n   * Before destroy event - clean up\n   */\n  beforeDestroy() {\n    document.removeEventListener('mouseup', this.chartMouseUp);\n    document.removeEventListener('mousemove', this.chartMouseMove);\n    document.removeEventListener('touchmove', this.chartMouseMove);\n    document.removeEventListener('touchend', this.chartMouseUp);\n  },\n};\n</script>\n", "import mod from \"-!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MainView.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MainView.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./MainView.vue?vue&type=template&id=51c3c04c&\"\nimport script from \"./MainView.vue?vue&type=script&lang=js&\"\nexport * from \"./MainView.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "/**\n * @fileoverview Styles for gantt-elastic\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n\nexport default function getStyle(fontSize = '12px', fontFamily = 'Arial, sans-serif') {\n  return {\n    fontSize,\n    fontFamily,\n    'main-view': {\n      background: '#FFFFFF',\n    },\n    'main-container-wrapper': {\n      overflow: 'hidden',\n      'border-top': '1px solid #eee',\n      'border-bottom': '1px solid #eee',\n    },\n    'main-container': {\n      float: 'left',\n      'max-width': '100%',\n    },\n    'main-view-container': {},\n    container: {\n      display: 'flex',\n      'max-width': '100%',\n      height: '100%',\n    },\n    'calendar-wrapper': {\n      'user-select': 'none',\n    },\n    calendar: {\n      width: '100%',\n      background: '#f3f5f7',\n      display: 'block',\n    },\n    'calendar-row': {\n      display: 'flex',\n      'justify-content': 'space-evenly',\n    },\n    'calendar-row--month': {},\n    'calendar-row--day': {},\n    'calendar-row--hour': {\n      'border-bottom': '1px solid #eee',\n    },\n    'calendar-row-rect': {\n      background: 'transparent',\n      display: 'flex',\n    },\n    'calendar-row-rect--month': {},\n    'calendar-row-rect--day': {},\n    'calendar-row-rect--hour': {},\n    'calendar-row-rect-child': {\n      display: 'block',\n      'border-right-width': '1px', // Calendar\n      'border-right-color': '#dadada',\n      'border-right-style': 'solid',\n      position: 'relative',\n    },\n    'calendar-row-rect-child--month': {},\n    'calendar-row-rect-child--day': { 'text-align': 'center' },\n    'calendar-row-rect-child--hour': { 'text-align': 'center' },\n    'calendar-row-text': {\n      'font-family': fontFamily, // GanttElastic\n      'font-size': fontSize, //GanttElastic\n      color: '#606060',\n      display: 'inline-block',\n      position: 'relative',\n    },\n    'calendar-row-text--month': {},\n    'calendar-row-text--day': {},\n    'calendar-row-text--hour': {},\n    'task-list-wrapper': {},\n    'task-list': { background: 'transparent', 'border-color': '#eee' },\n    'task-list-header': {\n      display: 'flex',\n      'user-select': 'none',\n      'vertical-align': 'middle',\n      'border-bottom': '1px solid #eee',\n      'border-left': '1px solid #eee',\n    },\n    'task-list-header-column': {\n      'border-left': '1px solid #00000050',\n      'box-sizing': 'border-box',\n      display: 'flex',\n      background: '#f3f5f7',\n      'border-color': 'transparent',\n    },\n    'task-list-expander-wrapper': {\n      display: 'inline-flex',\n      'flex-shrink': '0',\n      'box-sizing': 'border-box',\n      margin: '0 0 0 10px',\n    },\n    'task-list-expander-content': {\n      display: 'inline-flex',\n      cursor: 'pointer',\n      margin: 'auto 0px',\n      'box-sizing': 'border-box',\n      'user-select': 'none',\n    },\n    'task-list-expander-line': {\n      fill: 'transparent',\n      stroke: '#000000',\n      'stroke-width': '1',\n      'stroke-linecap': 'round',\n    },\n    'task-list-expander-border': {\n      fill: '#ffffffa0',\n      stroke: '#000000A0',\n    },\n    'chart-expander-wrapper': {\n      display: 'block',\n      'line-height': '1',\n      'box-sizing': 'border-box',\n      margin: '0',\n    },\n    'chart-expander-content': {\n      display: 'inline-flex',\n      cursor: 'pointer',\n      margin: 'auto 0px',\n      'box-sizing': 'border-box',\n      'user-select': 'none',\n    },\n    'chart-expander-line': {\n      fill: 'transparent',\n      stroke: '#000000',\n      'stroke-width': '1',\n      'stroke-linecap': 'round',\n    },\n    'chart-expander-border': {\n      fill: '#ffffffa0',\n      stroke: '#000000A0',\n    },\n    'task-list-container': {},\n    'task-list-header-label': {\n      overflow: 'hidden',\n      'text-overflow': 'ellipsis',\n      'font-family': fontFamily,\n      'font-size': fontSize,\n      'box-sizing': 'border-box',\n      margin: 'auto 6px',\n      'flex-grow': '1',\n      'vertical-align': 'middle',\n    },\n    'task-list-header-resizer-wrapper': {\n      background: 'transparent',\n      height: '100%',\n      width: '6px',\n      cursor: 'col-resize',\n      display: 'inline-flex',\n      'vertical-align': 'center',\n    },\n    'task-list-header-resizer': { margin: 'auto 0px' },\n    'task-list-header-resizer-dot': {\n      width: '3px',\n      height: '3px',\n      background: '#ddd',\n      'border-radius': '100%',\n      margin: '4px 0px',\n    },\n    'task-list-items': {\n      overflow: 'hidden',\n    },\n    'task-list-item': {\n      'border-top': '1px solid #eee',\n      'border-right': '1px solid #eee',\n      'box-sizing': 'border-box',\n      display: 'flex',\n      background: 'transparent',\n    },\n    'task-list-item-column': {\n      display: 'inline-flex',\n      'flex-shrink': '0',\n      'border-left': '1px solid #00000050',\n      'box-sizing': 'border-box',\n      'border-color': '#eee',\n    },\n    'task-list-item-value-wrapper': {\n      overflow: 'hidden',\n      display: 'flex',\n      width: '100%',\n    },\n    'task-list-item-value-container': {\n      margin: 'auto 0px',\n      overflow: 'hidden',\n    },\n    'task-list-item-value': {\n      display: 'block',\n      'flex-shrink': '100',\n      'font-family': fontFamily,\n      'font-size': fontSize,\n      'margin-top': 'auto',\n      'margin-bottom': 'auto',\n      'margin-left': '6px', // TaskList\n      'margin-right': '6px',\n      overflow: 'hidden',\n      'text-overflow': 'ellipsis',\n      'line-height': '1.5em',\n      'word-break': 'keep-all',\n      'white-space': 'nowrap',\n      color: '#606060',\n      background: '#FFFFFF',\n    },\n    'grid-lines': {},\n    'grid-line-horizontal': {\n      stroke: '#00000010',\n      'stroke-width': 1,\n    },\n    'grid-line-vertical': {\n      stroke: '#00000010',\n      'stroke-width': 1,\n    },\n    'grid-line-time': {\n      stroke: '#FF000080',\n      'stroke-width': 1,\n    },\n    chart: {\n      'user-select': 'none',\n      overflow: 'hidden',\n    },\n    'chart-calendar-container': {\n      'user-select': 'none',\n      overflow: 'hidden',\n      'max-width': '100%',\n      'border-right': '1px solid #eee',\n    },\n    'chart-graph-container': {\n      'user-select': 'none',\n      overflow: 'hidden',\n      'max-width': '100%',\n      'border-right': '1px solid #eee',\n    },\n    'chart-area': {},\n    'chart-graph': {\n      overflow: 'hidden',\n    },\n    'chart-row-text-wrapper': {},\n    'chart-row-text': {\n      background: '#ffffffa0',\n      'border-radius': '10px',\n      'font-family': fontFamily,\n      'font-size': fontSize,\n      'font-weight': 'normal',\n      color: '#000000a0',\n      height: '100%',\n      display: 'inline-block',\n    },\n    'chart-row-text-content': {\n      padding: '0px 6px',\n    },\n    'chart-row-text-content--text': {},\n    'chart-row-text-content--html': {},\n    'chart-row-wrapper': {},\n    'chart-row-bar-wrapper': {},\n    'chart-row-bar': {},\n    'chart-row-bar-polygon': {\n      stroke: '#E74C3C',\n      'stroke-width': 1,\n      fill: '#F75C4C',\n    },\n    'chart-row-project-wrapper': {},\n    'chart-row-project': {},\n    'chart-row-project-polygon': {},\n    'chart-row-milestone-wrapper': {},\n    'chart-row-milestone': {},\n    'chart-row-milestone-polygon': {},\n    'chart-row-task-wrapper': {},\n    'chart-row-task': {},\n    'chart-row-task-polygon': {},\n    'chart-row-progress-bar-wrapper': {},\n    'chart-row-progress-bar': {},\n    'chart-row-progress-bar-line': {\n      stroke: '#ffffff25',\n      'stroke-width': 20,\n    },\n    'chart-row-progress-bar-solid': {\n      fill: '#0EAC51',\n      height: '20%',\n    },\n    'chart-row-progress-bar-pattern': {\n      fill: 'url(#diagonalHatch)',\n      transform: 'translateY(0.1) scaleY(0.8)',\n    },\n    'chart-row-progress-bar-outline': {\n      stroke: '#E74C3C',\n      'stroke-width': 4,\n    },\n    'chart-dependency-lines-wrapper': {},\n    'chart-dependency-lines-path': {\n      fill: 'transparent',\n      stroke: '#FFa00090',\n      'stroke-width': 2,\n    },\n    'chart-scroll-container': {},\n    'chart-scroll-container--horizontal': {\n      overflow: 'auto',\n      'max-width': '100%',\n    },\n    'chart-scroll-container--vertical': {\n      'overflow-y': 'auto',\n      'overflow-x': 'hidden',\n      'max-height': '100%',\n      float: 'right',\n    },\n    'chart-days-highlight-rect': {\n      fill: '#f3f5f780',\n    },\n    'slot-header-beforeOptions': {\n      display: 'inline-block',\n    },\n  };\n}\n", "<!--\n/**\n * @fileoverview GanttElastic component\n * @license MIT\n * <AUTHOR> <<EMAIL>>\n * @package GanttElastic\n */\n-->\n<template>\n  <div\n    class=\"gantt-elastic\"\n    style=\"width: 100%\"\n    @keydown=\"onKeyDown\"\n    @keyup=\"onKeyUp\"\n    @mousewheel=\"onMousewheel\"\n    tabindex=\"-1\"\n  >\n    <slot name=\"header\"></slot>\n    <main-view ref=\"mainView\"></main-view>\n    <slot name=\"footer\"></slot>\n  </div>\n</template>\n\n<!-- eslint-disable no-prototype-builtins -->\n<script>\nimport VueInstance from 'vue';\nimport dayjs from 'dayjs';\nimport MainView from './components/MainView.vue';\nimport getStyle from './style.js';\nimport ResizeObserver from 'resize-observer-polyfill';\n\nconst ctx = document.createElement('canvas').getContext('2d');\nlet VueInst = VueInstance;\nfunction initVue() {\n  if (typeof Vue !== 'undefined' && typeof VueInst === 'undefined') {\n    // eslint-disable-next-line no-undef\n    VueInst = Vue;\n  }\n}\ninitVue();\n\nlet hourWidthCache = null;\n\n/**\n * Helper function to fill out empty options in user settings\n *\n * @param {object} userOptions - initial user options that will merge with those below\n * @returns {object} merged options\n */\nfunction getOptions(userOptions) {\n  let localeName = 'en';\n  if (typeof userOptions.locale !== 'undefined' && typeof userOptions.locale.name !== 'undefined') {\n    localeName = userOptions.locale.name;\n  }\n  return {\n    slots: {\n      header: {},\n    },\n    taskMapping: {\n      //*\n      id: 'id',\n      start: 'start',\n      label: 'label',\n      duration: 'duration',\n      progress: 'progress',\n      type: 'type',\n      style: 'style',\n      collapsed: 'collapsed',\n    },\n    width: 0,\n    height: 0,\n    clientWidth: 0,\n    outerHeight: 0,\n    rowsHeight: 0,\n    allVisibleTasksHeight: 0,\n    scroll: {\n      scrolling: false,\n      dragXMoveMultiplier: 3, //*\n      dragYMoveMultiplier: 2, //*\n      top: 0,\n      taskList: {\n        left: 0,\n        right: 0,\n        top: 0,\n        bottom: 0,\n      },\n      chart: {\n        left: 0,\n        right: 0,\n        percent: 0,\n        timePercent: 0,\n        top: 0,\n        bottom: 0,\n        time: 0,\n        timeCenter: 0,\n        dateTime: {\n          left: '',\n          right: '',\n        },\n      },\n    },\n    scope: {\n      //*\n      before: 1,\n      after: 1,\n    },\n    times: {\n      timeScale: 60 * 1000,\n      timeZoom: 17, //*\n      timePerPixel: 0,\n      firstTime: null,\n      lastTime: null,\n      firstTaskTime: 0,\n      lastTaskTime: 0,\n      totalViewDurationMs: 0,\n      totalViewDurationPx: 0,\n      stepDuration: 'day',\n      steps: [],\n    },\n    row: {\n      height: 24, //*\n    },\n    maxRows: 20, //*\n    maxHeight: 0, //*\n    chart: {\n      grid: {\n        horizontal: {\n          gap: 6, //*\n        },\n      },\n      progress: {\n        width: 20, //*\n        height: 6, //*\n        pattern: true,\n        bar: false,\n      },\n      text: {\n        offset: 4, //*\n        xPadding: 10, //*\n        display: true, //*\n      },\n      expander: {\n        type: 'chart',\n        display: false, //*\n        displayIfTaskListHidden: true, //*\n        offset: 12, //*\n        size: 18,\n      },\n    },\n    taskList: {\n      display: true, //*\n      resizeAfterThreshold: true, //*\n      widthThreshold: 75, //*\n      columns: [\n        //*\n        {\n          id: 0,\n          label: 'ID',\n          value: 'id',\n          width: 40,\n        },\n      ],\n      percent: 100, //*\n      width: 0,\n      finalWidth: 0,\n      widthFromPercentage: 0,\n      minWidth: 18,\n      expander: {\n        type: 'task-list',\n        size: 16,\n        columnWidth: 24,\n        padding: 16,\n        margin: 10,\n        straight: false,\n      },\n    },\n    calendar: {\n      workingDays: [1, 2, 3, 4, 5], //*\n      gap: 6, //*\n      height: 0,\n      strokeWidth: 1,\n      hour: {\n        height: 20, //*\n        display: true, //*\n        widths: [],\n        maxWidths: { short: 0, medium: 0, long: 0 },\n        formatted: {\n          long: [],\n          medium: [],\n          short: [],\n        },\n        format: {\n          //*\n          long(date) {\n            return date.format('HH:mm');\n          },\n          medium(date) {\n            return date.format('HH:mm');\n          },\n          short(date) {\n            return date.format('HH');\n          },\n        },\n      },\n      day: {\n        height: 20, //*\n        display: true, //*\n        widths: [],\n        maxWidths: { short: 0, medium: 0, long: 0 },\n        format: {\n          long(date) {\n            return date.format('DD dddd');\n          },\n          medium(date) {\n            return date.format('DD ddd');\n          },\n          short(date) {\n            return date.format('DD');\n          },\n        },\n      },\n      month: {\n        height: 20, //*\n        display: true, //*\n        widths: [],\n        maxWidths: { short: 0, medium: 0, long: 0 },\n        format: {\n          //*\n          short(date) {\n            return date.format('MM');\n          },\n          medium(date) {\n            return date.format(\"MMM 'YY\");\n          },\n          long(date) {\n            return date.format('MMMM YYYY');\n          },\n        },\n      },\n    },\n    locale: {\n      //*\n      name: 'en',\n      weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),\n      weekdaysShort: 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n      weekdaysMin: 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n      months: 'January_February_Marc1h_April_May_June_July_August_September_October_November_December'.split('_'),\n      monthsShort: 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),\n      weekStart: 1,\n      relativeTime: {\n        future: 'in %s',\n        past: '%s ago',\n        s: 'a few seconds',\n        m: 'a minute',\n        mm: '%d minutes',\n        h: 'an hour',\n        hh: '%d hours',\n        d: 'a day',\n        dd: '%d days',\n        M: 'a month',\n        MM: '%d months',\n        y: 'a year',\n        yy: '%d years',\n      },\n      formats: {\n        LT: 'HH:mm',\n        LTS: 'HH:mm:ss',\n        L: 'DD/MM/YYYY',\n        LL: 'D MMMM YYYY',\n        LLL: 'D MMMM YYYY HH:mm',\n        LLLL: 'dddd, D MMMM YYYY HH:mm',\n      },\n      ordinal: (n) => {\n        const s = ['th', 'st', 'nd', 'rd'];\n        const v = n % 100;\n        return `[${n}${s[(v - 20) % 10] || s[v] || s[0]}]`;\n      },\n    },\n  };\n}\n\n/**\n * Prepare style\n *\n * @returns {object}\n */\nfunction prepareStyle(userStyle) {\n  let fontSize = '12px';\n  let fontFamily = window.getComputedStyle(document.body).getPropertyValue('font-family').toString();\n  if (typeof userStyle !== 'undefined') {\n    if (typeof userStyle.fontSize !== 'undefined') {\n      fontSize = userStyle.fontSize;\n    }\n    if (typeof userStyle.fontFamily !== 'undefined') {\n      fontFamily = userStyle.fontFamily;\n    }\n  }\n  return getStyle(fontSize, fontFamily);\n}\n\n/**\n * Helper function to determine if specified variable is an object\n *\n * @param {any} item\n *\n * @returns {boolean}\n */\nfunction isObject(item) {\n  return (\n    item &&\n    typeof item === 'object' &&\n    !Array.isArray(item) &&\n    !(item instanceof HTMLElement) &&\n    !(item instanceof CanvasRenderingContext2D) &&\n    typeof item !== 'function'\n  );\n}\n\n/**\n * Helper function which will merge objects recursively - creating brand new one - like clone\n *\n * @param {object} target\n * @params {object} sources\n *\n * @returns {object}\n */\nexport function mergeDeep(target, ...sources) {\n  if (!sources.length) {\n    return target;\n  }\n  const source = sources.shift();\n  if (isObject(target) && isObject(source)) {\n    for (const key in source) {\n      if (isObject(source[key])) {\n        if (typeof target[key] === 'undefined') {\n          target[key] = {};\n        }\n        target[key] = mergeDeep(target[key], source[key]);\n      } else if (Array.isArray(source[key])) {\n        target[key] = [];\n        for (let item of source[key]) {\n          if (isObject(item)) {\n            target[key].push(mergeDeep({}, item));\n            continue;\n          }\n          target[key].push(item);\n        }\n      } else {\n        target[key] = source[key];\n      }\n    }\n  }\n  return mergeDeep(target, ...sources);\n}\n\n/**\n * Detect if object or array is observable\n *\n * @param {object|array} obj\n *\n * @returns {boolean}\n */\nfunction isObservable(obj) {\n  // eslint-disable-next-line no-prototype-builtins\n  return typeof obj === 'object' && obj.hasOwnProperty('__ob__');\n}\n\n/**\n * Same as above but with reactivity in mind\n *\n * @param {object} target\n * @params {object} sources\n *\n * @returns {object}\n */\nexport function mergeDeepReactive(component, target, ...sources) {\n  if (!sources.length) {\n    return target;\n  }\n  const source = sources.shift();\n  if (isObject(target) && isObject(source)) {\n    for (const key in source) {\n      if (isObject(source[key])) {\n        if (typeof target[key] === 'undefined') {\n          component.$set(target, key, {});\n        }\n        mergeDeepReactive(component, target[key], source[key]);\n      } else if (Array.isArray(source[key])) {\n        component.$set(target, key, source[key]);\n      } else if (typeof source[key] === 'function') {\n        if (source[key].toString().indexOf('[native code]') === -1) {\n          target[key] = source[key];\n        }\n      } else {\n        component.$set(target, key, source[key]);\n      }\n    }\n  }\n  return mergeDeepReactive(component, target, ...sources);\n}\n/**\n * Check if objects or arrays are equal by comparing nested values\n *\n * @param {object|array} left\n * @param {object|array} right\n *\n * @returns {boolean}\n */\nexport function notEqualDeep(left, right, cache = [], path = '') {\n  if (typeof right !== typeof left) {\n    return { left, right, what: path + '.typeof' };\n  } else if (Array.isArray(left) && !Array.isArray(right)) {\n    return { left, right, what: path + '.isArray' };\n  } else if (Array.isArray(right) && !Array.isArray(left)) {\n    return { left, right, what: path + '.isArray' };\n  } else if (Array.isArray(left) && Array.isArray(right)) {\n    if (left.length !== right.length) {\n      return { left, right, what: path + '.length' };\n    }\n    let what;\n    for (let index = 0, len = left.length; index < len; index++) {\n      if ((what = notEqualDeep(left[index], right[index], cache, path + '.' + index))) {\n        return what;\n      }\n    }\n  } else if (isObject(left) && !isObject(right)) {\n    return { left, right, what: path + '.isObject' };\n  } else if (isObject(right) && !isObject(left)) {\n    return { left, right, what: path + '.isObject' };\n  } else if (isObject(left) && isObject(right)) {\n    for (let key in left) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (!left.hasOwnProperty(key) || !left.propertyIsEnumerable(key)) {\n        continue;\n      }\n      // eslint-disable-next-line no-prototype-builtins\n      if (!right.hasOwnProperty(key)) {\n        return { left, right, what: path + '.' + key };\n      }\n      let what;\n      if ((what = notEqualDeep(left[key], right[key], cache, path + '.' + key))) {\n        return what;\n      }\n    }\n  } else if (left !== right) {\n    return { left, right, what: path + '. !==' };\n  }\n  return false;\n}\n\n/**\n * GanttElastic\n * Main vue component\n */\nconst GanttElastic = {\n  name: 'GanttElastic',\n  components: {\n    MainView,\n  },\n  props: ['tasks', 'options', 'dynamicStyle'],\n  provide() {\n    const provider = {};\n    const self = this;\n    Object.defineProperty(provider, 'root', {\n      enumerable: true,\n      get: () => self,\n    });\n    return provider;\n  },\n  data() {\n    return {\n      state: {\n        tasks: [],\n        options: {\n          scrollBarHeight: 0,\n          allVisibleTasksHeight: 0,\n          outerHeight: 0,\n          scroll: {\n            left: 0,\n            top: 0,\n          },\n        },\n        dynamicStyle: {},\n        refs: {},\n        tasksById: {},\n        taskTree: {},\n        ctx,\n        emitTasksChanges: true, // some operations may pause emitting changes to parent component\n        emitOptionsChanges: true, // some operations may pause emitting changes to parent component\n        resizeObserver: null,\n        unwatchTasks: null,\n        unwatchOptions: null,\n        unwatchStyle: null,\n        unwatchOutputTasks: null,\n        unwatchOutputOptions: null,\n        unwatchOutputStyle: null,\n        parentNode: null,\n      },\n\n      controlLongPress: false,\n    };\n  },\n  methods: {\n    mergeDeep,\n    mergeDeepReactive,\n\n    /**\n     * Calculate height of scrollbar in current browser\n     *\n     * @returns {number}\n     */\n    getScrollBarHeight() {\n      const outer = document.createElement('div');\n      outer.style.visibility = 'hidden';\n      outer.style.height = '100px';\n      outer.style.msOverflowStyle = 'scrollbar';\n      document.body.appendChild(outer);\n      var noScroll = outer.offsetHeight;\n      outer.style.overflow = 'scroll';\n      var inner = document.createElement('div');\n      inner.style.height = '100%';\n      outer.appendChild(inner);\n      var withScroll = inner.offsetHeight;\n      outer.parentNode.removeChild(outer);\n      const height = noScroll - withScroll;\n      this.style['chart-scroll-container--vertical']['margin-left'] = `-${height}px`;\n      return (this.state.options.scrollBarHeight = height);\n    },\n\n    /**\n     * Fill out empty task properties and make it reactive\n     *\n     * @param {array} tasks\n     */\n    fillTasks(tasks) {\n      for (let task of tasks) {\n        if (typeof task.x === 'undefined') {\n          task.x = 0;\n        }\n        if (typeof task.y === 'undefined') {\n          task.y = 0;\n        }\n        if (typeof task.width === 'undefined') {\n          task.width = 0;\n        }\n        if (typeof task.height === 'undefined') {\n          task.height = 0;\n        }\n        if (typeof task.mouseOver === 'undefined') {\n          task.mouseOver = false;\n        }\n        if (typeof task.collapsed === 'undefined') {\n          task.collapsed = false;\n        }\n        if (typeof task.dependentOn === 'undefined') {\n          task.dependentOn = [];\n        }\n        if (typeof task.parentId === 'undefined') {\n          task.parentId = null;\n        }\n        if (typeof task.style === 'undefined') {\n          task.style = {};\n        }\n        if (typeof task.children === 'undefined') {\n          task.children = [];\n        }\n        if (typeof task.allChildren === 'undefined') {\n          task.allChildren = [];\n        }\n        if (typeof task.parents === 'undefined') {\n          task.parents = [];\n        }\n        if (typeof task.parent === 'undefined') {\n          task.parent = null;\n        }\n        if (typeof task.startTime === 'undefined') {\n          task.startTime = dayjs(task.start).valueOf();\n        }\n        if (typeof task.endTime === 'undefined' && task.hasOwnProperty('end')) {\n          task.endTime = dayjs(task.end).valueOf();\n        } else if (typeof task.endTime === 'undefined' && task.hasOwnProperty('duration')) {\n          task.endTime = task.startTime + task.duration;\n        }\n        if (typeof task.duration === 'undefined' && task.hasOwnProperty('endTime')) {\n          task.duration = task.endTime - task.startTime;\n        }\n      }\n      return tasks;\n    },\n\n    /**\n     * Map tasks\n     *\n     * @param {Array} tasks\n     * @param {Object} options\n     */\n    mapTasks(tasks, options) {\n      for (let [index, task] of tasks.entries()) {\n        tasks[index] = {\n          ...task,\n          id: task[options.taskMapping.id],\n          start: task[options.taskMapping.start],\n          label: task[options.taskMapping.label],\n          duration: task[options.taskMapping.duration],\n          progress: task[options.taskMapping.progress],\n          type: task[options.taskMapping.type],\n          style: task[options.taskMapping.style],\n          collapsed: task[options.taskMapping.collapsed],\n        };\n      }\n      return tasks;\n    },\n\n    /**\n     * Initialize component\n     */\n    initialize(itsUpdate = '') {\n      let options = mergeDeep({}, this.state.options, getOptions(this.options), this.options);\n      let tasks = this.mapTasks(this.tasks, options);\n      if (Object.keys(this.state.dynamicStyle).length === 0) {\n        this.initializeStyle();\n      }\n      dayjs.locale(options.locale, null, true);\n      dayjs.locale(options.locale.name);\n      if (typeof options.taskList === 'undefined') {\n        options.taskList = {};\n      }\n      options.taskList.columns = options.taskList.columns.map((column, index) => {\n        column.thresholdPercent = 100;\n        column.widthFromPercentage = 0;\n        column.finalWidth = 0;\n        if (typeof column.height === 'undefined') {\n          column.height = 0;\n        }\n        if (typeof column.style === 'undefined') {\n          column.style = {};\n        }\n        column._id = `${index}-${column.label}`;\n        return column;\n      });\n      this.state.options = options;\n      tasks = this.fillTasks(tasks);\n      this.state.tasksById = this.resetTaskTree(tasks);\n      this.state.taskTree = this.makeTaskTree(this.state.rootTask, tasks);\n      this.state.tasks = this.state.taskTree.allChildren.map((childId) => this.getTask(childId));\n      this.calculateTaskListColumnsDimensions();\n      this.state.options.scrollBarHeight = this.getScrollBarHeight();\n      this.state.options.outerHeight = this.state.options.height + this.state.options.scrollBarHeight;\n      this.globalOnResize();\n    },\n\n    /**\n     * Initialize style\n     */\n    initializeStyle() {\n      this.state.dynamicStyle = mergeDeep({}, prepareStyle(this.dynamicStyle), this.dynamicStyle);\n    },\n\n    /**\n     * Get calendar rows outer height\n     *\n     * @returns {int}\n     */\n    getCalendarHeight() {\n      return this.state.options.calendar.height + this.state.options.calendar.strokeWidth;\n    },\n\n    /**\n     * Get maximal level of nested task children\n     *\n     * @returns {int}\n     */\n    getMaximalLevel() {\n      let maximalLevel = 0;\n      this.state.tasks.forEach((task) => {\n        if (task.parents.length > maximalLevel) {\n          maximalLevel = task.parents.length;\n        }\n      });\n      return maximalLevel - 1;\n    },\n\n    /**\n     * Get maximal expander width - to calculate straight task list text\n     *\n     * @returns {int}\n     */\n    getMaximalExpanderWidth() {\n      return (\n        this.getMaximalLevel() * this.state.options.taskList.expander.padding +\n        this.state.options.taskList.expander.margin\n      );\n    },\n\n    /**\n     * Synchronize scrollTop property when row height is changed\n     */\n    syncScrollTop() {\n      if (\n        this.state.refs.taskListItems &&\n        this.state.refs.chartGraph.scrollTop !== this.state.refs.taskListItems.scrollTop\n      ) {\n        this.state.options.scroll.top =\n          this.state.refs.taskListItems.scrollTop =\n          this.state.refs.chartScrollContainerVertical.scrollTop =\n            this.state.refs.chartGraph.scrollTop;\n      }\n    },\n\n    /**\n     * Calculate task list columns dimensions\n     */\n    calculateTaskListColumnsDimensions() {\n      let final = 0;\n      let percentage = 0;\n      for (let column of this.state.options.taskList.columns) {\n        if (column.expander) {\n          column.widthFromPercentage =\n            ((this.getMaximalExpanderWidth() + column.width) / 100) * this.state.options.taskList.percent;\n        } else {\n          column.widthFromPercentage = (column.width / 100) * this.state.options.taskList.percent;\n        }\n        percentage += column.widthFromPercentage;\n        column.finalWidth = (column.thresholdPercent * column.widthFromPercentage) / 100;\n        final += column.finalWidth;\n        column.height = this.getTaskHeight() - this.style['grid-line-horizontal']['stroke-width'];\n      }\n      this.state.options.taskList.widthFromPercentage = percentage;\n      this.state.options.taskList.finalWidth = final;\n    },\n\n    /**\n     * Reset task tree - which is used to create tree like structure inside task list\n     */\n    resetTaskTree(tasks) {\n      this.$set(this.state, 'rootTask', {\n        id: null,\n        label: 'root',\n        children: [],\n        allChildren: [],\n        parents: [],\n        parent: null,\n        __root: true,\n      });\n      const tasksById = {};\n      for (let i = 0, len = tasks.length; i < len; i++) {\n        let current = tasks[i];\n        current.children = [];\n        current.allChildren = [];\n        current.parent = null;\n        current.parents = [];\n        tasksById[current.id] = current;\n      }\n      return tasksById;\n    },\n\n    /**\n     * Make task tree, after reset - look above\n     *\n     * @param {object} task\n     * @returns {object} tasks with children and parents\n     */\n    makeTaskTree(task, tasks) {\n      for (let i = 0, len = tasks.length; i < len; i++) {\n        let current = tasks[i];\n        if (current.parentId === task.id) {\n          if (task.parents.length) {\n            task.parents.forEach((parent) => current.parents.push(parent));\n          }\n          if (!task.propertyIsEnumerable('__root')) {\n            current.parents.push(task.id);\n            current.parent = task.id;\n          } else {\n            current.parents = [];\n            current.parent = null;\n          }\n          current = this.makeTaskTree(current, tasks);\n          task.allChildren.push(current.id);\n          task.children.push(current.id);\n          current.allChildren.forEach((childId) => task.allChildren.push(childId));\n        }\n      }\n      return task;\n    },\n\n    /**\n     * go to current time line\n     */\n    goCurrentTime() {\n      this.$emit('recenterPosition');\n    },\n\n    /**\n     * Get task by id\n     *\n     * @param {any} taskId\n     * @returns {object|null} task\n     */\n    getTask(taskId) {\n      if (typeof this.state.tasksById[taskId] !== 'undefined') {\n        return this.state.tasksById[taskId];\n      }\n      return null;\n    },\n\n    getTaskMapping() {\n      return this.state.options.taskMapping;\n    },\n\n    /**\n     * 更新任务\n     * 由于任务时间特殊，因此该方法无法更改时间\n     *\n     * @param {any} taskId 任务id\n     * @param {object|null} data 修改后的 TaskRow\n     * @param {object|null} props 配置选项\n     */\n    updateTask(taskId, data) {\n      if (!taskId) {\n        return;\n      }\n\n      let taskRow = this.getTask(taskId);\n\n      Object.keys(data).forEach((item) => {\n        // 过滤\n        if (['id', 'start', 'startTime', 'end', 'endTime'].includes(item)) {\n          return;\n        }\n\n        if (typeof taskRow === 'object' && taskRow.hasOwnProperty('__ob__') && typeof taskRow[item] !== 'undefined') {\n          taskRow[item] = data[item];\n        }\n      });\n\n      return taskRow;\n    },\n\n    /**\n     * 更新任务时间\n     * @param {*} taskId\n     * @param {number} start\n     * @param {number} end\n     */\n    updateTaskTime(taskId, start, end) {\n      if (!taskId) {\n        return;\n      }\n\n      let taskRow = this.getTask(taskId);\n\n      taskRow['startTime'] = start;\n      taskRow['start'] = start;\n      taskRow['endTime'] = end;\n      taskRow['end'] = end;\n\n      taskRow['duration'] = taskRow['endTime'] - taskRow['startTime'];\n    },\n\n    /**\n     * Get children tasks for specified taskId\n     *\n     * @param {any} taskId\n     * @returns {array} children\n     */\n    getChildren(taskId) {\n      return this.state.tasks.filter((task) => task.parent === taskId);\n    },\n\n    /**\n     * Is task visible\n     *\n     * @param {Number|String|Task} task\n     */\n    isTaskVisible(task) {\n      if (typeof task === 'number' || typeof task === 'string') {\n        task = this.getTask(task);\n      }\n      for (let i = 0, len = task.parents.length; i < len; i++) {\n        if (this.getTask(task.parents[i]).collapsed) {\n          return false;\n        }\n      }\n      return true;\n    },\n\n    /**\n     * Get svg\n     *\n     * @returns {string} html svg image of gantt\n     */\n    getSVG() {\n      return this.state.options.mainView.outerHTML;\n    },\n\n    /**\n     * Get image\n     *\n     * @param {string} type image format\n     * @returns {Promise} when resolved returns base64 image string of gantt\n     */\n    getImage(type = 'image/png') {\n      return new Promise((resolve) => {\n        const img = new Image();\n        img.onload = () => {\n          const canvas = document.createElement('canvas');\n          canvas.width = this.state.options.mainView.clientWidth;\n          canvas.height = this.state.options.rowsHeight;\n          canvas.getContext('2d').drawImage(img, 0, 0);\n          resolve(canvas.toDataURL(type));\n        };\n        img.src = 'data:image/svg+xml,' + encodeURIComponent(this.getSVG());\n      });\n    },\n\n    /**\n     * Get gantt total height\n     *\n     * @returns {number}\n     */\n    getHeight(visibleTasks, outer = false) {\n      let height =\n        visibleTasks.length * (this.state.options.row.height + this.state.options.chart.grid.horizontal.gap * 2) +\n        this.state.options.calendar.height +\n        this.state.options.calendar.strokeWidth +\n        this.state.options.calendar.gap;\n      if (outer) {\n        height += this.state.options.scrollBarHeight;\n      }\n      return height;\n    },\n\n    /**\n     * Get one task height\n     *\n     * @returns {number}\n     */\n    getTaskHeight(withStroke = false) {\n      if (withStroke) {\n        return (\n          this.state.options.row.height +\n          this.state.options.chart.grid.horizontal.gap * 2 +\n          this.style['grid-line-horizontal']['stroke-width']\n        );\n      }\n      return this.state.options.row.height + this.state.options.chart.grid.horizontal.gap * 2;\n    },\n\n    /**\n     * Get specified tasks height\n     *\n     * @returns {number}\n     */\n    getTasksHeight(visibleTasks) {\n      return visibleTasks.length * this.getTaskHeight();\n    },\n\n    /**\n     * Convert time (in milliseconds) to pixel offset inside chart\n     *\n     * @param {int} ms\n     * @returns {number}\n     */\n    timeToPixelOffsetX(ms) {\n      let x = ms - this.state.options.times.firstTime;\n      if (x) {\n        x = x / this.state.options.times.timePerPixel;\n      }\n      return x;\n    },\n\n    /**\n     * Convert pixel offset inside chart to corresponding time offset in milliseconds\n     *\n     * @param {number} pixelOffsetX\n     * @returns {int} milliseconds\n     */\n    pixelOffsetXToTime(pixelOffsetX) {\n      let offset = pixelOffsetX + this.style['grid-line-vertical']['stroke-width'] / 2;\n      return offset * this.state.options.times.timePerPixel + this.state.options.times.firstTime;\n    },\n\n    /**\n     * Determine if element is inside current view port\n     *\n     * @param {number} x - element placement\n     * @param {number} width - element width\n     * @param {int} buffer - or threshold, if element is outside viewport but offset from view port is below this value return true\n     * @returns {boolean}\n     */\n    isInsideViewPort(x, width, buffer = 5000) {\n      return (\n        (x + width + buffer >= this.state.options.scroll.chart.left &&\n          x - buffer <= this.state.options.scroll.chart.right) ||\n        (x - buffer <= this.state.options.scroll.chart.left &&\n          x + width + buffer >= this.state.options.scroll.chart.right)\n      );\n    },\n\n    /**\n     * Chart scroll event handler\n     *\n     * @param {event} ev\n     */\n    onScrollChart(ev) {\n      this._onScrollChart(\n        this.state.refs.chartScrollContainerHorizontal.scrollLeft,\n        this.state.refs.chartScrollContainerVertical.scrollTop\n      );\n    },\n\n    /**\n     * After same as above but with different arguments - normalized\n     *\n     * @param {number} left\n     * @param {number} top\n     */\n    _onScrollChart(left, top) {\n      if (this.state.options.scroll.chart.left === left && this.state.options.scroll.chart.top === top) {\n        return;\n      }\n      const chartContainerWidth = this.state.refs.chartContainer.clientWidth;\n      this.state.options.scroll.chart.left = left;\n      this.state.options.scroll.chart.right = left + chartContainerWidth;\n      this.state.options.scroll.chart.percent = (left / this.state.options.times.totalViewDurationPx) * 100;\n      this.state.options.scroll.chart.top = top;\n      this.state.options.scroll.chart.time = this.pixelOffsetXToTime(left);\n      this.state.options.scroll.chart.timeCenter = this.pixelOffsetXToTime(left + chartContainerWidth / 2);\n      this.state.options.scroll.chart.dateTime.left = dayjs(this.state.options.scroll.chart.time).valueOf();\n      this.state.options.scroll.chart.dateTime.right = dayjs(\n        this.pixelOffsetXToTime(left + this.state.refs.chart.clientWidth)\n      ).valueOf();\n      this.scrollTo(left, top);\n    },\n\n    /**\n     * Scroll current chart to specified time (in milliseconds)\n     *\n     * @param {int} time\n     */\n    scrollToTime(time) {\n      let pos = this.timeToPixelOffsetX(time);\n      const chartContainerWidth = this.state.refs.chartContainer.clientWidth;\n      pos = pos - chartContainerWidth / 2;\n      if (pos > this.state.options.width) {\n        pos = this.state.options.width - chartContainerWidth;\n      }\n      this.scrollTo(pos);\n    },\n\n    /**\n     * Scroll chart or task list to specified pixel values\n     *\n     * @param {number|null} left\n     * @param {number|null} top\n     */\n    scrollTo(left = null, top = null) {\n      if (left !== null) {\n        this.state.refs.chartCalendarContainer.scrollLeft = left;\n        this.state.refs.chartGraphContainer.scrollLeft = left;\n        this.state.refs.chartScrollContainerHorizontal.scrollLeft = left;\n        this.state.options.scroll.left = left;\n      }\n      if (top !== null) {\n        this.state.refs.chartScrollContainerVertical.scrollTop = top;\n        this.state.refs.chartGraph.scrollTop = top;\n        this.state.refs.taskListItems.scrollTop = top;\n        this.state.options.scroll.top = top;\n        this.syncScrollTop();\n      }\n    },\n\n    /**\n     * After some actions like time zoom change we need to recompensate scroll position\n     * so as a result everything will be in same place\n     */\n    fixScrollPos() {\n      this.scrollToTime(this.state.options.scroll.chart.timeCenter);\n    },\n\n    /**\n     * Mouse wheel event handler\n     */\n    onWheelChart(ev) {\n      if (!ev.shiftKey && ev.deltaX === 0) {\n        let top = this.state.options.scroll.top + ev.deltaY;\n        const chartClientHeight = this.state.options.rowsHeight;\n        const scrollHeight = this.state.refs.chartGraph.scrollHeight - chartClientHeight;\n        if (top < 0) {\n          top = 0;\n        } else if (top > scrollHeight) {\n          top = scrollHeight;\n        }\n        this.scrollTo(null, top);\n      } else if (ev.shiftKey && ev.deltaX === 0) {\n        let left = this.state.options.scroll.left + ev.deltaY;\n        const chartClientWidth = this.state.refs.chartScrollContainerHorizontal.clientWidth;\n        const scrollWidth = this.state.refs.chartScrollContainerHorizontal.scrollWidth - chartClientWidth;\n        if (left < 0) {\n          left = 0;\n        } else if (left > scrollWidth) {\n          left = scrollWidth;\n        }\n        this.scrollTo(left);\n      } else {\n        let left = this.state.options.scroll.left + ev.deltaX;\n        const chartClientWidth = this.state.refs.chartScrollContainerHorizontal.clientWidth;\n        const scrollWidth = this.state.refs.chartScrollContainerHorizontal.scrollWidth - chartClientWidth;\n        if (left < 0) {\n          left = 0;\n        } else if (left > scrollWidth) {\n          left = scrollWidth;\n        }\n        this.scrollTo(left);\n      }\n    },\n\n    /**\n     * Time zoom change event handler\n     */\n    onTimeZoomChange(timeZoom) {\n      this.state.options.times.timeZoom = timeZoom;\n      this.recalculateTimes();\n      this.calculateSteps();\n      this.fixScrollPos();\n    },\n\n    /**\n     * 获取当前缩放大小\n     */\n    getTimeZoom() {\n      return this.state.options.times.timeZoom;\n    },\n\n    /**\n     * 长按空格键 + 滚轮滚动，放大缩小chart\n     */\n    onKeyDown(event) {\n      if (event.key === 'Control') {\n        this.controlLongPress = true;\n      }\n    },\n    onKeyUp(event) {\n      if (event.key === 'Control') {\n        this.controlLongPress = false;\n      }\n    },\n    onMousewheel(event) {\n      if (this.controlLongPress) {\n        const { wheelDelta } = event;\n        const to = wheelDelta > 0 ? -1 : 1;\n        let timeZoom = this.getTimeZoom() + 1 * to;\n\n        if (timeZoom <= 0) {\n          timeZoom = 1;\n          return;\n        } else if (timeZoom >= 21) {\n          timeZoom = 21;\n        }\n        this.onTimeZoomChange(timeZoom);\n      }\n    },\n\n    /**\n     * Row height change event handler\n     */\n    onRowHeightChange(height) {\n      this.state.options.row.height = height;\n      this.calculateTaskListColumnsDimensions();\n      this.syncScrollTop();\n    },\n\n    /**\n     * Scope change event handler\n     */\n    onScopeChange(value) {\n      this.state.options.scope.before = value;\n      this.state.options.scope.after = value;\n      this.initTimes();\n      this.calculateSteps();\n      this.computeCalendarWidths();\n      this.fixScrollPos();\n    },\n\n    /**\n     * Task list width change event handler\n     */\n    onTaskListWidthChange(value) {\n      this.state.options.taskList.percent = value;\n      this.calculateTaskListColumnsDimensions();\n      this.fixScrollPos();\n    },\n\n    /**\n     * Task list column width change event handler\n     */\n    onTaskListColumnWidthChange() {\n      this.calculateTaskListColumnsDimensions();\n      this.fixScrollPos();\n    },\n\n    /**\n     * Listen to specified event names\n     */\n    initializeEvents() {\n      this.$on('chart-scroll-horizontal', this.onScrollChart);\n      this.$on('chart-scroll-vertical', this.onScrollChart);\n      this.$on('chart-wheel', this.onWheelChart);\n      this.$on('times-timeZoom-change', this.onTimeZoomChange);\n      this.$on('row-height-change', this.onRowHeightChange);\n      this.$on('scope-change', this.onScopeChange);\n      this.$on('taskList-width-change', this.onTaskListWidthChange);\n      this.$on('taskList-column-width-change', this.onTaskListColumnWidthChange);\n    },\n\n    /**\n     * When some action was performed (scale change for example) - recalculate time variables\n     */\n    recalculateTimes() {\n      let max = this.state.options.times.timeScale * 60;\n      let min = this.state.options.times.timeScale;\n      let steps = max / min;\n      let percent = this.state.options.times.timeZoom / 100;\n      this.state.options.times.timePerPixel =\n        this.state.options.times.timeScale * steps * percent + Math.pow(2, this.state.options.times.timeZoom);\n      this.state.options.times.totalViewDurationMs = dayjs(this.state.options.times.lastTime).diff(\n        this.state.options.times.firstTime,\n        'milliseconds'\n      );\n      this.state.options.times.totalViewDurationPx =\n        this.state.options.times.totalViewDurationMs / this.state.options.times.timePerPixel;\n      this.state.options.width =\n        this.state.options.times.totalViewDurationPx + this.style['grid-line-vertical']['stroke-width'];\n    },\n\n    /**\n     * Initialize time variables\n     */\n    initTimes() {\n      this.state.options.times.firstTime = dayjs(this.state.options.times.firstTaskTime)\n        .locale(this.state.options.locale.name)\n        .startOf('day')\n        .subtract(this.state.options.scope.before, 'days')\n        .startOf('day')\n        .valueOf();\n      this.state.options.times.lastTime = dayjs(this.state.options.times.lastTaskTime)\n        .locale(this.state.options.locale.name)\n        .endOf('day')\n        .add(this.state.options.scope.after, 'days')\n        .endOf('day')\n        .valueOf();\n      this.recalculateTimes();\n    },\n\n    /**\n     * Calculate steps\n     * Steps are days by default\n     * Each step contain information about time offset and pixel offset of this time inside gantt chart\n     */\n    calculateSteps() {\n      const steps = [];\n      const lastMs = dayjs(this.state.options.times.lastTime).valueOf();\n      const currentDate = dayjs(this.state.options.times.firstTime);\n      steps.push({\n        time: currentDate.valueOf(),\n        offset: {\n          ms: 0,\n          px: 0,\n        },\n      });\n      for (\n        let currentDate = dayjs(this.state.options.times.firstTime)\n          .add(1, this.state.options.times.stepDuration)\n          .startOf('day');\n        currentDate.valueOf() <= lastMs;\n        currentDate = currentDate.add(1, this.state.options.times.stepDuration).startOf('day')\n      ) {\n        const offsetMs = currentDate.diff(this.state.options.times.firstTime, 'milliseconds');\n        const offsetPx = offsetMs / this.state.options.times.timePerPixel;\n        const step = {\n          time: currentDate.valueOf(),\n          offset: {\n            ms: offsetMs,\n            px: offsetPx,\n          },\n        };\n        const previousStep = steps[steps.length - 1];\n        previousStep.width = {\n          ms: offsetMs - previousStep.offset.ms,\n          px: offsetPx - previousStep.offset.px,\n        };\n        steps.push(step);\n      }\n      const lastStep = steps[steps.length - 1];\n      lastStep.width = {\n        ms: this.state.options.times.totalViewDurationMs - lastStep.offset.ms,\n        px: this.state.options.times.totalViewDurationPx - lastStep.offset.px,\n      };\n      this.state.options.times.steps = steps;\n    },\n\n    /**\n     * Calculate calendar widths - when scale was changed for example\n     */\n    computeCalendarWidths() {\n      this.computeDayWidths();\n      this.computeHourWidths();\n      this.computeMonthWidths();\n    },\n\n    /**\n     * Compute width of calendar hours column widths basing on text widths\n     */\n    computeHourWidths() {\n      const style = {\n        ...this.style['calendar-row-text'],\n        ...this.style['calendar-row-text--hour'],\n      };\n      this.state.ctx.font = style['font-size'] + ' ' + style['font-family'];\n      const localeName = this.state.options.locale.name;\n      let currentDate = dayjs('2018-01-01T00:00:00').locale(localeName); // any date will be good for hours\n      let maxWidths = this.state.options.calendar.hour.maxWidths;\n      if (maxWidths.length) {\n        return;\n      }\n      for (let formatName in this.state.options.calendar.hour.format) {\n        maxWidths[formatName] = 0;\n      }\n      for (let hour = 0; hour < 24; hour++) {\n        let widths = { hour };\n        for (let formatName in this.state.options.calendar.hour.format) {\n          const hourFormatted = this.state.options.calendar.hour.format[formatName](currentDate);\n          this.state.options.calendar.hour.formatted[formatName].push(hourFormatted);\n          widths[formatName] = this.state.ctx.measureText(hourFormatted).width;\n        }\n        this.state.options.calendar.hour.widths.push(widths);\n        for (let formatName in this.state.options.calendar.hour.format) {\n          if (widths[formatName] > maxWidths[formatName]) {\n            maxWidths[formatName] = widths[formatName];\n          }\n        }\n        currentDate = currentDate.add(1, 'hour');\n      }\n    },\n\n    /**\n     * Compute calendar days column widths basing on text widths\n     */\n    computeDayWidths() {\n      const style = {\n        ...this.style['calendar-row-text'],\n        ...this.style['calendar-row-text--day'],\n      };\n      this.state.ctx.font = style['font-size'] + ' ' + style['font-family'];\n      const localeName = this.state.options.locale.name;\n      let currentDate = dayjs(this.state.options.times.steps[0].time).locale(localeName);\n      let maxWidths = this.state.options.calendar.day.maxWidths;\n      this.state.options.calendar.day.widths = [];\n      Object.keys(this.state.options.calendar.day.format).forEach((formatName) => {\n        maxWidths[formatName] = 0;\n      });\n      for (let day = 0, daysLen = this.state.options.times.steps.length; day < daysLen; day++) {\n        const widths = {\n          day,\n        };\n        Object.keys(this.state.options.calendar.day.format).forEach((formatName) => {\n          widths[formatName] = this.state.ctx.measureText(\n            this.state.options.calendar.day.format[formatName](currentDate)\n          ).width;\n        });\n        this.state.options.calendar.day.widths.push(widths);\n        Object.keys(this.state.options.calendar.day.format).forEach((formatName) => {\n          if (widths[formatName] > maxWidths[formatName]) {\n            maxWidths[formatName] = widths[formatName];\n          }\n        });\n        currentDate = currentDate.add(1, 'day');\n      }\n    },\n\n    /**\n     * Months count\n     *\n     * @description Returns number of different months in specified time range\n     *\n     * @param {number} fromTime - date in ms\n     * @param {number} toTime - date in ms\n     *\n     * @returns {number} different months count\n     */\n    monthsCount(fromTime, toTime) {\n      if (fromTime > toTime) {\n        return 0;\n      }\n      let currentMonth = dayjs(fromTime);\n      let previousMonth = currentMonth.clone();\n      let monthsCount = 1;\n      while (currentMonth.valueOf() <= toTime) {\n        currentMonth = currentMonth.add(1, 'day');\n        if (previousMonth.month() !== currentMonth.month()) {\n          monthsCount++;\n        }\n        previousMonth = currentMonth.clone();\n      }\n      return monthsCount;\n    },\n\n    /**\n     * Compute month calendar columns widths basing on text widths\n     */\n    computeMonthWidths() {\n      const style = {\n        ...this.style['calendar-row-text'],\n        ...this.style['calendar-row-text--month'],\n      };\n      this.state.ctx.font = style['font-size'] + ' ' + style['font-family'];\n      let maxWidths = this.state.options.calendar.month.maxWidths;\n      this.state.options.calendar.month.widths = [];\n      Object.keys(this.state.options.calendar.month.format).forEach((formatName) => {\n        maxWidths[formatName] = 0;\n      });\n      const localeName = this.state.options.locale.name;\n      let currentDate = dayjs(this.state.options.times.firstTime).locale(localeName);\n      const monthsCount = this.monthsCount(this.state.options.times.firstTime, this.state.options.times.lastTime);\n      for (let month = 0; month < monthsCount; month++) {\n        const widths = {\n          month,\n        };\n        Object.keys(this.state.options.calendar.month.format).forEach((formatName) => {\n          widths[formatName] = this.state.ctx.measureText(\n            this.state.options.calendar.month.format[formatName](currentDate)\n          ).width;\n        });\n        this.state.options.calendar.month.widths.push(widths);\n        Object.keys(this.state.options.calendar.month.format).forEach((formatName) => {\n          if (widths[formatName] > maxWidths[formatName]) {\n            maxWidths[formatName] = widths[formatName];\n          }\n        });\n        currentDate = currentDate.add(1, 'month');\n      }\n    },\n\n    /**\n     * Prepare time and date variables for gantt\n     */\n    prepareDates() {\n      let firstTaskTime = Number.MAX_SAFE_INTEGER;\n      let lastTaskTime = 0;\n      for (let index = 0, len = this.state.tasks.length; index < len; index++) {\n        let task = this.state.tasks[index];\n        if (task.startTime < firstTaskTime) {\n          firstTaskTime = task.startTime;\n        }\n        if (task.startTime + task.duration > lastTaskTime) {\n          lastTaskTime = task.startTime + task.duration;\n        }\n      }\n      this.state.options.times.firstTaskTime = firstTaskTime;\n      this.state.options.times.lastTaskTime = lastTaskTime;\n      this.state.options.times.firstTime = dayjs(firstTaskTime)\n        .locale(this.state.options.locale.name)\n        .startOf('day')\n        .subtract(this.state.options.scope.before, 'days')\n        .startOf('day')\n        .valueOf();\n      this.state.options.times.lastTime = dayjs(lastTaskTime)\n        .locale(this.state.options.locale.name)\n        .endOf('day')\n        .add(this.state.options.scope.after, 'days')\n        .endOf('day')\n        .valueOf();\n    },\n\n    /**\n     * Setup and calculate everything\n     */\n    setup(itsUpdate = '') {\n      this.initialize(itsUpdate);\n      this.prepareDates();\n      this.initTimes();\n      this.calculateSteps();\n      this.computeCalendarWidths();\n      this.state.options.taskList.width = this.state.options.taskList.columns.reduce(\n        (prev, current) => {\n          return { width: prev.width + current.width };\n        },\n        { width: 0 }\n      ).width;\n    },\n\n    /**\n     * Global resize event (from window.addEventListener)\n     */\n    globalOnResize() {\n      if (typeof this.$el === 'undefined' || !this.$el) {\n        return;\n      }\n      this.state.options.clientWidth = this.$el.clientWidth;\n      if (\n        this.state.options.taskList.widthFromPercentage >\n        (this.state.options.clientWidth / 100) * this.state.options.taskList.widthThreshold\n      ) {\n        const diff =\n          this.state.options.taskList.widthFromPercentage -\n          (this.state.options.clientWidth / 100) * this.state.options.taskList.widthThreshold;\n        let diffPercent = 100 - (diff / this.state.options.taskList.widthFromPercentage) * 100;\n        if (diffPercent < 0) {\n          diffPercent = 0;\n        }\n        this.state.options.taskList.columns.forEach((column) => {\n          column.thresholdPercent = diffPercent;\n        });\n      } else {\n        this.state.options.taskList.columns.forEach((column) => {\n          column.thresholdPercent = 100;\n        });\n      }\n      this.calculateTaskListColumnsDimensions();\n      this.$emit('calendar-recalculate');\n      this.syncScrollTop();\n    },\n  },\n\n  computed: {\n    /**\n     * Get visible tasks\n     * Very important method which will bring us only those tasks that are visible inside gantt chart\n     * For example when task is collapsed - children of this task are not visible - we should not render them\n     */\n    visibleTasks() {\n      const visibleTasks = this.state.tasks.filter((task) => this.isTaskVisible(task));\n      const maxRows = visibleTasks.slice(0, this.state.options.maxRows);\n      this.state.options.rowsHeight = this.getTasksHeight(maxRows);\n      let heightCompensation = 0;\n      if (this.state.options.maxHeight && this.state.options.rowsHeight > this.state.options.maxHeight) {\n        heightCompensation = this.state.options.rowsHeight - this.state.options.maxHeight;\n        this.state.options.rowsHeight = this.state.options.maxHeight;\n      }\n      this.state.options.height = this.getHeight(maxRows) - heightCompensation;\n      this.state.options.allVisibleTasksHeight = this.getTasksHeight(visibleTasks);\n      this.state.options.outerHeight = this.getHeight(maxRows, true) - heightCompensation;\n      let len = visibleTasks.length;\n      for (let index = 0; index < len; index++) {\n        let task = visibleTasks[index];\n        task.width =\n          task.duration / this.state.options.times.timePerPixel - this.style['grid-line-vertical']['stroke-width'];\n        if (task.width < 0) {\n          task.width = 0;\n        }\n        task.height = this.state.options.row.height;\n        task.x = this.timeToPixelOffsetX(task.startTime);\n        task.y =\n          (this.state.options.row.height + this.state.options.chart.grid.horizontal.gap * 2) * index +\n          this.state.options.chart.grid.horizontal.gap;\n      }\n      return visibleTasks;\n    },\n\n    /**\n     * Style shortcut\n     */\n    style() {\n      return this.state.dynamicStyle;\n    },\n\n    /**\n     * Get columns and compute dimensions on the fly\n     */\n    getTaskListColumns() {\n      this.calculateTaskListColumnsDimensions();\n      return this.state.options.taskList.columns;\n    },\n\n    /**\n     * Tasks used for communicate with parent component\n     */\n    outputTasks() {\n      return this.state.tasks;\n    },\n\n    /**\n     * Options used to communicate with parent component\n     */\n    outputOptions() {\n      return this.state.options;\n    },\n  },\n\n  /**\n   * Watch tasks after gantt instance is created and react when we have new kids on the block\n   */\n  created() {\n    this.initializeEvents();\n    this.setup();\n    this.state.unwatchTasks = this.$watch(\n      'tasks',\n      (tasks) => {\n        const notEqual = notEqualDeep(tasks, this.outputTasks);\n        if (notEqual) {\n          this.setup('tasks');\n        }\n      },\n      { deep: true }\n    );\n    this.state.unwatchOptions = this.$watch(\n      'options',\n      (opts) => {\n        const notEqual = notEqualDeep(opts, this.outputOptions);\n        if (notEqual) {\n          this.setup('options');\n        }\n      },\n      { deep: true }\n    );\n    this.state.unwatchStyle = this.$watch(\n      'dynamicStyle',\n      (style) => {\n        const notEqual = notEqualDeep(style, this.dynamicStyle);\n        if (notEqual) {\n          this.initializeStyle();\n        }\n      },\n      { deep: true, immediate: true }\n    );\n\n    this.state.unwatchOutputTasks = this.$watch(\n      'outputTasks',\n      (tasks) => {\n        this.$emit(\n          'tasks-changed',\n          tasks.map((task) => task)\n        );\n      },\n      { deep: true }\n    );\n    this.state.unwatchOutputOptions = this.$watch(\n      'outputOptions',\n      (options) => {\n        this.$emit('options-changed', mergeDeep({}, options));\n      },\n      { deep: true }\n    );\n    this.state.unwatchOutputStyle = this.$watch(\n      'style',\n      (style) => {\n        this.$emit('dynamic-style-changed', mergeDeep({}, style));\n      },\n      { deep: true }\n    );\n\n    this.$root.$emit('gantt-elastic-created', this);\n    this.$emit('created', this);\n  },\n\n  /**\n   * Emit before-mount event\n   */\n  beforeMount() {\n    this.$emit('before-mount', this);\n  },\n\n  /**\n   * Emit ready/mounted events and deliver this gantt instance to outside world when needed\n   */\n  mounted() {\n    this.state.options.clientWidth = this.$el.clientWidth;\n    this.state.resizeObserver = new ResizeObserver((entries, observer) => {\n      this.globalOnResize();\n    });\n    this.state.parentNode = this.$el.parentNode;\n    this.state.resizeObserver.observe(this.$el.parentNode);\n    this.globalOnResize();\n    this.$emit('ready', this);\n    this.$root.$emit('gantt-elastic-mounted', this);\n    this.$emit('mounted', this);\n    this.$root.$emit('gantt-elastic-ready', this);\n  },\n\n  /**\n   * Emit event when data was changed and before update (you can cleanup dom events here for example)\n   */\n  beforeUpdate() {\n    this.$emit('before-update');\n  },\n\n  /**\n   * Emit event when gantt-elastic view was updated\n   */\n  updated() {\n    this.$nextTick(() => {\n      this.$emit('updated');\n    });\n  },\n\n  /**\n   * Before destroy event - clean up\n   */\n  beforeDestroy() {\n    this.state.resizeObserver.unobserve(this.state.parentNode);\n    this.state.unwatchTasks();\n    this.state.unwatchOptions();\n    this.state.unwatchStyle();\n    this.state.unwatchOutputTasks();\n    this.state.unwatchOutputOptions();\n    this.state.unwatchOutputStyle();\n    this.$emit('before-destroy');\n  },\n\n  /**\n   * Emit event after gantt-elastic was destroyed\n   */\n  destroyed() {\n    this.$emit('destroyed');\n  },\n};\nexport default GanttElastic;\n</script>\n\n<style>\n[class^='gantt-elastic'],\n[class*=' gantt-elastic'] {\n  box-sizing: border-box;\n}\n.gantt-elastic:focus-visible {\n  outline: none;\n}\n.gantt-elastic__main-view svg {\n  display: block;\n}\n.gantt-elastic__grid-horizontal-line,\n.gantt-elastic__grid-vertical-line {\n  stroke: #a0a0a0;\n  stroke-width: 1;\n}\nforeignObject > * {\n  margin: 0px;\n}\n.gantt-elastic .p-2 {\n  padding: 10rem;\n}\n.gantt-elastic__main-view-main-container,\n.gantt-elastic__main-view-container {\n  overflow: hidden;\n  max-width: 100%;\n}\n.gantt-elastic__task-list-header-column:last-of-type {\n  border-right: 1px solid #00000050;\n}\n.gantt-elastic__task-list-item:last-of-type {\n  border-bottom: 1px solid #00000050;\n}\n.gantt-elastic__task-list-item-value-wrapper:hover {\n  overflow: visible !important;\n}\n.gantt-elastic__task-list-item-value-wrapper:hover > .gantt-elastic__task-list-item-value-container {\n  position: relative;\n  overflow: visible !important;\n}\n.gantt-elastic__task-list-item-value-wrapper:hover > .gantt-elastic__task-list-item-value {\n  position: absolute;\n}\n</style>\n", "import mod from \"-!../node_modules/vue-loader/lib/index.js??vue-loader-options!./GanttElastic.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/vue-loader/lib/index.js??vue-loader-options!./GanttElastic.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./GanttElastic.vue?vue&type=template&id=1ca074be&\"\nimport script from \"./GanttElastic.vue?vue&type=script&lang=js&\"\nexport * from \"./GanttElastic.vue?vue&type=script&lang=js&\"\nimport style0 from \"./GanttElastic.vue?vue&type=style&index=0&id=1ca074be&prod&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}