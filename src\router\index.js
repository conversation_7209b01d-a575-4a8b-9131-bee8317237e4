import BasicLayout from '@/layouts/BasicLayout.vue'
import BasicLayoutday from '@/layouts/BasicLayoutday.vue'
import Vue from 'vue'
import VueRouter from 'vue-router'
import HomePage from '@/views/homepage/index.vue'
import Index from '@/views/Index.vue'
import Mind from '@/views/mind/index.vue'
import MindList from '@/views/mindList/Index.vue'
import Article from '@/views/editor/pages/article.vue'
import BookNote from '@/views/editor/pages/bookNote.vue'
import Draft from '@/views/editor/pages/draft.vue'
import Mp3List from '@/views/mp3List/index.vue'
import TopIndex from '@/views/topic/index.vue'
import TopicDetail from '@/views/topic/detail.vue'
import EditorIndex from '@/views/editor/index.vue'
import EditorNote from '@/views/editor/pages/note.vue'
import EditorPdf from '@/views/editor/pages/pdf.vue'
import EditorDocx from '@/views/editor/pages/docx.vue'
import EditorPdfEmbed from '@/views/editor/pages/pdfEmbed.vue'
import BookComment from '@/views/editor/pages/bookComment.vue'
import Epub from '@/views/editor/pages/epub.vue'
import Bubble from '@/views/bubble/Index.vue'
import Invite from '@/views/invite/index.vue'
import CorporateAccount from '@/views/corporateAccount/index.vue'
import Personal from '@/views/personal/index.vue'
import Login from '@/views/login/index.vue'
import Invitation from '@/views/invitation/index.vue'
import PaperMatrix from '@/views/papermatrix/index.vue'
import WorkReadTask from '@/views/workreadTask/index.vue'
import DetailIndex from '@/views/detail/index.vue'
import DailyUpdates from '@/views/detail/dailyupdates/index.vue'
import PersonalBookReview from '@/views/detail/personalbookreview/index.vue'
import PublicBookReview from '@/views/detail/publicbookreview/index.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Homepage',
    component: HomePage
  },
  {
    path: '/index',
    name: 'Index',
    component: Index,
    meta: { requiresAuth: true },
  },
  {
    path: '/mind/:id',
    name: 'Mind',
    component: Mind
  },
  {
    path: '/mindList',
    name: 'MindList',
    component: MindList
  },
  {
    path: '/topic',
    name: 'Topic',
    redirect: '/topic/index',
    component: BasicLayout,
    children: [
      {
        path: '/topic/index',
        component: TopIndex,
        meta: { requiresAuth: true }
      },
      {
        path: '/topic/detail',
        component: TopicDetail,
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/editor',
    name: 'Editor',
    component: EditorIndex,
    redirect: '/editor/note',
    meta: { requiresAuth: true }, // 表示该路由需要进行登录验证
    children: [
      {
        path: '/editor/note',
        name: 'note',
        component: EditorNote
      },
      {
        path: '/editor/pdf',
        name: 'pdf',
        component: EditorPdf
      },
      {
        path: '/editor/docx',
        name: 'docx',
        component: EditorDocx
      },
      {
        path: '/editor/pdfEmbed',
        name: 'pdfEmbed',
        component: EditorPdfEmbed
      },
      {
        path: '/editor/bookComment',
        name: 'bookComment',
        component: BookComment
      },
      {
        path: '/editor/article',
        name: 'article',
        component: Article
      },
      {
        path: '/editor/bookNote',
        name: 'bookNote',
        component: BookNote
      },
      {
        path: '/editor/draft',
        name: 'draft',
        component: Draft
      },
      {
        path: '/editor/epub',
        name: 'epub',
        component: Epub
      },
      {
        path: '/editor/mp3List',
        name: 'mp3List',
        component: Mp3List,
        meta: { requiresAuth: true } // 表示该路由需要进行登录验证
      },
      {
        path: '/editor/bubble',
        name: 'bubble',
        component: Bubble
      }
    ]
  },
  {
    path: '/invite/:id',
    name: 'Invite',
    component: Invite
  },
  {
    path: '/corporateAccount',
    name: 'CorporateAccount',
    component: CorporateAccount,
    meta: { requiresAuth: true } // 表示该路由需要进行登录验证
  },
  {
    path: '/personal',
    name: 'Personal',
    component: Personal,
    meta: { requiresAuth: true } // 表示该路由需要进行登录验证
  },
  {
    path: '/login',
    name: 'Login',
    redirect: '/login',
    component: BasicLayoutday,
    children: [
      {
        path: '/login',
        component: Login,
        meta: { requiresAuth: false } // 不需要进行身份验证
      }
    ]
  },
  {
    path: '/invitation/:date',
    name: 'Invitation',
    redirect: '/invitation',
    component: BasicLayoutday,
    children: [
      {
        path: '/invitation/:date',
        component: Invitation
      }
    ]
  },
  {
    path: '/papermatrix',
    name: 'PaperMatrix',
    component: PaperMatrix,
    meta: { requiresAuth: true } // 表示该路由需要进行登录验证
  },
  {
    path: '/workreadTask',
    name: 'WorkReadTask',
    component: WorkReadTask,
    meta: { requiresAuth: true } // 表示该路由需要进行登录验证
  },
  {
    path: '/detail',
    component: DetailIndex,
    meta: { requiresAuth: false }, // 不需要进行身份验证
    children: [
      {
        path: 'dailyupdates',
        component: DailyUpdates,
        meta: { requiresAuth: false } // 不需要进行身份验证
      },
      {
        path: 'personalbookreview',
        component: PersonalBookReview,
        meta: { requiresAuth: false } // 不需要进行身份验证
      },
      {
        path: 'publicbookreview',
        component: PublicBookReview,
        meta: { requiresAuth: false } // 不需要进行身份验证
      }

    ]

  }
]

const router = new VueRouter({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// router.beforeEach((to, from, next) => {
//   const token = localStorage.getItem('authorization')
//   if (!token) {
//     // 记录当前路由信息
//     store.commit('setLastRoute', {
//       path: to.path,
//       query: to.query,
//       params: to.params
//     })
//     next({ name: 'Login' })
//   } else {
//     next()
//   }
// })

export default router
