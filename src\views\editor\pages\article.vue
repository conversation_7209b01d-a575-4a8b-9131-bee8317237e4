
<template>
<div class="collect-article-detail">
  <div v-if="article">
    <div style="background-color: white; padding: 10px">预览文章</div>
    <div
      style="
        background-color: white;
        padding: 0 10px 10px;
        font-weight: 600;
      "
    >
      {{ article.title }}
    </div>
    <div><a :href="article.url" target="_blank" style="text-decoration: none;">查看原文</a></div>
    <div
      style="background-color: #f7f7f7;height: 80vh; overflow-y: auto;padding-bottom: 10px 10px 180px 10px;"
      v-html="articleCon"
    ></div>
  </div>
  <el-dialog :title="keyword.name" :visible.sync="visible">
    <div v-if="keyword.list" class="keyword-detail">
      <div class="item" v-for="(item, idx) in keyword.list" :key="idx">
        <div v-html="item.content"></div>
        <p class="book">
          {{ item.bookName }}
        </p>
      </div>
    </div>
    <div v-else class="keyword-detail">
      <div v-html="keyword.desc"></div>
      <p class="book" v-if="keyword?.book">
        《{{ keyword.book.book_name }}》
      </p>
    </div>
  </el-dialog>
</div>
</template>
<script>
import request from '@/http/request';
import { Loading } from 'element-ui';
import { replaceKeyword } from '../../../utils/replaceKeyword';
export default {
  data () {
    return {
      visible: false,
      article: null,
      articleCon: '',
      keyword: {
        name: '',
        book: null,
        desc: ''
      },
      loadingInstance: null
    }
  },
  created () {
    this.$watch('$route.query', (newQuery) => {
      this.getDetail(newQuery.id)
    })
  },
  methods: {
    getContentText (con) {
      const el = document.createElement('div')
      el.innerHTML = con
      return el.textContent || el.innerText
    },
    getDetail (paperId) {
      this.loadingInstance = Loading.service({
        fullscreen: true, // 是否全屏遮罩
        text: '加载中...', // 显示的文本
        background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
      })
      request('/api/wechatarticle/getArticleById', {
        id: paperId
      }).then((result) => {
        this.article = result
        const articleCon = replaceKeyword(result.content_show)
        this.articleCon = articleCon
        this.$emit('getArticleData', {
          ...this.article,
          type: 'article'
        })
        this.loadingInstance.close()
      }).catch(() => {
        this.loadingInstance.close()
      })
    }
  },
  mounted () {
    const paperId = this.$route.query.id
    this.getDetail(paperId)
    document.addEventListener('click', (event) => {
      // 检查点击的元素是否是.keyword-tag
      if (event.target.matches('.keyword-tag')) {
        // 假设你有一个全局对象或者Vue实例来存储这些值
        const descr = event.target.getAttribute('descr')
        const bookId = event.target.getAttribute('tag')
        const info = event.target.getAttribute('info')
        const label = event.target.innerText
        const describe = descr.replace(/\\n?\\r/g, '<br><br>')
        let list = []
        if (info) {
          list = JSON.parse(info) || []
        }
        request('/api/Keywords/getBookInfoByKeywordsId', {
          book_keywords_id: bookId,
          book_keywords: label,
          description: describe
        }).then((res) => {
          if (res) {
            this.keyword = {
              name: label,
              book: res,
              desc: describe
            }
          } else {
            this.keyword = {
              name: label,
              desc: describe,
              list: list.length ? list : null
            }
          }
          this.visible = true // 假设visible是全局变量或者Vue实例的属性
        }).catch(() => {
          this.visible = true // 假设visible是全局变量或者Vue实例的属性
          this.keyword = {
            name: label,
            desc: describe
          }
        })
      }
    }, false) // fal
  }
}
</script>
<style lang="scss">
.collect-article-detail{
  margin-top: 10px;
  .keyword-tag{
    color: #06f;
    cursor: pointer;
  }
  .book{
    font-weight: 600;
    color: #000;
    text-align: right;
  }
  .keyword-detail{
    max-height: 60vh;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 2px; /* 滚动条的宽度 */
    }
    &::-webkit-scrollbar-track {
      background: #f5f5f5;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #999;
      border-radius: 6px;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 20px!important;
  }
}
</style>
