<template>
    <div class="bg">
        <!-- 背景图 -->
        <!-- <canvas id="canvas"></canvas> -->

        <!-- 主体 -->
        <div class="context">
            <div class="title">
                <i class="el-icon-search"></i>
                <input type="text" class="input">
                <!-- 获取登录头像 -->
                <!-- <div> -->
                <img :src="userInfo.member_avatar" class="userImg" />
                <!-- </div> -->

            </div>
            <div class="main">
                <router-view></router-view>
            </div>
        </div>

        <!-- 右侧菜单定位 -->
        <div class="pf">

        </div>
    </div>
</template>

<script>
import Nebula from '@/components/nebula/index'
import * as THREE from 'three'
import { mapState } from 'vuex'
import request from '@/http/request'

// Data and visualization
import { CompositionShader } from '@/components/nebula/shaders/CompositionShader.js'
import {
  BLOOM_PARAMS
} from '@/components/nebula/config/renderConfig.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js'
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js'
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js'
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js'
export default {
  components: {
    Nebula
  },
  data () {
    return {
      state: {
        // 背景图dom
        canvas: null
      },
      input: ''

    }
  },
  mounted () {
    this.initThree()
  },
  computed: {
    ...mapState('user', {
      userInfo: (state) => state.userInfo
    })

  },
  methods: {
    initThree () {
      // grab canvas
      this.state.canvas = document.querySelector('#canvas')
      if (!this.state.canvas) return
      // scene
      this.state.scene = new THREE.Scene()
      this.$store.commit(
        'indexsearch/setSceneChildren',
        this.state.scene.children
      )
      this.state.scene.fog = new THREE.FogExp2(0xebe2db, 0.00003)

      // camera
      this.state.camera = new THREE.PerspectiveCamera(
        60,
        window.innerWidth / window.innerHeight,
        0.1,
        5000000
      )
      this.state.camera.position.set(0, 100, 100)
      this.state.camera.up.set(0, 0, 1)
      this.state.camera.lookAt(0, 0, 0)
      // map orbit
      this.state.orbit = new OrbitControls(this.state.camera, this.state.canvas)
      this.state.orbit.enableDamping = true // an animation loop is required when either damping or auto-rotation are enabled
      this.state.orbit.dampingFactor = 0.05
      this.state.orbit.screenSpacePanning = false
      this.state.orbit.minDistance = 80 // 设置最小距离
      this.state.orbit.maxDistance = 600 // 设置最大距离
      this.state.orbit.maxPolarAngle = Math.PI / 2 - Math.PI / 360
      this.state.orbit.addEventListener('change', () => {
        if (this.state.orbit.getDistance() <= this.state.orbit.minDistance) {
          this.logVisibleObjects()
          this.state.orbit.enablePan = false
          this.state.orbit.enableRotate = false
          this.state.orbit.enableDamping = false
          this.isLocked = true
        } else {
          this.clearObjectsLabels()
          this.state.orbit.enablePan = true
          this.state.orbit.enableRotate = true
          this.state.orbit.enableDamping = true
          this.isLocked = false
          this.isShowInfoArrayLocked = false
        }
      })
      this.initRenderPipeline()
    },
    initRenderPipeline () {
      // Assign Renderer
      this.state.renderer = new THREE.WebGLRenderer({
        antialias: true,
        canvas: this.state.canvas,
        logarithmicDepthBuffer: true,
        alpha: true
      })
      this.state.renderer.setPixelRatio(window.devicePixelRatio)
      this.state.renderer.setSize(window.innerWidth, window.innerHeight)
      this.state.renderer.outputEncoding = THREE.SRGBColorSpace
      this.state.renderer.toneMapping = THREE.ACESFilmicToneMapping
      this.state.renderer.toneMappingExposure = 0.5
      if (this.isLightMode) {
        this.state.renderer.setClearColor(0xa6a6a6, 0.4)
      }
      // General-use rendering pass for chaining
      const renderScene = new RenderPass(this.state.scene, this.state.camera)
      // Rendering pass for bloom
      const bloomPass = new UnrealBloomPass(
        new THREE.Vector2(window.innerWidth, window.innerHeight),
        1.5,
        0.4,
        0.85
      )
      bloomPass.threshold = BLOOM_PARAMS.bloomThreshold
      bloomPass.strength = BLOOM_PARAMS.bloomStrength
      bloomPass.radius = BLOOM_PARAMS.bloomRadius

      // bloom composer
      this.state.bloomComposer = new EffectComposer(this.state.renderer)
      this.state.bloomComposer.renderToScreen = false
      this.state.bloomComposer.addPass(renderScene)
      this.state.bloomComposer.addPass(bloomPass)

      // overlay composer
      this.state.overlayComposer = new EffectComposer(this.state.renderer)
      this.state.overlayComposer.renderToScreen = false
      this.state.overlayComposer.addPass(renderScene)

      // Shader pass to combine base layer, bloom, and overlay layers
      const finalPass = new ShaderPass(
        new THREE.ShaderMaterial({
          uniforms: {
            baseTexture: { value: null },
            bloomTexture: {
              value: this.state.bloomComposer.renderTarget2.texture
            },
            overlayTexture: {
              value: this.state.overlayComposer.renderTarget2.texture
            }
          },
          vertexShader: CompositionShader.vertex,
          fragmentShader: CompositionShader.fragment,
          defines: {}
        }),
        'baseTexture'
      )
      finalPass.needsSwap = true

      // base layer composer
      this.state.baseComposer = new EffectComposer(this.state.renderer)
      this.state.baseComposer.addPass(renderScene)
      this.state.baseComposer.addPass(finalPass)
    },
    // 获取截面的标签并展示
    logVisibleObjects () {
      // 创建Frustum对象
      const frustum = new THREE.Frustum()
      const cameraViewProjectionMatrix = new THREE.Matrix4()

      // 根据相机视锥体设置可见范围
      cameraViewProjectionMatrix.multiplyMatrices(
        this.state.camera.projectionMatrix,
        this.state.camera.matrixWorldInverse
      )
      frustum.setFromProjectionMatrix(cameraViewProjectionMatrix)

      // 遍历场景图中的所有 sprite 对象
      let visibleObjects = []
      this.state.scene.traverse((object) => {
        if (object.type === 'Sprite' && frustum.intersectsObject(object)) {
          // 获取 sprite 在世界坐标系中的位置
          const spritePos = new THREE.Vector3()
          object.getWorldPosition(spritePos)

          // 计算当前 sprite 距离相机的距离
          const distanceToCamera = spritePos.distanceTo(
            this.state.camera.position
          )

          // 判断当前 sprite 是否在相机视野之内，并且距离小于 60px
          if (frustum.containsPoint(spritePos) && distanceToCamera <= 60) {
            visibleObjects.push(object)
          }
        }
      })
      // 打印每个对象的userdata中的keywords属性值到控制台
      setTimeout(() => {
        visibleObjects = visibleObjects.filter((object) => {
          if (object.userData?.keywords) {
            const position = this.getObjectScreenPosition(
              object,
              this.state.camera
            )
            const screenWidth = window.innerWidth
            const screenHeight = window.innerHeight
            if (
              position.y - 30 + 100 > screenHeight ||
                            position.x - 10 + 100 > screenWidth
            ) {
              return false // 返回false来排除在可视范围之外的元素
            } else {
              return true // 返回true来保留在可视范围之内的元素
            }
          } else {
            return false
          }
        })
        visibleObjects.forEach((object) => {
          const position = this.getObjectScreenPosition(
            object,
            this.state.camera
          )
          const visibleMarker = document.createElement('div')
          // 设置标签样式和属性
          visibleMarker.className = 'visible-marker'
          visibleMarker.style.top = `${position.y - 30}px`
          visibleMarker.style.left = `${position.x - 10}px`
          visibleMarker.style.position = 'absolute'
          visibleMarker.style.color = '#eab204'
          visibleMarker.style.fontSize = '16px'
          visibleMarker.style.border = '1px solid #9ab6c8'
          visibleMarker.style.padding = '2px 4px'
          visibleMarker.style.cursor = 'pointer'
          visibleMarker.style.backgroundColor = 'black'
          visibleMarker.innerHTML = object.userData.keywords || ''

          // visibleMarker.addEventListener("click", this.handleClick(object));
          document.body.appendChild(visibleMarker)
        })

        Promise.all(
          visibleObjects.map(async (obj) => {
            const bookKeywordsId = obj.userData.book_keywords_id
            const memberKeywordsId = obj.userData.member_keywords_id
            const params = {
              book_keywords_id: bookKeywordsId,
              member_keywords_id: memberKeywordsId
            }
            const result = await request(
              '/api/keywords/getDescriptionByKeyWordsId',
              params
            )
            // 将当前的结果与对应的子物体的 userData 进行组合
            return {
              keywords: obj.userData.keywords, // 假设 userData 中存在 keywords 属性
              result: result,
              book_keywords_id: obj.userData.book_keywords_id,
              member_keywords_id: obj.userData.member_keywords_id
            }
          })
        ).then((results) => {
          this.isShowInfo = false
          this.isShowInfoArrayLocked = true
          this.isShowInfoArray = false
          this.starLockedArray = results
        })
      }, 200)
    },
    // 清除页面上的截面标签
    clearObjectsLabels () {
      const visibleMarkerList =
                document.getElementsByClassName('visible-marker')
      while (visibleMarkerList.length > 0) {
        visibleMarkerList[0].parentNode.removeChild(visibleMarkerList[0])
      }
    }

  }
}
</script>

<style scope lang='scss'>
.bg {
    width: 100%;
    height: 100vh;
    background-color: #000;

    #canvas {
        position: fixed;
        z-index: 1;
        width: 100%;
        height: 100%;
    }
    .context {
    position: fixed;
    z-index: 999;

    .title {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 20px;
        line-height: 20px;
        // background-color: #f00;
        float: right;
        margin-right: 50px;
        vertical-align: middle;
        padding-top: 20px;

        .input {
            width: 160px;
            height: 20px;
            border: 1px solid #fff;
            border-radius: 20px;
            background-color: transparent;
            margin-right: 20px;
        }

        .userImg {
            width: 20px;
            height: 20px;
            border-radius: 16px;
        }

    }

}

.el-icon-search {
    font-size:21px;
    margin-right: 3px;
    font-weight: bold;
    color: #fff;
}
}

</style>
