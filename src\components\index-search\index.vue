<template>
  <div>
    <div class="search-box">
      <input
        class="search-box-input"
        type="text"
        placeholder=" "
        v-model="keyword"
      />
      <!-- 放大镜 -->
      <div v-show="!keyword">
        <div class="search-box-icon"></div>
        <div class="search-box-icon-line"></div>
      </div>
    </div>
    <!-- 搜索结果 -->
    <div v-show="isShowSearchResult">
      <!-- 词条 -->
      <div v-show="searchResultEntry.length > 0" class="search-box-result">
        <div
          v-for="(item, i) in searchResultEntry"
          :key="i"
          class="search-box-item"
          @click="chooseSearchItem(item)"
        >
          {{ item.user }}
        </div>
      </div>
      <div v-show="searchResultEntry.length == 0" class="no-search-result">
        暂无词条
      </div>
      <!-- 议题 -->
      <div class="search-topic-list">
        <el-tag
          v-for="item in topicsList"
          :key="item.topics_id"
          size="mini"
          effect="dark">
          {{ item.topics_content }}
        </el-tag>
      </div>

      <!-- 作者-->
      <div v-show="searchResultAuthors.length > 0" class="author-list-box">
        <div
          v-for="(item, index) in searchResultAuthors"
          :key="index"
          class="list-item"
          @click="$store.commit('book/showBookAuthor', item)"
        >
          <div style="margin: 2px 10px">
            {{ item.author_name }}
          </div>

          <div style="display: flex">
            <div style="height: 86px; margin: 10px">
              <img
                :src="fullImageUrl(item.author_img)"
                alt=""
                class="list-img"
                style="width: 76px"
              />
            </div>
            <div
              v-if="item.author_sign"
              class="no-scroll-bar"
              style="
                overflow-y: scroll;
                height: 100px;
                font-size: 14px;
                color: #ccc;
              "
            >
              {{ item.author_sign }}
            </div>
            <div v-else style="height: 100px; font-size: 14px; color: #ccc">
              暂无简介
            </div>
          </div>
        </div>
      </div>
      <div v-show="searchResultAuthors.length == 0" class="no-search-result">
        暂无作者
      </div>

      <!-- 书籍-->
      <div v-show="searchResultBooks.length > 0" class="book-list-box">
        <div
          v-for="(item, index) in searchResultBooks"
          :key="index"
          class="list-item"
          @click.stop="chooseSearchBookItem(item.book_id)"
        >
          <div style="height: 86px">
            <img :src="item.book_image" alt="" class="list-img" style="" />
          </div>

          <div class="list-info">
            <div class="list-info-title">{{ item.book_name }}</div>
            <div class="list-info-time">{{ item.publishing_time }}</div>
          </div>
        </div>
      </div>
      <div v-show="searchResultBooks.length == 0" class="no-search-result">
        暂无书籍
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/http/request'
import { mapState, mapMutations } from 'vuex'
import { EventBus } from '@/main.js'
export default {
  data () {
    return {
      keyword: '',
      isShowSearchResult: false,
      searchResultEntry: [], // 词条搜索结果
      searchResultAuthors: <AUTHORS>
      searchResultBooks: [],
      isLoadingSearchList: false,
      topicsList: [] // 议题
    }
  },

  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('indexsearch', {
      sceneChildren: (state) => state.sceneChildren,
      isNotFirstSearch: (state) => state.isNotFirstSearch
    }),
    ...mapState('book', ['choosedBookId', 'isShowBookDetail']),
    fullImageUrl () {
      const baseUrl = 'https://oss.bookor.com.cn'
      return (imagePath) => {
        if (!imagePath) {
          return 'https://oss.bookor.com.cn/uploads/20191123/8d134c52225868b83013fa42ee065a27.jpg'
        }
        return baseUrl + imagePath
      }
    }
  },
  created () {
    this.debouncedHandleInputChange = this.debounce(this.onSearchInputAll, 300)
  },
  watch: {
    sceneChildren () {},
    keyword () {
      this.debouncedHandleInputChange()
    }
  },
  props: ['forceData', 'bondList'],
  methods: {
    apiGetSearchResult () {
      this.isLoadingSearchList = true
      const keyword = this.keyword.trim().toLowerCase()
      let list = []
      this.bondList.forEach((item) => {
        if (item.title && item.title.length === 1) {
          const child = item.title[0]
          if (child.lines_title.includes(keyword)) {
            list.push({
              user: child.lines_title,
              id: child.mix_keywords_lines_id,
              type: 'KEYWORD_TITLE' // 定义特殊的type， 为了方便搜索的时候查找
            })
          }
        }
      })
      const nodes = this.forceData.nodes.filter(item => item.user.includes(keyword) && item.user)
      list = [...list, ...nodes]
      this.searchResultEntry = list
    },
    getTopics () {
      const keyword = this.keyword.trim().toLowerCase()
      request('/api/Booktopics/searchBooktopicsList', {
        search: keyword,
        page: 1,
        limit: 10
      }).then((result) => {
        this.topicsList = result
      })
    },
    debounce (func, delay) {
      let timer
      return function () {
        clearTimeout(timer)
        timer = setTimeout(func, delay)
      }
    },
    clearSearch () {
      this.isShowSearchResult = false

      this.keyword = ''
      this.searchResultEntry = []
      this.searchResultBooks = []
      this.searchResultAuthors = []
    },

    onSearchInputAll () {
      if (!this.keyword.trim()) {
        this.clearSearch()
        return
      }

      // 首次搜索的话，改变对应变量
      this.isShowSearchResult = true
      if (!this.isNotFirstSearch) {
        this.$store.commit('indexsearch/setFirstSearch', true)
        setTimeout(() => {
          this.apiGetSearchResult()
          // this.handleSearchEntry() // 搜索词条
          // 小程序里的搜索(包括搜索作者、书籍、笔记等。)
          this.handleSearchBooks()
          this.getTopics() // 搜索议题
        }, 1000) // 延迟1秒执行后续搜索
      } else {
        this.apiGetSearchResult()
        // this.handleSearchEntry() // 搜索词条
        // 小程序里的搜索(包括搜索作者、书籍、笔记等。)
        this.handleSearchBooks()
        this.getTopics() // 搜索议题
      }
    },

    handleSearchEntry () {
      this.searchResultEntry = [] // 清空之前的搜索结果
    },

    // 搜索书&作者&书评所有
    handleSearchBooks () {
      request('/api/books/getContentForSearch', {
        search_type: 'all',
        search_words: this.keyword,
        page: 1,
        limit: 10
      }).then((result) => {
        this.searchResultBooks = result.book_list.list
        this.searchResultAuthors = result.author_list.list
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 选中单个词条
    chooseSearchItem (item) {
      this.isShowSearchResult = false
      this.clearSearch()
      EventBus.$emit('openDetail', item)
    },

    // 选中单本书
    ...mapMutations({
      setChosenBookId: 'book/SET_CHOSED_BOOK_ID',
      setShowDetails: 'book/showDetails'
    }),
    chooseSearchBookItem (id) {
      this.setChosenBookId(id)
      this.clearSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-box {
  border: 2px solid #8faadc;
  width: 300px;
  padding-right: 15px;
  cursor: pointer;
  border-radius: 30px;
}
.search-box-input {
  border: none;
  background: 0 0;
  width: 315px;
  height: 27px;
  color: white;
  border-radius: 30px;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 0 10px;
  box-sizing: border-box;
}
.search-topic-list{
  margin: 5px 10px;
}
.search-topic-list .el-tag{
  margin-right: 5px;
}
.search-box-result {
  // position: fixed;
  // right: 110px;
  // top: 62px;
  /* background-color: #8faadc; */
  /* border: 2px solid #8faadc; */
  max-height: 150px; /* 设置固定高度 */
  overflow-y: auto; /* 当内容超出高度时出现滚动条 */
  width: 315px;
  /* margin: 5px 10px; */
  /* border-radius: 10px; */
}

.search-box-result::-webkit-scrollbar {
  display: none;
}

.search-box-item {
  padding: 5px 10px;
  color: #b2cae0;
  font-size: 16px;
  cursor: pointer;
  /* border-radius: 10px; */
}

.search-box-item:hover {
  color: #8faadc;
  background-color: white;
}

.book-list-box {
  width: 315px;
  display: flex;
  overflow-x: scroll;
  padding: 5px 0;
  // width: 400px;
  .list-item {
    margin-left: 5px;
    height: 140px;
    width: 100px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 5px 0px #ddd;
    margin-right: 10px;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    cursor: pointer;

    background-color: rgba(0, 0, 0, 0.7);
    .list-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .list-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0 5px;
      padding-top: 3px;
      height: 51px;
      color: white;
      .list-info-title {
        font-size: 14px;
        /* color: #333; */
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 1.2;
      }
      .list-info-time {
        font-size: 12px;
        margin-top: 5px;
      }
    }
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // display: none;
  }
  // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
  &::-webkit-scrollbar-button {
    display: none;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }
  // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    cursor: pointer;
    border-radius: 4px;
  }
  // 边角，即两个滚动条的交汇处
  &::-webkit-scrollbar-corner {
    display: none;
  }
  // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
  &::-webkit-resizer {
    display: none;
  }
}

.search-box-icon{
  width: 16px;
  height: 16px;
  position: absolute;
  top: 4px;
  right: 20px;
  border-radius: 50%;
  border: 2px solid rgb(143, 170, 220);
}
.search-box-icon-line{
  height: 13px;
  width: 2px;
  background-color: rgb(143, 170, 220);
  position: absolute;
  right: 18px;
  bottom: 2px;
  transform: rotate(135deg);
}

.author-list-box {
  width: 315px;
  display: flex;
  overflow-x: scroll;
  padding: 5px 0;
  // width: 400px;
  .list-item {
    margin-left: 5px;
    height: 140px;
    width: 305px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 5px 0px #ddd;
    margin-right: 10px;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    cursor: pointer;
    color: white;
    background-color: rgba(0, 0, 0, 0.7);
    .list-img {
      width: 100%;
      // height: 100%;
      // object-fit: cover;
    }
    .list-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0 5px;
      padding-top: 3px;
      height: 51px;
      color: white;
      .list-info-title {
        font-size: 14px;
        /* color: #333; */
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 1.2;
      }
      .list-info-time {
        font-size: 12px;
        margin-top: 5px;
      }
    }
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // display: none;
  }
  // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
  &::-webkit-scrollbar-button {
    display: none;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }
  // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    cursor: pointer;
    border-radius: 4px;
  }
  // 边角，即两个滚动条的交汇处
  &::-webkit-scrollbar-corner {
    display: none;
  }
  // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
  &::-webkit-resizer {
    display: none;
  }
}

.no-search-result {
  color: #b3b3b3;
  font-size: 15px;
  margin: 10px 20px;
}
.no-scroll-bar::-webkit-scrollbar {
  display: none;
}

@media (max-width: 600px) {
  .search-box {
    width: 255px;
  }
  .search-box-input,
  .search-box-result,
  .book-list-box,
  .author-list-box {
    width: 270px;
  }
}

</style>
