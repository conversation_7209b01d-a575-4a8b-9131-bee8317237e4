export const getId = () => {
  const id = parseInt(Math.random() * 100000000).toString()
  return id
}

export const getRectPort = (width, height) => {
  const obj = {
    groups: {
      group1: {
        attrs: {
          circle: {
            r: 6,
            magnet: true,
            stroke: '#31d0c6',
            fill: '#fff',
            strokeWidth: 2,
            visibility: 'hidden' // 默认隐藏连接桩
          }
        },
        position: {
          name: 'absolute'
        }
      }
    },
    items: [
      {
        id: 'port1',
        group: 'group1',
        args: {
          x: 0,
          y: height / 2
        }
      },
      {
        id: 'port2',
        group: 'group1',
        args: {
          x: width,
          y: height / 2
        }
      },
      {
        id: 'port3',
        group: 'group1',
        args: {
          x: width / 2,
          y: 0
        }
      },
      {
        id: 'port4',
        group: 'group1',
        args: {
          x: width / 2,
          y: height
        }
      }
    ]
  }
  return obj
}
export const getCirPort = (width, height) => {
  const obj = {
    groups: {
      group1: {
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#31d0c6',
            fill: '#fff',
            strokeWidth: 2,
            visibility: 'hidden' // 默认隐藏连接桩
          }
        },
        position: {
          name: 'absolute'
        }
      }
    },
    items: [
      {
        id: 'port1',
        group: 'group1',
        args: {
          x: 0,
          y: height / 2
        }
      },
      {
        id: 'port2',
        group: 'group1',
        args: {
          x: width,
          y: height / 2
        }
      },
      {
        id: 'port3',
        group: 'group1',
        args: {
          x: width / 2,
          y: 0
        }
      },
      {
        id: 'port4',
        group: 'group1',
        args: {
          x: width / 2,
          y: height
        }
      }
    ]
  }
  return obj
}

export const getHexPort = (width, height) => {
  const obj = {
    groups: {
      group1: {
        attrs: {
          circle: {
            r: 6,
            magnet: true,
            stroke: '#31d0c6',
            fill: '#fff',
            strokeWidth: 2,
            visibility: 'hidden' // 默认隐藏连接桩
          }
        },
        position: {
          name: 'absolute'
        }
      }
    },
    items: [
      {
        id: 'port1',
        group: 'group1',
        args: {
          x: 0,
          y: height / 2
        }
      },
      {
        id: 'port2',
        group: 'group1',
        args: {
          x: width,
          y: height / 2
        }
      },
      {
        id: 'port3',
        group: 'group1',
        args: {
          x: width / 2,
          y: 0
        }
      },
      {
        id: 'port4',
        group: 'group1',
        args: {
          x: width / 2,
          y: height
        }
      }
    ]
  }
  return obj
}

export const typeLine = (type) => {
  switch (type) {
    case 1:
      return {
        sourceMarker: 'classic',
        targetMarker: 'classic'
      }
    case 2:
      return {
        sourceMarker: 'classic',
        targetMarker: 'classic'
      }
    case 3:
      return {
        sourceMarker: '',
        targetMarker: 'classic'
      }
    case 4:
      return {
        sourceMarker: '',
        targetMarker: 'classic'
      }
    case 5:
      return {
        sourceMarker: '',
        targetMarker: ''
      }
    case 6:
      return {
        sourceMarker: '',
        targetMarker: ''
      }
    default:
      return {
        sourceMarker: '',
        targetMarker: ''
      }
  }
}

export const getType = (edge) => {
  const { targetMarker, sourceMarker, strokeDasharray } = edge.getAttrs().line
  if (targetMarker && sourceMarker) {
    return 1
  }
  if (sourceMarker && targetMarker && strokeDasharray) {
    return 2
  }
  if (!sourceMarker && targetMarker && !strokeDasharray) {
    return 3
  }
  if (!sourceMarker && targetMarker && strokeDasharray) {
    return 4
  }
  if (!sourceMarker && !targetMarker && !strokeDasharray) {
    return 5
  }
  if (!sourceMarker && !targetMarker && strokeDasharray) {
    return 6
  }
}

export const textNode = (obj) => {
  const {
    x = 10,
    y = 10,
    width = 100,
    height = 40,
    parentId = '',
    label = '新建文本'
  } = obj || {}
  const id = getId()
  return {
    id,
    shape: 'rect',
    x,
    y,
    label,
    width,
    height,
    attrs: {
      body: {
        stroke: '#D97070',
        strokeWidth: 0,
        fill: "transparent",
        rx: 5,
        ry: 5
      },
      text: {
        fill: '#000',
        fontSize: 12
      }
    },
    ports: getRectPort(width, height),
    tools: [
      {
        name: 'node-editor'
      }
    ],
    data: {
      id,
      parentId
    },
    zIndex: 1
  }
}

export const rectNode = (obj) => {
  const {
    x = 10,
    y = 10,
    width = 100,
    height = 40,
    parentId = '',
    label = '新建文本'
  } = obj || {}
  const id = getId()
  return {
    id,
    shape: 'rect',
    x,
    y,
    label,
    width,
    height,
    attrs: {
      body: {
        stroke: '#D97070',
        strokeWidth: 0,
        fill: '#D97070',
        rx: 5,
        ry: 5
      },
      text: {
        fill: '#fff',
        fontSize: 12
      }
    },
    ports: getRectPort(width, height),
    tools: [
      {
        name: 'node-editor'
      }
    ],
    data: {
      id,
      parentId
    },
    zIndex: 1
  }
}
export const cirNode = (obj) => {
  const id = getId()
  const {
    x = 10,
    y = 10,
    width = 60,
    height = 60,
    label = '新建文本'
  } = obj || {}
  return {
    id,
    shape: 'circle',
    x,
    y,
    label,
    width,
    height,
    attrs: {
      body: {
        stroke: '#D97070',
        strokeWidth: 0,
        fill: '#D97070',
        rx: 5,
        ry: 5
      },
      text: {
        fill: '#fff',
        fontSize: 12
      }
    },
    ports: getCirPort(60, 60),
    zIndex: 1,
    data: {
      id
    },
    tools: [
      {
        name: 'node-editor'
      }
    ]
  }
}

export const hexagonNode = (obj) => {
  const id = getId()
  const {
    x = 10,
    y = 10,
    width = 100,
    height = 80,
    label = '新建文本'
  } = obj || {}
  return {
    id,
    shape: 'path',
    x,
    y,
    width,
    height,
    label,
    // 使用 path 属性指定路径的 pathData，相当于指定路径的 refD 属性
    // https://x6.antv.vision/zh/docs/api/registry/attr#refdresetoffset
    path: 'M 50 0 L 150 0 L 200 86.6025 L 150 173.205 L 50 173.205 L 0 86.6025 Z',
    attrs: {
      body: {
        fill: '#D97070',
        stroke: '#D97070',
        strokeWidth: 0
      },
      text: {
        fill: '#fff',
        fontSize: 12
      }
    },
    ports: getHexPort(width, height),
    data: {
      id
    },
    tools: [
      {
        name: 'node-editor'
      }
    ]
  }
}

// 计算子节点x和y坐标的函数
export const getChildNodePositions = (data, childCount) => {
  // 初始化子节点位置数组
  const childPositions = []
  // const maxPercent = childCount // 最大的倍率， 表示排列最大为父元素的高度
  const spacing = 10
  const total = childCount * 50 + childCount * 10
  // 计算子节点的x坐标
  const x = data.x + data.width + 80
  // 当只有一个子节点时，子节点与父节点对齐
  if (childCount === 1) {
    const y = data.y
    childPositions.push({ x, y })
  } else {
    // 计算子节点的间距
    // 计算子节点的y坐标并添加到位置数组中
    for (let i = 0; i < childCount; i++) {
      const top = total / 2 - data.height / 2
      const y = data.y - top + (spacing + 50) * i
      childPositions.push({ x, y })
    }
  }

  // 返回子节点位置数组
  return childPositions
}

export const getRandomId = (id) => {
  const randomId = id + Math.round(Date.now() + Math.random() * 100000)
  return randomId
}

export const childNode = (obj, x, y) => {
  const { data, id } = obj
  const originLabel = obj.label
  let label = originLabel
  let isMore = false
  if (label.length > 40) {
    isMore = true
    label = label.substring(0, 40) + '...'
  }
  return {
    id,
    // parent: data.id,
    shape: 'rect',
    x,
    y,
    label,
    width: obj.width,
    height: 26,
    idx: obj.idx,
    attrs: {
      // selectable: true,
      body: {
        stroke: '#333',
        strokeWidth: 1,
        // fill: '#999',
        rx: 5,
        ry: 5
      },
      ref: {
        refX: 0.5
      },
      text: {
        fill: '#000',
        fontSize: 12
      }
    },
    ports: getRectPort(obj.width, 26),
    data: {
      id,
      // selectable: true,
      originLabel,
      isMore
    },
    isChild: true,
    // nodeMovable: false,
    // resizable: false, // 无法放大缩小
    // embeddable: false, // 禁止元素进行
    zIndex: 9
  }
}

export const getTextWidth = (text) => {
  let label = text
  if (text.length > 40) {
    label = label.substring(0, 40) + '...'
  }
  // 创建一个临时的span元素
  var span = document.createElement('span')
  span.style.fontSize = '12px' // 隐藏元素
  span.style.visibility = 'hidden' // 隐藏元素
  span.style.position = 'absolute' // 脱离文档流
  span.style.whiteSpace = 'nowrap' // 保持文本在一行
  span.textContent = label // 设置文本内容

  // 将元素添加到body中
  document.body.appendChild(span)
  // 获取宽度
  var width = span.getBoundingClientRect().width + 10

  // 移除元素
  document.body.removeChild(span)

  // 返回宽度
  return width
}

export const calculateStringWidth = (str) => {
  let width = 0

  for (let i = 0; i < str.length; i++) {
    const char = str[i]
    if (/^[\u4e00-\u9fa5]$/.test(char)) {
      // 中文字符
      width += 13
    } else {
      // 英文字符
      width += 7
    }
  }

  return width
}
