<template>
    <!-- 阻止事件冒泡 -->
    <div id="mask" @click="$store.commit('book/showBookVideo')" style="position: relative;">
        <div class="video" @click.stop>
            <video :src="bookVideoItem.short_video_url" controls height="100%" autoplay></video>
            <!-- {{  userInfo }} -->
        </div>

    </div>
</template>

<script>
import { mapState } from 'vuex'

export default {

  data () {
    return {
      src: ''
    }
  },

  computed: {
    ...mapState('user', {
      userInfo: (state) => state.userInfo
    }),
    ...mapState('book', {
      bookVideoItem: state => state.bookVideoItem
    })
  },

  methods: {

  },
  mounted () {

  }
}

</script>

<style scope lang='scss'>
#mask {
    width: 100%;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0.5);
}

.video {
    width: 800px;
    height: 600px;
    // border: 1px solid red;
    position: absolute;
    top: 40px;
    left: 0;
    bottom: 0;
    right: 0;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
