<template>
  <div
    v-show="isShowNotePopSub"
    class="container"
    ref="containerRef"
    :style="{ top: notePopTop - 60 + 'px', left: notePopLeft + 'px' }"
  >
    <div class="title">
      <input
        class="inputTitle"
        type="text"
        maxlength="1000"
        v-model="title"
        :placeholder="isShowPlaceholder ? '请输入词条名称' : ''"
        @focus="hidePlaceholder"
        @blur="showPlaceholder"
      />

      <div>
        <img
          @click="isShowNotePopSub = false"
          style="width: 20px"
          src="@/assets/editor/close.png"
          alt=""
        />
      </div>
    </div>
    <div id="selectText" class="desc no-scroll-bar" contenteditable="true">
      {{ selectText }}
    </div>
    <div @click="addNote" class="saveBtn" style="">保存</div>
  </div>
</template>

<script>
import request from '@/http/request'
export default {
  data () {
    return {
      title: '',
      isShowNotePopSub: false,
      isShowPlaceholder: true
    }
  },
  props: {
    selectText: {
      type: String,
      default: ''
    },
    chapterInformation: {
      type: String,
      default: ''
    },
    isShowNotePop: {
      type: Boolean,
      default: false
    },
    notePopTop: {
      type: Number,
      default: 0
    },
    notePopLeft: {
      type: Number,
      default: 0
    },
    paper_id: {
      type: Number
    },
    entry_position: {
      type: String
    }
  },
  watch: {
    selectText (newValue) {
      if (newValue) {
        this.isShowNotePopSub = true
      }
    },
    isShowNotePop (newValue) {
      this.isShowNotePopSub = newValue
      if (newValue === false) {
        this.title = ''
      }
    }
  },
  methods: {
    async addNote () {
      // 打印词条的名称
      const title = this.title.trim()
      if (!title) {
        this.$message({
          message: '词条名称不能为空',
          type: 'error',
          duration: 1000
        })
        return
      }
      const divContent = document.getElementById('selectText').innerText
      if (!divContent) {
        this.$message({
          message: '词条内容不能为空',
          type: 'error',
          duration: 1000
        })
        return
      }
      if (!JSON.parse(localStorage.getItem('userinfo'))) {
        this.$message({
          message: '登录失败，请重新登录',
          type: 'error',
          duration: 1000
        })
        this.$router.push('/login')
      }
      const params = {
        description: divContent + this.chapterInformation,
        keywords: title,
        mk_tag_id: ''
      }
      if (this.paper_id) {
        params.paper_id = this.paper_id
      }
      if (this.entry_position) {
        params.entry_position = this.entry_position
      }
      const pos = document.querySelector('.pdf-viewer')?.contentWindow?.highlight?.highlightHandler(divContent, title)
      if (pos?.length) {
        params.entry_position = JSON.stringify(pos)
      }
      const res = request('/api/Keywords/addMemberKeywords', params).then((result) => {
        this.$message({
          message: '添加词条成功',
          type: 'success',
          duration: 1000
        })
        this.title = ''
        this.isShowNotePopSub = false
      }).catch(() => {
        this.$message({
          message: res.message,
          type: 'error',
          duration: 1000
        })
        this.title = ''
        this.isShowNotePopSub = false
      })
    },
    hidePlaceholder () {
      this.isShowPlaceholder = false
    },
    showPlaceholder () {
      if (!this.title.trim()) {
        this.isShowPlaceholder = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #d9d9d9;
  position: absolute;
  padding: 10px 12px 28px;
  border-radius: 5px;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.3);
  max-height: 150px;
  .title {
    display: flex;
    justify-content: space-between;
    .inputTitle {
      font-size: 16px;
      border: 0;
      color: #2f84c8;
      background-color: transparent;
      border-bottom: 1px solid;
      font-weight: 600;
      width: 115px;
      &::placeholder {
        color: #2f84c8;
      }
      &:focus {
        outline: none; /* 移除焦点时的默认边框 */
      }
    }
  }
  .desc {
    max-width: 300px;
    max-height: 120px;
    overflow-y: scroll;
    position: relative;
  }

  .saveBtn {
    cursor: pointer;
    position: absolute;
    bottom: 5px;
    right: 10px;
    color: #203864;
    background-color: #9baabf;
    padding: 1px 5px;
  }
  .no-scroll-bar::-webkit-scrollbar {
    display: none;
  }
}
</style>
