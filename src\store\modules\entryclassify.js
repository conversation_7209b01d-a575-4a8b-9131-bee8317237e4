import request from '@/http/request'
const entryclassify = {
  namespaced: true,
  state: {
    myMemberKeywordsTagList: []
  },
  mutations: {
    // 获取分类数据
    setMyMemberKeywordsTagList (state, val) {
      state.myMemberKeywordsTagList = val
    }
  },
  actions: {
    // 获取已有的分类
    async getMemberKeywordsTagList ({ commit }) {
      try {
        const token = localStorage.getItem('authorization')
        if (!token) {
          return
        }
        request('/api/Keywords/getClassifyList', {}).then((result) => {
          commit('setMyMemberKeywordsTagList', result)
        })
      } catch (error) {
        console.error(error)
      }
    }
  }
}

export default entryclassify
