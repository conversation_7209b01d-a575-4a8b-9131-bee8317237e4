<template>
  <div v-if="isShowBookDetail" @click.stop="$store.commit('book/closeDetails')" class="container-mask">
    <div class="container" @click.stop v-slide-scroll>
      <!-- 书基本信息 -->
      <div class="book-info">
        <img @click.once="isShow" class="book-info-img" :src="bookcover.book_image" alt="" />
        <div class="book-info-content">
          <div class="book-info-content-title">
            《{{ bookcover.book_name }}》
          </div>
          <!-- 专家评分 -->
          <div class="book-score-data-wrap">
            <div class="book-detail-score-num-wrap">
              <div id="star-rate-show" style="display: flex; margin-right: 30rpx">
                <div>专家评分:</div>
                <div v-if="bookScoreInitData != null" class="bookScoreNum">
                  {{ bookScoreInitData.total_average_score }}
                </div>
                <div class="bookNoScore" v-if="bookScoreInitData == null"
                  @click="$store.commit('book/showBookScoreInfo', 'score')">
                  暂无评分
                </div>
              </div>
              <div class="book-detail-score-wrap" @click="$store.commit('book/showBookScoreInfo', 'score')">
                <div style="position: relative; cursor: pointer"
                  @click="$store.commit('book/showBookScoreInfo', 'score')">
                  <StarRate id="bookAverageStar" :readonly="true" :scoreValue="bookScoreInitData != null
                    ? bookScoreInitData.total_average_star
                    : 0
                    " />
                </div>
              </div>
              <img id="star-rate-info" class="book-detail-score-info" style="" src="./img/info.png" alt="info"
                @click="$store.commit('book/showBookScoreInfo', 'info')" />
            </div>
          </div>

          <!-- 市场热度 -->
          <div class="book-hot">
            市场热度: <span>{{ bookcover.book_click }}</span>
          </div>

          <!-- 其他信息 -->
          <div class="book-detail">
            <div class="txtWrapping" style="display: flex">
              <div style="flex: none">作者：</div>
              <div>
                <div v-for="(item, index) in bookcover.author" :key="index">
                  <div v-if="item.author_name != null && item.author_name !== ''">
                    <span class="txtWrapping-sub txtWrapping-sub-cursor"
                      @click="$store.commit('book/showBookAuthor', item)">{{ item.author_name }}</span>
                    <!-- <span
                      v-if="
                        item.author_english_name != '' &&
                        item.author_english_name != null
                      "
                      >({{ item.author_english_name }})</span
                    > -->
                    <span v-if="bookcover.author.length > 1 &&
                      index < bookcover.author.length - 1
                      ">、</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="txtWrapping" style="display: flex">
              <div style="flex: none">译者：</div>
              <div>
                <span v-for="(item, index) in bookcover.translator" :key="index" style="margin-right: 10rpx">
                  <span v-if="item.translator_name != null &&
                    item.translator_name !== ''
                    ">
                    <span class="txtWrapping-sub txtWrapping-sub-cursor">{{
                      item.translator_name
                    }}</span>
                  </span>
                </span>
                <div v-for="(item, index) in selfTranslatorAuthorList" :key="index" style="
                    display: inline-div;
                    margin-right: 10rpx;
                    max-width: 120rpx;
                  ">
                  <span class="txtWrapping-sub txtWrapping-sub-cursor">{{
                    item.apply_name
                  }}</span>
                </div>
              </div>
            </div>
            <div class="txtWrapping">
              出版社：<span class="txtWrapping-sub">{{ bookcover.press_name }}
              </span>
            </div>
            <div class="txtWrapping">
              出版日期：{{ bookcover.publishing_time }}
            </div>
            <!-- 标签 -->
            <div class="scrollTagRegionWrap" v-if="allTagList.length > 0">
              <div class="tagBox" v-for="(item, index) in allTagList" :key="index"
                @click="$store.commit('book/showTag', item)">
                <div class="tagItem">{{ item.keywords }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 书简介 -->
      <div class="book-desc" style="width: 260px">
        <div class="book-desc-title">简介</div>
        <div class="book-desc-content" v-html="introduce"></div>
      </div>
      <!-- 议题 -->
      <div class="book-desc" style="width: 220px">
        <div class="book-desc-title" style="cursor: pointer; width: 220px" @click="$store.commit('book/showBookTopic')">
          相关议题
        </div>
        <BookIssues :bookName="bookcover.book_name" :bookImg="bookcover.book_image" />
      </div>
      <!-- 书导图 -->
      <div class="book-desc">
        <div class="book-desc-title">导图</div>
        <div style="overflow-y: scroll" class="book-desc-content">
          <MindMap :book_id="book_id" />
        </div>
      </div>
      <!--预览  -->
      <div class="book-desc">
        <div class="book-desc-title">预览</div>
        <div v-if="pdfUrl" @click.once="isShow">
          <BookPreview :src="pdfUrl" />
        </div>
        <div v-else style="font-size: 14px">暂无预览</div>
      </div>
      <!-- 书评 -->
      <div class="book-desc">
        <div class="book-desc-title">书评</div>
        <BookComments />
      </div>
      <!-- 笔记 -->
      <div class="book-desc">
        <div class="book-desc-title">笔记</div>
        <BookNotes />
      </div>
      <!-- 视频 -->
      <!-- 上传视频 -->
      <div class="pdf-choose-box" style="display: none;">
        <input class="pdf-choose-btn" type="file" multiple accept="video/*" ref="pdfInput" @change="onUploadFile" />
      </div>

      <div class="book-desc">
        <div class="book-desc-title">
          <div>
            视频
          </div>
          <div style="margin-left: 30px;">
            <i v-if="uploadLoading" class="el-icon-loading"></i>
            <img  v-else src="../../assets/editor/upload.png"  alt="" class="uploadImg" @click="playVideo">
          </div>
        </div>
        <div class="video_content">
          <div class="video_item" v-for="item in videoList" :key="item.book_id" @click="changeVideoItem(item)" :style="{
            backgroundImage: `url(https://api.bookor.com.cn/${item.video_img})`
          }">
            <!-- 删除图标 -->
            <i class="el-icon-circle-close  close_icon" @click.stop="delVideo(item)"></i>
            <i class="el-icon-video-play play_icon"></i>
          </div>
        </div>

      </div>

    </div>

    <div v-if="isShowBookTopic && echartsData" @click.stop style="justify-content: center; display: flex; margin: 0 10vw">
      <graph-echart :echartsData="echartsData" />
    </div>
  </div>
</template>

<script>
import request from '@/http/request'
import axiosMe from 'axios'
import StarRate from '@/components/public/star-rate.vue'
import MindMap from '@/components/public/mind-map.vue'

import BookPreview from '@/components/book-details/book-preview'
import BookComments from '@/components/book-details/book-comments'
import BookNotes from '@/components/book-details/book-notes'
import BookIssues from '@/components/book-details/book-issues'

import { GraphEchart } from '@/components/Echarts'
import { mapState } from 'vuex'
export default {
  props: {
    book_id: {
      type: Number,
      required: true
    }
  },
  provide () {
    return {
      book_id: this.book_id
    }
  },
  data () {
    return {
      uploadLoading: false,
      bookcover: '',
      bookScoreInitData: null,

      allTagList: '',
      selfTranslatorAuthorList: [], // 自己提交的未审核的译者信息
      introduce: '',

      // pdf预览
      pdfUrl: '',
      // 视频
      videoFile: '',
      // 视频上传多选列表
      videoFileList: [],
      // 视频列表展示
      videoList: [
      ]
    }
  },

  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      echartsData: (state) => state.echartsData,
      isShowBookTopic: (state) => state.isShowBookTopic,
      isShowBookDetail: (state) => state.isShowBookDetail,
      bookPDFUrl: (state) => state.bookPDFUrl
    }),
    // 用户的信息获取
    ...mapState('user', {
      userInfo: (state) => state.userInfo
    })

  },
  watch: {
    echartsData: function (newValue, oldValue) {
      // 在这里执行变量改变时的逻辑
    },
    isShowBookTopic: function (newValue, oldValue) {
      // 在这里执行变量改变时的逻辑
    }
  },
  components: {
    StarRate,
    MindMap,
    BookPreview,
    BookComments,
    BookNotes,
    BookIssues,

    GraphEchart
  },
  methods: {
    // 删除视频
    delVideo (item) {
      const params = {
        short_video_id: item.short_video_id
      }
      request({
        url: '/api/books/deleteBookShortVideo',
        params
      }).then(() => {
        this.getVideoList()
      })
    },
    // 获取视频资源
    getVideoList () {
      const params = {
        book_id: this.book_id,
        data_type: 'book_self_all',
        page: 1,
        limit: 888
      }
      request('/api/books/getBookShortVideoList', params, 'get').then((result) => {
        this.videoList = result?.list
      })
    },
    // 是否显示弹窗
    isShow () {
      if (this.bookPDFUrl) {
        this.$store.commit('book/showPBookDetail')
      } else {
        this.$message({
          message: '当前没有可以预览的书本详情',
          type: 'warning'
        })
      }
    },

    // 点击视频弹窗
    changeVideoItem (item) {
      // 让视频弹窗显示
      this.$store.commit('book/showBookVideo')
      this.$store.commit('book/setBookVideoItem', item)
    },

    // 上传视频按钮事件
    playVideo () {
      this.$refs.pdfInput.value = ''
      this.$refs.pdfInput.click()

      // 让视频弹窗显示
      // this.$store.commit('book/showBookVideo')
    },
    // 上传视频
    // 本地上传
    async onUploadFile () {
      const file = this.$refs.pdfInput.files[0]
      this.videoFile = file
      this.videoFileList = this.$refs.pdfInput.files
      if (!file) {
        return
      }
      if (file.size > (1024 * 1024 * 50)) {
        return this.$message({
          message: '视频大小不能超过50M',
          type: 'warning'
        })
      }
      await this.postFileList()
    },
    // 上传视频
    async postFile () {
      const formData = new FormData()
      formData.append('file', this.videoFile)
      formData.append('key', this.userInfo?.key)
      formData.append('upload_type', 'video')

      axiosMe({
        method: 'POST',
        url: '/api/upload/index',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(async res => {
        const result = res.data.result.short_url

        // 详情页短视频添加接口
        request('/api/books/uploadBookShortVideo', {
          short_video_url: result,
          book_id: this.book_id,
          video_title: this.videoFile.name
        }).then(() => {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          // 重新调用获取视频的接口,刷新视频展示
          this.getVideoList()
          this.uploadLoading = false
        }).catch(() => {
          this.uploadLoading = false
        })
      }).catch(() => {
        this.uploadLoading = false
      })
    },
    // 上传视频多选
    async postFileList () {
      this.uploadLoading = true
      // 添加多个视频
      for (let index = 0; index < this.videoFileList.length; index++) {
        const file = this.videoFileList[index]

        const formData = new FormData()
        formData.append('file', file)
        formData.append('key', this.userInfo?.key)
        formData.append('upload_type', 'video')

        await axiosMe({
          method: 'POST',
          url: '/api/upload/index',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: this.userInfo?.key
          }
        }).then(async (res) => {
          if (res.data.code === 200) {
            // 获取接口返回的短视频地址
            const url = res.data.result.short_url

            // 书本id上传短视频
            request('/api/books/uploadBookShortVideo', {
              short_video_url: url,
              book_id: this.book_id,
              video_title: file.name
            }).then((result) => {
              this.$message({
                message: '上传成功',
                type: 'success'
              })
              this.getVideoList()
            }).catch(() => {
              this.uploadLoading = false
            })
          }
        }).catch(() => {
          this.uploadLoading = false
        })
      }
    },

    // 获取书本信息
    shuaxin (search_click = false) {
      const params = {
        book_id: this.book_id,
        search_click
      }
      request('/api/book/bookCoverDetail', params).then((result) => {
        this.bookcover = result.bookcover
        const reg = /。/g
        const introduce = result.bookcover?.book_introduce
        if (introduce) {
          this.introduce = introduce.replace(reg, '。<br>')
        } else {
          this.introduce = '暂无更多简介'
        }
        this.$store.commit('book/SET_BOOKCOVER_DATA', this.bookcover)
      })
    },

    // 获取词条
    getTagList () {
      const params = {
        book_id: this.book_id
      }
      request('/api/keywords/getBookKeywordsRelevance', params, 'get')
        .then((result) => {
          if (result) {
            this.allTagList = result
            this.calcTag()
          }
        })
    },
    calcTag () { },

    // 获得评分
    getBookScoreData () {
      const params = {
        book_id: this.book_id
      }
      request('/api/books/toCalculateExpertRating', params, 'get')
        .then((result) => {
          const scoreData = result
          scoreData.heuristic = scoreData.heuristic_average_star * 2
          scoreData.individualization =
              scoreData.individualization_average_star * 2
          scoreData.legibility = scoreData.legibility_average_star * 2
          scoreData.logicality = scoreData.logicality_average_star * 2
          scoreData.professionalism =
              scoreData.professionalism_average_star * 2
          scoreData.total_average_score = parseFloat(
            scoreData.total_average_score
          ).toFixed(1)
          scoreData.total_average_star =
              parseFloat(scoreData.total_average_star) * 2
          this.bookScoreInitData = scoreData
        }).catch(() => {
          const scoreData = {}
          scoreData.heuristic = 0
          scoreData.individualization = 0
          scoreData.legibility = 0
          scoreData.logicality = 0
          scoreData.professionalism = 0
          this.bookScoreInitData = scoreData
        })
      this.$store.commit('book/SET_SCORE_DATA', this.bookScoreInitData)
    },

    // 查询本人提交的未审核译者信息
    apiGetSelfTranslatorMes () {
      const params = {
        book_id: this.book_id
      }
      request('/api/Author/getBookTranslator', params).then((res) => {
        this.selfTranslatorAuthorList = res
      })
    },

    // 获取图书pdf
    apiGetBookCover () {
      request('/api/book/bookPreview', {
        book_id: this.book_id
      })
        .then((result) => {
          if (result?.book_document) {
            this.pdfUrl = result.book_document
            // 把图书pdf存储在vuex
            this.$store.commit('book/setBookPDFUrl', this.pdfUrl)
          } else {
            this.$store.commit('book/setBookPDFUrl', '')
          }
        })
    }
  },
  mounted () {
    // 获取图书视频信息
    // TODO 看着没啥用，先不掉用吧
    // this.getVideoList()
    this.getTagList()
    this.shuaxin()
    this.getBookScoreData()
    this.apiGetSelfTranslatorMes()
    this.apiGetBookCover()

    const container = document.querySelector('.container')
    let isDown = false
    let startX
    let scrollLeft
    if (container) {
      container.addEventListener('mousedown', (e) => {
        isDown = true
        container.classList.add('active')
        startX = e.pageX - container.offsetLeft
        scrollLeft = container.scrollLeft
      })

      container.addEventListener('mouseleave', () => {
        isDown = false
        container.classList.remove('active')
      })

      container.addEventListener('mouseup', () => {
        isDown = false
        container.classList.remove('active')
      })

      container.addEventListener('mousemove', (e) => {
        if (!isDown) return
        e.preventDefault()
        const x = e.pageX - container.offsetLeft
        const walk = (x - startX) * 3
        container.scrollLeft = scrollLeft - walk
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./style.scss";
@import "@/assets/scss/global.scss";

.video_content {
  display: flex;
  height: 200px;
  // flex-direction: column;
  align-items: center;

  .video_item {
    position: relative;
    // padding: 5px;
    // border: 1px solid #fff;
    width: 100px;
    height: 100%;
    cursor: pointer;
    margin-top: 5px;
    margin-right: 7px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-repeat: no-repeat;
    background-size: cover;

    // &:hover {
    //   .play_icon {
    //     opacity: 1;
    //   }
    // }
    .close_icon {
      position: absolute;
      top: 0px;
      right: 0px;
      font-size: 25px;
      color: red;
      // display: none;
    }

    .play_icon {
      // opacity: 0;
      opacity: 1;
      font-size: 50px;
      // color: #ccc;
    }
  }
}

.container-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
}

.container {
  overflow-x: scroll;
  width: 100vw;
  color: #8fabb9;
  display: flex;
  height: 244px;
  position: absolute;
  bottom: 0;
  z-index: 1;

  .book-info {
    display: flex;
    @include box-border;
    background-color: #333333;
    margin-right: 8px;
    width: 381px;

    .book-info-img {
      width: 150px;
      height: 225px;
    }

    .book-info-content {
      margin-left: 8px;
      width: 300px;
      overflow-y: scroll;

      .book-info-content-title {
        font-weight: bold;
        text-align: center;
      }

      .book-score-data-wrap {
        padding-top: 10rpx;
        padding-left: 20rpx;

        .book-detail-score-num-wrap {
          display: flex;
          font-size: 14px;
          align-items: center;

          .book-detail-score-info {
            width: 19px;
            height: 19px;
            margin: 3px 0 0 3px;
            cursor: pointer;
          }
        }

        .bookScoreNum {
          color: #fcba01;
          font-weight: bold;
          margin: 0 8px 0 4px;
        }
      }

      .book-hot {
        font-size: 14px;

        span {
          font-size: 13px;
          color: #ca0303;
          font-weight: bold;
        }
      }

      .book-detail {
        margin-top: 6px;
        // overflow-y: scroll;
        // height: 144px;

        .txtWrapping {
          font-size: 14px;
          margin-bottom: 3px;

          .txtWrapping-sub {
            margin-right: 8px;
            font-weight: bold;
          }

          .txtWrapping-sub-cursor {
            cursor: pointer;
          }
        }

        .scrollTagRegionWrap {
          .tagBox {
            display: inline-block;
            cursor: pointer;
          }

          .tagItem {
            display: inline-block;
            padding: 3px 8px;
            background-color: #2e72b5;
            border-radius: 5px;
            font-size: 12px;
            color: white;
            margin: 4px 4px 0px 0;
            font-weight: bold;
          }
        }
      }
    }

    .book-info-content::-webkit-scrollbar {
      display: none;
    }
  }

  .book-desc {
    @include box-border;
    // background-color: #03070f;
    // width: 280px;
    flex-shrink: 0;
    margin-right: 8px;
    max-width: 300px;
    background-color: #333333;

    .book-desc-title {
      color: white;
      font-weight: bold;
      // padding-left: 10px;
      padding-bottom: 5px;
      border-bottom: 1px dotted #65818f;
      display: flex;
      // justify-content: space-between;
      align-items: center;
    }

    .book-desc-content {
      font-size: 14px;
      // padding: 10px;
      overflow-y: scroll;
      height: 187px;
      max-width: 360px;
    }

    .book-desc-content::-webkit-scrollbar {
      display: none;
    }
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

.pdf-choose-box {
  width: 100px;
  background-color: #00f;

}

.uploadImg {
  // border: 2px solid #28619a;
  // width: 40px;
  // height: 40px;
  width: 20px;
  height: 20px;
  text-align: center;
  border-radius: 4px;
  line-height: 15px;
  // color: white !important;
  filter: invert(1);
  // margin-top: 5px;
  cursor: pointer;
}
</style>
