import request from '@/http/request'
const corporateAccountClassify = {
  namespaced: true,
  state: {
    bookClassifyList: [],
    sellBookList: [], // 完整的
    sellBookListClassify: [] // 分类后的
  },
  mutations: {
    // 获取分类数据
    setBookClassifyList (state, val) {
      state.bookClassifyList = val
    },
    // 原始已上架图书数据
    SET_SELL_BOOK_LIST (state, sellBookList) {
      state.sellBookList = sellBookList
    },
    SET_SELL_BOOK_LIST_CLASSIFY (state, sellBookListClassify) {
      state.sellBookListClassify = sellBookListClassify
    }
  },
  actions: {
    // 获取已有的分类
    async getBookClassifyList ({ commit }) {
      try {
        request('/api/BookCategory/getFirstCategoryList', {}).then((result) => {
          const classifyData = []

          // 遍历一级分类
          for (const firstCategory of result) {
            const params = {
              pid: firstCategory.id
            }

            // 发送请求获取对应一级分类下的二级分类列表
            request(
              '/api/BookCategory/getSecondCategoryList',
              params
            ).then((result) => {
              const combinedData = {
                firstCategory: firstCategory,
                secondCategory: result
              }
              classifyData.push(combinedData)
            })
          }
          commit('setBookClassifyList', classifyData)
        })
      } catch (error) {
        console.error(error)
      }
    },

    // 获取某个分类的图书
    async getGoodsBookByCategory ({ commit }, id) {
      const params = {
        category_id: id
      }
      request(
        '/api/BookCategory/getGoodsBookByCategory',
        params
      ).then((result) => {
        const sellBookList = result
        commit('SET_SELL_BOOK_LIST_CLASSIFY', sellBookList)
      })
    }
  }
}

export default corporateAccountClassify
