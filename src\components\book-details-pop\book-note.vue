<template>
  <div @click.stop>
    <div class="note-box">
      <div class="note-box-top">笔记</div>
      <div v-html="this.noteContent" class="note-content no-scroll-bar"></div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import request from '@/http/request'
export default {
  data () {
    return {
      noteContent: ''
    }
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore,
      userInfo: (state) => state.userInfo
    }),
    ...mapState('book', {
      curBookNoteId: (state) => state.curBookNoteId
    })
  },
  methods: {
    async getNoteContent () {
      const url = '/mobile/Bookwrite/getNote'
      const info = {
        note_id: this.curBookNoteId,
        member_type: 'none'
      }
      const result = request(url, info)
      this.noteContent = result.content
    }
  },
  mounted () {
    this.getNoteContent()
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
.note-box {
  background-color: #ebf1f5;
  height: 80vh;
  width: 30vw;
  position: absolute;
  border-radius: 13px;
  left: 50%;
  top: 4%;
  transform: translateX(-50%);
  .note-box-top {
    text-align: center;
    margin-top: 3px;
    font-weight: 600;
  }
  .note-content {
    overflow-y: scroll;
    height: 74vh;
    padding: 2px 10px 0;
    font-size: 14px;
    background-color: white;
  }
  .no-scroll-bar::-webkit-scrollbar {
    display: none;
  }
}
</style>
