<template>
<div class="section">
  <div class="page-box">
      <div class="title-part1">“新阅读”用户享有的优质服务</div>
      <div class="content-box-part1">
          <div class="content-inside">
              <div class="content-inside-item">
                  <div class="child">
                      <img src="../img/pdf doc.png" />
                  </div>
                  <div class="content-sub-title-part1">
                      多文本编辑下载权
                  </div>
                  <div class="text">
                      PDF/Doc/Epub 跨格式文本摘抄、编辑和下载权限
                  </div>
              </div>
              <div class="content-inside-item">
                  <div class="child">
                      <img src="../img/doctor.png" />
                  </div>
                  <div class="content-sub-title-part1">
                      “解读专家”称号
                  </div>
                  <div class="text">
                      该称号与图书永久绑定，扩大影响力，享有流量扶持
                  </div>
              </div>
              <div class="content-inside-item">
                  <div class="child">
                      <img src="../img/佣金.png" class="img3"/>
                  </div>
                  <div class="content-sub-title-part1">
                      分享赚佣金
                  </div>
                  <div class="text">
                      分享到微信的已上架的图书链接，享有分销佣金
                  </div>
              </div>
              <div class="content-inside-item">
                  <div class="child">
                      <img src="../img/ai doctor.png" />
                  </div>
                  <div class="content-sub-title-part1">
                      AI知识顾问
                  </div>
                  <div class="text">
                      享有ChatGPT AI的知识解答，文本修改的权力
                  </div>
              </div>
          </div>
      </div>
      <div class="bottom-section">
          <div class="input">
              <input type="text" placeholder="输入邮箱地址" v-model="email" @keyup.enter="handleEnter"
                  class="email-box" />
          </div>
      </div>
  </div>
</div>
</template>
<script>
export default {
  data () {
    return {
      email: ''
    }
  },
  methods: {
    handleEnter () {
      this.$emit('handleEnter', this.email)
    }
  }
}
</script>
<style lang="scss" scoped>
.title-part1 {
  color: white;
  font-size: 26px;
  font-weight: 600;
  text-align: center;
  margin-top: 5%;
}
.content-inside{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  width: 1000px;
  margin: 6% auto;
  .content-inside-item{
    width: 23%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .child{
      width: 100px;
      height: 100px;
      background: #005878;
      border-radius: 10px;
      text-align: center;
      img{
        width: 80px;
        height: 80px;
        margin-top: 10px
      }
      .img3{
        width: 60px;
        height: 60px;
        margin-top: 20px;
      }
    }
  }
}
.text{
  color: #68696a;
  font-weight: 600;
  font-size: 14px;
  margin: 5px 0;
}
.content-sub-title-part1{
  color: #a78bfb;
  font-size: 22px;
  margin-top: 10%;
}
.bottom-section{
    .input{
      margin-top: 10%;
    }
  }
@media only screen and (max-width: 420px) {
  .title-part1 {
    font-size: 24px;
  }
  .content-sub-title-part1{
    color: #a78bfb;
    font-size: 18px;
    margin-top: 10px;
  }
  .content-inside{
    display: flex;
    flex-direction: column;
    width: 100%;
    .content-inside-item{
      width: 100%;
      .child{
        text-align: center;
        width: 70px;
        height: 70px;
        img{
          margin-top: 10px;
          width: 50px;
          height: 50px;
        }
        .img3{
          width: 30px;
          height: 30px;
          margin-top: 20px;
        }
      }
    }
  }
  .container .content .bottom-section{
    height: auto;
    display: flex;
    justify-content: center;
    .input{
      margin-top: 0;
    }
    input{
      padding: 4px 10px;
    }
  }
}
</style>
