<template>
  <div class="index">
    <!-- <nebula></nebula> -->
    <force-star
        v-if="forceData.nodes.length"
        :forceData="forceData"
        :bondList="bondList">
    </force-star>
    <div class="logo-box" @click="handleToHomePage">
      <div class="logo-before-B">
        <div>B</div>
        <div class="logo-after-B">B</div>
      </div>
    </div>
    <transition name="fade">
      <div
        v-if="!isNotFirstSearch"
        style="
          color: white;
          transform: translate(-50%, -50%);
          position: absolute;
          top: 35%;
          left: 50%;
          text-align: center;
        "
      >
        <div
          style="font-size: 51px; color: rgb(154, 182, 200); font-weight: 700"
        >
          Bookor Brain
        </div>
        <div style="font-size: 26px; font-weight: 600">联想 洞见 创见</div>
      </div>
    </transition>
    <div
      :style="{
        position: 'absolute',
        top: isNotFirstSearch ? (isMobile ? '5px' : '10px') : '50%',
        left: '50%',
        transition: 'all 0.5s ease',
        transform: isNotFirstSearch
          ? 'translateX(-50%)'
          : 'translate(-50%, -50%)',
      }"
    >
      <IndexSearch :forceData="forceData" :bondList="bondList"/>
    </div>
    <el-popover
    placement="top-start"
    width="600"
    trigger="hover"
    >
    <div class="category-list">
      <el-tag v-for="item in tagList" :key="item.classify_id" @click="getCategory(item.classify_id, item.type)">{{ item.classify_name }}</el-tag>
    </div>
    <div class="category-container" slot="reference">
      <img src="https://bookor-application.oss-cn-beijing.aliyuncs.com/category.png"/>
    </div>
  </el-popover>

    <!-- <div v-if="sliders.length" class="slider">
      <div
        class="list"
        ref="sliderList"
        :style="'transform: translateX(' + offX + 'px)'"
        @mousedown="handleMouseDown($event)"
        @mousemove="handleMouseMove($event)"
        @mouseup="handleMouseUp($event)"
      >
        <div
          class="item"
          v-for="(item, index) in sliders"
          :key="index"
          @click.stop="handleDetail(item.book_id)"
        >
          <img class="img" :src="item.book_image" />
        </div>
      </div>
    </div> -->
    <drop-welt />
    <div ref="cubeInfo" class="cube-info"></div>
  </div>
</template>

<script>
import request from '@/http/request'
import DropWelt from '@/components/dropwelt/index'
import ForceStar from '@/components/force-star/star.vue'
import IndexSearch from '@/components/index-search/index'
import { mapState } from 'vuex'
import { getRandomColor, pointConvertNode, lineConvertLink } from '../components/force-star/utils'
export default {
  name: 'Index',
  components: {
    ForceStar,
    DropWelt,
    IndexSearch
  },
  data () {
    return {
      forceData: {
        nodes: [],
        links: []
      },
      bondList: [],
      tagList: [],
      // sliders: [],
      offX: 0,
      isMobile: false // 移动端适配
    }
  },
  computed: {
    scrollWrapper () {
      return this.$refs.scrollContainer.$refs.wrap
    },
    ...mapState('indexsearch', {
      isNotFirstSearch: (state) => state.isNotFirstSearch
    })
  },

  created () {
    const finishedSplash = sessionStorage.getItem('finishedSplash')
    if (finishedSplash && finishedSplash === 'true') {
      this.$store.commit('SETISSHOWSPLASH', false)
    } else {
      setTimeout(() => {
        this.$store.commit('SETISSHOWSPLASH', true)
      }, 13000)
    }
  },
  methods: {
    handleToHomePage () {
      this.$router.push('/')
    },
    getLinks (nodes, list) {
      // 目前展示不满一页的数据
      const listMap = new Map()
      const newList = []
      // 太特么傻逼了，接口返回重复的数据，只能前端去过滤
      list.forEach((item) => {
        if (!listMap.has(item.mix_keywords_lines_id)) {
          listMap.set(item.mix_keywords_lines_id)
          newList.push(item)
        }
      })
      this.bondList = newList
      let points = []
      let pointLinks = []
      for (let i = 0; i < newList.length; i++) {
        const item = newList[i]
        const nodes = pointConvertNode(item.point_list, item.title[0])
        const links = lineConvertLink(nodes, item.line_list)
        points = [...points, ...nodes]
        pointLinks = [...pointLinks, ...links]
      }
      this.forceData.nodes = [...nodes, ...points]
      this.forceData.links = pointLinks
    },
    getCategoryList () {
      request('/api/Nebula/keywordsCategoryList', {
      }).then((result) => {
        this.tagList = result
      })
    },
    getData () {
      request('/api/Nebula/newKeywordsList', {
      }).then((result) => {
        const list = result
        const nodes = []
        for (let i = 0; i < list.length; i += 1) {
          const item = list[i]
          nodes.push({
            id: parseInt(Math.random() * 100000000000000000),
            user: item.keywords,
            color: getRandomColor(),
            bookId: item.book_keywords_id,
            nodeId: item.member_keywords_id
          })
        }
        this.forceData.nodes = [...nodes]
      })
    },
    getCategory (categoryId, common) {
      this.forceData.nodes = []
      request('/api/Nebula/getKeywordsByCategory', {
        category_id: categoryId,
        type: common
      }).then((result) => {
        const list = result.point_data
        if (!list.length) {
          this.$message.info('暂无词条')
          return
        }
        const nodes = []
        for (let i = 0; i < list.length; i += 1) {
          const item = list[i]
          nodes.push({
            id: parseInt(Math.random() * 100000000000000000),
            user: item.keywords,
            color: getRandomColor(),
            bookId: item.book_keywords_id,
            nodeId: item.member_keywords_id
          })
        }
        this.getLinks(nodes, result.line_data)
      })
    }
  },
  mounted () {
    this.getCategoryList()
    this.getData()
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/scss/index.scss";
@import "@/assets/scss/global.scss";
.index-mask {
  @include mask;
}
.fade-enter-active,
.fade-leave-active {
  transition: all 3s ease-in-out;
}
.fade-enter,
.fade-leave-to {
  transition: all 0.5s ease-out;
  opacity: 0;
}
.category-container{
  position: fixed;
  bottom: 100px;
  left: 40px;
  img{
    position: relative;
    width: 30px;
    height: 30px;
    cursor: pointer;
  }
}
.category-list{
  width: 540px;
  display:  flex;
  flex-wrap: wrap;
  gap: 8px;
  .el-tag{
    cursor: pointer;
  }
}
</style>
