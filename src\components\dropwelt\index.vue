<template>
  <div class="drop-welt">
    <div id="dom" class="nav-mini" v-if="!isMobile">
      <div
        class="nav-list"
        v-show="isList"
        style="transform: translate(calc(-50% + 6px), -82px)"
      >
        <div class="nav-item layer2" @click="handleChangeTabs(0)">
          <img
            src="https://bookor-application.oss-cn-beijing.aliyuncs.com/%E6%B0%94%E6%B3%A1%E5%9B%BE.png"
            alt=""
          />
        </div>
      </div>
      <!--AI和动态  -->
      <div
        class="nav-list"
        v-show="isList"
        style="transform: translate(calc(-50% + 6px), -42px)"
      >
        <div class="nav-item layer2" @click="handleChangeTabs(1)">
          <img
            src="https://jabinfo.oss-cn-beijing.aliyuncs.com/music.png"
            alt=""
          />
        </div>
      </div>
      <!-- 用户头像 -->
      <div
        v-show="isShowAvatar"
        class="main-icon"
        @click.stop="handleList()"
        @mousedown="startPress"
        @mouseup="endPress"
        @touchstart="startPress"
        @touchend="endPress"
      >
        <img :src="userInfo.member_avatar" />
      </div>
      <!-- 秒记和脑图 -->
      <div class="nav-list" v-show="isList">
        <div class="nav-item layer2" @click="handleChangeTabs(2)">
          <img
            src="https://oss.bookor.com.cn/static/img/mini_programs/images/20221113/dock-bar-icon-1.png"
            alt=""
          />
        </div>
        <div class="nav-item layer1" @click="handleChangeTabs(3)">
          <img
            src="https://oss.bookor.com.cn/static/img/mini_programs/images/20221113/dock-bar-icon-2.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div id="dom" class="nav-mini" v-else>
      <!-- 用户头像 -->
      <div
        v-show="isShowAvatar"
        class="main-icon"
        @click.stop="handleList()"
        @mousedown="startPress"
        @mouseup="endPress"
        @touchstart="startPress"
        @touchend="endPress"
      >
        <img :src="userInfo.member_avatar" />
      </div>
      <!--AI和动态  -->
      <!-- 秒记和脑图 -->
      <div class="nav-list" v-show="isList">
        <div class="AI-icon layer1">AI</div>
        <div class="nav-item layer2">
          <img
            src="https://oss.bookor.com.cn/static/img/mini_programs/images/20221113/home-nav-icon-3.png"
            alt=""
          />
        </div>
        <div class="nav-item layer2" @click="handleChangeTabs(2)">
          <img
            src="https://oss.bookor.com.cn/static/img/mini_programs/images/20221113/dock-bar-icon-1.png"
            alt=""
          />
        </div>
        <div class="nav-item layer1" @click="handleChangeTabs(3)">
          <img
            src="https://oss.bookor.com.cn/static/img/mini_programs/images/20221113/dock-bar-icon-2.png"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'DropWelt',
  data () {
    return {
      isShowSplash: false, // 是否展示开屏动画

      list: [
        { url: require('@/assets/img/icon_brain.png'), zIndex: 9 },
        { url: require('@/assets/img/icon_article.png'), zIndex: 8 },
        { url: require('@/assets/img/icon_cloud.png'), zIndex: 7 }
      ],
      isShowAvatar: true,
      isList: false,
      dom: null,
      domW: null,
      domH: null,
      cuntW: 0,
      cuntH: 0,
      beforeIndex: 0,
      // member_avatar: JSON.parse(localStorage.getItem("userinfo")).member_avatar,
      pressTimer: null, // 长按事件

      isMobile: false // 移动端适配
    }
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore,
      userInfo: (state) => state.userInfo
    }),
    fullImageUrl () {
      const baseUrl = 'https://oss.bookor.com.cn'
      return (imagePath) => {
        if (!imagePath) {
          return 'https://oss.bookor.com.cn/uploads/20191123/8d134c52225868b83013fa42ee065a27.jpg'
        }
        return baseUrl + imagePath
      }
    }
  },
  created () {
    // const finishedSplash = localStorage.getItem("finishedSplash");
    const finishedSplash = sessionStorage.getItem('finishedSplash')
    if (finishedSplash && finishedSplash === 'true') {
      this.isShowSplash = false
    } else {
      this.isShowAvatar = false
      setTimeout(() => {
        this.isShowSplash = true
        this.isList = true
        this.isShowAvatar = true
      }, 4000)
      setTimeout(() => {
        const layer1 = document.querySelectorAll('.layer1')
        layer1.forEach((item) => {
          item.style.opacity = '0'
        })
      }, 6000)
      setTimeout(() => {
        const layer2 = document.querySelectorAll('.layer2')
        layer2.forEach((item) => {
          item.style.opacity = '0'
        })

        this.isShowSplash = false
      }, 8000)
    }
  },
  mounted () {
    this.handleResize()
    const _this = this
    _this.$nextTick(() => {
      _this.dom = document.querySelector('#dom')
      _this.domW = _this.dom.offsetWidth
      _this.domH = _this.dom.offsetHeight
      //   _this.dom.style.left = parseInt(Math.random() * (document.body.offsetWidth - _this.domW)) + 'px';
      //   _this.dom.style.top = parseInt(Math.random() * (document.body.offsetHeight - _this.domH)) + 'px';
      _this.dom.onmousedown = function (e) {
        const domL = e.clientX - _this.dom.offsetLeft
        const domT = e.clientY - _this.dom.offsetTop
        document.onmousemove = function (e) {
          _this.cuntW = 0
          _this.cuntH = 0
          _this.dom.direction = ''
          _this.dom.style.left = e.clientX - domL + 'px'
          _this.dom.style.top = e.clientY - domT + 'px'
          if (e.clientX - domL < 5) {
            _this.dom.direction = 'left'
          }
          if (e.clientY - domT < 5) {
            _this.dom.direction = 'top'
          }
          if (e.clientX - domL > document.body.offsetWidth - _this.domW - 5) {
            _this.dom.direction = 'right'
          }
          if (e.clientY - domT > document.body.offsetHeight - _this.domH - 5) {
            _this.dom.direction = 'bottom'
          }
          _this.move(_this.dom, 0, 0)
        }
      }
      document.onmouseup = function () {
        document.onmousemove = null
      }
    })
  },
  methods: {
    handleList () {
      if (!this.isShowSplash) {
        const layer1 = document.querySelectorAll('.layer1')
        const layer2 = document.querySelectorAll('.layer2')
        if (layer1) {
          layer1.forEach((item) => {
            item.style.opacity = '1'
            item.classList.remove('layer1')
          })
        }
        if (layer2) {
          layer2.forEach((item) => {
            item.style.opacity = '1'
            item.classList.remove('layer2')
          })
        }
      }
      this.isList = !this.isList
    },
    move (obj, w, h) {
      if (obj.direction === 'left') {
        obj.style.left = 0 - w + 'px'
      } else if (obj.direction === 'right') {
        obj.style.left = document.body.offsetWidth - this.domW + w + 'px'
      }
      if (obj.direction === 'top') {
        obj.style.top = 0 - h + 'px'
      } else if (obj.direction === 'bottom') {
        obj.style.top = document.body.offsetHeight - this.domH + h + 'px'
      }
    },
    rate (obj, a) {
      obj.style.transform = ' rotate(' + a + ')'
    },
    action (obj) {
      const dir = obj.direction
      switch (dir) {
        case 'left':
          this.rate(obj, '90deg')
          break
        case 'right':
          this.rate(obj, '-90deg')
          break
        case 'top':
          this.rate(obj, '-180deg')
          break
        default:
          this.rate(obj, '-0')
          break
      }
    },
    // handleOver(index) {
    //   const item = JSON.parse(JSON.stringify(this.list[index]));
    //   this.beforeIndex = item.zIndex;
    //   item.zIndex = 15;
    //   this.$set(this.list, index, item);
    // },
    // handleOut(index) {
    //   const item = JSON.parse(JSON.stringify(this.list[index]));
    //   item.zIndex = this.beforeIndex;
    //   this.$set(this.list, index, item);
    // },

    // handleShowDetails(index) {
    //   if (index == 0) {
    //     this.$store.commit("dropwelt/handleMindMap");
    //   }
    // },

    // 功能切换
    handleChangeTabs (index) {
      // 甘特图
      if (index === 0) {
        this.$router.push('/editor/bubble')
      }

      // 每日快讯
      if (index === 1) {
        this.$router.push('/editor/mp3List')
      }

      // editor
      if (index === 2) {
        this.$router.push('/editor')
      }
      // 时间轴
      if (index === 3) {
        // 脑图
        this.$router.push('/mindList')
        // this.$store.commit('dropwelt/handleMindMap')
      }
    },

    // 个人空间
    startPress () {
      this.pressTimer = setTimeout(() => {
        this.$router.push('/personal')
        // 执行长按逻辑
      }, 800)
    },
    endPress () {
      clearTimeout(this.pressTimer)
    },

    handleResize () {
      this.isMobile = window.innerWidth < 768 // 这里只是读取 isMobile 的值
    }
  }
}
</script>

<style scoped lang="scss">
// ai-icon
.aiIcon{
  width: 35px;
  height: 35px;
  margin: auto;
}
.nav-mini {
  position: fixed;
  left: calc(100% - 34px);
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  .main-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: rgb(255, 242, 204);
    box-shadow: 2px 2px 2px 3px rgba(100, 100, 100, 0.6);
    overflow: hidden;
    position: relative;
    user-select: none;
    z-index: 10;
    transition: all 1s ease;
    img {
      width: 45px;
      height: 45px;
      -webkit-user-drag: none;
    }
  }
  .list {
    width: 50px;
    position: absolute;
    left: 50%;
    transform: translateX(calc(-50% + 8px));
    .item {
      width: 35px;
      height: 35px;
      // background: rgb(255, 242, 204);
      border-radius: 50%;
      // box-shadow: 2px 2px 2px 3px rgba(100, 100, 100, 0.6);
      margin-top: -5px;
      position: relative;
      img {
        display: block;
        width: 35px;
        height: 35px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      &:last-of-type {
        img {
          // width: 47px;
          // height: 47px;
        }
      }
      &:hover {
        transform: scale(1.2);
      }
    }
  }
  .nav-list {
    width: 50px;
    position: absolute;
    left: 50%;
    transform: translateX(calc(-50% + 10px));
    transition: all 1s ease;
    .nav-item {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      position: relative;

      // &:last-of-type {
      //   img {
      //     width: 47px;
      //     height: 47px;
      //   }
      // }
      &:hover {
        transform: scale(1.2);
      }
      img {
        display: block;
        width: 35px;
        height: 35px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .AI-icon {
      color: white;
      // background-color: #76a7d8;
      border-radius: 50%;
      height: 34px;
      width: 34px;
      text-align: center;
      line-height: 34px;
      font-size: 17px;
      font-weight: 600;
      // margin-left: 2px;
      margin-bottom: 2px;
      &:hover {
        transform: scale(1.2);
      }
    }
  }
  .layer1 {
    opacity: 1;
    transition: all 1s ease;
  }
  .layer2 {
    opacity: 1;
    transition: all 1s ease;
  }
}

@media (max-width: 768px) {
  .nav-mini {
    top: 4%;
  }
}
</style>
