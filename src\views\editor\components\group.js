import { getRectPort, getId } from './util'
class Group {
  constructor (graph) {
    this.ctrlPressed = false
    this.embedPadding = 20
    this.graph = graph

    this.init()
  }

  init () {
    this.graph.on('node:embedding', ({ e }) => {
      this.ctrlPressed = e.meta<PERSON>ey || e.ctrlKey
    })
    this.graph.on('node:embedded', () => {
      this.ctrlPressed = false
    })
    this.graph.on('node:change:size', ({ node, options }) => {
      if (options.skipParentHandler) {
        return
      }

      const children = node.getChildren()
      if (children && children.length) {
        node.prop('originSize', node.getSize())
      }
    })

    this.graph.on('node:change:position', ({ node, options }) => {
      if (options.skipParentHandler || this.ctrlPressed) {
        return
      }

      const children = node.getChildren()
      if (children && children.length) {
        node.prop('originPosition', node.getPosition())
      }

      const parent = node.getParent()
      if (parent && parent.isNode()) {
        let originSize = parent.prop('originSize')
        if (originSize == null) {
          originSize = parent.getSize()
          parent.prop('originSize', originSize)
        }

        let originPosition = parent.prop('originPosition')
        if (originPosition == null) {
          originPosition = parent.getPosition()
          parent.prop('originPosition', originPosition)
        }

        let x = originPosition.x
        let y = originPosition.y
        let cornerX = originPosition.x + originSize.width
        let cornerY = originPosition.y + originSize.height
        let hasChange = false

        const children = parent.getChildren()
        if (children) {
          children.forEach((child) => {
            const bbox = child.getBBox().inflate(this.embedPadding)
            const corner = bbox.getCorner()

            if (bbox.x < x) {
              x = bbox.x
              hasChange = true
            }

            if (bbox.y < y) {
              y = bbox.y
              hasChange = true
            }

            if (corner.x > cornerX) {
              cornerX = corner.x
              hasChange = true
            }

            if (corner.y > cornerY) {
              cornerY = corner.y
              hasChange = true
            }
          })
        }

        if (hasChange) {
          parent.prop(
            {
              position: { x, y },
              size: { width: cornerX - x, height: cornerY - y }
            },
            { skipParentHandler: true }
          )
        }
      }
    })
  }

  addGroup () {
    const id = getId()
    this.graph.addNode({
      id,
      x: 40,
      y: 40,
      width: 360,
      height: 160,
      zIndex: 0,
      label: '元素组',
      attrs: {
        body: {
          fill: '#fffbe6',
          stroke: '#999',
          strokeDasharray: '5 3',
          strokeWidth: 1
        },
        label: {
          fontSize: 12,
          refX: 0.5, // 水平锚点，0.5 表示标签居中
          refY: 0.9 // 垂直锚点，0.5 表示标签
        }
      },
      data: {
        id,
        isGroup: true
      },
      ports: getRectPort(360, 160)
    })
    // parent.addChild(source)
    // parent.addChild(target)
  }

  delGroup () {

  }
}

export default Group
