import * as THREE from 'three'
import { Star } from './star.js'
import {
  CORE_X_DIST,
  CORE_Y_DIST,
  GALAXY_THICKNESS,
  NUM_STARS
} from '../config/galaxyConfig.js'
import { gaussianRandom } from '../utils.js'
import { Haze } from './haze.js'
// import { Interactive } from "three.interactive";

export class Galaxy {
  constructor (scene) {
    this.scene = scene
    const star_num = NUM_STARS
    this.stars = this.generateObject(star_num, (pos) => new Star(pos))

    this.haze = this.generateObject(star_num, (pos) => new Haze(pos))

    this.stars.forEach((star) => {
      star.toThreeObject(scene)
    })
    this.haze.forEach((haze) => haze.toThreeObject(scene))
  }

  updateScale (camera) {
    this.stars.forEach((star) => {
      star.updateScale(camera)
    })

    this.haze.forEach((haze) => {
      haze.updateScale(camera)
    })
  }

  generateObject (numStars, generator) {
    const objects = []

    let idx = 0 // 初始化 idx

    for (let i = 0; i < numStars; i++) {
      const pos = new THREE.Vector3(
        gaussianRandom(0, CORE_X_DIST * 4),
        gaussianRandom(0, CORE_Y_DIST * 4),
        gaussianRandom(0, GALAXY_THICKNESS * 4)
      )
      const obj = generator(pos) // 传入 idx
      objects.push(obj)
      idx++ // 自增 idx
    }

    return objects
  }
}
