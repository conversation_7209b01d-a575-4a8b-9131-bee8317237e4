import * as THREE from 'three'
import { starTypes } from '../starDistributions.js'

const texture = new THREE.TextureLoader().load(require('./white.png'))

let materials
materials = starTypes.color.map(
  (color) =>
    new THREE.SpriteMaterial({
      map: texture,
      color: color
    })
)

export class Star {
  constructor (position) {
    this.position = position
    this.starType = this.generateStarType()
    this.obj = null
  }

  generateStarType () {
    let num = Math.random() * 100.0
    const pct = starTypes.percentage
    for (let i = 0; i < pct.length; i++) {
      num -= pct[i]
      if (num < 0) {
        return i
      }
    }
    return 0
  }

  updateScale (camera) {
    const dist = this.position.distanceTo(camera.position) / 450

    // update star size
    let starSize = dist * starTypes.size[this.starType]
    starSize = this.clamp(starSize, 1, 10)
    this.obj?.scale.copy(new THREE.Vector3(starSize, starSize, starSize))
  }

  clamp (value, minimum, maximum) {
    return Math.min(maximum, Math.max(minimum, value))
  }

  toThreeObject (scene) {
    const sprite = new THREE.Sprite(materials[this.starType])

    sprite.scale.multiplyScalar(starTypes.size[this.starType])
    sprite.position.copy(this.position)

    this.obj = sprite
    scene.add(sprite)
  }
}
