
<template>
  <div class="edit-padding-box" v-if="link">
    <iframe :key="link" :src="link" scrolling="yes" ></iframe>
  </div>
</template>

<script>
// import { Loading } from 'element-ui'
export default {
  data () {
    return {
      link: null,
      loadingInstance: null
    }
  },

  created () {
    this.$watch('$route.query', (newQuery) => {
      // this.loadingInstance = Loading.service({
      //   fullscreen: true, // 是否全屏遮罩
      //   text: '加载中...', // 显示的文本
      //   background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
      // })
      const id = this.$route.query.id
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      const url = `https://mnwv.bookor.com.cn/#/book-review/${userInfo.key}?is_release=true&member_id=${userInfo.userid}&book_id=&book_title=&book_comment_id=${id}&type=note`
      this.link = url
      // setTimeout(() => {
      //   this.loadingInstance.close()
      // }, 1000)
    })
  },
  mounted () {
    // this.loadingInstance = Loading.service({
    //   fullscreen: true, // 是否全屏遮罩
    //   text: '加载中...', // 显示的文本
    //   background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
    // })
    const id = this.$route.query.id
    const userInfo = JSON.parse(localStorage.getItem('userinfo'))
    const url = `https://mnwv.bookor.com.cn/#/book-review/${userInfo.key}?is_release=true&member_id=${userInfo.userid}&book_id=&book_title=&book_comment_id=${id}&type=note`
    this.link = url
    // setTimeout(() => {
    //   this.loadingInstance.close()
    // }, 1000)
  }
}
</script>
<style lang="scss" scoped>
.edit-padding-box{
  height: 100vh;
  width: 80vw;
  iframe{
    margin-top: 10px;
    width: 96%;
    height: 93vh;
    border: none;
    margin-left: 2%;
  }
}
</style>
