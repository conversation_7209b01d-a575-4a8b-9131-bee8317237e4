<template>
  <div>
    <base-echart
      :id="treeId"
      class="tree-echart"
      :width="width"
      :height="height"
      :option="option"
    />
  </div>
</template>

<script>
import BaseEchart from './BaseEchart.vue'
export default {
  name: 'eCharts',
  data () {
    return {
      dataArray: [], // 空数组用来存放数据
      option: {},
      localHeight: '' // 添加localHeight变量，用于保存动态高度
    }
  },
  props: {
    treeObj: {
      type: Object,
      required: true
    },
    treeId: {
      type: String
    },
    width: {
      type: String
    },
    height: {
      type: String
    },
    isCut: {
      type: Boolean,
      default: false
    }
  },
  components: {
    BaseEchart
  },
  watch: {
    treeObj: {
      handler (newVal) {
        if (Object.keys(newVal).length !== 0) {
          this.treedata = this.formatTreeData(newVal)
          this.dataArray.push(this.treedata)
          if (this.isCut) {
            this.dataArray.forEach((node) => {
              this.truncateNestedChildren(node, 1)
            })
          }
          if (this.isCut) {
            this.option = this.setOptionCut()
          } else {
            this.option = this.setOption()
          }
        }
      },
      immediate: true, // 初始立即执行一次
      deep: true // 监听对象的深层属性变化
    }
  },
  mounted () {
  },
  methods: {
    setOptionCut () {
      return {
        series: [
          {
            type: 'tree',
            data: this.dataArray,

            // 添加点击事件处理函数
            top: '1%',
            left: '15%',
            bottom: '1%',
            right: '15%',

            edgeShape: 'curve', // 连接线类型 curve曲线 polyline折线
            symbol: 'rectangle', // 长方形
            symbolSize: [60, 30], // 长方形的宽、高
            itemStyle: {
              normal: {
                color: '#525252', // 节点背景色
                borderWidth: 0.5,
                borderColor: '#a2a2a2',
                lineStyle: {
                  color: '#eaeaea',
                  width: 1,
                  type: 'solid' // 连线的样式 'curve'|'broken'|'solid'|'dotted'|'dashed'
                }
              }
            },

            orient: 'horizontal', // 放置组件的方式：水平（'horizontal'）或者竖直（'vertical'）
            initialTreeDepth: 2,

            label: {
              // 每个节点所对应的标签的样式
              normal: {
                formatter: function (params) {
                  var maxLength = 4 // 最大显示字符数
                  var content = params.name
                  if (content.length > maxLength) {
                    content = content.substring(0, maxLength) + '...' // 超出5个字符使用省略号
                  }
                  return content // 返回处理后的节点名称
                },
                fontSize: 12, // 文字的字体大小
                color: '#eaeaea'
              }
            },

            expandAndCollapse: false,

            emphasis: {
              disabled: true
            },
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      }
    },
    setOption () {
      return {
        series: [
          {
            type: 'tree',
            data: this.dataArray,
            edgeShape: 'curve',
            top: '15%',
            left: '10%',
            bottom: '15%',
            right: '25%',
            nodeClick: false,
            symbolSize: 7,
            roam: true, // 缩放，平移开启
            zoom: 1,

            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 12,
              color: '#FFFFFF',
              backgroundColor: '#525252',
              borderColor: '#777777',
              borderWidth: 1,
              borderType: 'solid',
              formatter: function (params) {
                var text = params.name
                var len = text.length

                var lines = []
                var line = ''
                var width = 0

                for (var i = 0; i < len; i++) {
                  var char = text.charAt(i)
                  var charWidth = /[^\x00-\xff]/.test(char) ? 12 : 6 // 判断字符是否是中文字符
                  var lineWidth = width + charWidth

                  if (lineWidth > 180) {
                    lines.push(line)
                    line = ''
                    width = 0
                  }

                  line += char
                  width += charWidth
                }
                lines.push(line)
                var wrapText = lines.join('\n')
                return '{wrap|' + wrapText + '}'
              },

              rich: {
                wrap: {
                  padding: [4, 4, 4, 4],
                  width: 'auto', // 自适应宽度
                  overflow: 'break'
                  // textOverflow: "ellipsis",
                }
              }
            },
            labelLayout: {
              moveOverlap: 'shiftY'
              // draggable: true, // 开启节点拖动功能
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left',
                color: '#FFFFFF',
                backgroundColor: '#525252', // 添加标签背景颜色
                borderColor: '#777777', // 添加标签边框颜色
                borderWidth: 1, // 添加标签边框宽度
                borderType: 'solid' // 添加标签边框类型
              }
            },
            initialTreeDepth: 2,
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750,
            edgeLength: 10, // 调整连线长度
            nodeGap: 30 // 调整节点间距
          }
        ]
      }
    },
    formatTreeData (node) {
      const data = {
        name: node.content,
        children: []
      }
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          data.children.push(this.formatTreeData(child))
        }
      }

      return data
    },

    // 节选只显示3层，最多只显示3个节点
    truncateNestedChildren (node, level) {
      // 设置每层最多显示的节点数量
      const maxNodesPerLevel = 3
      let displayedNodes = 0

      if (level > 2) {
        node.children = []
        return
      }

      if (node.children && node.children.length > 0) {
        node.children = node.children.filter((child) => {
          displayedNodes++
          return displayedNodes <= maxNodesPerLevel
        })

        node.children.forEach((child) => {
          this.truncateNestedChildren(child, level + 1)
        })
      }
    }
  }
}
</script>

<style></style>
