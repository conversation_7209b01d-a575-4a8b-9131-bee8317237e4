<template>
  <div>
    <vue-office-docx class="docx-container" :src="url"/>
  </div>
</template>

<script>
// 引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx'
// 引入相关样式
import '@vue-office/docx/lib/index.css'

export default {
  components: {
    VueOfficeDocx
  },
  props: {
    url: {
      type: String,
      default: ''
    }
  },

  watch: {
    url (value) {
      const container = document.querySelector('.docx-container')
      if (container) {
        container.scrollTo(0, 0)
      }
    }
  },

  data () {
    return {
      // url: "https://api.bookor.com.cn/uploads/pdf2word/a330ced3d3fbb9d8c9a1a302e6b0d431.docx", //设置文档网络地址，可以是相对地址
    }
  },
  methods: {
  }
}
</script>

<style scoped lang="scss">
.docx-container{
  border: 1px solid #ccc;
}
.docx-container ::v-deep .docx-wrapper {
  background-color: #d5d5d8;
  padding: 0;
}
.docx-container ::v-deep .docx-wrapper > section.docx {
  width: 68vw !important;
  padding: 20px !important;
}

.docx-container {
  height: 93vh;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // display: none;
  }
  // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
  &::-webkit-scrollbar-button {
    display: none;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }
  // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    cursor: pointer;
    border-radius: 4px;
  }
  // 边角，即两个滚动条的交汇处
  &::-webkit-scrollbar-corner {
    display: none;
  }
  // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
  &::-webkit-resizer {
    display: none;
  }
}

@media (max-width: 600px) {
  .docx-container ::v-deep .docx-wrapper > section.docx {
    width: 93vw !important;
  }
}
</style>
