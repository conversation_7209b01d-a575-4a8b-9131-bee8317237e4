<template>
  <div class="container" v-if="isShowPopup" @click="$store.commit('book/closePopup')">
    <!-- 书本详情 -->
    <div v-if="isShowPBookDetail">
      <BookDetail :src="bookPDFUrl"></BookDetail>
    </div>
    <!-- 议题echarts -->
    <!-- <div v-if="isShowBookTopic">
      <BookTopicEcharts />
    </div> -->
    <!-- 评分弹窗 -->
    <div v-if="isShowBookScoreInfo">
      <BookScoreInfo />
    </div>
    <!-- 作者弹窗 -->
    <div v-if="isShowBookAuthor" style="
        display: flex;
        justify-content: center;

        position: absolute;
        bottom: 244px;
        left: 50%;
        transform: translateX(-50%);
      ">
      <BookAuthor />
    </div>
    <!-- 标签弹窗 -->
    <div v-if="isShowTag">
      <BookTag />
    </div>
    <!-- 书评笔记 -->
    <div v-if="isShowBookNote">
      <BookNote />
    </div>
    <!-- 视频弹窗 -->
    <div v-if="isShowBookVideo">
     <BookVideo/>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import BookScoreInfo from '@/components/book-details-pop/book-score-info'
import BookAuthor from '@/components/book-details-pop/book-author'
import BookTag from '@/components/book-details-pop/book-tag'
import BookTopicEcharts from '@/components/book-details-pop/book-topic-echarts'
import BookNote from '@/components/book-details-pop/book-note'
import BookDetail from '@/components/book-details-pop/book-detail'
import BookVideo from '@/components/book-details-pop/book-video'
export default {
  components: {
    BookScoreInfo,
    BookAuthor,
    BookTag,
    BookTopicEcharts,
    BookNote,
    BookDetail,
    BookVideo
  },
  data () {
    return {}
  },
  computed: {
    ...mapState('book', {
      isShowPopup: (state) => state.isShowPopup,
      isShowBookScoreInfo: (state) => state.isShowBookScoreInfo,
      isShowBookAuthor: (state) => state.isShowBookAuthor,
      isShowTag: (state) => state.isShowTag,
      isShowBookTopic: (state) => state.isShowBookTopic,
      isShowBookNote: (state) => state.isShowBookNote,
      isShowPBookDetail: (state) => state.isShowPBookDetail,
      // 新增的书本的视频
      isShowBookVideo: (state) => state.isShowBookVideo,
      // pdf
      bookPDFUrl: (state) => state.bookPDFUrl

    }),
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    })
  }
}
</script>
<style scoped lang="scss">
@import "@/assets/scss/global.scss";

.container {
  // background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
  @include mask;
}

.flex-cc {
  @include center-both;
  height: 100vh;
}
</style>
