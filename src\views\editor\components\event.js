// import { getId } from './util'

// 快捷键处理
class GraphOnEvent {
  constructor (graph, history) {
    this.graph = graph
    this.history = history
  }

  // 快捷键
  init (keyboard, selection, clipboard) {
    keyboard.bindKey('backspace', () => {
      const cells = selection.getSelectedCells()
      cells.forEach(current => {
        const childrenList = this.graph.getCells().filter(item => {
          return (
            item?.data?.id?.includes(current.id) &&
            item?.data?.id !== current.id
          )
        })
        if (childrenList.length) {
          childrenList.forEach(item => {
            this.graph.removeCell(item)
          })
        }
      })
      this.graph.removeCells(cells)
      return false
    })
    keyboard.bindKey('ctrl+c', () => {
      const cells = selection.getSelectedCells()
      if (cells.length) {
        // 如果是子节点，则不可以进行复制
        if (cells[0].getProp().isChild) {
          return
        }
        const children = cells[0]?.children ?  cells[0].children : []
        clipboard.copy([...cells, ...children ])
      }
      return false
    })
    keyboard.bindKey('ctrl+v', () => {
      const list = clipboard.getCellsInClipboard()
      const edgesList = [] // 记录所有的连线数据
      list.forEach(item => {
        const newId = item.id // 新元素的id
        const copyId = item.data?.id
        const nodeList = this.graph.getNodes()
        const edgeList = this.graph.getEdges()
        const childNodeList = nodeList.filter(item => {
          if (item?.data?.id?.includes(copyId) && item?.data?.id !== copyId) {
            return item
          }
        })
        childNodeList.forEach(childItem => {
          const current = JSON.parse(JSON.stringify(childItem))
          const childId = newId + parseInt(Math.random() * 100000)
          current.id = childId
          current.data.id = childId
          const { x, y } = current.position
          current.position = {
            x: x + 48,
            y: y + 48
          }
          this.graph.addNode(current)
          edgeList.forEach(child => {
            if (
              child?.target?.cell?.includes(childItem.data.id) &&
              child.source.cell.includes(copyId)
            ) {
              edgesList.push({
                source: {
                  cell: newId,
                  port: child.source.port,
                  vertices: child.vertices
                },
                target: {
                  cell: childId,
                  port: child.target.port,
                  vertices: child.vertices
                }
              })
            }
          })
        })
        if (item.data) {
          item.data.id = item?.id
        }
      })
      if (list.length) {
        clipboard.paste({
          offset: 48,
          nodeProps: {
            zIndex: 0
          }
        })
      }
      for (let i = 0; i < edgesList.length; i++) {
        const itemEdge = edgesList[i]
        const { vertices } = itemEdge.source
        this.graph.addEdge({
          source: { cell: itemEdge.source.cell, port: itemEdge.source.port },
          target: { cell: itemEdge.target.cell, port: itemEdge.target.port },
          attrs: {
            line: {
              stroke: '#8f8f8f',
              strokeWidth: 2,
              targetMarker: '',
              sourceMarker: ''
            }
          },
          vertices: { x: vertices.x + 48, y: vertices.y + 48 },
          zIndex: 0,
          connector: 'smooth'
        })
      }
    })
    keyboard.bindKey('ctrl+x', () => {
      const cells = selection.getSelectedCells()
      if (cells.length) {
        this.graph.removeCells(cells)
      }
      return false
    })
    keyboard.bindKey('ctrl+z', () => {
      this.history.undo()
      return false
    })
    keyboard.bindKey('ctrl+y', () => {
      this.history.redo()
      return false
    })
  }
}

export default GraphOnEvent
