
<template>
  <div
    class="edit-padding-box"
  >
    <a
      :href="wordHtml"
      class="change-to-word"
      style="top: 11px; text-decoration: none"
      download
      >下载</a
    >
    <VueOfficeDocx :url="wordHtml" />

    <notePop
      v-if="paperId"
      :selectText="selectText"
      :chapterInformation="chapterInformation"
      :isShowNotePop="isShowNotePop"
      :notePopTop="notePopTop"
      :notePopLeft="notePopLeft"
      :paper_id="paperId"
      :entry_position="entry_position"
    />
  </div>
</template>

<script>
import VueOfficeDocx from '@/components/editor/vue-office-docx'
import notePop from '@/components/editor/note-editor'
export default {
  data () {
    return {
      wordHtml: '',
      paperId: 0,
      isShowNotePop: false,
      selectText: '',
      notePopTop: null,
      notePopLeft: null,
      entry_position: '', // 当前划线在文档中的位置
      chapterInformation: '' // 完整信息：书名+大纲信息
    }
  },
  components: {
    VueOfficeDocx,
    notePop
  },
  created () {
    this.$watch('$route.query', (newQuery) => {
      const url = this.$route.query.url
      this.paperId = Number(this.$route.query.paperId)
      this.wordHtml = url
    })
  },
  methods: {
    // 获取位置信息
    getSelectedTextRect (range) {
      const rect = range.getBoundingClientRect()

      const span =
        range.startContainer.parentNode.nodeName === 'SPAN'
          ? range.startContainer.parentNode
          : null
      return {
        x: span?.style?.left,
        y: span?.style?.top,
        width: rect.width,
        height: rect.height
      }
    },
    // 选中word的内容
    handleMouseUp () {
      const selection = window.getSelection()
      if (selection && !selection.isCollapsed) {
        const range = selection.getRangeAt(0)
        const selectedText = range.toString()

        this.selectText = selectedText
        this.chapterInformation = this.chapterInformation_bookTitle + ' '
        if (selectedText) {
          this.notePopTop = range.getBoundingClientRect().top - 60
          this.notePopLeft = range.getBoundingClientRect().left
          this.isShowNotePop = true
        } else {
          this.isShowNotePop = false
        }
      }
    },
    getSelectTextWord () {
      document.addEventListener('mouseup', this.handleMouseUp)
    }
  },
  mounted () {
    const url = this.$route.query.url
    this.paperId = Number(this.$route.query.paperId)
    this.wordHtml = url

    this.getSelectTextWord()
  }
}
</script>

<style lang="scss" scoped>
.edit-padding-box{
  height: 100vh;
  padding-top: 10px;
}
.change-to-word{
  position: absolute;
    color: #fff;
    right: 132px;
    background-color: grey;
    top: 11px;
    border-radius: 5px;
    padding: 0 4px;
    cursor: pointer;
}
</style>
