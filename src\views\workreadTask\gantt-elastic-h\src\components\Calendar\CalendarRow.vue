<!--
/**
 * @fileoverview CalendarRow component
 * @license MIT
 * <AUTHOR> <<EMAIL>>
 * @package GanttElastic
 */
-->
<template>
  <div :class="'gantt-elastic__calendar-row gantt-elastic__calendar-row--' + which" :style="rowStyle">
    <div
      v-for="(item, itemIndex) in items"
      :key="item.key"
      :class="'gantt-elastic__calendar-row-rect gantt-elastic__calendar-row-rect--' + which"
      :style="rectStyle"
    >
      <div
        :class="'gantt-elastic__calendar-row-rect-child gantt-elastic__calendar-row-rect-child--' + which"
        v-for="(child, childIndex) in item.children"
        :key="child.key"
        :style="rectChildStyle[itemIndex][childIndex]"
      >
        <div
          :class="'gantt-elastic__calendar-row-text gantt-elastic__calendar-row-text--' + which"
          :style="textStyle(child)"
        >
          {{ child.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CalendarRow',
  inject: ['root'],
  props: ['items', 'which'],
  data() {
    return {};
  },
  methods: {
    /**
     * Get x position
     *
     * @returns {number}
     */
    getTextX(item) {
      let x = item.x + item.width / 2 - item.textWidth / 2;
      if (this.which === 'month' && this.root.isInsideViewPort(item.x, item.width, 0)) {
        let scrollWidth = this.root.state.options.scroll.chart.right - this.root.state.options.scroll.chart.left;
        x = this.root.state.options.scroll.chart.left + scrollWidth / 2 - item.textWidth / 2 + 2;
        if (x + item.textWidth + 2 > item.x + item.width) {
          x = item.x + item.width - item.textWidth - 2;
        } else if (x < item.x) {
          x = item.x + 2;
        }
      }
      return x - item.x;
    },
  },
  computed: {
    rowStyle() {
      return {
        ...this.root.style['calendar-row'],
        ...this.root.style['calendar-row--' + this.which],
      };
    },
    rectStyle() {
      return {
        ...this.root.style['calendar-row-rect'],
        ...this.root.style['calendar-row-rect--' + this.which],
      };
    },
    rectChildStyle() {
      const basicStyle = {
        ...this.root.style['calendar-row-rect-child'],
        ...this.root.style['calendar-row-rect-child--' + this.which],
      };
      const style = [];
      for (let item of this.items) {
        const childrenStyle = [];
        for (let child of item.children) {
          childrenStyle.push({
            ...basicStyle,
            width: child.width + 'px',
            height: child.height + 'px',
          });
        }
        style.push(childrenStyle);
      }
      return style;
    },
    textStyle() {
      const basicStyle = {
        ...this.root.style['calendar-row-text'],
        ...this.root.style['calendar-row-text--' + this.which],
      };
      return (child) => {
        const style = { ...basicStyle };
        if (this.which === 'month') {
          style.left = this.getTextX(child) + 'px';
        }
        return style;
      };
    },
  },
};
</script>
