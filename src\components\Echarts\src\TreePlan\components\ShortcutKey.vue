<template>
  <Sidebar ref="sidebar" title="快捷键">
    <div class="box">
      <div v-for="item in shortcutKeyList" :key="item.type">
        <div class="title">{{ item.type }}</div>
        <div class="list" v-for="item2 in item.list" :key="item2.value">
          <div class="item">
            <span
              v-if="item2.icon"
              class="icon iconfont"
              :class="[item2.icon]"
            ></span>
            <span class="name" :title="item2.name">{{ item2.name }}</span>
            <div class="value" :title="item2.value">{{ item2.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </Sidebar>
</template>

<script>
import { mapState } from 'vuex';
import { shortcutKeyList } from '../config/index';
import Sidebar from './Sidebar.vue';

/**
 * @Author: 王林
 * @Date: 2021-06-24 22:54:14
 * @Desc: 快捷键
 */
export default {
  name: 'ShortcutKey',
  components: {
    Sidebar
  },
  data() {
    return {}
  },
  props: {
    mindMap: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapState({
      activeSidebar: state => state.mind.activeSidebar
    }),

    shortcutKeyList() {
      return shortcutKeyList.zh
    }
  },
  methods: {
    open() {
      if (this.$refs.sidebar) {
        this.$refs.sidebar.show = true
      }
    },
    close() {
      this.$refs.sidebar.show = false
    }
  },
  watch: {
    activeSidebar(val) {
      if (val === 'shortcutKey') {
        this.$refs.sidebar.show = true
      } else {
        this.$refs.sidebar.show = false
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.box {
  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 26px 0 20px;
  }

  .list {
    font-size: 14px;

    .item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .icon {
        font-size: 16px;
        margin-right: 16px;
      }

      .name {
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .value {
        color: #909090;
        margin-left: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
