<template>
  <div>
    <div v-if="comment.length > 0" class="comment-box">
      <div>
        {{ comment[0].main_title }}
      </div>
      <div v-html="comment[0].comment"></div>
    </div>
    <div v-else-if="outCommentList.length > 0">
      <div>{{ outCommentList[0].title }}</div>
      <div v-html="outCommentList[0].introduce"></div>
    </div>
    <div v-else style="font-size: 14px">暂无精彩书评</div>
  </div>
</template>

<script>
// import axios from '@/http'
import request from '@/http/request';
export default {
  inject: ['book_id'],
  data () {
    return {
      comment: [],
      outCommentList: []
    }
  },
  methods: {
    // 获取书评
    getCommentSta () {
      const promise1 = request('/api/Book/getLocalWonderfulBookComment', {
        page: 1,
        limit: 10,
        book_id: this.book_id
      })
      const promise2 = request('/api/Book/getBookCommentArticle', {
        page: 1,
        limit: 10,
        book_id: this.book_id
      })
      Promise.all([promise1, promise2]).then((res) => {
        const result1 = res[0]
        const result2 = res[1]
        const changedResult = this.getCommentImg(result1.concat(result2))
        this.comment = changedResult
      })
    },
    // 获取本地书评所有对应的文中图片
    getCommentImg (comments) {
      const that = this
      comments.forEach((item) => {
        const imgReg = /<img.*?(?:>|\/>)/gi // 匹配图片中的img标签
        const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i // 匹配图片中的src
        const str = item.comment
        const arr = str.match(imgReg) // 筛选出所有的img
        if (arr) {
          const srcArr = []
          for (let i = 0; i < arr.length; i++) {
            const src = arr[i].match(srcReg)
            // 获取图片地址
            srcArr.push(src[1])
          }
          item.firstImg = srcArr[0]
        } else {
          if (that.bookcover && that.bookcover.book_image) {
            item.firstImg = that.bookcover.book_image
          } else {
            item.firstImg = ''
          }
        }
      })
      return comments
    },
    getCommentOut () {
      request('/api/Book/getForeignWonderfulBookComment', {
        page: 1,
        limit: 10,
        book_id: this.book_id
      }).then((res) => {
        this.outCommentList = res.result
      })
    }
  },
  mounted () {
    this.getCommentSta()
    this.getCommentOut()
  }
}
</script>

<style scoped lang="scss">
.comment-box {
  height: 195px;
  overflow-y: scroll;
  padding: 5px 0;
  font-size: 14px;
}
.comment-box::-webkit-scrollbar {
  display: none;
}
</style>
