<template>
  <div class="search-con">
    <img class="search-icon" src="@/assets/editor/search.png" />
    <input
      class="search-box"
      v-model="searchInput"
      type="text"
      placeholder="请输入内容进行搜索"
      @keyup.enter="handleInput"
      @focus="showSearchResult = true"
    />
  </div>
</template>

<script>
import request from '@/http/request';
import { EventBus } from '@/main.js';
import { mapState } from 'vuex';
export default {
  data () {
    return {
      searchInput: '',
      // 搜索结果
      searchResult: [],
      showSearchResult: true,

      offset: 0,
      limit: 10,

      isLoadingSearchList: false
    }
  },
  computed: {
    ...mapState(['choosedResult'])
  },
  methods: {
    handleInput () {
      const input = this.searchInput.trim()
      this.searchResult = [] // 清空搜索结果
      // 设置搜索计时器，避免频繁搜索 防抖
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        if (!this.isLoadingSearchList) {
          this.apiGetSearchResult(input)
        } else {
          this.$message({
            message: '请稍后',
            type: 'error',
            duration: 1000
          })
        }
        this.searchTimer = null
      }, 500)
    },

    getTotal (keyword) {
      return new Promise((resolve, reject) => {
        request('/api/Paper/searchContentByTypeTotal', {
          query: keyword,
          type: 1
        }).then((result) => {
          resolve(result)
        })
      })
    },

    apiGetSearchResult (input) {
      request('api/Paper/searchContent', {
        query: input,
        page: 1,
        limit: 10,
        search_type: 'paper'
      }).then(async (result) => {
        const total = await this.getTotal(input)
        const list = result.paper_list.list
        this.$emit('openSearchBar', input, list, total)
      })
    },

    clickThesis (paperId, title, url) {
      // 后台上传的论文
      if (url.startsWith('https://oss.bookor.com.cn')) {
        this.apiAddPaper(paperId, title, url)
      } else {
        // 搜索网站获取的论文
        this.apiGetThesisMes(paperId, title, url)
      }
    },
    // 存本地的api
    apiGetThesisMes (id, title, url) {
      if (url) {
        this.apiAddPaper(id, title, url)
      } else {
        this.$message({
          message: '暂未搜索到pdf',
          type: 'error',
          duration: 1000
        })
      }
    },

    // 提交服务器的api
    apiAddPaper (paperId, title, url) {
      if (!url) {
        this.$message({
          message: '暂未搜索到对应文件',
          type: 'error',
          duration: 1000
        })
        return
      }
      const params = {
        paper_title: title,
        paper_url: url,
        paper_type: 'paper' // 区分上传的类型
      }
      request('/api/Paper/addPaper', params).then(() => {
        this.$message({
          message: '论文保存成功',
          type: 'success',
          duration: 1000
        })

        this.choosedResult.unshift(params)
        this.$store.commit('updateChoosedResult', this.choosedResult)
        // 新增完论文，直接展示
        const popData = {
          pdfUrl: url,
          title: title
        }
        this.$emit('getUrlData', popData)

        // 触发文档列表里论文数据的更新
        EventBus.$emit('refreshThesisList')
        this.showSearchResult = false
      }).catch(() => {
        this.$message({
          message: '此论文已存在',
          type: 'error',
          duration: 1000
        })
      })
    },

    handleScrollSearchResult (event) {
      const elem = event.target
      if (elem.scrollTop + elem.clientHeight >= elem.scrollHeight) {
        this.$message({
          message: '没有更多了',
          type: 'error',
          duration: 1000
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-con{
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
}
.search-icon {
  width: 18px;
  height: 18px;
  position: absolute;
  left: 10px;
}
.search-box {
  width: 40vw;
  height: 24px;
  border: 1px solid black;
  border-radius: 14px;
  margin: 8px 0;
  padding-left: 30px;
  font-size: 14px;
}

.search-box-result {
  position: absolute;
  top: 35px;
  z-index: 1;
  overflow-y: auto;
  height: 50vh;
  background-color: white;
  width: 42vw;
  left: 7px;
}
::-webkit-scrollbar {
  display: none;
}
.search-box-item {
  // margin-bottom: 5px;
  padding: 5px;
  margin: 5px;
  // border-bottom: 1px solid #a6a6a6;
  cursor: pointer;
}
.search-box-item:not(:last-child) {
  border-bottom: 1px solid #a6a6a6;
}
.search-box-item-top {
  font-size: 14px;
}

.search-box-item-bottom {
  display: flex;
  justify-content: space-between;
  color: #96989b;
  font-size: 12px;
  font-weight: 600;
}
</style>
