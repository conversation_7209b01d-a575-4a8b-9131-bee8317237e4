import axios from 'axios'
// import router from '../router'

// import { Message } from 'element-ui'

const service = axios.create({
  baseURL: '/',
  timeout: 30000 // 设置请求超时时间
})
// let loading = "";
// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求发送之前做一些处理
    if (!(config.headers['Content-Type'])) {
      if (config.method === 'post') {
        config.headers['Content-Type'] =
     'application/json;charset=UTF-8'
        for (var key in config.data) {
          if (config.data[key] === '') {
            delete config.data[key]
          }
        }
        config.data = JSON.stringify(config.data)
      } else {
        config.headers['Content-Type'] =
     'application/x-www-form-urlencoded;charset=UTF-8'
        config.data = JSON.stringify(config.data)
      }
    }
    const authorization = localStorage.getItem('authorization')
    // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    if (!config.headers.Authorization && authorization) {
      config.headers.Authorization = authorization
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const dataAxios = response.data
    return dataAxios
  },
  (error) => {
    return Promise.reject(error)
  }
)

export default service
