<template>
  <div style="width: 160px; cursor: pointer">
    <pdf
      v-for="i in 4"
      :key="i"
      :src="src"
      :page="i"
      style="display: inline-block; width: 80px; margin: 5px 0 0 0"
    ></pdf>
  </div>
</template>

<script>
import pdf from "vue-pdf";
export default {
  components: {
    pdf,
  },
  props: {
    src: {
      type: String,
    },
  },
  data() {
    return {
      numPages: undefined,
    };
  },
  async mounted() {
    const pdfFile = await pdf.createLoadingTask(this.src).promise;
    this.numPages = pdfFile.numPages;
  },
};
</script>
