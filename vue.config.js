var webpack = require('webpack')
var TerserPlugin = require('terser-webpack-plugin')

// 生成6位的时间戳hash
var timestamp = Date.now().toString().slice(-6);
var assetsVersion = 'v' + timestamp;

module.exports = {
  lintOnSave: false,
  transpileDependencies: ['yjs', 'lib0', 'quill', '@antv/g6', 'ml-matrix', /@antv\/*/],
  parallel: false,
  devServer: {
    hot: true,
    proxy: {
      '/api': {
        target: 'https://api.bookor.com.cn/index.php',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/api': '/api'
        }
      },
      '/zhipuAI': {
        target: 'https://zhipu.bookor.com.cn',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/zhipuAI': '/zhipuAI'
        }
      },
      '/moonshotAI': {
        target: 'https://zhipu.bookor.com.cn',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/zhipuAI': '/zhipuAI'
        }
      },
      '/ai': {
        target: 'https://node.bookor.com.cn/',
        // target: 'http://localhost:3000',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/ai': '/ai'
        }
      },
      '/mobile': {
        target: 'https://api.bookor.com.cn/index.php',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/mobile': '/mobile'
        }
      },

      '/aiapi': {
        target: 'https://pdfgpt.bookor.com.cn',
        changeOrigin: true,
        pathRewrite: {
          '^/aiapi': '/'
        }
      },
      // 小牛翻译
      '/niutrans': {
        target: 'https://api-doc.niutrans.com',
        changeOrigin: true,
        pathRewrite: {
          '^/niutrans': '/'
        }
      }
    }
  },
  chainWebpack: config => {
    config.devtool('source-map')

    config.module
      .rule('vue')
      .use('vue-loader')
      .tap(options => ({
        ...options,
        sourceMap: true
      }))

    // 禁用代码压缩
    config.optimization.minimize(false);

    // 保留 debugger
    config.optimization.minimizer('terser').tap((args) => {
      args[0].terserOptions = {
        ...args[0].terserOptions,
        compress: {
          drop_debugger: false
        },
        mangle: false
      };
      return args;
    });

    config.module
      .rule('worker')
      .test(/\.worker\.(js|ts)$/)
      .use('worker-loader')
      .loader('worker-loader')
      .options({
        inline: 'no-fallback',
        worker: {
          type: 'module'  // 支持 ES Module 类型的 worker
        }
      })
      .end();
    config.module
      .rule('js')
      .use('babel-loader')
      .loader('babel-loader')
      .tap(options => ({
        presets: [
          '@vue/cli-plugin-babel/preset',
          ['@babel/preset-env', { targets: { node: 'current' } }]
        ],
        plugins: [
          '@babel/plugin-proposal-class-properties',
          '@babel/plugin-proposal-private-methods',
          '@babel/plugin-transform-modules-commonjs'
        ]
      }));
  },
  configureWebpack: {
    plugins: [
      new webpack.ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        'windows.jQuery': 'jquery'
      })
    ],
    optimization: {
      minimize: false,  // 禁用代码压缩
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_debugger: false,  // 保留 debugger
              drop_console: false    // 保留 console
            },
            mangle: false,          // 不混淆变量名
            keep_classnames: true,  // 保持类名
            keep_fnames: true       // 保持函数名
          },
          extractComments: false    // 不提取注释
        })
      ]
    },
    module: {
      rules: [
        // 移除这部分配置
        // {
        //   test: /\.js$/,
        //   use: ['source-map-loader'],
        //   enforce: 'pre'
        // }
      ]
    },
    devtool: 'source-map'
  },
  // 根据环境设置不同的 publicPath
  publicPath: process.env.NODE_ENV === 'production'
    ? `https://bookor-fe-resource.oss-cn-beijing.aliyuncs.com`
    : '/',
  outputDir: 'dist',
  assetsDir: assetsVersion,
  css: {
    sourceMap: true
  },
  productionSourceMap: true
}
