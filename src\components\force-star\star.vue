<template>
  <div>
    <div id="3d-graph" @click="handleClick"></div>
    <div class="book-container" id="book-container">
      <h3 v-if="lineTitle" class="keyword-title">{{ lineTitle.title }}</h3>
      <book
        :isShowInfo="isShowInfo"
        :currentBookId="currentBookId"
        :currentKeyword="currentKeyword"
        :currentNodeId="currentNodeId"
        @showBookDetail="showBookDetail"
      />
      <book
        v-for="item in currentBondList"
        :key="item?.book_keywords_id || item.member_keywords_id"
        :isShowInfo="isShowInfo"
        :currentBookId="item.book_keywords_id"
        :currentKeyword="item.keywords"
        :currentNodeId="item.member_keywords_id"
        @showBookDetail="showBookDetail"
      />
    </div>
    <BookDetail :book_id="choosedBookId" :key="choosedBookId" />
    <BookDetailsPopup />
    <MindMap />
    <div class="node-active" v-show="activeLabel.text" :style="getStyle">
      <p>{{activeLabel.text}}</p>
    </div>
  </div>
</template>

<script lang="js">
import * as THREE from 'three'
import ForceGraph3D from '3d-force-graph'
import { getBondNode, getBondTitle } from './utils'
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass'
import book from './components/book/index.vue'
import MindMap from '@/components/mind-map/index.vue'
import BookDetailsPopup from '@/components/nebula-sub/book-details-popup.vue'
import BookDetail from '@/components/nebula-sub/book-details.vue'
import { EventBus } from '@/main.js'
import { mapState, mapMutations } from 'vuex'
import { CSS2DRenderer, CSS2DObject } from './CSS2DRenderer'

let Graph = null
const isRotationActive = true

let isDragging = false // 是否正在拖动
let startX = 0 // 鼠标按下时的X坐标
let scrollLeft = 0 // 当前滚动的距离
const bloomPass = null

const dashedMaterial = new THREE.LineDashedMaterial({
  color: '#fff',
  dashSize: 1,
  gapSize: 2,
  linewidth: 5,
  scale: 1
})
export default {
  data: function () {
    return {
      activeLabel: {
        text: '',
        x: 0,
        y: 0
      },
      lineTitle: null,
      isShowInfo: false,
      currentKeyword: '',
      currentBookId: '',
      currentNodeId: '',
      currentBondList: [] // 当前关联列表
    }
  },
  computed: {
    ...mapState('book', ['choosedBookId', 'isShowBookDetail']),
    ...mapState('indexsearch', {
      choosedSearchItem: (state) => state.chooseSearchItem
    }),
    getStyle () {
      return {
        left: `${this.activeLabel.x}px`,
        top: `${this.activeLabel.y}px`
      }
    }
  },
  props: ['forceData', 'bondList'],
  watch: {
    choosedBookId (newVal) {
      // 在这里处理 choosedBookId 的变化
      this.setShowDetails()
    },
    isShowBookDetail (newVal) {
      if (newVal === 'false') {
        // 解除锁定
        this.isLocked = false
      }
    },
    choosedSearchItem (value) {
      this.isLocked = false
      this.chooseSearchItem(value)
    }
  },
  components: {
    book,
    MindMap,
    BookDetailsPopup,
    BookDetail
  },
  methods: {
    ...mapMutations({
      setChosenBookId: 'book/SET_CHOSED_BOOK_ID',
      setShowDetails: 'book/showDetails'
    }),
    handleClick () {
      this.isShowInfo = false
    },
    nodeAnimation (node) {
      // this.activeLabel.text = ''
      // bloomPass.strength = 0
      const distance = 200
      const distRatio = 1 + distance / Math.hypot(node.x, node.y, node.z)
      const newPos = node.x || node.y || node.z
        ? { x: node.x * distRatio, y: node.y * distRatio, z: node.z * distRatio }
        : { x: 0, y: 0, z: distance } // special case if node is in (0,0,0)
      Graph.cameraPosition(
        newPos, // new position
        node, // lookAt ({ x, y, z })
        3000 // ms transition duration
      )
      // setTimeout(() => {
      // bloomPass.strength = 2
      // this.activeLabel = {
      //   text: node.user,
      //   x: window.innerWidth / 2 - (node.user.length * 8),
      //   y: (window.innerHeight / 2) - 50
      // }
      // }, 3100)
    },
    init () {
      Graph = ForceGraph3D({
        extraRenderers: [new CSS2DRenderer()]
      })
      (document.getElementById('3d-graph'))
        .graphData(this.forceData)
        .nodeAutoColorBy('group')
        .nodeThreeObject(node => {
          const nodeEl = document.createElement('div')
          nodeEl.textContent = node.user
          nodeEl.style.color = node.color
          nodeEl.className = 'node-label'
          return new CSS2DObject(nodeEl)
        })
        .onNodeClick(node => {
          if (node.titleId) {
            this.lineTitle = {
              title: node.title,
              id: node.titleId
            }
          } else {
            this.lineTitle = null
          }
          // bloomPass.strength = 0
          const currentBond = getBondNode(this.bondList, node)
          if (currentBond) {
            this.currentBondList = currentBond.point_list.filter(item => item.keywords !== node.user)
          } else {
            this.currentBondList = []
          }
          this.nodeAnimation(node)
          this.isShowInfo = true
          this.currentBookId = node.bookId
          this.currentNodeId = node.nodeId
          this.currentKeyword = node.user
        })
        .nodeThreeObjectExtend(true)
      // Graph = ForceGraph3D({
      //   extraRenderers: [new CSS2DRenderer()]
      // })
      // const elem = document.getElementById('3d-graph')
      // Graph = ForceGraph3D({
      //   antialias: true
      // })(elem)
      //   .graphData(this.forceData)
      //   .backgroundColor('#000')
      //   .nodeAutoColorBy('group')
      //   .nodeColor(node => node.color)
      //   .nodeLabel(node => {
      //     return `<div class="node-label">
      //         <p>${node.user}</p>
      //       <div>`
      //   })
      //   .linkMaterial(node => {
      //     if (node.lines_code === 3) {
      //       return dashedMaterial
      //     }
      //   })
      //   .linkPositionUpdate(lineObj => { lineObj.computeLineDistances() })
      //   .linkDirectionalArrowRelPos(1)
      //   .linkCurvature(0) // 线的弯曲程度
      //   .linkDirectionalArrowLength(link => {
      //     return link.size
      //   }) // 箭头大小
      //   .onNodeClick(node => {
      //     if (node.titleId) {
      //       this.lineTitle = {
      //         title: node.title,
      //         id: node.titleId
      //       }
      //     } else {
      //       this.lineTitle = null
      //     }
      //     bloomPass.strength = 0
      //     const currentBond = getBondNode(this.bondList, node)
      //     if (currentBond) {
      //       this.currentBondList = currentBond.point_list.filter(item => item.keywords !== node.user)
      //     } else {
      //       this.currentBondList = []
      //     }
      //     this.nodeAnimation(node)
      //     this.isShowInfo = true
      //     this.currentBookId = node.bookId
      //     this.currentNodeId = node.nodeId
      //     this.currentKeyword = node.user
      //   })

      // 动画
      // let angle = 0
      // const distance = 1400
      // setInterval(() => {
      //   if (isRotationActive) {
      //     Graph.cameraPosition({
      //       x: distance * Math.sin(angle),
      //       z: distance * Math.cos(angle)
      //     })
      //     angle += Math.PI / 300
      //   }
      // }, 40)
      // setTimeout(() => {
      //   isRotationActive = false
      // }, 5000)

      // bloomPass = new UnrealBloomPass()
      // bloomPass.strength = 2 // 发光强度
      // bloomPass.radius = 1 // 表示泛光散发的半径
      // bloomPass.threshold = 0 // 表示产生泛光的强度阀值，如果照在屋里上的光照强度大于该值就会产生泛光
      // Graph.postProcessingComposer().addPass(bloomPass)
    },
    // 清除页面上的截面标签
    clearObjectsLabels () {
      // 移除之前所有的标识符元素
      // isShowInfo = false;
      const visibleMarkerList =
        document.getElementsByClassName('visible-marker')
      while (visibleMarkerList.length > 0) {
        visibleMarkerList[0].parentNode.removeChild(visibleMarkerList[0])
      }
    },
    clearShow () {
      this.isShowInfo = false
      this.isShowInfoArrayLocked = false
      this.isShowInfoArray = false
      this.entryids = {
        book_keywords_id: '',
        member_keywords_id: ''
      }
      this.isShowEntryAddClassify = false
    },
    showBookDetail (id) {
      if (id) {
        this.setChosenBookId(id)
        const previousLabel = document.getElementById('object-label')
        if (previousLabel) {
          previousLabel.remove()
        }
        this.keyword = ''
        this.clearObjectsLabels()
        this.clearShow()
      } else {
        this.$message({
          message: '暂无对应图书',
          type: 'error',
          duration: 1000
        })
      }
    }
  },
  mounted () {
    this.init()
    const container = document.getElementById('book-container')
    document.querySelectorAll('.node-label').forEach(item => {
      item.addEventListener('click', () => {
        alert('111')
      })
    })
    container.addEventListener('mousedown', function (event) {
      isDragging = true
      startX = event.clientX
      scrollLeft = container.scrollLeft
    })
    container.addEventListener('mousemove', function (event) {
      if (!isDragging) return

      var distance = event.clientX - startX
      container.scrollLeft = scrollLeft - distance
    })

    container.addEventListener('mouseup', function (event) {
      isDragging = false
    })

    EventBus.$on('openDetail', (item) => {
      this.isShowInfo = true
      const { nodes } = Graph.graphData()
      let node = null
      if (item.type && item.type === 'KEYWORD_TITLE') {
        const currentBond = getBondTitle(this.bondList, item)
        const first = currentBond.point_list[0]
        item.user = first.keywords
        this.currentBondList = currentBond.point_list.filter(child => child.keywords !== item.user)
        node = nodes.find(i => {
          if (first.book_keywords_id) {
            return i.bookId === first.book_keywords_id
          } else {
            return i.nodeId === first.member_keywords_id
          }
        })
        this.lineTitle = {
          title: currentBond.title[0].lines_title,
          id: currentBond.title[0].mix_keywords_lines_id
        }
      } else {
        node = nodes.find(i => {
          if (item.bookId) {
            return i.bookId === (item.bookId)
          } else {
            return i.nodeId === item.nodeId
          }
        })
        const currentBond = getBondNode(this.bondList, node)
        if (currentBond) {
          this.currentBondList = currentBond.point_list.filter(child => child.keywords !== item.user)
        } else {
          this.currentBondList = []
        }
        if (node.titleId) {
          this.lineTitle = {
            title: node.title,
            id: node.titleId
          }
        } else {
          this.lineTitle = null
        }
      }
      this.currentKeyword = item.user
      this.currentBookId = node.bookId
      this.currentNodeId = node.nodeId
      this.currentKeyword = node.user
      this.nodeAnimation(node)
    })
  }
}

</script>

<style lang="scss">
.book-container{
   position: absolute;
   bottom: 15px;
    z-index: 9999;
    bottom: 0;
    display: flex;
    max-width: 100%;
    overflow-x: scroll;
    padding-top: 30px;
    .keyword-title{
      position: absolute;
      color: #fff;
      top: 0;
      left: 12px;
      z-index: 9;
    }
}
.scene-tooltip{
  pointer-events: all;
}
.node-label{
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.263);
  // user-select: none;
  cursor: pointer;
  opacity: .5;
  transform: scale(.5);
}
.node-active{
  position: absolute;
  display: inline-block;
  padding: 2px 4px;
  color: rgb(234, 178, 4);
  border: 1px solid rgb(154, 182, 200);
}
</style>
