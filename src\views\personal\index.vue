<template>
  <div style="padding: 20px 40px">
    <div style="display: flex; align-items: center; justify-content: space-between">
      <div class="top-left">
        <div class="goHome item" @click="$router.push('/index')"><img src="@/assets/editor/homepage.png" alt=""></div>
        <div class="top-title item" @click="$router.push('/editor')">Bookor Note</div>
        <div class="item">个人空间</div>
      </div>
      <el-button @click="removeUserInfo">退出登录</el-button>
    </div>

    <el-card class="box-card" shadow="never">
      <div slot="header">
        <div class="header_title">文档翻译</div>
        <div class="header_content">

          <!-- 英文 -->
          <div class="common left">
            <el-select filterable v-model="fromValue" placeholder="请选择" ref="left">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>

          <div class="common center_icon">
            <i class="el-icon-sort" @click="change" ></i>
          </div>
          <!-- 中文 -->
          <div class="common right">
            <el-select filterable v-model="toValue" placeholder="请选择" class="common" ref="right">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>

          <el-button @click="upload" style="margin-left: 45%;" type="primary" icon="el-icon-upload2" circle></el-button>

          <!-- 文件上传 -->
          <div style="display: none;">
            <!-- 多文件上传 -->
            <input ref="uploadMultipleFile" type="file" multiple accept=".pdf" @change="changeMultipleFile">
            <!-- 修改上传的文件 -->
            <input ref="uploadFile" type="file" accept=".pdf" @change="changeUploadFile">
          </div>
        </div>

      </div>
      <div>
        <div style="margin-bottom: 45px;">
          <el-table :data="fileTableData" stripe style="width: 100%">
            <el-table-column prop="name" label="文件名">
            </el-table-column>
            <el-table-column prop="page" label="页数">
            </el-table-column>
            <el-table-column prop="fromToLang" label="语种方向">
            </el-table-column>
            <el-table-column prop="name" label="操作">
              <template slot-scope="scope">
                <el-button size="mini" @click="modifyFile(scope.$index, scope.row)">重新上传</el-button>
                <el-button size="mini" @click="submitFile(scope.$index, scope.row)" type="primary">提交</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-divider>翻译结果</el-divider>
        <div>
          <el-table :data="tableData" stripe style="width: 100%">
            <!-- <el-table-column prop="id" label="序号" width="50">
            </el-table-column> -->
            <el-table-column prop="name" label="文件名" width="280">
            </el-table-column>
            <el-table-column prop="page" label="页数" width="50">
            </el-table-column>
            <el-table-column prop="fromToLang" label="语种方向" width="180">
            </el-table-column>
            <el-table-column prop="glossary" label="术语库" width="100">
            </el-table-column>
            <el-table-column prop="memorybank" label="记忆库" width="100">
            </el-table-column>
            <el-table-column prop="area" label="领域" width="180">
            </el-table-column>
            <el-table-column prop="transStatus" label="翻译状态">
              <template slot-scope="scope">
                <i v-if="scope.row.transStatus == 105" class="el-icon-circle-check"></i>
                <i v-else-if="scope.row.transStatus == 103" class="el-icon-loading"></i>
                <i v-else class="el-icon-circle-close"></i>
                {{ transStatusArr.get(scope.row.transStatus) }}
                {{ scope.row.transFailureCause }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="操作" width="280">
              <template slot-scope="scope">
                <el-button :disabled="scope.row.transStatus != '105'" size="mini" type="primary"
                  @click="download(scope.$index, scope.row)">下载</el-button>
                <el-button :disabled="scope.row.transStatus != '103'" size="mini" type="primary"
                  @click="stopTranslate(scope.$index, scope.row)">终止翻译</el-button>
                <el-button :disabled="scope.row.transStatus == '103'" size="mini"
                  @click="handleDelete(scope.$index, scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

    </el-card>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import Translate2xn from '../../utils/translate2xn'
import axios from 'axios'
export default {
  data () {
    return {
      // 下拉列表配置项
      options: Translate2xn.langCodeArr,
      // 文档语言
      fromValue: 'en',
      // 目标语言
      toValue: 'zh',
      // 上传文件表格 未翻译
      fileTableData: [],
      // 显示表格 翻译完成
      tableData: [],
      // 当前需要修改的文件item索引
      changeIndex: 0,
      // 翻译状态表
      transStatusArr: Translate2xn.transStatusArr
    }
  },
  components: {},
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore,
      userInfo: (state) => state.userInfo
    })
  },
  methods: {

    // 切换中英文
    change () {
      const left = document.querySelector('.left')
      const right = document.querySelector('.right')
      if (left.classList.contains('left')) {
        left.classList.replace('left', 'right')
      } else {
        left.classList.replace('right', 'left')
      }
      if (right.classList.contains('right')) {
        right.classList.replace('right', 'left')
      } else {
        right.classList.replace('left', 'right')
      }
    },
    // 查询接口
    getInfo (params) {
      axios({
        method: 'GET',
        url: '/niutrans/documentTransApi/getInfo',
        // data: formData,
        params
      }).then(r => {
        // 不知道为啥的系统错误
        const data = r.data
        if (data.code === 200) {
          // 接口请求完毕后的添加信息
          const index = this.tableData.findIndex(item => item.fileNo == data.data.fileNo)

          // 判断当前表格是否存在该数据, 如果存在则替换数据
          if (index === -1) {
            this.tableData.push({
              name: data.data.fileName,
              page: data.data.pageNum,
              fileNo: data.data.fileNo,
              fromToLang: Translate2xn.getLang2Label(data.data.from) + '-' + Translate2xn.getLang2Label(data.data.to),
              glossary: '默认',
              memorybank: '默认',
              area: '通用',
              state: '状态',
              from: data.data.from,
              to: data.data.to,
              progress: 1, // 翻译进度
              transStatus: data.data.transStatus, // 翻译状态
              transFailureCause: data.data.transFailureCause// 失败原因
            })
          } else {
            this.tableData.splice(index, 1, {
              name: data.data.fileName,
              page: data.data.pageNum,
              fileNo: data.data.fileNo,
              fromToLang: Translate2xn.getLang2Label(data.data.from) + '-' + Translate2xn.getLang2Label(data.data.to),
              glossary: '默认',
              memorybank: '默认',
              area: '通用',
              state: '状态',
              from: data.data.from,
              to: data.data.to,
              progress: 1, // 翻译进度
              transStatus: data.data.transStatus, // 翻译状态
              transFailureCause: data.data.transFailureCause// 失败原因
            })
          }

          if (data.data.transStatus == '103') {
            this.getInfo(params)
          }
        }
      })
    },
    // 终止翻译
    stopTranslate (index, item) {
      const timestamp = +new Date()
      const authStr = Translate2xn.formatParams({
        appId: Translate2xn.appId,
        apikey: Translate2xn.apikey,
        timestamp: timestamp,
        fileNo: item.fileNo
      })

      const formData = new FormData()
      formData.append('appId', Translate2xn.appId)
      formData.append('timestamp', timestamp)
      formData.append('fileNo', item.fileNo)
      formData.append('authStr', authStr)

      axios({
        method: 'POST',
        url: '/niutrans/documentTransApi/interrupt',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.data.code === 200) {
          this.$message({
            message: res.data.msg,
            type: 'success'
          })
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 修改已上传文件
    changeUploadFile () {
      const file = this.$refs.uploadFile.files[0]

      this.fileTableData.splice(this.changeIndex, 1, {
        name: file.name,
        page: '未知',
        fromToLang: Translate2xn.getLang2Label(this.fromValue) + '-' + Translate2xn.getLang2Label(this.toValue),
        file: file,
        from: this.fromValue,
        to: this.toValue
      })
    },
    // 多文件上传
    changeMultipleFile () {
      const fileList = this.$refs.uploadMultipleFile.files

      for (let index = 0; index < fileList.length; index++) {
        const thisFile = fileList[index]
        this.fileTableData.push({
          name: thisFile.name,
          page: '未知',
          fromToLang: Translate2xn.getLang2Label(this.fromValue) + '-' + Translate2xn.getLang2Label(this.toValue),
          file: thisFile,
          from: this.fromValue,
          to: this.toValue
        })
      }
    },
    // 文件上传修改
    modifyFile (index, item) {
      this.changeIndex = index
      this.$refs.uploadFile.value = ''
      this.$refs.uploadFile.click()
    },
    submitFile (index, item) {
      const timestamp = +new Date()
      const authStr = Translate2xn.formatParams({
        appId: Translate2xn.appId,
        apikey: Translate2xn.apikey,
        from: item.from,
        to: item.to,
        timestamp: timestamp

      })

      const formData = new FormData()
      formData.append('appId', Translate2xn.appId)
      formData.append('from', item.from)
      formData.append('to', item.to)
      formData.append('timestamp', timestamp)
      formData.append('file', item.file)
      formData.append('authStr', authStr)

      axios({
        method: 'POST',
        url: '/niutrans/documentTransApi/uploadAndTrans',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        // https://api-doc.niutrans.com/documentTransApi/getInfo
        const timestamp = +new Date()
        const authStr = Translate2xn.formatParams({
          appId: Translate2xn.appId,
          apikey: Translate2xn.apikey,
          timestamp: timestamp,
          fileNo: res.data?.data?.fileNo
        })
        // const formData = new FormData();
        // formData.append('appId', Translate2xn.appId);
        // formData.append('timestamp', timestamp);
        // formData.append('fileNo', res.data?.data?.fileNo);
        // formData.append('authStr', authStr);
        const params = {
          appId: Translate2xn.appId,
          timestamp: timestamp,
          fileNo: res.data?.data?.fileNo,
          authStr: authStr
        }
        this.getInfo(params)
      })
    },
    upload () {
      this.$refs.uploadMultipleFile.value = ''
      this.$refs.uploadMultipleFile.click()
    },
    download (index, item) {
      const timestamp = +new Date()
      const authStr = Translate2xn.formatParams({
        appId: Translate2xn.appId,
        apikey: Translate2xn.apikey,
        timestamp: timestamp,
        type: '1', // 0 原文 1 译文 2 双语对比 3 解析文件
        fileNo: item.fileNo

      })

      const formData = new FormData()
      formData.append('appId', Translate2xn.appId)
      formData.append('type', '1')
      formData.append('timestamp', timestamp)
      formData.append('fileNo', item.fileNo)
      formData.append('authStr', authStr)

      axios({
        method: 'POST',
        url: '/niutrans/documentTransApi/download',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        // 限定返回值的类型
        responseType: 'blob'
      }).then(res => {
        if (res.data.code == '20015') {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
          return
        }

        // 下载到我本地浏览器
        const blob = new Blob([res.data], { type: 'application/word' })
        const alink = document.createElement('a')
        alink.download = item.name + '.docx' // 设置文件名
        alink.style.display = 'none'
        alink.href = URL.createObjectURL(blob) // 这里是将文件流转化为一个文件地址
        document.body.appendChild(alink)
        alink.click()
        URL.revokeObjectURL(alink.href) // 释放URL 对象
        document.body.removeChild(alink)
        // 若想要弹出文件另存为保存框, 需要用户开启 浏览器 设置中, 下载内容, 是否询问保存位置 选项
      })
    },
    handleDelete (index, item) {
      this.tableData.splice(index, 1)
    },
    // 退出登录
    removeUserInfo () {
      localStorage.removeItem('userinfo')
      localStorage.removeItem('authorization')
      this.$message({
        message: '已退出登录',
        type: 'success',
        duration: 1000
      })
      // 跳转到首页
      this.$router.push('/')
    }
  },
  mounted () {
    if (this.userInfoMore.is_enterprise == 1) {
      this.$router.replace('/corporateAccount')
    }
  }
}
</script>
<style scoped lang="scss">
.top-left {
  display: flex;
  align-items: center;

  .item {
    margin-right: 10px;
  }

  .goHome {
    cursor: pointer;

    img {
      width: 25px;
      height: 25px;
    }
  }

  .top-title {
    font-family: fantasy;
    word-spacing: 6px;
    letter-spacing: 1px;
    text-align: center;
    align-items: center;
    line-height: 40px;
    font-size: 19px;
    cursor: pointer;
  }

}

.box-card {
  margin-top: 3vh;

  .header_title {
    font-size: 23px;
    font-weight: 600;
    color: #251618;
    text-align: center;
  }

  .header_content {
    margin-top: 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .center_icon {
      transform: rotate(90deg);
      position: absolute;
      left: 256px;

      i {
        margin: 10px 30px;
      }
    }

    // .common {}

    .left {
      position: absolute;
      left: 43px;
      transition: all .5s linear;
    }
    .right {
      position: absolute;
      left: 25%;
      transition: all .5s linear;

    }
  }
}
</style>
