<template>
  <div class="info-box" v-if="isShowInfo" @click="itemClick">
    <div class="info-title-box">
      <div class="info-title">
        {{ currentKeyword }}
      </div>
      <div
        class="info-addLink"
        @click.stop="handleEntryAddClassify(clickedObjectData)"
      >
        +
      </div>
    </div>
    <div class="info-content-box" v-if="bookList.length">
      <div v-if="bookList[0].book_keywords_id">
        <div
          v-for="item in bookList"
          class="item-info"
          :key="item.id"
        >
          <img
            :src="`${item.book_image}`"
            alt=""
            style=""
            class="info-box-img"
            @click="showBookDetail(item.book_id)"
          />
          <div>
            <div style="font-size: 14px">{{ item.description }}</div>
          </div>
        </div>
      </div>
      <div v-else>
        <div
          style="
            overflow-y: scroll;
            word-wrap: break-word;
            font-size: 14px;
            padding: 5px 0;
          "
          class="no-scroll-bar"
          v-html="bookList[0].description"
        ></div>
      </div>
    </div>
    <div
      v-if="isShowEntryAddClassify"
      style="position: absolute; top: 39px; right: 5px"
    >
      <EntryAddClassify :entryids="entryids" />
    </div>
</div>

</template>
<script>

// import axios from '@/http'
import request from '@/http/request'
import EntryAddClassify from '@/components/nebula-sub/entry-add-classify.vue'
export default {
  data () {
    return {
      isShowInfo: false,
      isShowEntryAddClassify: false,
      bookList: [],
      entryids: {
        book_keywords_id: '',
        member_keywords_id: ''
      }
    }
  },
  props: ['currentBookId', 'currentNodeId', 'currentKeyword'],
  components: {
    EntryAddClassify
  },
  watch: {
    currentBookId (val) {
      if (!val) {
        this.isShowInfo = false
        return
      }
      this.isShowEntryAddClassify = false
      this.getBookDetail()
    },
    currentNodeId (val) {
      if (!val) {
        this.isShowInfo = false
        return
      }
      this.getBookDetail()
    }
  },
  methods: {
    showBookDetail (id) {
      this.$emit('showBookDetail', id)
    },
    itemClick () {
      if (this.isShowEntryAddClassify) {
        this.isShowEntryAddClassify = false
      }
    },
    handleEntryAddClassify () {
      if (this.currentBookId) {
        this.entryids.book_keywords_id = this.currentBookId
      } else if (this.currentNodeId) {
        this.entryids.member_keywords_id = this.currentBookId
      }
      this.isShowEntryAddClassify = !this.isShowEntryAddClassify
    },
    getBookDetail () {
      request('/api/keywords/getDescriptionByKeyWordsId', {
        book_keywords_id: this.currentBookId,
        member_keywords_id: this.currentNodeId
      }).then((result) => {
        if (result.book_keywords_description.length === 0 && !result.member_keywords_description) {
          this.isShowInfo = false
          return
        }
        this.isShowInfo = true
        if (result.book_keywords_description.length) {
          this.bookList = result.book_keywords_description
        } else {
          this.bookList = [result.member_keywords_description]
        }
      })
    }
  },
  mounted () {
    this.getBookDetail()
  }
}
</script>

<style scoped lang="scss">

.info-box-img {
  width: 75px;
  height: 100px;
  float: left;
  margin-right: 8px;
  cursor: pointer;
}
.info-box {
  // position: absolute;
  position: relative;
  margin-left: 10px;
  background-color: #222f36;
  color: white;
  border-radius: 10px;
  min-width: 240px;
  height: 320px;
  max-width: 300px;
  z-index: 99;
  border: 2px solid #404040;
  overflow: hidden;
}
.info-box:first-child{
  border: 2px solid #8faadc
}
.item-info{
  margin-bottom: 15px;
  display: flex;
  gap: 4px;
}
.info-content-box {
  border-top: 1px dotted #ffffff;
  overflow-y: scroll;
  height: 240px;
  padding: 12px;
  padding-bottom: 30px;
}

.info-box::-webkit-scrollbar {
  display: none;
}

.info-box-array-outter {
  &::-webkit-scrollbar {
    display: none;
  }
}

.info-box-array {
  background-color: #222f36;
  color: white;
  border: 2px solid #404040;
  border-radius: 10px;
  min-width: 20vw;
  position: relative;
  /* min-height: 60px; */
  /* max-height: 30vh; */
  max-width: 300px;
  /* padding: 0 5px; */
  overflow-y: scroll;
  /* padding: 3px 10px; */
  display: inline-block;
  margin-left: 5px;
  z-index: 1;
}

.info-box-array::-webkit-scrollbar {
  display: none;
}

.info-title-box {
  display: flex;
  align-items: center;
  background-color: #1a2329;
  width: 100%;
  border-radius: 12px;
}

.info-title {
  background-color: #1a2329;
  color: white;
  /* border-radius: 6px; */
  font-weight: 600;
  padding: 8px 8px;
  /* display: inline-block; */
  /* margin: 5px 0; */
  font-size: 15px;
  // width: 100%;
  box-sizing: border-box;
}

.info-box-array-html {
  overflow-y: scroll;
  word-wrap: break-word;
  font-size: 15px;
}

.info-box-array-html::-webkit-scrollbar {
  display: none;
}

.info-content-box::-webkit-scrollbar {
  display: none;
}

.info-addLink {
  color: white;
  border: 2px solid #2a75b5;
  margin: 5px;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
  height: 18px;
  line-height: 16px;
  cursor: pointer;
}

/* 鼠标放大镜 */
.cursor-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: none;
  position: absolute;
  z-index: 9999;
}

.cursor-circle:active {
  display: none;
}

.no-scroll-bar::-webkit-scrollbar {
  display: none;
}

.visible-marker {
  position: absolute;
  z-index: 9999;
  margin: 0;
  pointer-events: none;
  color: red;
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.6),
    0 0 3px rgba(0, 0, 0, 0.4);
}

@media (max-width: 700px) {
  .info-box-array {
    min-width: 30vw;
  }

  .info-box-img {
    width: 15vw !important;
    height: 23vw !important;
  }
}
</style>
