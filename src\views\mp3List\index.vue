<template>
  <div class="mp3-list">
    <div class="mp3-category">
      <div class="content">
        <el-tag type="info" :class="{ active: tag === '' }" @click="handleTagClick('')">全部</el-tag>
        <el-tag v-for="item in tagList" :key="item.id" type="info" :class="{ active: tag === item.id }"
          @click="handleTagClick(item.id)">{{ item.name }}</el-tag>
      </div>
      <div class="search">
        <el-input
          prefix-icon="el-icon-search"
          v-model:value="keyword"
          @keyup.enter.native="handleSearch"
          placeholder="输入关键词进行搜素" />
      </div>
    </div>
    <div
      class="mp3-list-content infinite-list"
      v-infinite-scroll="getMp3List"
      :infinite-scroll-disabled="disabled"
      style="overflow:auto">
      <div class="mp3-list-item" v-for="(item, index) in mp3List" :key="item.id"
        :class="{ active: currentIdx === index }" @click="handleItemClick(index)">
        <div class="left-con">
          <el-tooltip class="item" :content="item.title" placement="top">
            <h3 class="title" @click.stop="handleTitleClick(item)">{{ item.title }}</h3>
          </el-tooltip>
          <p class="time">{{ item.ai_mp3_duration }}</p>
          <img class="like" v-if="item.is_like" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/like.png"
            @click="toLike(item, index)">
          <img class="like" v-else src="https://chrome1.oss-cn-shanghai.aliyuncs.com/like.png"
            @click.stop="toLike(item, index)" />
        </div>
        <div class="right-con">
          <div class="cover">
            <img :src="item.ai_image" alt="mp3">
          </div>
        </div>
      </div>
      <div v-if="!mp3List.length" class="empty-con">
        <el-empty :image-size="200"></el-empty>
      </div>
    </div>

    <div class="mp3-player">
      <i class="el-icon-video-play" @click="handlePlay" v-if="!playing" />
      <i class="el-icon-video-pause" @click="handlePause" v-else />
      <div class="player-con">
        <span>{{ formatTime(currentTime) }}</span>
        <el-slider v-model="currentTime" :max="duration" @change="updateTime"></el-slider>
        <span>{{ formatTime(duration) }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/http/request';

let timer = null
export default {
  data () {
    return {
      keyword: '',
      audioCon: null,
      time: 0,
      activeItem: null,
      mp3List: [],
      tagList: [],
      oldIdx: null,
      currentIdx: 0,
      currentTime: 0,
      duration: 0,
      playing: false,
      tag: '',
      page: 1,
      disabled: false,
    }
  },
  props: ['rightExpanded'],
  watch: {
    rightExpanded (val) {
      if (val) {
        this.handlePause()
      }
    }
  },
  methods: {
    toLike (item, idx) {
      request('/api/Wechatarticle/updateArticleLike', {
        article_id: item.id,
        like_status: item.is_like ? 0 : 1
      }).then((res) => {
        this.$message.success(item.is_like ? '取消点赞' : '点赞成功')
        this.mp3List[idx].is_like = item.is_like ? 0 : 1
      })
    },
    handleTitleClick(item) {
      const el = document.createElement('a');
      el.href = item.url;
      el.target = '_blank';
      el.click();
    },
    updateTime (value) {
      this.currentTime = value
      this.audioCon.currentTime = this.currentTime
    },
    handleItemClick (index) {
      if (index === this.currentIdx) return
      this.currentIdx = index
      this.oldIdx = index
      this.audioCon.src = this.mp3List[this.currentIdx].ai_mp3
      // this.$emit('setMp3Url', this.mp3List[index])
      this.handlePlay()
    },
    formatTime (time) {
      const minutes = Math.floor(time / 60)
      const seconds = Math.floor(time % 60)
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    },
    handlePlay () {
      this.playing = true
      if (!this.audioCon.src) {
        this.audioCon.src = this.mp3List[this.currentIdx].ai_mp3
      }
      this.audioCon.play()
    },
    handlePause () {
      this.playing = false
      this.audioCon.pause()
      clearTimeout(timer)
    },
    clearTag () {
      this.tag = ''
      this.getMp3List()
    },
    handleTagClick(tag) {
      this.page = 1
      this.disabled = false
      this.tag = tag
      this.getMp3List()
    },
    getMp3List () {
      request('/api/Wechatarticle/getGoodArticleList', {
         page: this.page,
         limit: 20,
         search: this.keyword,
         tag: this.tag
        }, 'get').then(res => {
        this.tagList = res.tag_list
        if (res.article_list < 20) {
          this.disabled = true
        }
        if (this.page === 1) {
          this.mp3List = res.article_list
          this.audioCon.src = this.mp3List[0].ai_mp3
          this.$emit('setMp3Url', this.mp3List[0])
        } else {
          this.mp3List = this.mp3List.concat(res.article_list)
        }
        this.page += 1
      })
    },
    handleSearch() {
      this.page = 1
      this.getMp3List()
    }
  },
  mounted () {
    this.audioCon = document.createElement('audio')
    // this.getMp3List()

    this.audioCon.addEventListener('ended', () => {
      this.oldIdx = null
      this.handleItemClick(this.currentIdx + 1)
    })

    this.audioCon.addEventListener('loadedmetadata', () => {
      this.duration = this.audioCon.duration
    })
    this.audioCon.addEventListener('timeupdate', () => {
      this.currentTime = this.audioCon.currentTime
    })
  }
}
</script>

<style lang="scss" scoped>
.mp3-list {
  width: 80%;
  min-width: 1200px;
  margin: 40px auto 0;
  position: relative;
  overflow: auto;

  .mp3-category {
    display: flex;
    gap: 60px;
    .content{
      display: flex;
      gap: 12px;
    }
    .search{
      :deep(.el-input__inner) {
        height: 32px;
        line-height: 32px;
      }
      :deep(.el-input__icon) {
        line-height: 32px;
      }
    }


    .el-tag {
      cursor: pointer;
    }

    .active {
      background-color: rgba(255, 0, 0, 0.654);
      color: #fff;
    }

    .el-icon-circle-close {
      cursor: pointer;
      color: #999;
      position: relative;
      top: 8px;
    }
  }
}

.mp3-list-content {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: left;
  gap: 12px;
  overflow: auto;
  padding-bottom: 60px;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    width: 0;
  }

  .mp3-list-item {
    background-color: #1d6ac85e;
    border-color: rgba(6, 7, 9, 0.1);
    border-radius: 8px;
    padding: 12px;
    width: 21%;
    display: flex;
    justify-content: space-between;
    position: relative;
    height: 140px;
    border: 2px solid transparent;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .04), 0 4px 12px 0 rgba(0, 0, 0, .02);

    &.active {
      border: 2px solid rgb(217, 112, 112);
    }

    &:hover {
      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, .16), 0 16px 48px 0 rgba(0, 0, 0, .08);
    }
  }

  .left-con {
    width: calc(100% - 160px);

    .title {
      cursor: pointer;
      -webkit-line-clamp: 2;
      /* 限制在一个块元素显示的文本的行数 */
      display: -webkit-box;
      -webkit-box-orient: vertical;
      font-size: 14px;
      overflow: hidden;
      /* 超出部分隐藏 */
      text-overflow: ellipsis;
      /* 超出部分显示省略号 */
    }

    .time {
      margin-top: 10px;
      color: #999;
    }

    .like {
      width: 20px;
      height: 20px;
      position: absolute;
      left: 10px;
      bottom: 10px;
      cursor: pointer;
    }
  }

  .right-con {
    background-color: #fff;
    border-radius: 8px;
    width: 50%;
    position: absolute;
    right: 10px;
    top: 10px;

    img {
      width: 100%;
      object-fit: cover;
      height: 140px;
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
    }
  }

  .empty-con {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.mp3-player {
  position: fixed;
  bottom: 30px;
  width: 520px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgb(217, 112, 112);
  display: flex;
  align-items: center;
  padding: 10px;
  gap: 12px;
  border-radius: 12px;

  i {
    color: #fff;
    font-size: 24px;
    cursor: pointer;
  }

  .player-con {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #fff;
  }

  .el-slider {
    width: 320px;

    :deep(.el-slider__runway) {
      background-color: #fff;
    }

    :deep(.el-slider__bar) {
      background: #FFF;
    }

    :deep(.el-slider__button) {
      border-color: #fff;
    }
  }
}
</style>
