<template>
    <div style="overflow-y: scroll; height: 100vh" class="introduce-box">

        <div>

            <!-- 第一页 “人机一体”的新阅读 wheel滚轮事件-->
            <div class="section" v-if="curSubPage == 1">
                <!-- @wheel="oneSectionScrollWheel" -->
                <div class="page-box">
                    <div class="title-part1" style="color: #a78bfb">
                        “人机一体”的新阅读
                    </div>
                    <div class="content-box-part1">
                        <div class="content-title-part1" style="text-align: center">
                            借助“外脑”，实现三个层次的思维跃升
                        </div>

                        <div class="transition_box" style="display: flex; justify-content: center;">
                            <div class="transition_item" v-if="onePage.currentNum == 1" style="
                  margin-top: 5%;
                  color: #747576;
                  font-size: 20px;
                  font-weight: 600;
                  min-width: 200px;
                  max-width: 400px;
                ">
                                <span style="color: #40dbff; font-size: 25px">“外脑”</span>

                                <span>肩负储存元知识信息的电脑系统，让人脑只专注于以元知识之间的连接的创新思维活动(联想、洞见和创造)。人机一体化目的让人高效建立起系统化知识体系。</span>
                            </div>
                            <Inside v-if="onePage.currentNum == 2" :onePage="onePage"></Inside>
                        </div>

                    </div>
                </div>
            </div>

            <!-- 第二页 阅读的误区-->
            <SubPageTwo v-if="curSubPage == 2"></SubPageTwo>

            <!-- 第三页 传统阅读方式的弊端s-->
            <SubPageThr v-if="curSubPage == 3" :threePage="threePage"></SubPageThr>

            <!-- 第四页 拥有系统知识的4个标准-->
            <SubPageFour v-if="curSubPage == 4"></SubPageFour>

            <!-- 第五页 跨文本阅读 -->
            <SubPageFive v-if="curSubPage == 5"></SubPageFive>

            <!-- 第六页 什么是元知识 -->
            <SubPageSix v-if="curSubPage == 6"></SubPageSix>

            <!-- 第七页 Bookor Brain系统的理论依据 -->
            <SubPageSeven v-if="curSubPage == 7"></SubPageSeven>

            <!-- 第八页 实现学习的良性循环 -->
            <SubPageEight v-if="curSubPage == 8"></SubPageEight>
            <!-- 第九页 “新阅读”用户享有的优质服务 -->
            <SubPageNice  v-if="curSubPage >= 9 && !isShowEmail" @handleEnter="handleEnter"></SubPageNice>

            <email v-if="isShowEmail" :back="setCurPage" :email="email"></email>

        </div>
        <div class="down-arrow" @click="clickNext" v-if="isShowArrow">
            <svg id="more-arrows">
                <polygon class="arrow-top" points="37.6,27.9 1.8,1.3 3.3,0 37.6,25.3 71.9,0 73.7,1.3 " />
                <polygon class="arrow-middle" points="37.6,45.8 0.8,18.7 4.4,16.4 37.6,41.2 71.2,16.4 74.5,18.7 " />
                <polygon class="arrow-bottom" points="37.6,64 0,36.1 5.1,32.8 37.6,56.8 70.4,32.8 75.5,36.1 " />
            </svg>
        </div>
    </div>
</template>
<script>
import Video from '@/components/public/Video.vue'
import CommitInfo from '@/components/homepage/commit-info.vue'
import email from '@/components/splash-screen/email.vue'
import Inside from './introduces/inside.vue'
import SubPageTwo from './introduces/subPageTwo.vue'
import SubPageThr from './introduces/subPageThr.vue'
import SubPageFour from './introduces/subPageFour.vue'
import SubPageFive from './introduces/subPageFive.vue'
import SubPageSix from './introduces/subPageSix.vue'
import SubPageSeven from './introduces/subPageSeven.vue'
import SubPageEight from './introduces/subPageEight.vue'
import SubPageNice from './introduces/subPageNice.vue'

export default {
  props: {
    setCurPage: {
      type: Function
    },
    setcurrentPage: {
      type: Function
    },
    curSubPage: {
      type: Number
    },
    onePage: {
      type: Object
    },
    threePage: {
      type: Object
    },
    clickNext: {
      type: Function
    }

  },

  data () {
    return {
      isShowArrow: true,
      isShowEmail: false,
      src: 'http://oss.bookor.com.cn/uploads/20221013/69c1ae538aba5daf90c9a494c1b98deb.mp4',
      fadeInElements: [],
      email: '', // 邮箱
      name: '', // 名字
      // 专业领域
      profession_area: '',
      profession_area_options: [
        { value: 1, text: '学生' },
        { value: 2, text: '研究人员' },
        { value: 3, text: '职场从业者' },
        { value: 4, text: '自雇及知识博主' },
        { value: 5, text: '企业主' },
        { value: 6, text: '其他' }
      ],
      // 阅读习惯
      read_habit: '',
      read_habit_options: [
        { value: 1, text: '按顺序逐页阅读' },
        { value: 2, text: '随机选读有兴趣的章节' },
        { value: 3, text: '快读先了解文章结构' },
        { value: 4, text: '只关注有用的知识点' }
      ],
      // 是否做读书笔记
      is_note: '',
      is_note_options: [
        { value: 1, text: '每次阅读都做笔记' },
        { value: 2, text: '重要的会做笔记' },
        { value: 3, text: '偶尔随手记录' },
        { value: 4, text: '从不做笔记' }
      ],
      // 笔记工具
      note_tools: '',
      note_tools_options: [
        { value: 1, text: '书页空白处手写记录' },
        { value: 2, text: '纸质笔记本' },
        { value: 3, text: '电脑笔记本工具' },
        { value: 4, text: '手机记录工具' }
      ],
      noteToolsShow: false
    }
  },
  components: {
    Inside,
    SubPageTwo,
    SubPageThr,
    SubPageFour,
    SubPageFive,
    SubPageSix,
    SubPageSeven,
    SubPageEight,
    SubPageNice,
    Video,
    CommitInfo,
    email
  },
  mounted () {
  },

  methods: {
    oneSectionScrollWheel (event) {
      if (this.onePage.currentNum <= 1) {
        event.preventDefault()

        if (this.onePage.throttleBoo) {
          this.onePage.throttleBoo = false

          if (this.onePage.isShow) {
            this.onePage.isShow = false
          }
          this.onePage.currentNum++
          this.onePage.throttleBoo = true
          // setTimeout(() => {
          //     this.onePage.throttleBoo = true;
          // }, 300)
        }
      }
    },

    beforeChange (prev, next) {
      // 默认锁定
      if (next === 2 && this.isLock) return false
    },
    afterChange (prev, next) {
    },
    noteToolsChange () {
      if (this.is_note == 4) {
        this.noteToolsShow = false
      } else {
        this.noteToolsShow = true
      }
    },
    // 滚动的逻辑改为了点击
    handleMouseWheel (event) {
      // if (event.deltaY > 0 && this.onePage.currentNum > 3) {
      if (this.curSubPage == 3) {
        if (this.threePage.currentNum < 4) {
          this.threePage.currentNum++
          return
        }
      }
      if (this.onePage.currentNum >= 1) {
        this.setcurrentPage(true) // 鼠标向下滚动时，页数增加
        // } else if (event.deltaY < 0 && this.curSubPage > 1) {
      } else if (event.deltaY < 0 && this.curSubPage > 1) {
        this.setcurrentPage(false) // 鼠标向上滚动时，页数减少，但不会小于1
        if (this.curSubPage == 1) {
          this.$emit('toPrevPage')
        }
      }

      if (this.curSubPage == 1) {
        this.oneSectionScrollWheel(event)
        return
      }
      // // 箭头判断显示与隐藏
      if (this.curSubPage >= 9) {
        this.isShowArrow = false
      }
    },

    handleEnter (email) {
      const regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
      if (regex.test(email)) {
        this.email = email
        this.curSubPage++
        this.isShowEmail = true
      } else {
        this.$message.error({
          message: '请输入正确的邮箱地址',
          duration: 1500
        })
        this.email = ''
      }
    },

    handleRefresh () {
      // location.reload();
      // this.changePage();
      this.curSubPage = 10
      setTimeout(() => {
        this.$router.push('/index')
      }, 2000)
    }

  }
}
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 2s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.content_box {
    .content-inside-item {
        cursor: pointer;
        position: relative;
    }

    .content-inside-item::before {
        content: '';
        position: absolute;
        left: -10px;
        width: 5px;
        height: 100%;
        background-color: transparent;
    }

    .item_box::before {
        background-color: #a583f1 !important;
    }
}

.item_box {
    .item_title {
        color: #a583f1 !important;
    }

    .item {
        color: #fff !important;
    }
}

div.content_box.content-inside {
    color: #ccc;
}

.transition_item {
    opacity: 1;
    max-height: 200px;
    overflow: hidden;
    transition: all 2s ease;
}

.transition_item.opa0 {
    overflow: hidden;
    transition: all 2s ease;
    opacity: 0;
    max-height: 0;
}

.sub_title_item {
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.9s ease;
    color: rgb(104, 105, 106);
    font-weight: 600;
    font-size: 17px;
     margin-top: 10px;
}

.sub_title_item.opa1 {
    opacity: 1;
    max-height: 30px;
}

.out-box {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100vw;
    height: 100vh;
}

.title-part1 {
    color: white;
    font-size: 30px;
    font-weight: 600;
    text-align: center;
    margin-top: 5%;
}

.content-box-part1 {
    margin-top: 10px;

    .content-title-part1 {
        color: #68696a;
        font-weight: 600;
        font-size: 17px;
    }

    .content-inside {
        font-size: 18px;
        margin-top: 16px;
        color: white;

        .content-inside-item {
            margin-top: 10px;
            text-align: center;
            opacity: 1;
            // transition: all 0.5s ease;
        }

        .opa0 {
            transition: all 0.5s ease;
            opacity: 0;
        }

        .blue-dot {
            color: #40dbff;
            margin-right: 17px;
        }

        .content-sub-title-part1 {
            color: #37dbff;
        }
    }
}

.content-add-top {
    position: absolute;
    left: 41%;
    top: 6%;
    width: 500px;
    color: white;
    font-size: 18px;
}

// part2样式
.title-part2 {
    color: white;
    font-size: 21px;
    // position: absolute;
    top: 21%;
    left: 5%;
}

.content-box-part2 {
    color: #ffc001;
    font-size: 18px;
    margin-left: 15px;

    // position: absolute;
    .content-item {
        .arc-arrow {
            font-size: 20px;
            margin-right: 20px;
        }

        margin: 13px 0;
    }
}

.success-tips {
    text-align: center;
    //position: absolute;
    // left: 40%;
    color: #ffc000;
    margin-top: 20%;
    font-size: 18px;
}

.fade-enter-active,
.fade-leave-active {
    transition: all 3s ease-in-out;
    // transition: opacity 5s;
}

.fade-enter,
.fade-leave-to {
    transition: all 0.5s ease-out;
    // opacity: 0;
}

.introduce-box {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        // display: none;
    }

    // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
    &::-webkit-scrollbar-button {
        display: none;
    }

    // 滚动条的轨道（里面装有Thumb）
    &::-webkit-scrollbar-track {
        background: transparent;
    }

    // 滚动条的轨道（里面装有Thumb）
    &::-webkit-scrollbar-track-piece {
        background-color: transparent;
    }

    // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
    &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        cursor: pointer;
        border-radius: 4px;
    }

    // 边角，即两个滚动条的交汇处
    &::-webkit-scrollbar-corner {
        display: none;
    }

    // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
    &::-webkit-resizer {
        display: none;
    }
}

.page-box {
    width: 99vw;
    height: 100vh;
    // display: inline-block;
    padding: 20px;
    box-sizing: border-box;
    // transition: 0.3s all ease-out;
    // opacity: 0;
    // transform: scale(0.8);
    // transform: translate(-50%, -50%);
    // left: 50%;
    // top: 50%;
}

.info-box {

    // background-color: red;
    ::placeholder {
        color: #0f0f0f;
        // color: brown;

    }
}

.down-arrow {
    // color: white;
    position: absolute;
    top: 90%;
    left: 50%;
    transform: translate(-50%, -50%);
    // // width: 200px;
    // // height: 40px;
    // text-align: center;
    // line-height: 40px;

    /* Arrow & Hover Animation */
    #more-arrows {
        width: 75px;
        height: 65px;
        transform: scale(0.4);
    }

    @keyframes arrow-animation {
        0% {
            fill: #d9dadb;
            opacity: 0.5;
        }

        33.33% {
            fill: #d9dadb;
            opacity: 0.75;
        }

        66.66% {
            fill: #d9dadb;
            opacity: 1;
        }

        100% {
            fill: #d9dadb;
            opacity: 0.75;
        }
    }

    polygon.arrow-top {
        animation: arrow-animation 1s linear infinite;
    }

    polygon.arrow-middle {
        animation: arrow-animation 1s linear infinite 1.3s;
    }

    polygon.arrow-bottom {
        animation: arrow-animation 1s linear infinite 2.5s;
    }
}

::v-deep .bottomInfo .el-input__inner {
    // background-color: #d9d9d9 !important;
    // background-color: #ccc !important;
    height: 35px;
    border-radius: 10px;
    // text-align: center;
    width: 250px;
    // margin-left: 40%;
}
</style>
