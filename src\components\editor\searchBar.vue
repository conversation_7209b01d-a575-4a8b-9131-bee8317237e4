<template>
  <div class="search-bar-container">
        <el-drawer class="editor-search-bar" :visible.sync="drawer" :direction="direction" :before-close="handleClose">
          <div class="search-con">
            <el-select v-model="searchType" @change="typeChange">
              <el-option
                label="关键词"
                :value="0"/>
                <el-option
                label="词条"
                :value="1"/>
            </el-select>
            <el-input v-model="keyword" placeholder="请输入搜索内容" @keyup.enter.native="handleTabClick"></el-input>
          </div>
          <div class="tags-list" v-if="searchType">
            <el-tag effect="dark" v-for="item in tagList" :class="{active: activeTag === item.classify_id}" :key="item.classify_id" @click="handleTagClick(item)">{{ item.classify_name }}</el-tag>
          </div>
          <el-tabs class="search-bar-main" v-model="activeName" @tab-click="handleTabClick">
            <el-tab-pane v-for="(item, idx) in searchConfig" :key="idx" :label="`${item.label}(${totalList[idx]})`" :name="item.name"></el-tab-pane>
          </el-tabs>
          <List v-if="showType === 0" :activeName="activeName" @openUrl="openUrl"></List>
          <div v-else class="search-bar-con" v-loading="loading">
            <p v-if="!list.length" class="empty">暂无内容</p>
            <el-collapse v-model="activeNames" @change="change" v-if="activeName !== 'draft'">
              <el-collapse-item v-for="(item, idx) in list" :key="idx" :name="item.id" >
                <template slot="title">
                  <div class="item-title">
                    <img v-if="activeName === 'paper'" class="icon-pdf" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/Word-Local-hover.png"/>
                    <img v-else-if="activeName === 'article'" class="icon-pdf" src="../../assets/img/article.png"/>
                    <img v-else-if="activeName === 'note'" class="icon-pdf" src="../../assets/img/note.png"/>
                    <img v-else-if="activeName === 'comment'" class="icon-pdf" src="../../assets/img/article_comment.png"/>
                    <img v-else class="icon-pdf" src="../../assets/img/pdf.png" />
                    {{ item.title }}
                  </div>
                </template>
                <div v-for="(child, idx) in item.content_arr" :key="idx">
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="more-content-tooltip">
                    <div slot="content" class="more-content">{{ child?.complete }}</div>
                    <div class="item-text" @click="handleCon(item)"  v-html="formatStr(child.current)"></div>
                  </el-tooltip>
                </div>
              </el-collapse-item>
            </el-collapse>
            <div v-else class="draft-list-con">
              <div class="item-title"  v-for="item in list" :key="item.id" @click="toDraft(item.id)">
                <img class="icon-pdf" src="../../assets/img/draft.png"/>
                {{ item.title }}
                </div>
            </div>
          </div>
        </el-drawer>
        <div class="restart-search">
          <el-tooltip class="item" effect="dark" content="文章收录" placement="top">
              <i class="el-icon-monitor" @click="articleCollect"></i>
          </el-tooltip>
          <el-tooltip trigger="hover" popper-class="menu-tooltip"  placement="top">
              <ul slot="content" class="menu-list">
                  <li @click="addMindVisible = true">新建脑图</li>
                  <li @click="addNote">新建笔记</li>
                  <li click="addDraft" @click="addDraft">新建白板</li>
              </ul>
              <i class="el-icon-document-add"/>
          </el-tooltip>
          <el-tooltip effect="dark" content="论文矩阵">
            <img src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/aaavvv.png" @click="toAtom"/>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="查看文档" placement="bottom">
            <img
            class="doc-switch"
            @click="handleRestartSearch"
            src="@/assets/editor/switchdoc.png"
            alt=""
          >
          </el-tooltip>
        <div>
      </div>
      </div>
      <el-dialog title="新增脑图" :visible.sync="addMindVisible" width="40%">
        <el-input v-model="mindTitle" placeholder="请输入脑图名称" maxlength="20" show-word-limit clearable />
        <span slot="footer" class="dialog-footer">
          <el-button @click="addMindVisible = false">取 消</el-button>
          <el-button type="primary" @click="addMindMap">确 定</el-button>
        </span>
      </el-dialog>
  </div>
</template>

<script>
import axios from '@/http';
import request from '@/http/request';
import List from './list.vue';
import { convertList, getSearchContent, searchEmu } from './util';
export default {
  data () {
    return {
      addMindVisible: false,
      mindTitle: '',
      loading: false,
      drawer: false,
      showType: 0,  // 0 展示默认列表，1，展示搜索
      searchType: 0, // 搜索类型0，关键词，1，词条
      tagList: [], // 词条列表
      activeTag: '', // 选中的词条
      activeName: searchEmu.PAPER,
      searchConfig: [
        {
          label: '本地',
          name: searchEmu.PAPER
        },
        {
          label: '论文',
          name: searchEmu.THESIS
        },
        {
          label: '文章',
          name: searchEmu.ARTICLE
        },
        {
          label: '书评',
          name: searchEmu.BOOK_COMMENT
        },
        {
          label: '笔记',
          name: searchEmu.NOTE
        },
        {
          label: '白板',
          name: searchEmu.DRAFT
        }
      ],
      activeNames: '',
      direction: 'ltr',
      type: searchEmu.PAPER, // 当前tabName
      contentObj: {},
      list: [],
      totalList: [],
      keyword: ''
    }
  },
  props: ['searchDrawer', 'searchKeyword', 'searchList', 'searchTotal'],
  components: {
    List
  },
  watch: {
    searchDrawer (val) {
      this.drawer = val
    },
    searchKeyword (val) {
      this.keyword = val
      if(val) {
        this.showType = 1
        this.handleTabClick()
      }
    },
    keyword() {
      if(!this.keyword) {
        this.handleTabClick()
      }
    },
    searchList () {
      // this.updateTotal(this.searchTotal)
      this.activeName = searchEmu.PAPER
      this.list = convertList({
        paper_list: {
          list: this.searchList
        }
      }, searchEmu.PAPER)
    }
  },
  methods: {
    addMindMap() {
      if(!this.mindTitle) {
        this.$message.error('请输入脑图名称')
        return
      }
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      this.addMindVisible = false
      axios.post('/ai/mind/add', {
          title: this.mindTitle,
          memberId: userInfo.userid
      }).then(res => {
        if(res.code === 200) {
          this.$message.success('添加成功')
          window.open(`#/mind/${res.data}`, '_blank');
        } else {
          this.$message.error('添加失败')
        }
      })
    },
    openUrl(data) {
      // 本地文档
      if(this.activeName === searchEmu.PAPER) {
        this.$emit('getUrlData', {
          ...data,
          type: 'note',
          url: data.paper_url
        })
      } else {
        this.$emit('getUrlData',data)
      }
    },
    toAtom() {
      this.$router.push('/papermatrix')
    },
    addNote() {
      this.$router.push({
          name: 'note',
          query: {
            paperId: ''
          }
        })
    },
    addDraft() {
      this.$router.push({
        name: 'draft',
        query: {
          id: ''
        }
      })
    },
    articleCollect() {
      this.$emit('openArticleCollect')
    },
    handleRestartSearch () {
      this.drawer = true
      this.$emit('updateSearchDrawer', true)
    },
    updateCatTotal (searchTotal) {
      const paperTotal = searchTotal?.paper_count || 0  // 本地
      const thesisTotal = searchTotal.thesis_count || 0 // 论文
      const articleTotal = searchTotal?.article_count || 0 // 文章
      const commentTotal = searchTotal?.comment_count || 0 // 评论
      const noteTotal = searchTotal?.note_count || 0  // 笔记
      const draftTotal = searchTotal?.draft_count || 0 // 白板
      this.totalList = [paperTotal, thesisTotal, articleTotal, commentTotal, noteTotal, draftTotal]
    },
    getCatTotal(id) {
      return new Promise((resolve, reject) => {
        let params ={}
        if(id) {
          params = {
            type: 2,
            cat_id: id,
          }
        }
        const authorization = localStorage.getItem('authorization')
        if (authorization) {
          request('/api/Paper/searchContentByTypeTotal', params).then((result) => {
            resolve(result)
          })
        }
      })
    },
    getTotal (keyword) {
      return new Promise((resolve, reject) => {
        request('/api/Paper/searchContentByTypeTotal', {
          type: 1,
          query: keyword,
        }).then((result) => {
          resolve(result)
        })
      })
    },
    searchCatList() {
      request('api/Paper/searchContentByCatId', {
        cat_id: this.activeTag,
        page: 1,
        limit: 20,
        search_type: this.activeName
      }).then((res) => {
        if(!res.length) {
          this.list = []
          return
        }
        this.list = convertList(res, this.activeName)
      })
    },
    searchCat() {
      if(this.searchType) {
        request('api/Keywords/getClassifyList', {
            classify_name: this.keyword,
          }).then(async (result) => {
            if(result.length) {
              this.tagList = result
              const catId = result[0].classify_id
              this.activeTag = catId
              const total = await this.getCatTotal(catId)
              this.updateCatTotal(total)
              this.searchCatList()
            }
            this.loading = false
          })
        // if(!this.tagList.length) {

        // } else {
        //   this.searchCatList()
        //   this.loading = false
        // }
      }
    },
    async handleTagClick(item) {
      this.activeTag = item.classify_id
      this.showType = 1
      const total = await this.getCatTotal(this.activeTag)
      this.updateCatTotal(total)
      this.searchCatList()
    },
    typeChange() {
      if(this.searchType) {
        this.getClassifyList()
      }
      this.handleTabClick()
    },
    getClassifyList() {
      request('/api/Keywords/getClassifyList', {}).then((res) => {
        this.tagList = res
      })
    },
    handleTabClick () {
      if(!this.keyword) {
        this.showType = 0
        this.getCatTotal('').then((total) => {
          this.updateCatTotal(total)
        })
        return
      }
      this.showType = 1;
      this.loading = true
      if(this.searchType) {
       this.searchCat()
      } else {
        request('api/Paper/searchContent', {
          query: this.keyword,
          page: 1,
          limit: 20,
          search_type: this.activeName
        }).then(async (result) => {
          this.loading = false
          this.list = convertList(result, this.activeName)
          const total = await this.getTotal(this.keyword)
          this.updateCatTotal(total)
        }).catch((err) => {
          this.loading = false
          this.$message.error(err.message)
        })
      }
    },
    async searchContentDetail (id) {
      const current = this.list.find((item) => item.id === id)
      // 草稿
      if (!current.content_arr.length) {
        const arr = await getSearchContent(id, this.keyword, this.activeName)
        if(arr.length) {
          current.content_arr = arr
        } else {
          this.getParerInfo(current)
        }
      }
    },
    change (arr) {
      const id = arr[arr.length - 1]
      if (id) {
        this.searchContentDetail(id)
      }
    },
    toDraft(id) {
      this.$router.push({
        name: 'draft',
        query: {
          id
        }
      })
    },

    handleClose () {
      this.$emit('updateSearchDrawer', false)
    },
    getParerInfo (item) {
      const { id, url, title, type } = item
      const dotIndex = url?.lastIndexOf('.')
      const extension = url?.slice(dotIndex + 1)
      if (type === 'draft') {
        this.$router.push({
          name: 'draft',
          query: {
            id
          }
        })
        return
      }
      if (type === 'article') {
        this.$router.push({
          name: 'article',
          query: {
            id: id
          }
        })
        return
      }
      if (extension === 'pdf') {
        this.$router.push({
          name: 'pdf',
          query: {
            paperId: id,
            title: title,
            url,
            keyword: this.keyword
          }
        })
      } else if (extension === 'docx') {
        this.$router.push({
          name: 'docx',
          query: {
            paperId: id,
            title: title,
            url
          }
        })
      } else {
        this.$router.push({
          name: 'note',
          query: {
            paperId: id
          }
        })
      }
      const data = {
        id: id,
        url: url,
        type: '',
        title: title
      }
      this.$emit('getLocalUrlData', data)
    },
    handleCon (item) {
      if ([searchEmu.LOCAL, searchEmu.PAPER].includes(this.activeName)) {
        this.getParerInfo(item.id)
        return
      } else if (this.activeName === searchEmu.THESIS) {
        request('/api/Paper/getPaperInfo', {
          paper_id: id
        }).then((result) => {
          let url = ''
          if (result.paper_url.includes('https://oss.bookor.com.cn')) {
            url = result.paper_url
          } else {
            url = `https://oss.bookor.com.cn/${result.paper_url}`
          }
          this.$router.push({
            name: 'pdfEmbed',
            query: {
              url,
              id,
              title: item.title
            }
          })
        })
      }

      const { id } = item
      if (this.activeName === searchEmu.DRAFT) {
        this.$router.push({
          name: 'draft',
          query: {
            id
          }
        })
      } else if (this.activeName === searchEmu.ARTICLE) {
        this.$router.push({
          name: 'article',
          query: {
            id: id
          }
        })
      } else if (this.activeName === searchEmu.BOOK_COMMENT) {
        this.$router.push({
          name: 'bookComment',
          query: {
            id
          }
        })
      } else if (this.activeName === searchEmu.NOTE) {
        this.$router.push({
          name: 'bookNote',
          query: {
            id
          }
        })
      }
    },
    formatStr (con) {
      const reg = new RegExp(this.keyword, 'g')
      const str = con?.replace(reg, (item) => {
        return `<span class='tag-keyword'>${item}</span>`
      })
      return str
    }
  },
  async mounted() {
    const total = await this.getCatTotal('')
    this.updateCatTotal(total)
  }
}
</script>
<style lang="scss" scoped>
.search-bar-container{
  position: relative;
}
.search-bar-container .restart-search{
  position: fixed;
  top: 48%;
  left: 4px;
  // background-color: #000;
  width: 70px;
  height: 40px;
  flex-wrap: wrap;
  display: flex;

  gap: 4px;
  i{
    font-size: 28px;
    cursor: pointer;
  }
  img{
    cursor: pointer;
    width: 30px;
    height: 30px;
  }
}
.more-content-tooltip{
  background-color: #999!important;
  .popper__arrow, .popper__arrow:after{
      border-top-color: #999!important;
  }
}
.more-content{
  width: 600px;
  font-size: 14px;
}
.search-bar-main{
  padding: 0 20px;
}
.search-bar-con{
  padding: 0 20px;
  .tag-keyword{
    background-color: rgb(248, 180, 33);
  }
  :deep(.el-collapse-item__header) {
    border-bottom: 1px solid #ddd;
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 0;
  }
  .empty{
    color: #999;
    text-align: center;
    padding: 40px 0;
  }
  .item-title{
    white-space: nowrap;
    cursor: pointer;
    overflow: hidden;
    padding: 0 10px;
    text-overflow: ellipsis; /* 显示省略号 */
    width: 350px;
  }
  .icon-pdf{
    width: 20px;
    height: 20px;
    position: relative;
    top: 5px;
    margin-right: 4px;
  }
  .item-text{
    height: 24px;
    cursor: pointer;
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 内容超出部分隐藏 */
    text-overflow: ellipsis; /* 显示省略号 */
    margin-top: 5px;
    margin-left: 20px;
    position: relative;
    padding-left: 16px;
    &:before{
      content: '';
      display: block;
      width: 6px;
      height: 6px;
      background-color: #666;
      border-radius: 50%;
      position: absolute;
      top: 8px;
      left: 0px;
    }
  }
}
.editor-search-bar .el-input{
  // width: 80%;
  margin-bottom: 20px;
}
.search-con{
  width: 80%;
  margin-left: 10%;
  display: flex;
  gap: 8px;
}
.tags-list{
  width: 80%;
  display: flex;
  gap: 8px;
  margin-left: 10%;
  overflow-x: auto;
  .el-tag{
    cursor: pointer;
  }
  .active{
    background-color: #0003ff;
  }
  &::-webkit-scrollbar {
    height: 2px;
    background: transparent;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }
}
.draft-list-con{
  background-color: #fff;
  border-radius: 6px;
  .item-title{
    white-space: nowrap;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis; /* 显示省略号 */
    height: 50px;
    padding-left: 10px;
    line-height: 50px;
    border-bottom: 1px solid #EBEEF5;
    width: auto;
  }
}
.menu-tooltip{
  padding: 0;
}
:deep(.menu-tooltip) {
  padding: 0!important;
}
.menu-list{
  li{
    height: 30px;
    line-height: 30px;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    &:hover{
      background-color: #111;
    }
  }
}
</style>
