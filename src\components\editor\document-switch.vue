<template>
  <div class="switch-box">
    <div class="switch-box-top" style="display: flex" v-show="activeTab === 0">
      <input
        class="switch-box-top-search"
        type="text"
        v-model="searchInput"
        placeholder="搜索词条"
        maxlength="100"
      />
      <img class="switch-box-top-img" src="@/assets/editor/search.png" alt="" />

      <div
        style="width: 202px; overflow: scroll; white-space: nowrap"
        class="scroll-btn"
        v-slide-scroll
      >
        <button
          class="switch-box-top-btn-classify"
          v-for="(item, index) in myMemberKeywordsTagList"
          :key="index"
          :class="
            activeTabClassify === item.classify_id
              ? 'switch-box-top-btn-active-classify'
              : ''
          "
          @click="chooseEntryClassify(item)"
        >
          {{ item.classify_name }}
        </button>
      </div>
    </div>
    <div
      v-if="isShowEntryClassifyBox"
      class="switch-box-bottom"
      style="z-index: 100"
    >
      <!-- Book词条 -->
      <div
        v-for="(item, indexBook) in this.entryFinalList"
        :key="indexBook"
        class="entryListItem"
        @click.stop="clickEntryListItem(item)"
      >
        {{ item.keywords }}
      </div>
      <div
        v-if="!entryResultBookList && !entryResultMemberList"
        style="
          font-size: 14px;
          color: #605b5b;
          text-align: center;
          margin-top: 40px;
        "
      >
        暂无词条
      </div>
    </div>
    <div v-if="!isShowEntryClassifyBox">
      <div v-if="activeTab === 0">
        <!-- 本地文档 -->
        <local-file :searchInput="searchInput"/>
      </div>
      <!-- 论文 -->
      <div v-if="activeTab === 1" class="switch-box-bottom">
        <div
          v-if="choosedResult.length > 0"
          class="switch-box-bottom"
          @scroll="handleScrollThesis"
        >
          <div
            v-for="(item, index) in choosedResult"
            :key="index"
            class="local-box"
            :class="currentId === item.paper_id ? 'select-subitem' : ''"
          >
            <div
              @click="
                chooseUrl(item.paper_id, item.paper_title)
              "
            >
              {{ item.paper_title }}
            </div>
            <img
              src="@/assets/editor/close.png"
              alt=""
              @click="deleteUrl(item.paper_id)"
            />
          </div>
        </div>
        <div v-else class="switch-box-bottom-content-else">暂无论文</div>
      </div>
      <!-- 收藏 -->
      <div v-if="activeTab === 2" class="switch-box-bottom">
        <div
          v-if="articleList.length > 0"
          class="switch-box-bottom"
          @scroll="handleScrollArticle"
        >
          <div
            class="article-box"
            v-for="(item, index) in articleList"
            :key="index"
            @click="chooseArticle(item.id, 'article', item)"
          >
            <div class="article-item-top">
              <div class="article-item-top-left">
                <div
                  v-if="item.author != null && item.author != ''"
                  class="article-item-top-left-name"
                >
                  {{ item.author_new }}
                </div>

                <div v-else class="article-item-top-left-name text-ellipsis">
                  {{ item.url_name }}
                </div>
                <div
                  v-if="item.publish_time != null && item.publish_time != ''"
                  class="article-item-top-left-date"
                >
                  {{ item.publish_time_new }}
                </div>
                <div v-else class="article-item-top-left-date">
                  {{ item.createtime_at }}
                </div>
              </div>
            </div>
            <div class="article-title">
              {{ item.title }}
            </div>
            <div class="article-text">
              {{ item.sum_up }}
            </div>
            <img
              v-if="item.article_id"
              src="@/assets/editor/close.png"
              alt=""
              style="width: 20px; position: absolute; top: 0; right: 0"
              @click.stop="deleteCollectArticle(item)"
            />
          </div>
        </div>
        <div v-else class="switch-box-bottom-content-else">暂无收藏</div>
      </div>
      <!-- 书评 -->
      <div v-if="activeTab === 3" class="switch-box-bottom">
        <div
          v-if="commentList.length > 0"
          @scroll="handleScrollComment"
          class="switch-box-bottom"
        >
          <div
            class="comment-box"
            v-for="(item, index) in commentList"
            :key="index"
            @click="chooseArticle(item.article_id, 'comment')"
          >
            <img :src="item.image" alt="" />
            <div class="info">
              <div class="title">{{ item.title }}</div>
              <div class="user">
                <div class="userInfo">
                  <div class="desc">{{ item.add_time }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="switch-box-bottom-content-else">暂无书评</div>
      </div>
      <!-- 笔记 -->
      <div v-if="activeTab === 4" class="switch-box-bottom">
        <div
          v-if="noteList.length > 0"
          @scroll="handleScrollNote"
          class="switch-box-bottom"
        >
          <div
            v-for="(item, index) in noteList"
            :key="index"
            class="comment-box"
            @click="chooseArticle(item.article_id, 'note')"
          >
            <img :src="item.image" alt="" />
            <div class="info">
              <div class="title">{{ item.title }}</div>
              <div class="user">
                <div class="userInfo">
                  <div class="desc">{{ item.add_time }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="switch-box-bottom-content-else">暂无笔记</div>
      </div>
      <Draft v-if="activeTab === 5" @chooseDraft="chooseDraft"></Draft>
    </div>
    <div class="switch-box-top" style="margin-top: 5px">
      <button
        class="switch-box-top-btn"
        v-for="(item, index) in tabs"
        :key="index"
        :class="activeTab === index ? 'switch-box-top-btn-active' : ''"
        @click="changeTab(index)"
      >
        {{ item }}
      </button>
    </div>
  </div>
</template>

<script>
import request from '@/http/request'
import { mapState } from 'vuex'
import { MessageBox } from 'element-ui'
import { EventBus } from '@/main.js'
import Draft from './components/Draft.vue'
import LocalFile from './components/LocalFile.vue'
export default {
  data () {
    return {
      searchInput: '',
      activeTab: 0,
      tabs: ['本地', '论文', '收藏', '书评', '笔记', '白板'],
      activeTabClassify: -1,
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      fileList: [],
      thesisList: [],
      articleList: [],
      commentList: [],
      noteList: [],
      pageThesis: 1,
      pagelimitThesis: 10,

      pageArticle: 1,
      pageLimitArticle: 10,

      pageComment: 1,
      pageNote: 1,

      pageCollectArticle: 1,
      pageCollectLimitArticle: 10,

      keyWordComment: '',
      currentId: '', // 选中的论文

      activeLocalFileId: '', // 选中的本地文件

      isShowEntryClassifyBox: false, // 按照词条分类
      entryResultBookList: [], // 返回的图书词条
      entryResultMemberList: [], // 返回的自定义词条

      entryFinalList: [], // 合并图书和自定义词条
      entryFinalListOrigin: [] // 不改变的
    }
  },
  created () {
    // 监听事件
    EventBus.$on('refreshThesisList', this.refreshThesisList)
  },
  computed: {
    ...mapState({
      choosedResult: (state) => state.choosedResult
    }),
    ...mapState('entryclassify', {
      myMemberKeywordsTagList: (state) => state.myMemberKeywordsTagList
    })
  },
  components: {
    Draft,
    LocalFile
  },
  watch: {
    choosedResult (newVal) {
      const oldVal = this.choosedResult
      this.$store.commit('updateChoosedResult', newVal)
      // 深度监听数组变化
      this.$watch(
        'choosedResult',
        (newVal) => {
          if (newVal.length > oldVal.length) {
            const newEle = newVal.find((ele) => !oldVal.includes(ele))
            this.currentId = newEle.id
          }
        },
        { deep: true }
      )
    },
    myMemberKeywordsTagList (val) {
    }
  },
  methods: {
    changeTab (index) {
      this.searchInput = ''
      this.activeTab = index
      this.isShowEntryClassifyBox = false
      if (index === 1 && this.thesisList.length <= 0) {
        this.apiGetThesisData()
      } else if (index === 2 && this.articleList.length <= 0) {
        this.apiGetCollectData()
        this.getCollectArticleList()
      } else if (index === 3 && this.commentList.length <= 0) {
        this.apiGetComment()
      } else if (index === 4 && this.noteList.length <= 0) {
        this.apiGetNote()
      }
    },

    apiGetThesisData () {
      const params = {
        page: this.pageThesis,
        limit: this.pagelimitThesis,
        paper_type: 'paper'
      }
      request('/api/Paper/getPaperList', params).then((result) => {
        const list = result
        if (list && list.length > 0) {
          this.thesisList = [...this.thesisList, ...list]
          this.$store.commit('updateChoosedResult', this.thesisList)
        } else {
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
      }).catch(() => {
        this.$message({
          message: '未获取到数据，请重试',
          type: 'error',
          duration: 1000
        })
      })
    },

    apiGetCollectData () {
      const params = {
        page: this.pageArticle,
        limit: this.pageLimitArticle,
        search: ''
      }

      request('/api/wechatarticle/articleList', params).then((result) => {
        const list = result
        if (list) {
          // 获取url的域名部分
          const regex = /\/\/([^\/]+)\//
          list.map((item) => {
            if (item.url) {
              const match = item.url.match(regex)
              if (match) {
                item.url_name = match[1].replace(/^www\.|\.com$/gi, '')
              }
            }
          })
          if (result.length == this.pageLimitArticle) {
            for (let i = 0; i < list.length; i++) {
              let sumUp = list[i].sum_up
              sumUp = sumUp.replaceAll('/[\r\n]/g', ' ')
              sumUp = sumUp.replace(/&nbsp;/g, ' ')
              sumUp = sumUp.replace('hasSetZeroLevel:true', '')
              list[i].sum_up = sumUp.replace(/\[p\d+\]/g, '') // 展示sum_up时,替换一下段落标识
              const publishTime = list[i].publish_time
              if (publishTime) {
                list[i].publish_time = publishTime.split(' ')[0]
              }
              const publishTimeNew = list[i].publish_time_new
              if (publishTimeNew) {
                list[i].publish_time_new = publishTimeNew.split(' ')[0]
              }
            }

            this.articleList = [...this.articleList, ...list]
          } else {
            this.articleList = [...this.articleList, ...list]
          }
        } else {
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
      }).catch(() => {
        this.$message({
          message: '未获取到数据，请重试',
          type: 'error',
          duration: 1000
        })
      })
    },

    apiGetComment () {
      const userinfo = JSON.parse(localStorage.getItem('userinfo'))
      const type = 'comment'

      const params = {
        page: this.pageComment,
        limit: 10,
        search_status: this.keyWordComment ? 'on' : 'off',
        search_words: this.keyWordComment,
        data_type: 'self',
        article_type: type,
        member_type: 'self'
      }
      const apiUrl =
        'https://api.bookor.com.cn/index.php/mobile/member/reviewsList'
      request(apiUrl, params).then((result) => {
        const list = result.list
        if (list) {
          this.commentList = [...this.commentList, ...list]
        } else {
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
      }).catch(() => {
        this.$message({
          message: '未获取到数据，请重试',
          type: 'error',
          duration: 1000
        })
      })
    },

    apiGetNote () {
      const type = 'note'
      const params = {
        page: this.pageNote,
        limit: 10,
        search_status: this.keyWordComment ? 'on' : 'off',
        search_words: this.keyWordComment,
        data_type: 'self',
        article_type: type,
        member_type: 'self'
      }
      const apiUrl =
        'https://api.bookor.com.cn/index.php/mobile/member/reviewsList'
      request(apiUrl, params).then((result) => {
        const list = result.list
        if (list) {
          this.noteList = [...this.noteList, ...list]
        } else {
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
      }).catch(() => {
        this.$message({
          message: '未获取到数据，请重试',
          type: 'error',
          duration: 1000
        })
      })
    },

    handleScrollThesis (event) {
      const elem = event.target
      if (elem.scrollTop + elem.clientHeight >= elem.scrollHeight) {
        this.pageThesis++
        this.apiGetThesisData()
      }
    },

    handleScrollArticle (event) {
      const elem = event.target
      if (elem.scrollTop + elem.clientHeight >= elem.scrollHeight) {
        this.pageArticle++
        this.apiGetCollectData()
      }
    },
    handleScrollComment (event) {
      const elem = event.target
      if (elem.scrollTop + elem.clientHeight >= elem.scrollHeight) {
        this.pageComment++
        this.apiGetComment()
      }
    },
    handleScrollNote (event) {
      const elem = event.target
      if (elem.scrollTop + elem.clientHeight >= elem.scrollHeight) {
        this.pageNote++
        this.apiGetNote()
      }
    },

    chooseArticle (id, type, item) {
      if (type === 'comment') {
        this.$router.push({
          name: 'bookComment',
          query: {
            id
          }
        })
      } else if (type === 'note') {
        this.$router.push({
          name: 'bookNote',
          query: {
            id
          }
        })
      } else {
        this.$router.push({
          name: 'article',
          query: {
            id: id
          }
        })
      }
    },

    chooseDraft (id) {
      this.$router.push({
        name: 'draft',
        query: {
          id
        }
      })
      // this.$emit('getDraft', id)
    },

    // 新增后刷新论文数据
    refreshThesisList () {
      this.thesisList = []
      const params = {
        page: 1,
        limit: 10,
        paper_type: 'paper'
      }
      request('/api/Paper/getPaperList', params).then((result) => {
        const list = result
        if (list) {
          this.thesisList = [...this.thesisList, ...list]
          this.$store.commit('updateChoosedResult', this.thesisList)
        } else {
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
      }).catch(() => {
        this.$message({
          message: '未获取到数据，请重试',
          type: 'error',
          duration: 1000
        })
      })
    },

    // 刷新本地文档数据
    refreshFileFileList () {
      this.fileList = []
      const params = {
        page: 1,
        limit: 10,
        paper_type: 'others'
      }
      if (!this.userinfo?.userid) {
        return
      }
      request('/api/Paper/getPaperList', params).then((result) => {
        const list = result
        if (list) {
          this.fileList = [...this.fileList, ...list]
          this.$store.commit('updateUploadedFiles', this.fileList)
        } else {
          this.$message({
            message: '没有更多了',
            type: 'info',
            duration: 1000
          })
        }
      }).catch(() => {
        this.$message({
          message: '未获取到数据，请重试',
          type: 'error',
          duration: 1000
        })
      })
    },

    // 切换论文数组
    chooseUrl (id, paperTitle) {
      request('/api/Paper/getPaperInfo', {
        paper_id: id
      }).then((result) => {
        let url = ''
        if (result.paper_url.includes('https://oss.bookor.com.cn')) {
          url = result.paper_url
        } else {
          url = `https://oss.bookor.com.cn/${result.paper_url}`
        }
        this.$router.push({
          name: 'pdfEmbed',
          query: {
            url,
            id,
            title: paperTitle
          }
        })
      })
    },

    // 选中某个词条分类
    async chooseEntryClassify (item) {
      this.searchInput = ''
      this.isShowEntryClassifyBox = true
      this.activeTabClassify = item.classify_id
      const params = {
        classify_id: item.classify_id
      }
      request(
        '/api/Keywords/getKeywordsByClassifyId',
        params
      ).then((result) => {
        this.entryResultBookList = result.book_keywords_list || []
        this.entryResultMemberList = result?.member_keywords_list || []
        this.entryFinalList = this.entryResultBookList.concat(
          this.entryResultMemberList
        )
        this.entryFinalListOrigin = this.entryFinalList
      })
    },

    // 选中某个词条
    clickEntryListItem (item) {
      // 是用户自己创建的词条 包括划选epub创建的词条
      if (item?.member_keywords_id) {
        if (item?.paper_keywords?.length > 0) {
          this.$emit('getEntryData', item.paper_keywords[0])
        }
      }
    },

    // 获取导读收藏的文章
    async getCollectArticleList () {
      const params = {
        page: this.pageCollectArticle,
        limit: this.pageCollectLimitArticle
      }
      request('/api/Paper/getCollectArticleList', params).then((result) => {
        const list = result.list.map((item) => {
          item.id = item.article_id
          return item
        })
        this.articleList = [...list, ...this.articleList]
      })
    },
    // 取消收藏导读文章
    deleteCollectArticle (item) {
      MessageBox.confirm('是否删除该导读文章?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 用户点击确定按钮时触发的回调函数
          this.apiDeleteCollectArticle(item)
        })
    },
    async apiDeleteCollectArticle (item) {
      const params = {
        article_id: item.article_id,
        collect_member_id: item.member_id
      }
      request('/api/Paper/deleteCollectArticle', params).then(() => {
        this.$message({
          message: '删除导读文章成功',
          type: 'success',
          duration: 1000
        })
        this.articleList = this.articleList.filter((itemList) => {
          return itemList.article_id !== item.article_id
        })
      }).catch((res) => {
        this.$message({
          message: res.message,
          type: 'error',
          duration: 1000
        })
      })
    }
  },

  mounted () {
    this.$store.dispatch('entryclassify/getMemberKeywordsTagList')
  }
}
</script>

<style lang="scss">
.switch-box {
  position: absolute;
  z-index: 99;
  left: -225px;
  top: 20px;
  width: 300px;
  padding: 5px 10px;
  background-color: white;
  word-wrap: break-word;
  // height: 500px;
  box-shadow: 2px 2px 3px rgba($color: #000000, $alpha: 0.3);
  .switch-box-top {
    height: 30px;
    position: relative;
    .switch-box-top-search {
      background-color: #efefef;
      border: 1px solid #2d6ba4;
      border-radius: 5px;
      height: 25px;
      padding-left: 28px;
      width: 60px;
      margin-right: 5px;
    }
    .switch-box-top-img {
      width: 20px;
      height: 20px;
      position: absolute;
      top: 4px;
      left: 6px;
    }
    .switch-box-top-btn {
      background-color: #efefef;
      border: 1px solid #2d6ba4;
      padding: 3px;
      margin: 0 3px;
      height: 25px;
      border-radius: 5px;
      color: #6d8997;
      cursor: pointer;
    }
    .switch-box-top-btn-active {
      background-color: #6d8997;
      color: white;
    }

    .switch-box-top-btn-classify {
      background-color: #efefef;
      border: 1px solid #596882;
      padding: 3px 5px;
      margin: 0 3px;
      height: 25px;
      border-radius: 3px;
      color: #000000;
      cursor: pointer;
    }
    .switch-box-top-btn-active-classify {
      background-color: #8faadc;
      color: white;
    }
  }
  .article-box {
    background-color: white;
    padding: 5px;
    margin: 5px 0;
    border-radius: 5px;
    cursor: pointer;
    position: relative;

    .article-item-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      align-items: center;
    }

    .article-item-top-left {
      padding-right: 3%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .article-item-top-left-name {
      color: #065a9c;
      font-weight: 600;
      font-size: 13px;
      font-style: italic;
      text-overflow: ellipsis;
    }
    .article-item-top-left-date {
      font-size: 13px;
      font-style: italic;
      color: rgba(0, 0, 0, 0.5);
      padding-left: 7px;
    }

    .article-title {
      font-size: 14px;
      color: #000;
      font-weight: 600;
      margin-bottom: 10rpx;
      line-height: 50rpx;
      color: #353535;

      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .article-text {
      font-size: 14px;
      line-height: 14px;
      color: rgba(0, 0, 0, 0.5);
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }
  ::-webkit-scrollbar {
    display: none;
  }

  .comment-box {
    background-color: white;
    margin: 8px 10px;
    width: 120px;
    height: 200px;
    display: inline-block;
    cursor: pointer;
    img {
      width: 120px;
      height: 160px;
    }

    .info {
      background: #fff;
      padding: 5px;
    }
    .title {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      font-size: 13px;
      color: #333;
    }
    .desc {
      color: #999;
      font-size: 11px;
    }
  }

  .local-box {
    padding: 5px;
    font-size: 15px;
    background-color: white;
    margin: 5px 0;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    transition: background-color 0.8s, transform 0.8s; /* 过渡效果 */
    &:active{
      transform: scale(1.4); /* 放大按钮 */
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
    }
    .title {
      cursor: pointer;
      overflow: auto;
      width: 260px;
    }
    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
}

.select-subitem {
  background-color: #ddd9d9 !important;
}
</style>
