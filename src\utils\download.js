const download = async (url, filename) => {
  try {
    const response = await fetch(url)
    const blob = await response.blob()
    const blobUrl = window.URL.createObjectURL(blob)

    // 从 Content-Disposition 获取文件名
    const contentDisposition = response.headers.get('content-disposition')
    let originalFilename = ''
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        originalFilename = filenameMatch[1].replace(/['"]/g, '')
      }
    }

    const anchorElement = document.createElement('a')
    anchorElement.href = blobUrl
    // 优先使用传入的 filename,如果没有则使用原始文件名
    anchorElement.download = filename || originalFilename || 'download'

    document.body.appendChild(anchorElement)
    anchorElement.click()
    document.body.removeChild(anchorElement)

    window.URL.revokeObjectURL(blobUrl)
  } catch (error) {
    console.error('Download failed:', error)
  }
}

export default download
