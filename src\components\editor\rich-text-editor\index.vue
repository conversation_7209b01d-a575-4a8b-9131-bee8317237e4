<template>
  <div style="position: relative">
    <el-tiptap v-model="content" :extensions="extensions" lang="zh" placeholder="请输入内容 …" ref="editor"
      @addCreated="addCreated" @openAi="openAi" />

    <el-tooltip trigger="hover" popper-class="menu-tooltip">
      <ul slot="content" class="menu-list">
        <li @click="sendSave">直接保存</li>
        <li @click="saveAs">另存为</li>
      </ul>
      <img src="@/assets/editor/save.png" class="save-btn" />
    </el-tooltip>

    <el-tooltip class="item" effect="dark" content="文章保存为白板" placement="bottom">
      <img src="https://bookor-application.oss-cn-beijing.aliyuncs.com/board.png" class="board-save-btn"
        @click="saveBoard">
    </el-tooltip>
    <el-tooltip trigger="hover" popper-class="menu-tooltip">
      <ul slot="content" class="menu-list">
        <li @click="downloadNote">下载DOC</li>
        <li v-if="note && note.talk_mp3" @click="downloadConfirm">下载MP3</li>
      </ul>
      <i class="el-icon-download download-note" />
    </el-tooltip>
    <el-tooltip class="item" effect="dark" content="支持doc,pdf,epub" placement="bottom">
      <i class="el-icon-upload2 upload-note" @click="uploadDoc" />
    </el-tooltip>
    <div class="play-audio">
      <el-tooltip class="item" effect="dark" :content="paperMp3 ? '播放音频' : '暂无音频'" placement="bottom">
        <div @click="playMp3">
          <img src="https://bookor-application.oss-cn-beijing.aliyuncs.com/volumn1.png" v-if="paperMp3" @click="playMp3" style="width: 22px;height: 22px;"/>
          <img src="https://bookor-application.oss-cn-beijing.aliyuncs.com/volumn.png" v-else  style="width: 22px;height: 22px;"/>
        </div>
      </el-tooltip>
      <div class="volumn-slider" v-if="audioPlaying">
        <i class="el-icon-video-play" v-if="!audioPlaying" @click="playMp3"></i>
        <i class="el-icon-video-pause" v-else @click="pauseMp3"></i>
        <el-slider v-model="auduoPerctct" :min="0" :max="100" :show-input="false" @change="timeChange"></el-slider>
      </div>
    </div>
    <el-tooltip trigger="hover" popper-class="menu-tooltip">
      <ul slot="content" class="menu-list">
        <li @click="toShare(0)">好友分享</li>
        <li @click="toShare(1)">站外分享</li>
      </ul>
      <i class="el-icon-share"></i>
    </el-tooltip>
    <input type="file" ref="docFile" accept=".doc,.docx,.pdf,.epub" id="file" @change="handleFileChange"
      style="display: none;">
    <el-dialog :visible.sync="dialogVisible" class="title-box">
      <el-form label-position="left" label-width="80px">
        <!-- saveType === 1 白板保存 -->
        <el-form-item label="标题" v-if="saveType === 1">
          <el-input v-model="broadTitle" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="标题" v-if="saveType === 2">
          <el-input v-model="saveAsTitle" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="标题" v-if="saveType === 0">
          <el-input v-model="title" placeholder="请输入标题" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveWithTitle">确认</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="addVisible">
      <div class="dialog-content">
        <el-form label-position="left" label-width="80px" v-for="(item, idx) in createData.list" :key="idx">
          <el-form-item label="词条名称" v-show="!item.hide">
            <el-input v-model="item.title" placeholder="请输入词条名称标题" />
            <el-select v-model="item.type" placeholder="请选择图形">
              <el-option :key="2" label="白板方形" :value="2">
              </el-option>
              <el-option :key="1" label="白板圆形" :value="1">
              </el-option>
            </el-select>
            <i class="el-icon-delete" @click="deleteItem(idx)"></i>
          </el-form-item>
          <el-form-item label="词条内容" v-show="!item.hide">
            <p class="add-content">{{ item.content }}</p>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addVisible = false">取消</el-button>
        <el-button type="primary" @click="addEntry">确认</el-button>
      </span>
    </el-dialog>
    <el-dialog title="分享文章" :visible.sync="shareDialog" width="50%" :before-close="handleShareClose">
      <div class="friend-list">
        <el-checkbox-group v-model="checkList">
          <div class="item-friend" v-for="item in friendList" :key="item.bei_member_id">
            <div class="left">
              <el-checkbox :label="item.bei_member_id"></el-checkbox>
            </div>
            <div class="right">
              <img :src="item.member_avatar" />
              <span class="nick">{{ item.member_name }}</span>
            </div>
          </div>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="shareDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitShare">确 定</el-button>
      </span>
    </el-dialog>
    <p id="copyBtn" ref="copyBtn" style="display: none" :data-clipboard-text="copyText"></p>
  </div>
</template>

<script>
let audioUtil = null
import request from '@/http/request'
import { EventBus } from '@/main.js'
import ClipboardJS from 'clipboard'
import {
Blockquote,
Bold,
BulletList,
Doc,
Heading,
// FormatClear,
History,
Image,
Italic,
Link,
ListItem,
OrderedList,
Paragraph,
Strike,
Text,
TextAlign,
TextColor,
TextHighlight,
TrailingNode,
Underline
} from 'element-tiptap'
import 'element-tiptap/lib/index.css'
import { download } from '../../../utils/index.js'
import Ai from './ai/index.js'
import Create from './create/index.js'
const sizeLimit = 4 * 1024 * 1024
export default {
  data () {
    // 编辑器的 extensions
    // 它们将会按照你声明的顺序被添加到菜单栏和气泡菜单中
    return {
      //  menubar: false 菜单栏不展示
      extensions: [
        new Doc(), // 必须项
        new Text(), // 必须项
        new Paragraph(), // 必须项
        new History(), // 撤销
        new TrailingNode(), // 重做
        new Heading({ level: 6 }), // 标题
        new Bold({ bubble: true }), // 加粗 bubble: true 在气泡菜单中渲染菜单按钮
        new Underline({ bubble: true, menubar: false }), // 下划线 bubble: true, menubar: false 在气泡菜单而不在菜单栏中渲染菜单按钮
        new Italic({ bubble: true }), // 斜体
        new Strike({ bubble: true }), // 删除线
        new ListItem(), // 使用列表必须项
        new BulletList({ bubble: true }), // 无序列表
        new OrderedList({ bubble: true }), // 有序列表
        new Link(), // 链接
        new Image(), // 图片
        new Blockquote(), // 引用
        new TextAlign({ bubble: true }), // 文本对齐方式
        new TextHighlight({ bubble: true }), // 文本高亮
        new TextColor({ bubble: true }), // 文本颜色
        // new FormatClear({ bubble: true }), // 清除格式
        new Create({ bubble: true, menubar: false }),
        new Ai({ bubble: true, menubar: false }),
        // new Rect({ bubble: true }), // 白板方形
        // new Cir({ bubble: true }), // 白板圆形
        // new Help({ bubble: false, menubar: true })
      ],
      value: '',
      dialogVisible: false, // 弹窗显示状态
      // 编辑器的内容
      content: this.upcontent,
      title: this.uptitle,
      broadTitle: '',
      saveAsTitle: '', // 另存为标题
      saveType: 0, // 保存的方式 0 为保存文档，1 为保存白板 2.另存为
      addVisible: false,
      shareDialog: false,
      createData: {
        name: '',
        list: [],
        type: '',
        back: null
      },
      audioPlaying: false,
      showAudio: false,
      checkList: [],
      auduoPerctct: 0,
      copyText: '',
      friendList: [],
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
    }
  },
  props: ['note','uptitle', 'upcontent', 'update', 'paperMp3'],
  watch: {
    upcontent (newVal) {
      this.title = ''
      this.$nextTick(() => {
        this.content = newVal
      })
    },
    uptitle (newVal) {
      this.title = newVal
      this.oldtitle = newVal
    },
    update (newVal) {
      this.isUpdate = newVal
    }
  },
  computed: {
    memberIdentity () {
      return this.$store.state.user.userInfoMore.memberIdentity
    }
  },
  methods: {
    downloadConfirm() {
      download(this.note.talk_mp3)
      this.$message.success('下载成功')
    },
    timeChange(val) {
      const time = (val / 100) * audioUtil.duration
      audioUtil.currentTime = time
    },
    handleShareClose() {
      this.shareDialog = false
    },
    submitShare() {
      if (!this.checkList.length) {
        return this.$message.info('请选择分享人')
      }
      const paperId = this.$route.query.paperId
      request('api/Paper/paperShare', {
        paper_id: paperId,
        member_ids: this.checkList.join()
      }).then(() => {
        this.$message.success('分享成功')
        this.shareDialog = false
      })
    },
    toShare (command) {
      if (command) {
        if (!this.title) {
          return this.$message.info('文档名称不能为空')
        }
        if (this.userinfo) {
          const paperId = this.$route.query.paperId
          this.copyText = `你的好友分享了${this.title} 请复制链接在浏览器打开\n https://www.bookor.com.cn/#/editor/note?paperId=${paperId}&userId=${this.userinfo.userid}`
        }
        this.$nextTick(() => {
          this.$refs.copyBtn.click()
        })
      } else {
        request('/mobile/member/myFans', {
          key: this.userinfo?.key,
          type: '0'
        })
          .then((result) => {
            const list = result
            this.shareDialog = true
            this.friendList = list
          })
          .catch(() => {
            this.$message.error('获取好友失败')
          })
      }
    },
    playMp3() {
      if(!this.paperMp3) {
        return this.$message.info('暂无音频');
      }
      if(!audioUtil) {
        audioUtil = document.createElement('audio');
        audioUtil.src = this.paperMp3
        audioUtil.addEventListener('ended', () => {
          audioUtil.currentTime = 0
          this.pauseMp3()
        })
      }
      audioUtil.addEventListener('timeupdate', () => {
          this.auduoPerctct = Math.floor(audioUtil.currentTime / audioUtil.duration * 100)
      })
      audioUtil.play()
      this.audioPlaying = true
      this.showAudio = true
    },
    pauseMp3() {
      audioUtil.pause()
      audioUtil.removeEventListener('timeupdate', () => {})
      this.audioPlaying = false
    },
    uploadDoc () {
      const inputRef = this.$refs.docFile
      inputRef.click()
    },
    handleFileChange () {
      const loading = this.$loading({
        lock: true,
        text: '文件上传中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const fileInput = this.$refs.docFile
      const file = fileInput.files[0]

      if (file.size > sizeLimit) {
        fileInput.value = ''
        this.$message.error('文件大小超过限制')
        return
      }
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      const formData = new FormData()
      formData.append('file', file)
      formData.append('member_id', userInfo.userid)
      // 使用XMLHttpRequest或Fetch API发送数据
      var xhr = new XMLHttpRequest()
      xhr.open('POST', '/api/Paper/getPaperByWord', true)
      xhr.onload = () => {
        if (xhr.status === 200) {
          const result = JSON.parse(xhr.responseText).result
          const imgTagRegex = /<img[^>]*>/g

          // 使用 DOMParser 来安全地解析 HTML
          const parser = new DOMParser()
          const doc = parser.parseFromString(result.paper_url, 'text/html')

          // 移除所有图片标签
          const images = doc.getElementsByTagName('img')
          while(images.length > 0) {
            images[0].parentNode.removeChild(images[0])
          }
          // 获取处理后的 HTML
          const content = doc.body.innerHTML
          // 追加到现有内容
          this.content = this.content + content
          this.$message.success('文件导入成功')
          loading.close()
        } else {
          loading.close()
          this.$message.error('文件上传失败')
        }
        fileInput.value = ''
      }
      xhr.send(formData)
    },
    saveAs () {
      this.saveType = 2
      this.saveAsTitle = this.title
      this.dialogVisible = true // 显示弹窗
    },
    downloadNote () {
      request('/api/Paper/getWordByPaper', {
        content: this.content
      }).then((result) => {
        download(result.url)
        this.$message.success('下载成功')
      })
    },
    checkBoard (data) {
      return new Promise((resolve, reject) => {
        request('/api/Draft/paperGenerateDraftCheck', {
          content: this.content
        }).then(() => {
          this.$message({
            message: '保存白板成功',
            type: 'success'
          })
          resolve(true)
        }).catch((result) => {
          const con = result.message.replace(/\\r\\n/g, '<br>')
          this.$alert(con, '除以下内容，文档已成功生成元素', {
            confirmButtonText: '查看',
            callback: action => {
              this.$router.push({
                name: 'draft',
                query: {
                  id: data.id
                }
              })
            },
            dangerouslyUseHTMLString: true
          })
          reject(result)
        })
      })
    },
    async saveBoard () {
      this.saveType = 1
      this.broadTitle = ''
      this.dialogVisible = true // 显示弹窗
    },
    sendSave () {
      this.saveType = 0
      const strippedContent = this.content.replace(/(<([^>]+)>)/gi, '')
      if (!strippedContent.trim()) {
        this.$message.error('保存时内容不能为空')
      } else {
        if (this.title) {
          this.saveWithTitle()
        } else {
          this.saveType = 0
          this.dialogVisible = true // 显示弹窗
        }
      }
    },
    createNote () {
      const paperId = this.$route.query.paperId
      if (!paperId && this.content) {
        this.$confirm('你当前有内容尚未保存，你确定新建文件吗', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.title = ''
          this.content = ''
          this.$router.replace({
            name: 'note'
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消成功'
          })
        })
        return
      }
      this.content = ''
      this.title = ''
      this.$router.replace({
        name: 'note'
      })
    },
    deleteItem (idx) {
      let total = 1
      this.createData.list.forEach((item) => {
        if (item.hide) {
          total += 1
        }
      })
      if (this.createData.list.length === total) {
        return this.$message.error('至少需要一个词条内容')
      }
      this.createData.list[idx].hide = true
    },
    saveWithTitle () {
      if (this.saveType === 2) {
        if (!this.saveAsTitle) {
          return this.$message.error('标题不能为空')
        }
        this.apiAddFilesandWord(this.saveAsTitle, this.content)
        this.dialogVisible = false // 隐藏弹窗
        return
      }
      if (this.saveType === 1) {
        if (!this.broadTitle) {
          return this.$message.error('标题不能为空')
        }
        request('/api/Draft/paperGenerateDraft', {
          content: this.content,
          title: this.broadTitle
        }).then((res) => {
          this.checkBoard(res)
          this.broadTitle = ''
          this.dialogVisible = false
        }).catch((result) => {
          this.title = ''
          this.$message.error(result.message || '')
        })
        return
      }
      if (!this.title) {
        return this.$message.error('标题不能为空')
      }
      // 更新
      const paperId = this.$route.query.paperId
      if (paperId) {
        this.apiUpdateWord(this.title, this.content)
      } else {
        // 新建
        this.apiAddFilesandWord(this.title, this.content)
      }
      this.dialogVisible = false // 隐藏弹窗
    },
    addEntry () {
      const list = this.createData.list
      for (let i = 0; i < list.length; i += 1) {
        const item = list[i]
        if (!item.title && !item.hide) {
          return this.$message.error('词条标题不能为空')
        }
      }
      const paperId = this.$route.query.paperId || ''

      list.forEach((item) => {
        if (!item.hide) {
          request('/api/Keywords/addMemberKeywords', {
            description: item.content,
            keywords: item.title,
            paper_id: paperId
          })
        }
        this.addVisible = false
        this.$message.success('添加词条成功')
      })
      this.createData.back(this.createData.list)
    },
    // 上传内容到服务器 对于文档，title没带后缀、url就是内容
    apiAddFilesandWord (title, url) {
      const params = {
        paper_title: title,
        paper_url: url,
        paper_type: 'others' // 区分上传的类型 other表示上传的文档和word
      }
      request('/api/Paper/addPaper', params).then((result) => {
        this.isUpdate = true
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 1000
        })
        if (this.saveType !== 2) {
          this.$router.replace({
            query: {
              paperId: result.paper_id
            }
          })
        }
        setTimeout(() => {
          EventBus.$emit('refreshFileFileList')
        }, 1000)
      }).catch((result) => {
        this.title = ''
        this.$message({
          message: result?.message || '上传错误，请重新提交',
          type: 'error',
          duration: 1000
        })
      })
    },

    // 更新内容或标题
    apiUpdateWord (title, url) {
      const paperId = this.$route.query.paperId
      const params = {
        paper_title: title,
        paper_url: url,
        paper_type: 'others', // 区分上传的类型 other表示上传的文档和word
        paper_id: paperId
      }
      request('/api/Paper/updatePaper', params).then((result) => {
        this.$message({
          message: '编辑成功',
          type: 'success',
          duration: 1000
        })
        EventBus.$emit('refreshFileFileList')
      }).catch(() => {
        this.$message({
          message: '内容无变更，请变更后在进行保存',
          type: 'success',
          duration: 1000
        })
      })
    },
    addCreated (arr, callback) {
      this.addVisible = true
      this.createData.list = arr.map((con) => {
        const item = {
          type: null,
          name: '',
          content: con,
          hide: false
        }
        return item
      })
      this.createData.back = callback
    },
    openAi (text) {
      this.$parent.$parent.openAi(text, this.content)
    }
  },
  mounted () {
    const copyBtn = new ClipboardJS('#copyBtn')

    copyBtn.on('success', (e) => {
      this.$message.success('站外分享链接复制成功, 您可以发送给你的好友')
    })
    EventBus.$on('createNode', () => {
      this.createNote()
    })
  }
}
</script>

<style lang="scss" scoped>
.el-tiptap-editor {
  padding: 0 2px 0 0;
  height: 93vh;
  width: 80vw;
}

:deep(.download-popover){
  background: #030303;
  color: #fff;
  padding: 10px;
}

.save-btn {
  position: absolute;
  right: 12px;
  top: 10px;
  width: 18px;
  cursor: pointer;
}

.el-icon-share{
  position: absolute;
  right: 160px;
  top: 10px;
  font-size: 22px;
}

.board-save-btn {
  position: absolute;
  right: 42px;
  top: 8px;
  width: 22px;
  cursor: pointer;
}

.create-note {
  position: absolute;
  right: 72px;
  top: 10px;
  cursor: pointer;
  font-size: 20px;
}

.download-note {
  position: absolute;
  right: 72px;
  top: 10px;
  cursor: pointer;
  font-size: 20px;
}

.upload-note {
  position: absolute;
  right: 98px;
  top: 10px;
  cursor: pointer;
  font-size: 20px;
}
.play-audio{
  position: absolute;
  right: 128px;
  top: 10px;
  cursor: pointer;
  img{
    width: 22px;
    height: 22px;
  }
  .volumn-slider{
    background: #999;
    border-radius: 8px;
    position: absolute;
    top: 30px;
    left: -80px;
    width: 220px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    i{
      font-size: 22px;
      color: #fff;
    }
    .el-slider{
      width: 140px;
    }
  }
}

/* 使用 ::v-deep 选择器 */
.el-tiptap-editor ::v-deep .el-tiptap-editor__menu-bar {
  padding: 0 0 0 20px;
  justify-content: center;
}

.el-tiptap-editor ::v-deep .el-tiptap-editor__command-button {
  height: 35px;
  width: 35px;
}

.el-tiptap-editor ::v-deep .el-tiptap-editor__content {
  padding: 10px 20px;
}

.el-tiptap-editor ::v-deep .el-tiptap-editor__footer {
  padding: 5px 10px;
}

.el-tiptap-editor ::v-deep .border-bottom-radius {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.el-tiptap-editor ::v-deep .border-top-radius {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
</style>

<style lang="scss">
.title-box {
  .el-dialog__footer {
    text-align: center;
    padding: 0px 20px 20px;
  }

  .el-dialog__body {
    padding: 20px 20px 0;
  }

  .el-form--label-left .el-form-item__label {
    text-align: center;
  }
}

.menu-tooltip {
  padding: 0;
}

:deep(.menu-tooltip) {
  padding: 0 !important;
}

.menu-list {
  li {
    height: 30px;
    line-height: 30px;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: #111;
    }
  }
}

.add-content {
  line-height: 22px;
}

:deep(.el-form-item__content) {
  display: flex;
  gap: 10px;
}

.el-message-box__message {
  max-height: 600px;
  overflow-y: auto;
}

.dialog-content {
  max-height: 600px;
  overflow-y: auto;

  .el-icon-delete {
    position: relative;
    top: 12px;
  }
}
.mp3-audio-content {
  position: fixed;
  right: 12px;
  top: 260px;
  cursor: pointer;
  .el-progress-circle{
    width: 70px !important;
    height: 70px !important;
    position: relative;
    left: -6px;
  }
  .mp3-audio-content-btn{
    cursor: pointer;
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #eee;
    position: relative;
    border-radius: 50%;
    top: -70px;
    left: -1px;
  }
  i{
    font-size: 30px;
  }
}
.friend-list {
  max-height: 350px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  /* 定义滚动条轨道的颜色 */
  &::-webkit-scrollbar-track {
    background: #409eff;
  }

  /* 定义滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background: #409eff;
  }

  /* 鼠标悬停在滚动条上时滑块的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background: #409eff;
  }

  .item-friend {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    border-bottom: 1px solid #ddd;
    padding: 10px 0;

    .el-checkbox__label {
      display: none;
    }

    .right {
      margin-left: 12px;
      display: flex;
      align-items: center;
    }

    .nick {
      color: #000;
      font-weight: 600;
      font-size: 16px;
      margin-left: 12px;
    }
  }

  img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}
</style>
