<template>
  <!-- 阻止事件冒泡 -->
  <div
    id="mask"
    @click="$store.commit('book/showPBookDetail')"
    style="position: relative"
  >
    <div class="p_content" @click.stop="onClickPDFContext">
      <div>

        <pdf :src="src" :page="pdf.currentPage"></pdf>

        <!-- <img :src="item" alt="" v-for="(item,i) in pages" :key="i"> -->
      </div>
    </div>
  </div>
</template>

<script>
import pdf from 'vue-pdf'
export default {
  components: {
    pdf
  },
  props: {
    src: {
      type: String // 设置参数类型
    }
  },
  data () {
    return {
      pdf: {
        numPages: undefined, // 总页数
        src: undefined, // 文档对象
        currentPage: 1 // 当前页码
      }
    }
  },
  // 初始化pdf对象
  async mounted () {
    // this.loadPDF();
    this.pdf.src = await pdf.createLoadingTask(this.src)
    this.pdf.src.promise.then((pdf) => {
      // 获取pdf的总页码
      this.pdf.numPages = pdf.numPages
    })
  },
  //   键盘事件
  mounted () {
    var _this = this
    document.onkeyup = function (e) {
      if (e.code === 'ArrowLeft') {
        _this.prePage()
      } else if (e.code === 'ArrowRight') {
        _this.nextPage()
      }
    }
  },
  methods: {
    // 点击事件
    onClickPDFContext (event) {
      if (event.offsetX >= 250) {
        // 下一页
        this.nextPage()
      } else {
        // 上一页
        this.prePage()
      }
    },
    // 上一页
    prePage () {
      if (this.pdf.currentPage <= 1) {
        this.$message({
          message: '不能再上一页',
          type: 'warning'
        })
        return
      }
      this.pdf.currentPage--
    },
    // 下一页
    nextPage () {
      if (this.pdf.currentPage >= this.pdf.numPages) {
        this.$message({
          message: '不能再下一页',
          type: 'warning'
        })
        return
      }
      this.pdf.currentPage++
    }
  }
}
</script>

<style scope lang="scss">
#mask {
  width: 100%;
  height: 100vh;
  background-color: rgba($color: #000000, $alpha: 0.5);
}
.p_content {
  z-index: 999;
  width: 500px;
  height: 600px;
  // border: 1px solid red;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: 0 auto;
  background-color: #fff;
  .keyDown {
    width: 100%;
    // height:500px;
    // position:absolute;
  }
}
</style>
