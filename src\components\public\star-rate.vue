<template>
  <div>
    <div v-if="readonly" class="stars">
      <div
        v-for="(item, index) in stars"
        :key="index"
        :style="{
          width: size === 'small' ? '14px' : '',
          height: size === 'small' ? '14px' : '',
          marginRight: size === 'small' ? '3px' : '',
        }"
      >
        <img
          :src="item.flag1 === 1 ? item.bgImgL : item.bgfImgL"
          :data-index="index"
        />
        <img
          :src="item.flag2 === 1 ? item.bgImgR : item.bgfImgR"
          :data-index="index"
        />
      </div>
    </div>
    <div v-else class="stars">
      <div
        v-for="(item, index) in stars"
        :key="index"
        :style="{
          width: size === 'small' ? '14px' : '',
          height: size === 'small' ? '14px' : '',
          marginRight: size === 'small' ? '3px' : '',
        }"
      >
        <img
          :src="item.flag1 === 1 ? item.bgImgL : item.bgfImgL"
          :data-index="index"
          @click="scoreL(index)"
        />

        <img
          :src="item.flag2 === 1 ? item.bgImgR : item.bgfImgR"
          :data-index="index"
          @click="scoreR(index)"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    readonly: {
      default: false,
      type: Boolean
    },
    size: {
      default: 'small',
      type: String
    },
    scoreValue: {
      default: 0,
      type: Number
    },
    starSize: {
      default: 'small',
      type: String
    },
    bg: {
      default: 'grey',
      type: String
    }
  },
  data () {
    return {
      stars: [],
      star_num: 0,
      preUrl: 'https://oss.bookor.com.cn/static/img/mini_programs/images/'
    }
  },
  created () {
    this.initScore()
  },
  mounted () {
    // this.initScore();
  },
  watch: {
    scoreValue (value) {
      this.initScore()
    }
  },
  methods: {
    initScore () {
      const preUrl = this.preUrl
      const stars = []
      for (let i = 0; i < 5; i++) {
        stars.push({
          flag1: 1,
          flag2: 1,
          bgImgL: `${
            this.bg === 'grey'
              ? preUrl + 'bk-start-icon_star_1_grey.png'
              : preUrl + 'bk-start-icon_star_1.png'
          }`,
          bgfImgL: `${preUrl}bk-start-icon_star_active1.png`,
          bgImgR: `${
            this.bg === 'grey'
              ? preUrl + 'bk-start-icon_star_2_grey.png'
              : preUrl + 'bk-start-icon_star_2.png'
          }`,
          bgfImgR: `${preUrl}bk-start-icon_star_active2.png`
        })
      }
      const score = parseFloat(this.scoreValue)
      const starIndex = Math.round(score) / 2 - 1
      const starScore = Math.round(score) / 2
      if (score === 1) {
        const item1 = stars[0].flag1
        this.$set(stars[0], 'flag1', 2)
      } else {
        for (let i = 0; i <= starIndex; i++) {
          this.$set(stars[i], 'flag1', 2)
          this.$set(stars[i], 'flag2', 2)
        }
      }

      if (Math.floor(starScore) < Math.ceil(starScore)) {
        const maxScoreIndex = Math.ceil(starScore) - 1
        if (maxScoreIndex > 0) {
          this.$set(stars[maxScoreIndex], 'flag1', 2)
        }
      }

      this.stars = stars
      this.star_num = starScore
    },
    scoreL (index) {
      if (this.readonly) return
      const stars = JSON.parse(JSON.stringify(this.stars))
      for (let i = 0; i < stars.length; i++) {
        if (i < index) {
          this.$set(stars[i], 'flag1', 2)
          this.$set(stars[i], 'flag2', 2)
        } else if (i === index) {
          this.$set(stars[i], 'flag1', 2)
          this.$set(stars[i], 'flag2', 1)
        } else {
          this.$set(stars[i], 'flag1', 1)
          this.$set(stars[i], 'flag2', 1)
        }
      }
      const score = index * 2 + 1
      this.$emit('scoreChange', score / 2)
      this.$emit('input', score / 2)
      this.stars = stars
      this.star_num = score / 2
    },
    scoreR (index) {
      if (this.readonly) return
      const stars = JSON.parse(JSON.stringify(this.stars))
      for (let i = 0; i < stars.length; i++) {
        if (i <= index) {
          this.$set(stars[i], 'flag1', 2)
          this.$set(stars[i], 'flag2', 2)
        } else {
          this.$set(stars[i], 'flag1', 1)
          this.$set(stars[i], 'flag2', 1)
        }
      }

      const score = (index + 1) * 2
      this.$emit('scoreChange', score / 2)
      this.$emit('input', score / 2)
      this.stars = stars
      this.star_num = score / 2
    }
  }
}
</script>

<style scoped>
.stars {
  display: flex;
  height: 30px;
  display: flex;
  align-items: center;
}

.stars div {
  position: relative;
  width: 18px;
  height: 18px;
  margin-right: 5px;
}

.stars div img:nth-of-type(1) {
  width: 50%;
  height: 100%;
}
.stars div img:nth-of-type(2) {
  width: 50%;
  height: 100%;
}
</style>
