<template>
  <div class="flash_box">
    <!-- 历史记录弹窗 -->
    <HistoryFlash
      v-if="historyFlashBoo"
      :setHistoryFlashBoo="setHistoryFlashBoo"
      :item="item"
    />

    <!-- 中间弹窗 -->
    <div class="main">
      <div class="popover" v-if="isShow">
        <div class="video">
          <!-- <img :src="item.img" alt="" style="height: 100%; width: 100%;"> -->
          <el-image
            style="width: 100%; height: 100%"
            :src="item.img"
            :fit="'cover'"
          ></el-image>
          <audio
            :src="item.multi_media"
            controls
            v-show="item.multi_media"
            :data-id="`audio11`"
            @play="handlePlay(`audio11`)"
          ></audio>
          <!-- autoplay
            muted -->
          <!-- <audio controls v-show="item.multi_media>
                        <source :src="item.multi_media" type="audio/mpeg" />
                    </audio> -->
        </div>
        <div class="pTitle">
          <div class="text">
            {{ item.title }}
          </div>
          <div class="btn">
            <!-- 没有更多数据 -->
            <!-- <div class="noMore">没有更多数据</div> -->
            <el-button
              :disabled="currentIndex == 0 ? true : false"
              :type="currentIndex == 0 ? 'info' : 'primary'"
              plain
              size="mini"
              @click="previous"
              >上一篇</el-button
            >
            <el-button
              :disabled="
                currentIndex >= todayTrendsList.length - 1 ? true : false
              "
              :type="
                currentIndex >= todayTrendsList.length - 1 ? 'info' : 'primary'
              "
              plain
              size="mini"
              @click="next"
              >下一篇</el-button
            >
          </div>
        </div>
        <div class="content">
          <div class="summary">
            <!-- 内容提要： -->
          </div>
          <div v-html="item.detail" class="html_box"></div>
        </div>
      </div>
    </div>
    <!-- 轮播图 -->
    <div class="footer">
      <div class="swiper-container" @mouseenter="stopWiper">
        <div class="swiper-wrapper">
          <!-- @click="checkItem(item, index)" -->
          <div
            class="swiper-slide swiper-slide1"
            v-for="(item, index) in todayTrendsList"
            :key="index"
          >
            <!-- <el-image style="width: 100%; height: 100%" :src="item.img" :fit="'cover'"></el-image> -->
            <img style="width: 100%" :src="item.img" alt="" />
            <span class="typeText" :class="item.type">{{
              typeTextMap[item.type]
            }}</span>

            <audio
              controls
              v-if="item.multi_media"
              :data-id="`audio3${index}`"
              @click="handlePlay(`audio3${index}`)"
            >
              <source :src="item.multi_media" type="audio/mpeg" />
            </audio>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/http/request'
import HistoryFlash from './components/HistoryFlash.vue'
import Swiper from 'swiper'
import 'swiper/css/swiper.min.css'
let vm = null
export default {
  components: {
    HistoryFlash
  },

  data () {
    return {
      // 历史记录轮播图的显示隐藏
      historyFlashBoo: false,
      // 每日资讯的类别分类
      typeTextMap: {
        information: '文化快讯',
        book_comment: '书友圈',
        video: '图书视频',
        wonderful: '精选书评',
        aggregate_1: '书单',
        aggregate_2: '专题书单',
        foreign: '外站书评',
        topic: '话题'
      },
      todayTrendsList: [],
      // 显示与隐藏
      isShow: false,
      item: {},
      // 控制轮播图
      left: 0,
      timer: 0,
      currentIndex: 0,

      // modules: [Autoplay],
      swiper_: null, // swiper对象
      currentAudio: null, // 当前正在播放的audio
      videoElement: [] // 视频数组(音频数组)
    }
  },
  methods: {
    // 设置历史记录轮播图的显示与隐藏
    setHistoryFlashBoo (val) {
      this.historyFlashBoo = val
    },

    /** 点击切换详情 */
    checkItem (item, index) {
      // this.isShow = true
      this.item = item
      this.currentIndex = index
      // clearInterval(this.timer)

      this.setHistoryFlashBoo(true)
    },
    // 停止轮播
    Stop () {
      clearInterval(this.timer)
    },
    Start () {
      this.Carousel()
    },
    // 上一篇
    previous () {
      this.currentIndex--
      if (this.currentIndex <= 0) {
        this.currentIndex = 0
      }
      this.item = this.todayTrendsList[this.currentIndex]
    },
    // 下一篇
    next () {
      this.currentIndex++

      if (this.currentIndex >= this.todayTrendsList.length - 1) {
        this.currentIndex = this.todayTrendsList.length - 1
      }
      this.item = this.todayTrendsList[this.currentIndex]
    },
    // 轮播图
    Carousel () {
      this.timer = setInterval(() => {
        this.left += 10
        const _flash = this.$refs.flash
        _flash.style.left = '-' + this.left + 'px'

        const style = window.getComputedStyle(_flash)
        if (this.left >= parseFloat(style.width) / 2) {
          this.left = 0
        }
      }, 100)
    },

    // 同时播放只存在一个音视频
    handlePlay (id) {
      this.videoElement = document.getElementsByTagName('audio')
      Array.from(this.videoElement).forEach((item) => {
        if (item.dataset.id == id) {
          item.play()
        } else {
          item.pause()
        }
      })
    },

    // 鼠标移入停止swiper
    stopWiper () {
      this.swiper_?.autoplay?.stop()
    }
  },
  created () {
    vm = this
  },
  async mounted () {
    await request('api/books/getTodayTrends', { params: { search_words: '今日' } })
      .then((result) => {
        this.todayTrendsList = result
      })

    // 轮播图
    this.swiper_ = new Swiper('.swiper-container', {
      loop: true,
      slidesPerView: 5, // 展示几个子元素显示
      autoplay: {
        delay: 1300, // 切换时间间隔
        stopOnLastSlide: false, // 切换到最后一张停止
        disableOnInteraction: true, // 用户操作之后停止轮播
        pauseOnMouseEnter: true // 鼠标至于上方停止轮播
      },
      speed: 1000, // 每次切换的时间长短
      spaceBetween: 30, // 子元素间隔距离
      on: {
        click: function (event) {
          const src = event.target.src
          if (src) {
            vm.todayTrendsList.forEach((item, index) => {
              if (src.indexOf(item.img) != -1) {
                vm.checkItem(item, index) // 执行想要执行的方法
              }
            })
          }
        }
      }
    })
  },
  beforeDestroy () {
    this.Stop()
  }
}
</script>

<style scoped lang="scss">
.main {
  position: relative;
  z-index: 999;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;

  .popover {
    background-color: rgb(245, 245, 245);
    // width: 345px;
    // height: 410px;
    width: 30vw;
    height: 70vh;
    // border: 1px solid #00f;
    // 滚动条
    overflow-y: scroll;

    /* 隐藏标准的滚动条 */
    &::-webkit-scrollbar {
      width: 0;
    }

    &::-webkit-scrollbar {
      width: 0;
    }

    /* 隐藏 IE 和 Edge 浏览器的滚动条 */
    &::-ms-scrollbar {
      width: 0;
    }

    .video {
      width: 100%;
      height: 140px;
      background-color: #0f0f;
      position: relative;
      top: 0;
      display: flex;
      justify-content: center;

      .el-image {
        position: absolute;
      }

      audio {
        // display: block !important;
        width: 90%;
        height: 20px;
        position: absolute;
        bottom: 0;
        // left: 0;
      }
    }

    .pTitle {
      padding: 20px 15px;

      .text {
        font-size: 19px;
        font-weight: 600;
      }

      .btn {
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
        // .noMore{
        //     position: fixed;
        //     top: 0;
        //     bottom: 0;
        //     left: 0;
        //     right: 0;
        //     width: 200px;
        //     height: 200px;
        //     background-color: #000;

        // }
      }
    }

    .content {
      padding: 0 10px;

      > ul {
        margin-top: 15px;
        list-style: square;

        li {
          margin-left: 15px;
          letter-spacing: 1px;
          margin-bottom: 7px;
        }
      }
    }
  }
}

.footer {
  position: fixed;
  bottom: 15px;

  .flash {
    display: flex;
    position: relative;

    .item {
      width: 253px;
      height: 145px;
      // border: 1px solid #f00;
      margin-left: 13px;
      position: relative;
      display: flex;
      justify-content: center;

      img {
        position: absolute;
      }

      audio {
        // display: block !important;
        width: 90%;
        height: 20px;
        position: absolute;
        bottom: 0;
        // left: 0;
      }
    }
  }

  .swiper-container {
    width: 100vw;
    height: 150px;
    margin: 0;

    .swiper-wrapper {
      // -webkit-transition-timing-function: linear;
      // /*之前是ease-out*/
      // -moz-transition-timing-function: linear;
      // -ms-transition-timing-function: linear;
      // -o-transition-timing-function: linear;
      // transition-timing-function: linear;

      .swiper-slide {
        width: 253px;
        height: 145px;
        // background: red;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        img {
          position: absolute;
        }

        .typeText {
          position: absolute;
          top: 5px;
          right: 5px;
          z-index: 99;

          padding: 3px 7px;
          color: #fff;
          background-color: #0794b0;

          &.information {
            background-color: #5f9a88;
          }

          &.book_comment {
            background-color: #e28713;
          }

          &.video {
            background-color: #2c5e9c;
          }
        }

        audio {
          width: 90%;
          height: 20px;
          position: absolute;
          bottom: 3px;
          // left: 0;
        }
      }
    }
  }
}

.html_box {
  ul {
    padding: 0 0 0 1.2em !important;
    width: 100% !important;
    box-sizing: border-box;
  }

  img {
    width: 100% !important;
  }
}
</style>
