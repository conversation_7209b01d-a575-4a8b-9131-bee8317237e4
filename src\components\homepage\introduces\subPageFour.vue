<template>
  <div class="section">
  <div class="page-box">
      <div class="title-part1">
          拥有系统知识的4个标准
      </div>
      <div class="content-box-part1">
          <div class="content-inside">
              <div class="content-inside-item">
                <img src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/arror-3.png"/>
                掌握该专题的基本概念
              </div>
              <div class="content-inside-item">
                <img src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/arror-3.png"/>
                阅读过专题知识的必读文本
              </div>
              <div class="content-inside-item">
                <img src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/arror-3.png"/>
                理解关键概念之间的逻辑和推导关系
              </div>
              <div class="content-inside-item">
                <img src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/arror-3.png"/>
                能就核心概念提问并表达创新想法
              </div>
          </div>
          <div class="right-con">
              <video ref="video" class="video-js vjs-default-skin" controls autoplay>
                  <!-- <source src="./video/视频1.mp4" /> -->
              </video>
          </div>
      </div>
  </div>
</div>
</template>
<script>

</script>
<style lang="scss" scoped>

.title-part1 {
    color: white;
    font-size: 26px;
    font-weight: 600;
    text-align: center;
    margin-top: 5%;
}
.title-part1{
  text-align: center;
}

.content-box-part1{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 1000px;
  margin:  0 auto;
  margin-top: 6%;
}
.content-inside{
  width: 30%;
}
.content-inside-item{
  color: #ffc000;
  font-size: 22px;
  font-weight: 600;
  text-align: left;
  line-height: 30px;
  margin-bottom: 12px;
}
.content-inside-item img{
  width: 20px;
  display: inline;
}
.right-con{
  width: 70%;
  video{
    margin-left: 10px;
    width: 100%;
    height: 100%
  }
}
@media only screen and (max-width: 420px) {
  .content-box-part1{
    flex-direction: column;
  }
  .content-inside{
    width: calc(100vw - 20px);
    padding: 10px;
  }

  .right-con{
    margin-top: 40px;
    video{
      width: calc(100vw - 40px);
    }
  }
}
</style>
