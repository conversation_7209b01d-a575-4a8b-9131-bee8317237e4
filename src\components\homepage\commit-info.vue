<template>
  <div class="form">
    <!-- 导入 -->
    <div style="display: flex; margin-bottom: 5px">
      <div style="color: #ffc000">您将通过电子邮件收到确认。</div>
      <div
        style="
          width: 20px;
          height: 20px;
          background-color: white;
          border-radius: 50%;
          position: relative;
        "
      >
        <img
          style="width: 24px; position: absolute; top: -2px; left: -2px"
          src="@/assets/img/checked.png"
          alt=""
        />
      </div>
    </div>
    <!-- name -->
    <div class="input-group">
      <input type="text" v-model="name" placeholder="名字" maxlength="20" />
    </div>
    <!-- 专业领域 -->
    <div class="input-group">
      <el-select
        v-model="specialty"
        placeholder="专业领域"
        :popper-append-to-body="false"
      >
        <el-option
          v-for="option in optionsSpecialty"
          :key="option"
          :label="option"
          :value="option"
        ></el-option>
      </el-select>
    </div>
    <!-- 阅读习惯 -->
    <div class="input-group">
      <el-select
        v-model="readHabits"
        placeholder="阅读习惯"
        :popper-append-to-body="false"
      >
        <el-option
          v-for="option in optionsReadHabits"
          :key="option"
          :label="option"
          :value="option"
        ></el-option>
      </el-select>
    </div>

    <!--是否做阅读笔记  -->
    <div class="input-group">
      <el-select
        v-model="doReadNote"
        placeholder="是否做阅读笔记"
        :popper-append-to-body="false"
      >
        <el-option
          v-for="option in optionsDoReadNote"
          :key="option"
          :label="option"
          :value="option"
        ></el-option>
      </el-select>
    </div>

    <!--笔记工具  -->
    <div class="input-group" v-show="doReadNote != '从不做笔记'">
      <el-select
        v-model="readTools"
        placeholder="笔记工具"
        :popper-append-to-body="false"
      >
        <el-option
          v-for="option in optionsReadTools"
          :key="option"
          :label="option"
          :value="option"
        ></el-option>
      </el-select>
    </div>

    <!-- <div class="input-group">
      <input
        type="text"
        v-model="use_to"
        placeholder="您想用Bookor在哪提高效率?"
        maxlength="300"
      />
    </div> -->
    <button @click="sendInfo" class="send-btn">发送</button>
  </div>
</template>

<script>
// import Fetch from '@/http/fetch.js'
import request from '@/http/request.js'
export default {
  data () {
    return {
      optionsSpecialty: [
        '学生',
        '研究人员',
        '职场从业者',
        '自雇及知识博主',
        '企业主',
        '其他'
      ],
      optionsReadHabits: [
        '按顺序逐页阅读',
        '随机选读有兴趣的章节',
        '快读先了解文章结构',
        '只关注有用的知识点'
      ],
      optionsDoReadNote: [
        '每次阅读都做笔记',
        '重要的会做笔记',
        '偶尔随手记录',
        '从不做笔记'
      ],
      optionsReadTools: [
        '书页空白处手写记录',
        '纸质笔记本',
        '电脑笔记本工具',
        '手机记录工具'
      ],

      name: '',
      specialty: '',

      readHabits: '', // 阅读习惯
      doReadNote: '', // 是否做阅读笔记
      readTools: '', //	笔记工具
      // use_to: "",

      userinfo: JSON.parse(localStorage.getItem('userinfo'))
    }
  },
  props: {
    email: String
  },
  methods: {
    async sendInfo () {
      if (!this.userinfo?.userid) {
        this.$message({
          message: '请先登录',
          type: 'error',
          duration: 1000
        })
        this.$router.push('/login')
        return
      }
      const info = {
        name: this.name.trim(),
        specialty: this.specialty,
        postbox: this.email,
        reading_habit: this.readHabits,
        reading_note: this.doReadNote,
        note_tool: this.readTools
      }
      if (
        info.name === '' ||
        info.specialty === '' ||
        info.reading_habit === '' ||
        info.reading_note === ''
      ) {
        this.$message({
          message: '请填写完整',
          type: 'error',
          duration: 1000
        })
        return
      } else {
        if (info.reading_note === '从不做笔记') {
          info.note_tool = '' // 将笔记工具设置为空
        } else {
          if (info.note_tool === '') {
            this.$message({
              message: '请填写完整',
              type: 'error',
              duration: 1000
            })
            return
          }
        }
      }

      // 发送
      const url = '/mobile/Nokeyinterface/addApply'
      // const params = info;
      request(url, info).then((result) => {
        this.$message({
          message: '申请成功',
          type: 'success',
          duration: 1500
        })
        setTimeout(() => {
          // 回上一页
          this.$emit('refresh')
          this.name = ''
          this.specialty = ''
          this.reading_habit = ''
          this.reading_note = ''
          this.note_tool = ''
        }, 2000)
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1500
        })
        // location.reload();
        this.$router.push('/index')
      })
    }
    // },
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
.form {
  @include flex-ccc;

  height: 90vh;
  .input-group {
    input {
      @include radius-shadow;
      background-color: #d9d9d9;
      padding: 8px 15px;
      margin: 5px 10px;
      border: 0;
      width: 170px;
      height: 16px;
      &::placeholder {
        color: #313131; /* 在这里指定你想要的颜色 */
      }
    }
  }

  .input-group-box {
    display: flex;
    background-color: #d9d9d9;
    border-radius: 10px;
    padding: 6px 9px;
    margin-bottom: 35px;
    border: 0;
    .sent {
      font-size: 13px;
      color: #6d98cd;
    }

    .el-checkbox__inner {
      border-radius: 50%;
      border-color: rgb(8, 8, 8);
    }

    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate {
      background-color: rgb(8, 8, 8);
      border-color: rgb(8, 8, 8);
      border-radius: 50%;
      padding: 2px;
    }
    .el-checkbox__input.is-focus .el-checkbox__inner {
      border-color: rgb(8, 8, 8);
      border-radius: 50%;
      padding: 2px;
    }
  }

  .send-btn {
    @include radius-shadow;
    background-color: black;
    color: #d6d6d6;
    padding: 6px 14px;
    margin-top: 30px;
    border: 1px solid white;
    font-size: 12px;
    cursor: pointer;
  }
}

.input-group {
  .el-select {
    width: 200px;
    // height: 28px;
    // margin-left: 11px;

    ::v-deep .el-input--suffix {
      height: 100%;

      > input {
        border-radius: 10px;
        box-shadow: 1px 2px 1px rgba(0, 0, 0, 0.5);
        background-color: #d9d9d9;
        padding: 8px 15px;
        margin: 5px 0px;
        border: 0;
        width: 170px;
        height: 16px;
        color: #313131;
        box-sizing: content-box;
        &::placeholder {
          color: #313131; /* 在这里指定你想要的颜色 */
        }
      }

      .el-input__suffix {
        height: 40px;
        top: -2px;

        .el-icon-arrow-up:before {
          content: "";
          color: #2f3030;
        }
      }
    }
  }
  ::v-deep .el-popper {
    margin-top: 0;
  }
}
</style>
