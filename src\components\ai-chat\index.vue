<template>
  <div class="chat-con">
    <el-tabs v-model="activeName">
      <el-tab-pane label="ai问答" name="talk" v-if="!currentInfo.isMp3">
        <div>
          <div class="chat" v-show="isShowChat">
            <div class="messages" :class="{active: fileType}">
              <div class="ai ai_system">
                <img class="avatar avatar-server" src="./assets/ai-avatar-day.png" />
                <div class="answer-box">
                  Hi，我是您的知识顾问。
                </div>
              </div>
              <div v-for="(message, index) in msgList" :key="index" :class="message.role === 'system' ? 'ai' : 'sendMes'">
                <div v-if="message.role === 'system'" class="ai_system">
                  <img class="avatar avatar-server" src="./assets/ai-avatar-day.png" alt="" />
                  <div v-if="message.content.length > 0">
                    <div class="answer-box" >
                      {{ message.content }}
                    </div>
                    <el-button class="stop-btn" size="small" @click="stopTalk">停止输出</el-button>
                  </div>
                  <div v-else class="answer-box">
                    <span class="ai_loading"></span>
                  </div>
                </div>
                <div v-else>
                  <div class="send-box" v-show="message.content.length > 0">
                    {{ message.content }}
                  </div>
                </div>
              </div>
              <div class="preset-list" v-if="fileType">
                <div class="item-preset" v-for="(item, index) in presetList" :key="index" @click="handlePreset(item)">
                  {{ item }}
                </div>
              </div>
            </div>
            <div class="bottom-input">
              <el-tooltip class="item" effect="dark" content="历史记录" placement="top">
              <i class="el-icon-time" @click="showHistory"></i>
              </el-tooltip>
              <el-input v-model="textInput" placeholder="请输入你想要的所有问题"  @keyup.native="onKeyUp"></el-input>
              <el-button type="primary" size="small" @click="autoSendMessage">提问</el-button>
            </div>
          </div>

          <div v-show="isShowHistory" class="history">
            <div class="top">
              <i class="el-icon-arrow-left" @click="showChat"></i>
              <div>历史记录</div>
            </div>
            <div v-if="chatList" class="chatList-box" @scroll="handleScrollThrottle" ref="chatBox">
              <div v-for="(item, index) in chatList" :key="index" class="chatList-item">
                <div class="flex-sbc">
                  <div class="chatList-time">{{ item.chat_time }}</div>
                  <img class="chatList-del" src="./assets/icon-del.png" @click="delRecord(item.chat_id)" />
                </div>
                <el-dialog :visible.sync="deleteDialogVisible" title="提示" width="30%">
                  <span>是否删除该记录？</span>
                  <div slot="footer" class="dialog-footer">
                    <el-button @click="cancelDelete">取消</el-button>
                    <el-button type="primary" @click="confirmDelete">确定</el-button>
                  </div>
                </el-dialog>
                <div class="chatList-content">
                  <div class="chatList-content-left" v-if="item.chat_person === 1">
                    我:
                  </div>
                  <div class="chatList-content-left" v-else-if="item.chat_person === 2">
                    AI:
                  </div>
                  <div class="ellipsis" @click="showFullRecord(item.chat_content, item.chat_id)">
                    {{ item.chat_content }}
                  </div>
                </div>
                <!-- 显示完整记录 -->
                <div v-if="isShowFullRecord && item.chat_id == isShowId" class="mask-box" @click="hideModal">
                </div>
                <div v-if="isShowFullRecord && item.chat_id == isShowId" class="mask">
                  <div v-text="item.chat_content"></div>
                </div>
              </div>
            </div>
            <div v-else class="no-chatList">暂无历史记录</div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="论文讨论" name="discuss" v-if="isTalk">
        <talk
          v-if="activeName === 'discuss'"
          :currentInfo="currentInfo"
          :rightExpanded="rightExpanded"
          ></talk>
      </el-tab-pane>
  </el-tabs>
  </div>
</template>

<script>
import axios from '@/http'
import request from '@/http/request'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import getUrlExtension from '../../utils/getUrlExtension'
import talk from './talk.vue'

const ctrl = new AbortController()
export default {
  data () {
    return {
      msgList: [],
      textInput: '',
      isShowHistory: false,
      isShowChat: true,
      isLoadingAnswer: false, // 是否已经回答
      isChatIng: false, // 问答是否已经结束

      // 历史页
      chatList: [],
      isShowFullRecord: false,
      isShowId: '',
      deleteDialogVisible: false,
      item: {}, // 显示删除弹窗的聊天记录
      fileType: '', // 文件类型
      // 页数和limit
      currentPage: 1,
      limit: 15,
      total: 0, // 总数
      hasMore: true, // 有更多
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      timer: null,
      presetList: ['按照文档内的每章标题，总结每一章内容，罗列清单', '对本章的每一段落，分别总结，并罗列', '请摘录原文中的核心句子，罗列清单', '把原文内容，总结成词条形式的“知识单元”'], // 预设问题
      activeName: 'talk'
    }
  },
  components: {
    talk
  },
  props: ['currentInfo', 'defaultAi', 'rightExpanded'],
  watch: {
    msgList () {
      this.scrollToEnd()
    },
    defaultAi (val) {
      this.msgList = []
      this.textInput = `${val.text} 帮我分析一下这句话在内容中的含义和理解。`

      this.$nextTick(() => {
        this.newSendMessage('', this.defaultAi.content)
      })
    },
    currentInfo (item) {
      // 如果切换了文档，需要清空docId
      // 文章
      this.msgList = []
      if(item.isMp3) {
        this.activeName = 'discuss'
      }
      if (item.type === 'article') {
        this.textInput = '总结内容'
        this.fileType = ''
        return
      }
      const pType = getUrlExtension(this.currentInfo.url)
      if (['doc', 'pdf', 'docx'].includes(pType)) {
        this.textInput = '帮我整理核心内容'
        this.fileType = pType
      } else {
        // 文档
        if(this.currentInfo.type === 'note') {
          this.textInput = '帮我整理核心内容'
        } else {
          this.fileType = ''
          this.textInput = ''
        }
      }
    }
  },
  computed: {
    isTalk () {
      return this.currentInfo.isMp3 || this.$route.query?.url || this.currentInfo.url
    }
  },
  methods: {
    isDoc () {
      if (this.currentInfo?.url) {
        const pType = getUrlExtension(this.currentInfo.url)
        if (['doc', 'pdf', 'docx'].includes(pType)) {
          this.fileType = pType
          return true
        }
      }
      // 文章
      if (this.currentInfo?.type === 'article') {
        this.fileType = ''
        return true
      }
      this.fileType = ''
      return false
    },
    // 自动切换AI的类型
    autoSendMessage () {
      if (!this.textInput) {
        return this.$message.info('请输入问题进行提问')
      }
      if (this.isChatIng) {
        return this.$message.error({
          message: '你发送的太快了,请等等',
          duration: 600
        })
      }
      this.newSendMessage()
    },
    onKeyUp (event) {
      if (event.key === 'Enter' && this.textInput) {
        this.autoSendMessage()
      }
    },

    async handlePreset (val) {
      await this.newSendMessage(val)
    },

    // 将文档添加到AI上下文
    async addDocAi (fileContent) {
      const messages = [
        {
          role: 'system',
          content: '你是 Kimi，由 Moonshot AI 提供的人工智能助手，你更擅长中文和英文的对话。你会为用户提供安全，有帮助，准确的回答。同时，你会拒绝一切涉及恐怖主义，种族歧视，黄色暴力等问题的回答。Moonshot AI 为专有名词，不可翻译成其他语言。'
        },
        fileContent ? { role: 'system', content: fileContent } : null,
        this.defaultAi ? { role: 'system', content: '这篇文章的总结和回答，需要根据文章上下文来回答，不可以胡乱回答，且回答需要简短直接, 使用2句话句话进行表达' } : null,
        ...this.msgList
      ].filter(Boolean)
      fetchEventSource('/ai/stream-response', {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: messages
        }),
        signal: ctrl.signal,
        onmessage: (event) => {
          const message = JSON.parse(event.data)
          if (this.isLoadingAnswer) {
            this.msgList[this.msgList.length - 1].content = ''
          }
          this.isLoadingAnswer = false
          if (message.status === 2) {
            ctrl.abort()
            this.isChatIng = false
            const content = this.msgList[this.msgList.length - 1].content
            this.addChatHistory(content, 1) // 添加历史记录
            this.scrollToEnd() // 对话框滚到最后
          }
          this.msgList[this.msgList.length - 1].content += message.value
        },
        onclose: () => {
          ctrl.abort()
        },
        onerror: () => {
          console.error('EventSource failed:', event)
          ctrl.abort()
        }
      })
    },
    stopTalk () {
      ctrl.abort()
      this.isChatIng = false
      const content = this.msgList[this.msgList.length - 1].content
      this.addChatHistory(content, 1) // 添加历史记录
      this.scrollToEnd() // 对话框滚到最后
    },
    // 文档的AI
    async newSendMessage (msgContent, fileContent) {
      let text = ''
      if (msgContent) {
        text = msgContent
      } else {
        text = this.textInput.trim()
        this.textInput = '' // 清空用户输入的消息
      }

      if (!text) return
      this.addChatHistory(text, 1)
      this.msgList.push({
        role: 'user',
        content: text
      })

      this.msgList.push({
        role: 'system',
        content: ''
      })
      this.isLoadingAnswer = true
      this.isChatIng = true
      // 获取vuex中存储的在线pdf地址
      // 收藏的文章
      if (this.currentInfo.type === 'article') {
        const content = this.currentInfo.content
        await this.addDocAi(content)
      } else {
        if(this.currentInfo.type === 'note') {
          await this.addDocAi(this.currentInfo.url)
        } else {
          const dataUrl = this.currentInfo?.url || this.currentInfo?.pdfUrl
          console.log('msgList.length', this.msgList.length)
          if (dataUrl && this.msgList.length === 2) {
            axios.defaults.withCredentials = true
            const fileData = await axios.post('/ai/getFileId', {
              docId: this.currentInfo.id,
              url: dataUrl
            })
            if (fileData.code === 200) {
              const content = fileData.data
              await this.addDocAi(content)
            }
          } else {
            if (fileContent) {
              await this.addDocAi(fileContent)
            } else {
              await this.addDocAi()
            }
          }
        }
      }
    },

    scrollToEnd () {
      this.$nextTick(() => {
        const container = document.querySelector('.messages')
        if(container) {
          container.scrollTop = container?.scrollHeight
        }
      })
    },

    // 新增历史记录
    addChatHistory (content, person) {
      const params = {
        chat_content: content,
        chat_person: person // 对话者身份，用户问题对应1，ai回答对应2。1，用户；2，ai
      }
      request('/api/Gptchat/addChat', params)
    },

    // 历史
    showHistory () {
      this.isShowHistory = true
      this.isShowChat = false
      this.chatList = []
      this.apiGetHistory(1)
    },
    showChat () {
      this.isShowHistory = false
      this.isShowChat = true
    },

    // 获取历史记录
    async apiGetHistory (page) {
      const params = {
        page: page,
        limit: 10
      }
      const result = await request('/api/Gptchat/getChatList', params)
      const chatData = result.list
      const total = result.total
      chatData.map((item) => {
        item.chat_time = this.formatChatTime(item.chat_time * 1000) // 转换成需要的日期格式
      })
      this.chatList = this.chatList.concat(chatData)
      // 判断是否还有更多
      if (page * this.limit >= total) {
        this.hasMore = false
      }
      this.currentPage = page + 1
    },

    // 处理时间戳
    formatChatTime: function (timeStamp) {
      const date = new Date(timeStamp) // 将时间戳转换为 Date 对象
      const year = date.getFullYear()
      const month =
        date.getMonth() + 1 < 10
          ? '0' + (date.getMonth() + 1)
          : date.getMonth() + 1
      const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
      const hour =
        date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
      const minute =
        date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
      return `${day}/${month}/${year} ${hour}:${minute}`
    },

    // 触底
    handleScrollThrottle () {
      if (this.timer) return
      this.timer = setTimeout(() => {
        this.timer = null
        const chatBox = this.$refs.chatBox
        if (
          chatBox?.scrollHeight - chatBox?.scrollTop <=
          chatBox?.clientHeight + 20
        ) {
          if (!this.hasMore) {
            this.$message({
              message: '没有更多了',
              type: 'info',
              duration: 1000
            })
            return
          }
          this.apiGetHistory(this.currentPage)
        }
      }, 200)
    },

    // 删除
    delRecord (chatId) {
      // 此处是删除聊天记录的方法
      this.item = chatId
      this.showDeleteDialog()
    },
    showDeleteDialog () {
      this.deleteDialogVisible = true
    },
    cancelDelete () {
      this.deleteDialogVisible = false
    },
    confirmDelete () {
      // 在此处调用删除聊天记录的后端API
      this.apiDeleteRecord(this.item)
      this.deleteDialogVisible = false
    },
    apiDeleteRecord (id) {
      const params = {
        chat_id: id
      }
      request('/api/Gptchat/deleteChat', params).then((res) => {
        this.chatList = this.chatList.filter((item) => item.chat_id !== id)
        this.item = ''
      })
    },

    // 显示完整
    showFullRecord (content, id) {
      // 如果字数在2行以内，就不用弹窗
      if (content.length < 50) {
        return
      }
      this.isShowFullRecord = true
      this.isShowId = id
    },
    hideModal () {
      this.isShowFullRecord = false
    }
  },
  mounted () {
    if (this.defaultAi) {
      this.textInput = `${this.defaultAi.text} 帮我分析一下这句话在内容中的含义和理解。`

      this.$nextTick(() => {
        this.newSendMessage('', this.defaultAi.content)
      })
      return
    }
    if(this.currentInfo.isMp3) {
      this.activeName = 'discuss'
    }
    if (this.isDoc()) {
      this.textInput = '帮我整理核心内容'
    } else {
      this.textInput = '帮我分析文档的内容'
    }
    this.$nextTick(() => {
      this.scrollToEnd()
    })
  }
}
</script>

<style scoped lang="scss">
:deep(.el-drawer__header){
  margin-bottom: 0;
}
.chat-con{
  padding: 0 16px;
}
.chat {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 85vh;

  .ai_system{
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .AI-chat-history {
    cursor: pointer;
    margin-right: 5px;
  }

  .AI-chat-history img {
    width: 30px;
    height: 30px;
  }

  .preset-list{
    position: absolute;
    bottom: 60px;
    left: 34px;
    .item-preset{
      background-color: #fff;
      border: 1px solid #ccc;
      height: 30px;
      line-height: 30px;
      margin-top: 5px;
      border-radius: 4px;
      padding: 0px 6px;
      cursor: pointer;
    }
  }

  .messages {
    overflow-y: auto;
    padding: 18px;
    height: calc(100vh - 170px);
    &.active{
      padding-bottom: 160px;
      height: calc(100vh - 320px);
    }
    .ai {
      display: flex;
      align-items: center;
      text-align: left;
      color: #999;
      margin-bottom: 5px;
    }

    .sendMes {
      display: flex;
      justify-content: flex-end;
      padding: 1vw 0;
    }

    &.user {
      text-align: right;
      color: #333;
      margin-bottom: 5px;
    }

    .avatar {
      height: 40px;
      width: 40px;
      margin-right: 5px;
    }

    .avatar-server {
      height: 40px;
      width: 40px;
      background-color: #fff2cc;
      border-radius: 50%;
      box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
    }

    .dot {
      position: absolute;
      visibility: hidden;
      width: 35px;
      height: 30px;
    }

    .dot-ai {
      left: 60rpx;
      top: -20rpx;
      visibility: visible;
    }

    .answer-box {
      font-size: 16px;
      color: #444;

      padding: 1vw;
      background-color: #fff2cc;
      border-radius: 5px;
      // z-index: 10;
      box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
      max-width: 45vw;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-line;
      box-sizing: border-box;
      @keyframes blink {
        0%,to {
            background-color: currentColor
        }

        50% {
            background-color: transparent
        }
      }
      .ai_loading{
        width: 4px;
        height: 20px;
        display: block;
        animation: blink 1.2s infinite steps(1, start);
        color: #333;
      }
    }

    .stop-btn{
      margin-top: 8px;
    }

    .send-box {
      font-size: 16px;
      color: #444;
      padding: 1vw 2vw;
      background-color: #bdd7ee;
      border-radius: 5px;
      box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
      max-width: 45vw;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-line;
    }
  }

  .AI-set-box {
    display: flex;
    position: fixed;
    bottom: 70px;
    right: 10px;
    z-index: 10;

    .AI-set-item img {
      width: 35px;
      height: 35px;
      margin: 0 5px 0 0;
    }

    .AI-set-title {
      width: 30px;
      font-size: 15px;
      position: absolute;
      bottom: 50px;
      color: #3b72ad;
      text-decoration: underline #6fa2da;
    }

    .AI-set-title-expert {
      left: 15px;
    }

    .AI-set-title-teacher {
      right: 10px;
    }

    .AI-set-round {
      border-radius: 50%;
      width: 35px !important;
      height: 35px;
      border: 1px dashed #2da5c0;
      margin: 0 5px;
    }

    .AI-set-round img {
      width: 30px;
      height: 30px;
      margin: 2px;
    }
  }

  .bottom-input {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 4px;
    gap: 8px;
    .el-icon-time{
      font-size: 22px;
    }
    :deep(.el-input__inner) {
      height: 32px;
      line-height: 32px;
    }
  }

  /* 隐藏垂直滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}

.history {
  .top{
    display: flex;
    position: relative;
    justify-content: center;
    padding: 8px;
    text-align: center;
    font-size: 14px;
  }
  .el-icon-arrow-left{
    position: absolute;
    left: 12px;
    top: 12px;
  }
  .flex-sbc {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chatList-box {
    margin: 0 7px;
    height: 91vh;
    overflow-y: scroll;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  .chatList-item {
    margin-bottom: 7px;
  }

  .chatList-time {
    font-size: 14px;
    color: grey;
  }

  .chatList-del {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }

  .chatList-content {
    margin: 3px 0;
    font-size: 14px;
    display: flex;
  }

  .chatList-content-left {
    color: rgb(192, 4, 4);
    margin-right: 2px;
  }

  /* 记录溢出隐藏和显示 */
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    cursor: pointer;
  }

  .mask-box {
    background-color: rgba(0, 0, 0, 0.2);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .mask {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 15px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
    border-radius: 15px;

    max-height: 50vh;
    width: 60vw;
    font-size: 16px;

    overflow-y: scroll;
  }

  .bottom-tips {
    text-align: center;
    font-size: 13px;
    color: #999;
  }

  .pop-del {
    width: 30px;
    height: 30px;
    position: absolute;
    right: 0;
    top: 0;
  }

  .no-chatList {
    font-size: 15px;
    margin-top: 120px;
    color: grey;
    text-align: center;
    font-weight: bold;
  }
}
</style>
