<template>
  <div style="position: relative">
    <canvas id="canvas"></canvas>

    <!-- 移动到单个 -->
    <div v-show="isShowLabel" :style="labelStyle">
      {{ labelContent }}
    </div>
    <div
      style="width: 100vw; overflow-x: auto; position: absolute; bottom: 20px"
      v-show="isShowInfoArray"
      class="info-box-array-outter"
      v-slide-scroll
    >
      <div style="display: inline-flex">
        <div
          v-for="(item, index) in starListArray"
          :key="index"
          class="info-box-array"
        >
          <div class="info-title-box">
            <div class="info-title">
              {{ item.keywords }}
            </div>
            <div class="info-addLink" @click="handleEntryAddClassify(item)">
              +
            </div>
          </div>
          <div
            v-if="
              item.result.book_keywords_description.length > 0 ||
              item.result.member_keywords_description
            "
            style="border: 1px dotted white; margin: 0 0 3px 0"
          ></div>
          <div class="info-box-array-content">
            <div v-if="item.result.book_keywords_description.length > 0">
              <div
                style="
                  margin-bottom: 8px;
                  padding: 5px 0;
                  display: inline-block;
                "
                v-for="(book, index) in item.result.book_keywords_description"
                :key="index"
              >
                <img
                  :src="`${book.book_image}`"
                  alt=""
                  class="info-box-img"
                  @click="
                    showBookDetail(
                      item.result.book_keywords_description[0].book_id
                    )
                  "
                />
                <div>
                  <div style="font-size: 14px">{{ book.description }}</div>
                </div>
              </div>
            </div>
            <div
              v-if="item.result.member_keywords_description"
              class="info-box-array-html"
            >
              <div
                v-html="item.result.member_keywords_description.description"
              ></div>
            </div>
          </div>
          <div
            v-if="
              isShowEntryAddClassify &&
              ((item.book_keywords_id &&
                item.book_keywords_id == entryids.book_keywords_id) ||
                (item.member_keywords_id &&
                  item.member_keywords_id == entryids.member_keywords_id))
            "
            style="position: absolute; top: 39px; right: 5px"
          >
            <EntryAddClassify :entryids="entryids" />
          </div>
        </div>
      </div>
    </div>
    <!-- 锁屏时 -->
    <div
      style="width: 100vw; overflow-x: auto; position: absolute; bottom: 20px"
      v-if="isShowInfoArrayLocked"
      class="info-box-array-outter"
      v-slide-scroll
    >
      <div style="display: inline-flex">
        <div
          v-for="(item, index) in starLockedArray"
          :key="index"
          class="info-box-array"
        >
          <div class="info-title-box">
            <div class="info-title">
              {{ item.keywords }}
            </div>
            <div class="info-addLink" @click="handleEntryAddClassify(item)">
              +
            </div>
          </div>
          <div
            v-if="
              item.result.book_keywords_description.length > 0 ||
              item.result.member_keywords_description
            "
            style="border: 1px dotted white; margin: 0 0 3px 0"
          ></div>
          <div class="info-box-array-content">
            <div v-if="item.result.book_keywords_description.length > 0">
              <div
                style="
                  margin-bottom: 8px;
                  padding: 5px 0;
                  display: inline-block;
                "
                v-for="(book, index) in item.result.book_keywords_description"
                :key="index"
              >
                <img
                  :src="`${book.book_image}`"
                  alt=""
                  class="info-box-img"
                  @click="
                    showBookDetail(
                      item.result.book_keywords_description[0].book_id
                    )
                  "
                />
                <div>
                  <div style="font-size: 14px">{{ book.description }}</div>
                </div>
              </div>
            </div>
            <div
              v-if="item.result.member_keywords_description"
              class="info-box-array-html"
            >
              <div
                v-html="item.result.member_keywords_description.description"
              ></div>
            </div>
          </div>
          <div
            v-if="
              isShowEntryAddClassify &&
              ((item.book_keywords_id &&
                item.book_keywords_id == entryids.book_keywords_id) ||
                (item.member_keywords_id &&
                  item.member_keywords_id == entryids.member_keywords_id))
            "
            style="position: absolute; top: 39px; right: 3px"
          >
            <EntryAddClassify :entryids="entryids" />
          </div>
        </div>
      </div>
    </div>

    <!-- 展示书详情 -->
    <div>
      <BookDetail :book_id="choosedBookId" :key="choosedBookId" />
    </div>

    <!-- 书详情的弹窗 -->
    <div>
      <BookDetailsPopup />
    </div>
    <div>
      <MindMap />
    </div>
  </div>
</template>
<script>
// import { onMounted, ref, defineComponent, onBeforeUnmount } from "vue";
import * as THREE from 'three'
import { mapState, mapMutations } from 'vuex'

// Data and visualization
import { CompositionShader } from './shaders/CompositionShader.js'
import {
  BASE_LAYER,
  BLOOM_LAYER,
  BLOOM_PARAMS,
  OVERLAY_LAYER
} from './config/renderConfig.js'

// Rendering
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js'
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js'
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js'

import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js'
import { Galaxy } from './objects/galaxy.js'

// import axios from '@/http'
import request from '@/http/request.js'

import BookDetail from '@/components/nebula-sub/book-details.vue'
import BookDetailsPopup from '@/components/nebula-sub/book-details-popup.vue'
import EntryAddClassify from '@/components/nebula-sub/entry-add-classify.vue'

import MindMap from '@/components/mind-map/index.vue'
export default {
  data () {
    return {
      state: {
        canvas: null,
        renderer: null,
        camera: null,
        scene: null,
        orbit: null,
        baseComposer: null,
        bloomComposer: null,
        overlayComposer: null,
        startX: 0, // 鼠标按下时的位置
        startScrollLeft: 0, // 鼠标按下时容器的位置（scrollLeft）
        curScrollLeft: 0, // 当前容器的位置（scrollLeft）
        scrollTimer: null // 定时器
      },

      galaxy: null,
      isLocked: false,
      isShowInfo: false,
      isShowInfoArray: false,
      isShowInfoArrayLocked: false,

      clickedObjectData: null,
      selectedObject: { userData: {} },
      labelStyle: '',
      // 鼠标点击事件
      moveCameraInterval: null,
      starListArray: [],
      arrowMeshes: [],

      isShowLabel: false,
      labelContent: '',
      labelTop: 0,
      labelLeft: 0,

      description: [],

      isLightMode: localStorage.getItem('isLightMode') === 'true',

      // 搜索词条
      keyword: '',
      searchResult: [],

      // 词条分类
      isShowEntryAddClassify: false,
      entryids: {
        book_keywords_id: '',
        member_keywords_id: ''
      },

      // 锁屏时的数组
      starLockedArray: [],

      // 是否停止初始化动画
      stopFlag: false
    }
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', ['choosedBookId', 'isShowBookDetail']),
    ...mapState('indexsearch', {
      choosedSearchItem: (state) => state.chooseSearchItem
    })
  },
  watch: {
    choosedBookId (newVal) {
      // 在这里处理 choosedBookId 的变化
      this.setShowDetails()
    },
    isShowBookDetail (newVal) {
      if (newVal == 'false') {
        // 解除锁定
        this.isLocked = false
      }
    },
    choosedSearchItem (value) {
      this.isLocked = false
      this.chooseSearchItem(value)
    }
  },
  components: {
    BookDetail,
    BookDetailsPopup,
    MindMap,
    EntryAddClassify
  },

  methods: {
    ...mapMutations({
      setChosenBookId: 'book/SET_CHOSED_BOOK_ID',
      setShowDetails: 'book/showDetails'
    }),
    initThree () {
      // grab canvas
      this.state.canvas = document.querySelector('#canvas')

      // scene
      this.state.scene = new THREE.Scene()
      this.$store.commit(
        'indexsearch/setSceneChildren',
        this.state.scene.children
      )
      this.state.scene.fog = new THREE.FogExp2(0xebe2db, 0.00003)

      // camera
      this.state.camera = new THREE.PerspectiveCamera(
        60,
        window.innerWidth / window.innerHeight,
        0.1,
        5000000
      )
      this.state.camera.position.set(0, 100, 100)
      this.state.camera.up.set(0, 0, 1)
      this.state.camera.lookAt(0, 0, 0)
      // map orbit
      this.state.orbit = new OrbitControls(this.state.camera, this.state.canvas)
      this.state.orbit.enableDamping = true // an animation loop is required when either damping or auto-rotation are enabled
      this.state.orbit.dampingFactor = 0.05
      this.state.orbit.screenSpacePanning = false
      this.state.orbit.minDistance = 80 // 设置最小距离
      this.state.orbit.maxDistance = 600 // 设置最大距离
      this.state.orbit.maxPolarAngle = Math.PI / 2 - Math.PI / 360
      this.state.orbit.addEventListener('change', () => {
        if (this.state.orbit.getDistance() <= this.state.orbit.minDistance) {
          this.logVisibleObjects()
          this.state.orbit.enablePan = false
          this.state.orbit.enableRotate = false
          this.state.orbit.enableDamping = false
          this.isLocked = true
        } else {
          this.clearObjectsLabels()
          this.state.orbit.enablePan = true
          this.state.orbit.enableRotate = true
          this.state.orbit.enableDamping = true
          this.isLocked = false
          this.isShowInfoArrayLocked = false
        }
      })
      this.initRenderPipeline()
    },

    initRenderPipeline () {
      // Assign Renderer
      this.state.renderer = new THREE.WebGLRenderer({
        antialias: true,
        canvas: this.state.canvas,
        logarithmicDepthBuffer: true,
        alpha: true
      })
      this.state.renderer.setPixelRatio(window.devicePixelRatio)
      this.state.renderer.setSize(window.innerWidth, window.innerHeight)
      this.state.renderer.outputEncoding = THREE.SRGBColorSpace
      this.state.renderer.toneMapping = THREE.ACESFilmicToneMapping
      this.state.renderer.toneMappingExposure = 0.5
      if (this.isLightMode) {
        this.state.renderer.setClearColor(0xa6a6a6, 0.4)
      }
      // (state.scene);
      // General-use rendering pass for chaining
      const renderScene = new RenderPass(this.state.scene, this.state.camera)
      // Rendering pass for bloom
      const bloomPass = new UnrealBloomPass(
        new THREE.Vector2(window.innerWidth, window.innerHeight),
        1.5,
        0.4,
        0.85
      )
      bloomPass.threshold = BLOOM_PARAMS.bloomThreshold
      bloomPass.strength = BLOOM_PARAMS.bloomStrength
      bloomPass.radius = BLOOM_PARAMS.bloomRadius

      // bloom composer
      this.state.bloomComposer = new EffectComposer(this.state.renderer)
      this.state.bloomComposer.renderToScreen = false
      this.state.bloomComposer.addPass(renderScene)
      this.state.bloomComposer.addPass(bloomPass)

      // overlay composer
      this.state.overlayComposer = new EffectComposer(this.state.renderer)
      this.state.overlayComposer.renderToScreen = false
      this.state.overlayComposer.addPass(renderScene)

      // Shader pass to combine base layer, bloom, and overlay layers
      const finalPass = new ShaderPass(
        new THREE.ShaderMaterial({
          uniforms: {
            baseTexture: { value: null },
            bloomTexture: {
              value: this.state.bloomComposer.renderTarget2.texture
            },
            overlayTexture: {
              value: this.state.overlayComposer.renderTarget2.texture
            }
          },
          vertexShader: CompositionShader.vertex,
          fragmentShader: CompositionShader.fragment,
          defines: {}
        }),
        'baseTexture'
      )
      finalPass.needsSwap = true

      // base layer composer
      this.state.baseComposer = new EffectComposer(this.state.renderer)
      this.state.baseComposer.addPass(renderScene)
      this.state.baseComposer.addPass(finalPass)
    },

    resizeRendererToDisplaySize (renderer) {
      const canvas = renderer.domElement
      const width = canvas.clientWidth
      const height = canvas.clientHeight
      const needResize = canvas.width !== width || canvas.height !== height
      if (needResize) {
        renderer.setSize(width, height, false)
      }
      return needResize
    },

    renderPipeline () {
      this.state.camera.layers.set(BLOOM_LAYER)
      this.state.bloomComposer.render()
      this.state.camera.layers.set(OVERLAY_LAYER)
      this.state.overlayComposer.render()
      this.state.camera.layers.set(BASE_LAYER)
      this.state.baseComposer.render()
    },

    async render () {
      this.state.orbit.update()

      // fix buffer size
      if (this.resizeRendererToDisplaySize(this.state.renderer)) {
        const canvas = this.state.renderer.domElement
        this.state.camera.aspect = canvas.clientWidth / canvas.clientHeight
        this.state.camera.updateProjectionMatrix()
      }

      // fix aspect ratio
      const canvas = this.state.renderer.domElement
      this.state.camera.aspect = canvas.clientWidth / canvas.clientHeight
      this.state.camera.updateProjectionMatrix()

      this.galaxy.updateScale(this.state.camera)
      // Run each pass of the render pipeline
      this.renderPipeline()

      requestAnimationFrame(this.render)
    },

    // 点击事件处理函数
    onClick (event) {
      const previousLabel = document.getElementById('object-label')
      if (previousLabel) {
        previousLabel.remove()
      }
      // this.isShowInfo = false;
      this.keyword = ''
      clearInterval(this.moveCameraInterval)
      // this.isShowBookDetail = false;

      if (this.arrowMeshes.length > 0) {
        this.arrowMeshes.forEach((arrow) => this.state.scene.remove(arrow))
        this.arrowMeshes = []
      }
      // starListArray = [];
      const raycaster = new THREE.Raycaster()
      raycaster.near = 0
      raycaster.far = 1000
      const mouse = new THREE.Vector2()
      mouse.x = (event.clientX / window.innerWidth) * 2 - 1
      mouse.y = -(event.clientY / window.innerHeight) * 2 + 1
      raycaster.setFromCamera(mouse, this.state.camera)
      const intersects = raycaster.intersectObjects(this.state.scene.children)
      if (intersects.length > 0) {
        const clickedObject = intersects[0].object
        // 点击的是星链，增加箭头和线条
        if (clickedObject.userData.group) {
          const groupValue = clickedObject.userData.group
          const objectsArray = []
          this.state.scene.children.forEach((child) => {
            if (child.userData && child.userData.group === groupValue) {
              objectsArray.push(child)
            }
          })

          const centerPos = new THREE.Vector3(0, 0, 0)
          for (let i = 0; i < objectsArray.length; i++) {
            centerPos.add(objectsArray[i].position)
          }
          centerPos.divideScalar(objectsArray.length)

          // const material = new THREE.LineBasicMaterial({ color: 0xffffff });
          const material = new THREE.LineDashedMaterial({
            color: 0x8faadc, // 虚线的颜色
            dashSize: 5, // 虚线段的长度
            gapSize: 5 // 虚线间隔的长度
          })
          const geometry = new THREE.BufferGeometry()
          const points = []

          objectsArray.forEach((star) => points.concat(star.position))

          points[0] = centerPos

          geometry.setFromPoints(points)
          const line = new THREE.Line(geometry, material)
          // line.userData.group = `starList-${i}`; // 添加组别信息
          this.state.scene.add(line)
          // 为线条添加箭头
          for (let i = 1; i < objectsArray.length; i++) {
            const prevStarPos = objectsArray[i - 1].position.clone()
            const currStarPos = objectsArray[i].position.clone()

            const direction = currStarPos.clone().sub(prevStarPos).normalize()
            const length = prevStarPos.distanceTo(currStarPos)
            const color = 0x8faadc
            const headLength = length * 0.05
            const headWidth = headLength * 0.2

            const arrow = new THREE.ArrowHelper(
              direction,
              prevStarPos,
              length,
              color,
              headLength,
              headWidth
            )
            // arrow.userData.group = `starList-${i}`; // 添加组别信息
            this.arrowMeshes.push(arrow)
            this.state.scene.add(arrow)
          }

          // 遍历数组中的每个对象
          Promise.all(
            objectsArray.map(async (obj) => {
              const bookKeywordsId = obj.userData.book_keywords_id
              const params = {
                book_keywords_id: bookKeywordsId
              }
              const result = await request('/api/keywords/getDescriptionByKeyWordsId', params)

              // 将当前的结果与对应的子物体的 userData 进行组合
              return {
                keywords: obj.userData.keywords, // 假设 userData 中存在 keywords 属性
                result: result,
                book_keywords_id: obj.userData.book_keywords_id,
                member_keywords_id: obj.userData.member_keywords_id
              }
            })
          ).then((results) => {
            this.isShowInfo = false
            this.isShowInfoArrayLocked = false
            this.isShowInfoArray = true
            this.starListArray = results
          })
        }
        // 移除之前存在的标签
        const previousLabel = document.getElementById('object-label')
        if (previousLabel) {
          previousLabel.remove()
        }

        // 创建DOM元素作为标签
        const label = document.createElement('div')
        label.id = 'object-label'
        label.innerHTML = intersects[0].object.userData.keywords || ''
        label.style.position = 'absolute'
        label.style.color = '#eab204'
        label.style.backgroundColor = 'black'
        label.style.fontSize = '16px'
        label.style.border = '1px solid #9ab6c8'
        label.style.padding = '2px 4px'
        document.body.appendChild(label)

        if (
          clickedObject.userData &&
          clickedObject !== this.selectedObject &&
          clickedObject.userData.keywords !==
            this.selectedObject.userData.keywords
        ) {
          // 移除之前选中物体的边框
          if (this.selectedObject.userData.borderMesh) {
            this.state.scene.remove(this.selectedObject.userData.borderMesh)
            this.selectedObject.userData.borderMesh = null

            // 隐藏标签
            label.style.display = 'none'
            this.clearShow()
          }

          // 设置当前选中物体的边框
          // this.clickedObjectData = clickedObject.userData.keywords;
          this.clickedObjectData = clickedObject.userData
          this.getKeywordsInfo(clickedObject.userData)
          const borderGeometry = new THREE.SphereGeometry(0.95)
          const borderMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff
          })
          const borderMesh = new THREE.Mesh(borderGeometry, borderMaterial)
          borderMesh.position.copy(clickedObject.position)
          borderMesh.scale.copy(clickedObject.scale).multiplyScalar(1.1)
          clickedObject.userData.borderMesh = borderMesh
          this.state.scene.add(borderMesh)

          this.selectedObject = clickedObject

          // 设置标签的位置和显示状态
          const position = this.getObjectScreenPosition(
            clickedObject,
            this.state.camera
          )
          if (!this.isLocked) {
            label.style.display = 'block'
            label.style.top = `${position.y - 32}px`
            label.style.left = `${position.x - 10}px`
          } else {
            label.style.display = 'none'
          }

          this.isShowInfo = true
          this.isShowInfoArray = false
          this.isShowInfoArrayLocked = false
          this.keyword = ''
        } else {
          // 移除当前选中物体的边框
          this.state.scene.remove(this.selectedObject.userData.borderMesh)
          this.selectedObject.userData.borderMesh = null
          this.selectedObject = { userData: {} } // 清空选中物体的数据

          // 隐藏标签
          label.style.display = 'none'
          this.clearShow()
        }
      } else {
        // 移除当前选中物体的边框（如果有）
        if (this.selectedObject.userData.borderMesh) {
          this.state.scene.remove(this.selectedObject.userData.borderMesh)
          this.selectedObject.userData.borderMesh = null

          // 隐藏标签
          const previousLabel = document.getElementById('object-label')
          if (previousLabel) {
            previousLabel.remove()
          }

          this.clearShow()
          // this.isShowInfo = false;
          // this.isShowInfoArray = false;
          // this.isShowInfoArrayLocked = false;
        }
      }
    },

    // 获取物体在屏幕上的位置
    getObjectScreenPosition (object, camera) {
      const vector = new THREE.Vector3()
      vector.setFromMatrixPosition(object.matrixWorld)
      vector.project(camera)

      const width = window.innerWidth
      const height = window.innerHeight

      return {
        x: ((vector.x + 1) / 2) * width,
        y: (-(vector.y - 1) / 2) * height
      }
    },

    getKeywordsInfo (data) {
      const params = {
        book_keywords_id: data.book_keywords_id
      }
      request('/api/keywords/getDescriptionByKeyWordsId', params).then((result) => {
        this.description = []
        const bookKeywordsDescription = result.book_keywords_description
          ? result.book_keywords_description
          : []
        const memberKeywordsDescription =
              result.member_keywords_description &&
              result.member_keywords_description.description
                ? result.member_keywords_description.description
                : null
        if (memberKeywordsDescription) {
          this.description = this.description.concat(
            bookKeywordsDescription
          )
          this.description.push(memberKeywordsDescription)
        } else {
          this.description = this.description.concat(
            bookKeywordsDescription
          )
        }
      })
    },

    showBookDetail (id) {
      if (id) {
        this.setChosenBookId(id)
        const previousLabel = document.getElementById('object-label')
        if (previousLabel) {
          previousLabel.remove()
        }
        this.keyword = ''
        this.clearObjectsLabels()
        this.clearShow()
      } else {
        this.$message({
          message: '暂无对应图书',
          type: 'error',
          duration: 1000
        })
      }
    },

    // 从 Localstorage 中获取 isLightMode 值

    // 定义 handleSwichItem 函数用于切换模式并更新 Localstorage
    handleSwichItem () {
      this.isLightMode = !this.isLightMode
      localStorage.setItem('this.isLightMode', this.isLightMode)

      if (this.isLightMode) {
        this.state.renderer.setClearColor(0xa6a6a6, 0.4)
      } else {
        this.state.renderer.setClearColor(0x000000)
      }

      this.galaxy.updateScale(this.state.camera)
      this.renderPipeline()
    },

    // 选中某个搜索词条
    chooseSearchItem (item) {
      this.clearShow()
      const previousLabel = document.getElementById('object-label')
      if (previousLabel) {
        previousLabel.remove()
      }
      const clickedObject = item
      if (clickedObject.userData) {
        // Set border for newly selected object
        // this.clickedObjectData = clickedObject.userData.keywords;
        this.stopFlag = true
        // Move camera to show the selected object in the center of the screen
        const box = new THREE.Box3().setFromObject(clickedObject)
        const center = new THREE.Vector3()
        box.getCenter(center)
        const distance = box.getSize(new THREE.Vector3()).length() / 2 // 计算物体半径
        const direction = this.state.camera.position
          .clone()
          .sub(center)
          .normalize() // 计算相机指向物体的方向
        const newPos = center
          .clone()
          .add(direction.multiplyScalar(distance * 2)) // 计算相机位置
        this.state.orbit.target.copy(center) // 设置相机视点为物体中心

        this.moveObjectToCenter(clickedObject, newPos)
        // state.camera.position.copy(newPos); // 设置相机位置使物体居中
        // state.orbit.update(); // 更新相机控制器
        this.clickedObjectData = clickedObject.userData
        this.getKeywordsInfo(clickedObject.userData)

        this.isShowInfo = true
        this.searchResult = []
        this.keyword = clickedObject.userData.keywords
      } else {
        this.state.scene.remove(this.selectedObject.userData.borderMesh)
        // this.isShowInfo = false;
        // this.isShowInfoArray = false;
        // this.isShowInfoArrayLocked = false;
        this.clearShow()
      }
    },
    // 词条到屏幕中间
    moveObjectToCenter (clickedObject, pos) {
      // 获取相机当前的位置
      const cameraPosition = this.state.camera.position.clone()
      // 获取需要移动物体的位置
      const objectPosition = pos

      // 设置起始时间
      let startTime = null
      // 定义一个更新函数，用于更新相机和物体的位置
      const update = (time) => {
        if (!startTime) startTime = time
        const deltaTime = time - startTime

        // 将相机从当前位置移动到物体位置，并且使其朝向中心
        const progress = Math.min(deltaTime / 2000, 1)
        this.state.camera.position.lerpVectors(
          cameraPosition,
          objectPosition,
          progress
        )

        // 如果动画未完成，则继续执行下一帧动画
        if (progress < 1) {
          this.clearObjectsLabels()
          requestAnimationFrame(update)
        } else {
          this.clearObjectsLabels()
          // 创建DOM元素作为标签
          const label = document.createElement('div')
          label.id = 'object-label'
          label.innerHTML = clickedObject.userData.keywords || ''
          label.style.position = 'absolute'
          label.style.color = '#eab204'
          label.style.fontSize = '16px'
          label.style.border = '1px solid #9ab6c8'
          label.style.padding = '2px 4px'
          label.style.backgroundColor = 'black'

          document.body.appendChild(label)

          // 设置标签的位置和显示状态
          const position = this.getObjectScreenPosition(
            clickedObject,
            this.state.camera
          )
          // if (!isLocked) {
          label.style.display = 'block'
          label.style.top = `${position.y - 32}px`
          label.style.left = `${position.x - 10}px`
          // } else {
          //   label.style.display = "none";
          // }
          this.isLocked = false
          this.isShowInfo = true
        }
      }

      // 开始动画
      requestAnimationFrame(update)
    },

    // 获取截面的标签并展示
    logVisibleObjects () {
      // 创建Frustum对象
      const frustum = new THREE.Frustum()
      const cameraViewProjectionMatrix = new THREE.Matrix4()

      // 根据相机视锥体设置可见范围
      cameraViewProjectionMatrix.multiplyMatrices(
        this.state.camera.projectionMatrix,
        this.state.camera.matrixWorldInverse
      )
      frustum.setFromProjectionMatrix(cameraViewProjectionMatrix)

      // 遍历场景图中的所有 sprite 对象
      let visibleObjects = []
      this.state.scene.traverse((object) => {
        if (object.type === 'Sprite' && frustum.intersectsObject(object)) {
          // 获取 sprite 在世界坐标系中的位置
          const spritePos = new THREE.Vector3()
          object.getWorldPosition(spritePos)

          // 计算当前 sprite 距离相机的距离
          const distanceToCamera = spritePos.distanceTo(
            this.state.camera.position
          )

          // 判断当前 sprite 是否在相机视野之内，并且距离小于 60px
          if (frustum.containsPoint(spritePos) && distanceToCamera <= 60) {
            visibleObjects.push(object)
          }
        }
      })
      // 打印每个对象的userdata中的keywords属性值到控制台
      setTimeout(() => {
        visibleObjects = visibleObjects.filter((object) => {
          if (object.userData?.keywords) {
            const position = this.getObjectScreenPosition(
              object,
              this.state.camera
            )
            const screenWidth = window.innerWidth
            const screenHeight = window.innerHeight
            if (
              position.y - 30 + 100 > screenHeight ||
              position.x - 10 + 100 > screenWidth
            ) {
              return false // 返回false来排除在可视范围之外的元素
            } else {
              return true // 返回true来保留在可视范围之内的元素
            }
          } else {
            return false
          }
        })
        visibleObjects.forEach((object) => {
          const position = this.getObjectScreenPosition(
            object,
            this.state.camera
          )
          const visibleMarker = document.createElement('div')
          // 设置标签样式和属性
          visibleMarker.className = 'visible-marker'
          visibleMarker.style.top = `${position.y - 30}px`
          visibleMarker.style.left = `${position.x - 10}px`
          visibleMarker.style.position = 'absolute'
          visibleMarker.style.color = '#eab204'
          visibleMarker.style.fontSize = '16px'
          visibleMarker.style.border = '1px solid #9ab6c8'
          visibleMarker.style.padding = '2px 4px'
          visibleMarker.style.cursor = 'pointer'
          visibleMarker.style.backgroundColor = 'black'
          visibleMarker.innerHTML = object.userData.keywords || ''

          // visibleMarker.addEventListener("click", this.handleClick(object));
          document.body.appendChild(visibleMarker)
        })

        Promise.all(
          visibleObjects.map(async (obj) => {
            const bookKeywordsId = obj.userData.book_keywords_id

            const params = {
              book_keywords_id: bookKeywordsId
            }
            const result = await request(
              '/api/keywords/getDescriptionByKeyWordsId',
              params
            )
            // 将当前的结果与对应的子物体的 userData 进行组合
            return {
              keywords: obj.userData.keywords, // 假设 userData 中存在 keywords 属性
              result: result,
              book_keywords_id: obj.userData.book_keywords_id,
              member_keywords_id: obj.userData.member_keywords_id
            }
          })
        ).then((results) => {
          this.isShowInfo = false
          this.isShowInfoArrayLocked = true
          this.isShowInfoArray = false
          this.starLockedArray = results
        })
      }, 200)
    },
    // 点击截面的标签
    handleClick (object) {
      return () => {
        this.getKeywordsInfo(object.userData)
        this.isShowInfo = true
        this.clickedObjectData = object.userData
      }
    },
    // 清除页面上的截面标签
    clearObjectsLabels () {
      // 移除之前所有的标识符元素
      const visibleMarkerList =
        document.getElementsByClassName('visible-marker')
      while (visibleMarkerList.length > 0) {
        visibleMarkerList[0].parentNode.removeChild(visibleMarkerList[0])
      }
    },

    // +号，切换是否展示增加标签
    handleEntryAddClassify (item) {
      if (item.book_keywords_id) {
        this.entryids.book_keywords_id = item.book_keywords_id
      } else if (item.member_keywords_id) {
        this.entryids.member_keywords_id = item.member_keywords_id
      }
      this.isShowEntryAddClassify = !this.isShowEntryAddClassify
    },

    clearShow () {
      this.isShowInfo = false
      this.isShowInfoArrayLocked = false
      this.isShowInfoArray = false
      this.entryids = {
        book_keywords_id: '',
        member_keywords_id: ''
      }
      this.isShowEntryAddClassify = false
    }
  },
  mounted () {
    this.initThree()
    // 获取已有词条分类数据
    this.$store.dispatch('entryclassify/getMemberKeywordsTagList')
    request('/api/Keywords/getStarListForSurplus', {})
      .then((result) => {
        if ((result && result.pointData?.length)) {
          // const userinfo = JSON.parse(localStorage.getItem("userinfo"));
          request('/api/Keywords/getMixKeywordsShareList', {
            page: 1,
            limit: 30
          })
            .then((res) => {
              const list = res.list
              this.galaxy = new Galaxy(this.state.scene, result, list)
              requestAnimationFrame(this.render)
            })
          const duration = 4000
          const startPosition = new THREE.Vector3(0, 0, -700) // 初始位置
          const endPosition = this.state.camera.position.clone() // 目标位置
          let currentTime = 0

          // 星云闪烁问题
          clearInterval(this.moveCameraInterval)

          this.moveCameraInterval = setInterval(() => {
            currentTime += 16
            const progress = currentTime / duration
            this.state.camera.position.lerpVectors(
              startPosition,
              endPosition,
              progress
            )
            this.state.camera.lookAt(0, 0, 0)
            if (progress >= 1 || this.stopFlag) {
              clearInterval(this.moveCameraInterval)
            }
          }, 16)
        }
      })

    this.state.canvas.addEventListener('click', this.onClick, false)
  },

  beforeDestroy () {
    const previousLabel = document.getElementById('object-label')
    if (previousLabel) {
      previousLabel.remove()
    }
    this.clearObjectsLabels()
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
html,
body {
  margin: 0;
  height: 100%;
}

#canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.swich-box {
  width: 4vw;
  height: 2.2vw;
  background-color: #000000;
  border-radius: 20px;
  position: relative;
  box-shadow: 0px 0px 4px rgba(255, 255, 255, 0.3);
}

.swich-box-night {
  width: 4vw;
  height: 2.2vw;
  background-color: #a6a6a6;
  border-radius: 20px;
  /* 添加与 .swich-box 相同的边框半径 */
  position: relative;
  /* 将 .swich-box-night 的相对定位去掉 */
}

.swich-item {
  width: 2vw;
  height: 2vw;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  right: 0.33vw;
}

.swich-item-night {
  width: 2vw;
  height: 2vw;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  left: 0.33vw;
}

/* 隐藏垂直滚动条 */
.noScrollBar::-webkit-scrollbar {
  display: none;
}

/* ::-webkit-scrollbar {
  display: none;
} */
/*
 动态搜索 */
.search-btn-box {
  color: rgb(255, 255, 255);
  width: auto;
  border-radius: 20px;
  min-width: 25px;
  height: 25px;
  line-height: 25px;
  display: inline-block;
  position: relative;
  overflow: hidden;
  border: 3px solid #8faadc;
  background-size: 104% 104%;
  cursor: pointer;
}

.search-btn-box input {
  display: inline-block;
  background: 0 0;
  /* background: red; */
  border: none;
  color: #8faadc;
  padding-left: 25px;
  line-height: 25px !important;
  height: 25px;
  box-sizing: border-box;
  vertical-align: 4px;
  font-size: 16px;
  width: 25px;
  transition: all 0.5s ease-in-out;
}

.search-btn-box:hover input {
  display: inline-block;
  width: 180px;
  padding-right: 15px;
}

.search-btn-box input:not(:placeholder-shown) {
  display: inline-block;
  width: 180px;
  padding-right: 15px;
}

.search-btn-box:hover + .search-line {
  opacity: 0;
  height: 15px;
  transition: opacity 0.5s ease-out;
}

.search-line {
  height: 20px;
  width: 3px;
  background-color: #8faadc;
  position: absolute;
  left: 32px;
  bottom: -5px;
  transform: rotate(135deg);
  opacity: 1;
  transition: opacity 0.5s ease-in;
}

/* 画布上的搜索框+搜索结果 */
.search-box-input {
  position: fixed;
  right: 100px;
  top: 30px;
  z-index: 2000000;
}

/* 信息 */
.info-box-img {
  width: 6vw;
  height: 8vw;
  float: left;
  margin-right: 8px;
  cursor: pointer;
}

.info-box {
  position: absolute;
  bottom: 15px;
  left: 10px;
  background-color: #222f36;
  color: white;
  border-radius: 10px;
  min-width: 220px;
  min-height: 60px;
  max-height: 320px;
  max-width: 300px;
  /* padding: 0 5px; */
  overflow-y: scroll;
  /* padding: 3px 10px; */
  z-index: 1;
  border: 2px solid #404040;
}

.info-box::-webkit-scrollbar {
  display: none;
}

.info-box-array-outter {
  &::-webkit-scrollbar {
    display: none;
  }
}

.info-box-array {
  background-color: #222f36;
  color: white;
  border: 2px solid #404040;
  border-radius: 10px;
  min-width: 20vw;
  position: relative;
  /* min-height: 60px; */
  /* max-height: 30vh; */
  max-width: 300px;
  /* padding: 0 5px; */
  overflow-y: scroll;
  /* padding: 3px 10px; */
  display: inline-block;
  margin-left: 5px;
  z-index: 1;
}

.info-box-array::-webkit-scrollbar {
  display: none;
}

.info-title-box {
  display: flex;
  align-items: center;
  background-color: #1a2329;
  width: 100%;
}

.info-title {
  background-color: #1a2329;
  color: white;
  /* border-radius: 6px; */
  font-weight: 600;
  padding: 8px 8px;
  /* display: inline-block; */
  /* margin: 5px 0; */
  font-size: 15px;
  // width: 100%;
  box-sizing: border-box;
}

.info-box-array-html {
  overflow-y: scroll;
  word-wrap: break-word;
  font-size: 15px;
}

.info-box-array-html::-webkit-scrollbar {
  display: none;
}

.info-box-array-content {
  height: 22vh;
  overflow-y: scroll;
  padding: 0 5px;
}

.info-box-array-content::-webkit-scrollbar {
  display: none;
}

.info-addLink {
  color: white;
  border: 2px solid #2a75b5;
  margin: 5px;
  padding: 0 3px;
  border-radius: 3px;
  font-weight: bold;
  height: 18px;
  line-height: 16px;
  cursor: pointer;
}

/* 鼠标放大镜 */
.cursor-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: none;
  position: absolute;
  z-index: 9999;
}

.cursor-circle:active {
  display: none;
}

.no-scroll-bar::-webkit-scrollbar {
  display: none;
}

.visible-marker {
  position: absolute;
  z-index: 9999;
  margin: 0;
  pointer-events: none;
  color: red;
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.6),
    0 0 3px rgba(0, 0, 0, 0.4);
}

@media (max-width: 700px) {
  .info-box-array {
    min-width: 30vw;
  }

  .info-box-img {
    width: 15vw !important;
    height: 23vw !important;
  }
}
</style>
