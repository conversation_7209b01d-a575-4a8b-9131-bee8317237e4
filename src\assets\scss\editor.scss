.editor {
  box-sizing: border-box;

  .top {
    width: 98vw;
    height: 36px;
    box-shadow: 2px 2px 6px rgb(144, 143, 143);
    position: relative;
    border-radius: 3px;
    display: flex;
    justify-content: space-between;
    padding: 0 1vw;

    .top-left {
      display: flex;
      width: 20vw;

      .top-title {
        font-family: fantasy;
        word-spacing: 6px;
        letter-spacing: 1px;
        text-align: center;
        align-items: center;
        line-height: 40px;
        font-size: 19px;
        cursor: pointer;
      }


      /* 移动端样式 */
      @media screen and (max-width: 767px) {
        .top-title {
          font-size: 14px;
        }
      }
    }

    .nav {
      display: inline-flex;
      align-items: center;
      height: 40px;

      .item {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        box-shadow: 2px 2px 3px rgb(144, 143, 143);
        margin: 0 1vw 0 0;

        img {
          width: 25px;
          height: 25px
        }
      }
    }

    .doc-switch {
      display: inline-block;
      width: 25px;
      height: 25px;
      cursor: pointer;
    }
    .el-icon-folder-add, .el-icon-monitor, .el-icon-folder-add, .el-icon-document-add{
      font-size: 24px;
      margin-right: 1vw;
      cursor: pointer;
    }

    .doc-upload {
      display: inline-block;
      margin-left: 1vw;
      position: relative;
      cursor: pointer;
      padding: 5px 0 0;

      img {
        width: 25px;
        height: 25px;
      }
    }


    .right-box {
      float: right;
      height: 40px;
      display: inline-flex;
      align-items: center;
      // width: 25vw;

      .photo {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin: 0 1vw;
        box-shadow: 2px 2px 3px rgb(144, 143, 143);
      }
    }

    .top-middle {
      display: flex;

      img {
        width: 26px;
        margin-right: 5px;
        text-align: center;
        height: 26px;
        margin-top: 7px;
        cursor: pointer;
      }
    }

  }

  .box {
    display: flex;
    width: 100%;
    height: 94vh;
    overflow: hidden;
    position: relative;
    justify-content: center;

    .left,
    .right {
      width: calc(32% - 10px);
      height: 100%;
      background: rgb(213, 213, 216);
      float: left;
      box-shadow: 2px 2px 3px rgb(144, 143, 143);
      position: relative;
    }

    .left {
      width: calc(25% - 10px);
    }

    .resize {
      cursor: col-resize;
      float: left;
      position: relative;
      top: 45%;
      background-color: #d6d6d6;
      border-radius: 5px;
      margin-top: -10px;
      width: 10px;
      height: 50px;
      background-size: cover;
      background-position: center;
      font-size: 32px;
      color: white;
    }

    .resize:hover {
      color: #444444;
    }

    .mid {
      position: relative;
      float: left;
      // width: 58%;
      height: 100%;
      // background-color: #ffffff;
      // background: url('~@/assets/editor/loading.gif') no-repeat center center;
      // background: #fff2cc;
      padding: 5px 0;
      width: 100vw;
      display: flex;
      justify-content: center;

      iframe {
        width: 96%;
        height: 93vh;
        border: none;
        margin-left: 2%;
      }

      //这个本来是为了居中loading的？但是因为都是img，反而是在控制右折叠图标了。
      img {
        position: absolute;
        /* 使用绝对定位，使得图片可以居中 */
        left: 30%;
        /* 设置左侧偏移量为50%，使得图片会向右偏移到中心位置 */
        top: 50%;
        /* 同上，设置顶部偏移量为50% */
        transform: translate(-50%, -50%);
        /* 这里使用transform属性对图片进行微调，将其向左和向上偏移50%，实现完美居中 */
        // max-width: 100%;
        // /* 设置图片最大宽度为100%，防止图片超出容器范围 */
        // max-height: 100%;
        /* 设置图片最大高度为100%，防止图片超出容器范围 */
        height: 60px;
        width: 60px;
      }

      .doc-viewer {
        background-color: #ffffff;
      }

      .edit-padding-box {
        height: 100vh;
        width: 80vw;
        // background-color: #d5d5d8;
        // background: #d5d5d8 url('~@/assets/editor/loading.gif') no-repeat center center;
      }

      .epub-scroll-btn {
        position: absolute;
        right: -22px;
        // bottom: 0;
        width: 22px;
        height: 50px;
        left: auto;
        // top: auto;
        transform: inherit;
      }


      .epub-arrow-up {
        top: 50%;
        width: 25px;
        height: 25px;
        right: -34px;
      }

      .epub-arrow-down {
        top: 63%;
        transform: rotateX(180deg);
      }

      //弹窗AI-移动端
      .mobile-ai-btn {
        position: absolute;
        bottom: 215px;
        color: white;
        background-color: #97bee8;
        border-radius: 50%;
        height: 35px;
        width: 35px;
        font-size: 12px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;

        z-index: 99;
      }

      .mobile-ai-popup {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        // background-color: rgba(0, 0, 0, 0.5);
      }

      .mobile-ai-popup-content {
        position: absolute;
        height: 50vh;
        bottom: 0;
        background-color: #d5d5d8;
        width: 100vw;
        padding: 5px 0;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.51);
      }

    }

    .left-btn,
    .right-btn {
      position: fixed;
      width: 50px;
      height: 20px;
      // background-color: rgba(0, 0, 0, 0.5);
      // color: #fff;
      text-align: center;
      cursor: pointer;

    }

    .left-btn {
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      z-index: 1;
      margin-left: -5px;

      img {
        width: 40px;
        height: 40px;
      }
    }

    .right-btn {
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      // background-color: #d6d6d6;

      img {
        width: 40px;
        height: 40px;
        transform: rotate(180deg);
      }
    }


    .close-btn {
      // float: right;
      position: absolute;
      // z-index: 10;
      right: 0;

      img {
        width: 25px;
        height: 25px;
        cursor: pointer;
      }
    }
  }
}
// 覆盖编辑器的样式
:deep(.el-tiptap-editor>.el-tiptap-editor__content) {
  border: 1px solid #ccc;
}
:deep(.el-tiptap-editor__menu-bar) {
  border: 1px solid #ccc;
}
:deep(.el-tiptap-editor__footer) {
  border: 1px solid #ccc;
}

ul li, ol li{
  list-style: none;
}
