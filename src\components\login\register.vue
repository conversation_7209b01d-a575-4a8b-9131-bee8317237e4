<template>
  <div>
    <div v-show="isShowRegisterBox">
      <div
        style="
          font-weight: 600;
          font-size: 18px;
          text-align: center;
          margin-bottom: 10px;
        "
      >
        <!--class="register-title-box" <img src="@/assets/login/back.png" alt="" @click="goBack" /> -->
        <p style="" class="register-title">欢迎注册</p>
        <p style="" class="register-title">Bookor Brain</p>
      </div>
      <!-- <p class="register_line"></p> -->

      <div class="register-content-box">
        <div class="register-phone-box">
          <input
            class="account_security_phone_number"
            type="text"
            placeholder="手机号"
            maxlength="11"
            v-model="registerPhoneNumber"
          />
        </div>
        <!-- justify-content: center; -->
        <div style="display: flex; align-items: baseline">
          <input
            class="account_security_phone_number mobile_box"
            style="width: 157px"
            type="text"
            placeholder="输入验证码"
            maxlength="6"
            v-model="registerCaptcha"
          />
          <input
            class="send_sms"
            type="button"
            :value="smsButtonText"
            @click="getPhoneCaptcha"
          />
        </div>
        <input
          class="account_security_phone_number"
          type="password"
          name=""
          placeholder="8-20位密码,字母/数字/符号至少2种"
          maxlength="20"
          minlength="8"
          v-model="regitserPassword"
        />

        <div style="display: flex; margin: 10px 0">
          <!-- 复选框 -->
          <input
            class="select_radio"
            type="checkbox"
            style="
              width: 20px;
              height: 20px;
              display: block;
              margin: 0px 5px 0 0;
            "
            v-model="isChecked"
          />
          <!-- 同意用户协议和隐私条款 -->
          <p style="display: inline-block; font-size: 14px">
            我已同意并接受
            <span
              class="register-form"
              id="agreement"
              @click="showTermsOfService"
              >用户协议</span
            >
            和
            <span
              id="privacy_policy"
              class="register-form"
              @click="showPrivacyPolicy"
              >隐私条款</span
            >
          </p>
        </div>

        <!-- 注册按钮 -->
        <div class="register-btn-box">
          <button class="register_btn" @click="toRegister">立即注册</button>
          <el-button type="text"  @click="toLogin">去登陆</el-button>
        </div>
      </div>
    </div>
    <div v-show="isShowTermsOfService">
      <div class="register-title-box">
        <img src="@/assets/login/back.png" alt="" @click="showRegisterBox" />
        <p style="" class="register-title">用户协议</p>
      </div>
      <p class="register_line"></p>
      <div class="register-form-content">
        <div class="privacy_policy_contain_one">
          <p>一、总则</p>
          <p>
            “布客”（以下或称“我们”）注重保护用户（以下或称“您”）的个人信息（以下或称“信息”）及个人隐私。
            本隐私条款包含我们收集、处理、存储、使用、共享、转让、公开披露和保护用户个人信息的有关条款。
          </p>
          <p>
            本隐私条款为“布客”服务条款的一部分，并适用于您使用bookor.com网站、使用或购买我们开发或运营的产品和服务，
            或与“布客”交流的过程中我们获得的信息。我们希望通过本隐私条款向您清晰地介绍我们对您个人信息的处理方式，
            因此我们建议您完整地阅读本条款，以帮助您了解维护自己隐私权的方式。您的下载、安装、获取或使用“布客”，
            注册或登录等行为代表您已阅读并同意本隐私条款的约定，包括“布客”对本隐私条款随时所做的任何修改，
            您同意我们在遵守本隐私条款以及任何适用的法律的前提下，收集、处理、存储、使用和披露您的个人信息。
            如果您不同意接受本隐私条款或“布客”对本隐私条款所做的任何修改，您应立即停止使用“布客”服务、关闭帐户。
            我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维护您对我们的信任，恪守以下原则，
            保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。
            同时，我们承诺，我们将按业界成熟的安全标准，采取相应的安全保护措施来保护您的个人信息。如您对本隐私条款有任何疑问，
            您可以通过公开的“布客”客服联系方式与我们联系。
          </p>
          <p>
            请注意，尽管我们极力提供一个安全的服务架构，我们也意识到没有任何的安全机制是完全无懈可击的。我们将不为任何信息被个人以及非个人的盗窃负责。
            同时，身为我们的用户，您也必须为您的个人安全性小心谨慎，以确保您的密码及其他个人信息不被盗窃。我们亦不为任何盗窃相关的违法使用、侵入、或信息
            外泄负责。
          </p>
        </div>

        <div class="privacy_policy_contain_two">
          <p>二、适用范围</p>
          <p>
            本隐私条款适用于“布客”服务平台提供的所有服务，您访问“布客”网站、客户端、微信公众号，使用“布客”提供的服务，均适用本隐私条款。
            此外，针对某些特定服务，我们还将制定特定的隐私条款，以便更具体地说明我们在该特定服务中如何收集、处理、存储、使用、共享和保护您的信息。
            该特定服务的隐私条款构成本隐私条款的一部分。本隐私条款不适用于通过我们的服务平台而接入的其他第三方服务平台（包括但不限于您的交易相对方、
            任何第三方网站以及第三方服务提供者）收集的信息。我们对前述第三方收集、使用您个人信息的行为及后果不承担任何责任。请您注意，其他第三方可能有自己的隐私权保护条款；
            当您查看其他第三方创建的网页或使用其他第三方开发的应用程序时，可能会发现该网页或应用程序放置的Cookie或像素标签。这些Cookie或标签不受我们的控制，
            而且它们的使用不受本条款的约束。
          </p>
        </div>

        <div class="privacy_policy_contain_three">
          <p>三、信息采集授权</p>
          <p>
            个人信息是指以电子或者其他方式记录的，能够单独或者与其他信息结合，识别特定自然人身份或者反映特定自然人活动情况的各种信息。
            我们仅会出于本条款所述的为了遵守法律法规的要求，并努力提高您的服务体验之目的，收集和使用您的个人信息。我们需要收集某些必要的个人信息，
            以创建和支持您的“布客”帐户、向您提供方便而又个性化的帐户访问服务、以及改进我们产品的功能和可用性。同时掌握这些个人信息也使我们能够向您发送产品公告、
            软件更新和特价优惠信息。“布客”的注册用户可以登录“布客”网站访问“布客”帐户信息页面并点击“设置”链接，随时查看并修改其个人信息。
            未注册用户也可以使用部分“布客”网站及服务，在此情况下，我们不会收集未注册用户的个人信息。
          </p>
          <p>
            如果您不同意提供全部或部分个人信息，您将无法使用“布客”的全部或部分功能和服务，或无法获得更个性化的功能，
            或无法参加某些活动，或无法收到我们的通知等。
          </p>
          <p>
            您向我们提供下述信息或使用我们的服务时，即表示您同意并授权我们按照本条款规定收集您的个人信息，
            您同意承担由此带来的一切法律后果。您授权我们采集的信息种类如下：
          </p>
          <p>3.1与个人身份无关的信息</p>
          <p>
            诸如记录使用“布客”服务的每个用户的来源途径、访问顺序、浏览器版本等信息。
          </p>
          <p>3.2与个人身份有关的信息</p>
          <p>有关使用“布客”服务的每个用户的个人身份信息；包括但不限于：</p>
          <p>3.2.1基本用户信息：</p>
          <p>
            为了充分利用“布客”服务以及任何可下载的软件产品，您需要创建一个“布客”帐户。这也将允许“布客”向您提供更加个性化和便利的服务。
            为了开立您的帐户并处理付款，我们会收集并获取基本信息及您选择添加到您个人资料中的信息，比如您的姓名、手机号码、邮件地址、帐单地址、
            通讯地址、联系电话、发票信息、信用卡卡号等其他有关付款的信息，这要取决于您订购的是“布客”标准帐户、高级帐户还是企业版。
            如您通过第三方帐户（如微信等）创建、绑定或登录“布客”帐户，我们会从第三方获取您授权共享的第三方帐户信息（如头像、昵称等），
            并可能将该第三方帐户信息作为“布客”帐户信息。如您通过“布客”服务向我们授权的第三方软件或服务分享、传输内容，
            我们可能收集并获取您的实名认证信息及手机号码。
          </p>
          <p>3.2.2随笔和书评数据：</p>
          <p>
            当您使用“布客”时，我们收集数据以了解您和他人访问和使用服务的状况，同时收集您的操作信息（包括但不限于您创建的随笔和书评或者分享随笔和书评，
            随笔和书评的数量及字数，其中的图片、音频、视频、表格、PDF等文件的数量及大小、被标注的文件数量等外部数据）。
            收集的数据包括 cookies
            的使用、跟踪像素和类似的分析技术（下文将对Cookies有更全面的阐述）。
            此等信息将帮助我们为您提供服务，了解您如何使用服务，从而为您提供更为有用的服务。
          </p>
          <p>3.2.3分享信息：</p>
          <p>
            当您向您在第三方帐户（如微信）的联系人分享“布客”服务的随笔和书评文件时，我们将收集您所分享的文件信息，
            包括但不限于：分享时间、分享平台、分享文件的数量、阅读量等，以及文件中的链接、图片、音频、视频、表格、PDF、
            被标注的文件数量等已公开的外部数据。用于我们对分享服务的合规管理，同时帮助我们了解分享服务，为用户提供更优的使用体验；
            同时我们会审核被分享的文件内容，以进行我们对分享服务的合规管理。
          </p>
          <p>3.2.4位置信息：</p>
          <p>
            我们收集您用于连接到该服务的IP地址，以及（如果您选择了共享）您的移动设备的位置信息。
            这有助于我们就您的服务本地化，并根据您的位置设置，
            让我们向您显示您的帐户可访问的相关内容。
          </p>
          <p>3.2.5设备信息：</p>
          <p>
            我们收集您连接到服务的设备数量和设备类型的相关信息，以及有关这些设备操作系统的信息（例如iOS，Android，Windows），
            以确保服务达到您的需求。
          </p>
          <p>3.2.6其他信息：</p>
          <p>
            显示您和我们的某一个业务合作伙伴或促销活动的关联性的非个人身份识别信息。我们可能会将您的订阅者信息与来自我们合作伙伴和其他第三方的数据相关联，
            以了解您的需求，并为您提供更好的使用体验。
          </p>
        </div>
        <div class="privacy_policy_contain_four">
          <p>四、信息使用授权</p>
          <p>
            您同意并不可撤销地授权“布客”保存、整理、使用和披露上面所述的、通过合法途径收集的您的个人信息，以提供、维护、提升我们的平台和服务，提供解决问题的方案，
            提供客户支持等。信息使用用途描述如下：
          </p>
          <p>
            4.1通过接受本隐私条款，您明确同意通过电子邮件或手机短信从“布客”收到有关我们或我们的业务合作伙伴的产品或服务的信息。您可随时登入“布客”网站的个人帐户中，
            选择退出该等信息接收或改变您的联系偏好。或者您也可以点击位于您收到的电子邮件底部的“取消订阅”链接（针对电子邮件）或回复退订短信（针对手机短信）退出我们的营销通信。
          </p>
          <p>
            4.2当您注册创建“布客”个人帐户时，我们将根据您选择的注册方式通过发送短信验证码或邮件的方式来验证您的身份是否有效。当您忘记密码时，您在个人资料中添加的手机号码或者
            邮箱地址将作为验证身份以找回密码的路径。
          </p>
          <p>
            4.3当您向您在第三方帐户（如微信）的联系人分享随笔和书评时，您明确同意我们通过系统自动审核及必要时人工复核您分享的随笔和书评内容，以保证我们对分享服务的合规管理。
          </p>
          <p>
            4.4您可以随时选择不再参与我们提供的服务，或删除您已发布的信息和资料。如果您注销帐户或删除任何个人信息、随笔和书评内容，您或访问服务的其他人将无法再访问该等信息，
            但是由于电子设备的运行和持续数据备份及存档特性，该等信息的残余副本可能继续存在或保留于您曾使用过的客户端上一段时间，请您安全使用相关设备。
          </p>
          <p>
            4.5我们采用多项技术来帮助您获得最好的服务；我们的系统会自动收集及分析数据，以强化“布客”的功能和不断改进为您提供的服务，
            而无需用真人查看您的信息内容（但为了排除故障以及提供用户支持的情形除外）。这可能包括：
          </p>
          <p>当您进行帐户搜索时，您可以便捷地找到所需的信息；</p>
          <p>
            在特定的时间或地点，显示与您如何使用服务或者能够怎样使用服务最相关的信息；
          </p>
          <p>根据您存储的信息，为您提出相关的操作建议；</p>
          <p>
            为您推荐相关的“布客”产品或者服务功能，帮助您把“布客”的作用发挥到极致；
          </p>
          <p>
            为保护您的帐户及服务功能，我们的系统可能会分析您从“布客”帐户发送以及接收的邮件，及分析您共享的随笔和书评，从而删除垃圾邮件、恶意邮件及排除其他安全隐患。
            如果我们确信该内容中包含有侵害到服务条款的信息，我们可能会依照收件箱的垃圾邮件过滤器功能，阻止传输存在问题的信息或禁止其共享；
          </p>
          <p>
            为了排除故障、合规管理以及提供用户支持，我们的客户服务团队可能需要访问您的信息，例如您帐户的手机号码、您帐户的邮箱地址、
            您帐户关联的第三方帐户以及您正在使用的“布客”应用程序信息，并遵守相关保护条款。
          </p>
          <p>
            4.6出于为您提升服务水平的需要，我们将会使用去标识化后您的个人信息用于改进“布客”服务平台的设计和推广。
          </p>
          <p>
            4.7在不透露您及您提供数据的信息源的隐私资料的前提下，“布客”对整个用户及用户提供的数据的数据库进行分析
            并对该数据库进行商业上的利用。
          </p>
          <p>
            4.8根据法律规定及合理的商业惯例，在我们计划与其他公司合并或被其收购或进行其他资本市场活动（包括但不限于IPO，债券发行）时，
            以及其他情形下我们需要接受来自其他主体的尽职调查时，我们会把您的信息提供给必要的主体，但我们会通过和这些主体签署保密协议等
            方式要求其对您的个人信息采取合理的保密措施。
          </p>
          <p>
            4.9当我们要将信息用于本隐私条款未载明的其他用途时，会事先征求您的同意。当我们要将给予特定目的收集的信息用于其他目的时，
            亦会事先征求您的同意。
          </p>
        </div>
        <div class="privacy_policy_contain_five">
          <p>五、信息共享授权</p>
          <p>
            我们对您的个人信息承担保密义务，非经您授权，我们不会将您的信息提供给任何第三方机构或个人，同时您授权我们在下列情况中将您的信息与第三方共享：
          </p>
          <p>
            5.1为了提升我们的产品及服务质量或向您提供全新的产品及服务，我们会在关联公司内部共享您的相关信息，也可能在必要的程度内将您的合理必要信息提供给第三方用于分析和统计。
          </p>
          <p>
            5.2为了提供“布客”的服务、完成支付交易以及满足您的产品或服务要求（包括销售、交付和支援），我们可能会与代表我们处理数据的第三方服务提供商分享您的信息，这些服务提供商
            仅已获授权在提供服务所必需的范围内使用您的个人信息。
          </p>
          <p>
            5.3如果您通过“布客”服务平台使用的某些产品及服务是由我们的合作伙伴提供的，或是由我们与合作伙伴或供应商共同提供的，我们将与其共享向您提供相应产品及服务所必需的信息。
          </p>
          <p>
            5.4您明确要求获得来自“布客”业务合作伙伴的信息，而在这种情况下，收集个人信息者的身份以及所适用的隐私声明，将会在收集信息时有明确表示。
          </p>
          <p>
            5.5我们认为为了履行我们的法定义务或为了调查、预防非法活动、涉嫌诈骗行为、对人身或财产的潜在威胁，或对该等情形采取行动，
            有必要向司法机关或其他有权机关披露您的信息的情形：
          </p>
          <p>
            5.5.1我们认为为了调查和/或披露可能违反我们服务条款的行为或执行这些服务条款时，有必要披露您的信息；
          </p>
          <p>
            5.5.2在您违反与我们或我们的其他用户签订的协议时，您的违约信息将被提交至第三方征信机构及/或第三方信息服务公司，
            记录在该第三方服务机构的数据库中（您已明确知悉在此情况下，您日后的经济活动会受到不良影响）；
          </p>
          <p>
            5.5.3根据法律规定，刑事侦查机关为调查犯罪，依法定程序调取的必要个人信息；或行政机关、司法机构，
            依法定程序并经您授权同意调取的必要个人信息，我们会向其提供您的相关信息；
          </p>
          <p>
            5.5.4非经法定程序或未获得您的同意，我们不会将您的个人信息提供给任何第三方机构或个人。
          </p>
          <p>
            5.6随着我们业务的持续发展，我们有可能进行合并、收购、资产转让等交易，我们将告知您相关情形，
            按照法律法规及不低于本条款所要求的标准继续保护或要求新的控制者继续保护您的个人信息。
          </p>
        </div>
        <div class="privacy_policy_contain_six">
          <p>六、Cookies与同类技术</p>
          <p>6.1 关于Cookies</p>
          <p>
            您使用我们的服务时，我们会在您的计算机或移动设备上存储名为
            cookies的小数据文件。cookies通常包含标识符、站点名称以及一些号码和字符。我们使用该等信息判断注册用户是否已经登录、
            记录用户偏好、提升服务、提升产品质量及优化用户体验。如您不希望个人信息保存在
            cookies中，您可对浏览器进行配置，选择禁用cookies功能。禁用cookies功能后，
            可能影响您访问“布客”或不能充分取得“布客”提供的服务。
          </p>
          <p>
            6.2
            “布客”不会将cookies用于本政策所述目的之外的任何用途。您可根据自己的偏好管理或删除cookies。您可以清除计算机上保存的所有cookies，大部分网络浏览器都设有阻止cookies的功能。
          </p>
          <p>
            6.3
            “布客”可能允许在我们的部分网页上做广告的第三方业务合作伙伴在您的电脑中保存它们自己的cookies。这些业务合作伙伴无权存取“布客”的cookies，而它们的使用受其自身隐私条款的约束。
          </p>
          <p>
            6.4
            我们从您的设备上收集了专门的广告跟踪标识符；这些标识符是您设备的唯一标识，但您可以重新设置它们或通过您的设备限制其使用。这些标识符不包含您的姓名或电子邮件地址。
            我们使用这些广告跟踪标识符与我们的分析技术相结合，以便提醒我们有针对性的广告活动。
          </p>
          <p>
            6.5
            我们将不时与第三方网络广告商合作，这些网络广告商使用cookies和类似的技术以收集关于您访问“布客”网站的网页和在该等网页上动作的信息，您使用“布客”应用，
            以及您是否打开我们发送给您的电子邮件等信息。我们这样做是为了日后（例如当您在访问第三方网站时）向您宣传相关的“布客”产品和服务，以及衡量我们讯息的效果。
            在这个过程中，我们不会同第三方分享您的任何个人信息（比如您的名字、手机号码、电子邮箱或第三方帐户）或帐户内容。
          </p>
          <p>
            6.6
            我们还会采用一些技术来帮您最大限度地利用“布客”服务。我们的系统会自动分析您的数据，增强“布客”的一些特有功能，
            例如“搜索”和“推荐内容”。利用“推荐内容”功能，“布客”服务使用多种技术来向您显示相关内容。“推荐内容”绝不是广告内容。这些功能会自动进行，不涉及任何“布客”的人员查看您的内容的功能。
            我们相信这些功能会提升您的服务使用体验。“推荐内容”在提供该功能的应用程序中默认为开启，但是您可以选择将其关闭。利用“推荐内容”功能，“布客”服务使用多种技术来向您显示相关内容。
            此外，通过检测您在帐户中的特定操作（例如，通过电子邮件发送内容到您的帐户），我们可能会为您推荐“布客”产品或者服务功能，使您从我们的服务中获得最多。
            我们不会以任何宣传为目的而阅读或与任何人分享您的个人信息、随笔和书评内容。
          </p>
          <p>
            6.7
            “布客”在提供服务的过程中，可能需要您开通一些设备访问权限，例如通知、相册、相机、手机通讯录、蓝牙等。您也可以在设备的设置功能中随时选择关闭部分或者全部权限，
            从而拒绝“布客”收集相应的个人信息。关闭相关权限后，可能影响您访问“布客”或不能充分取得我们提供的服务。在不同设备中，权限显示方式及关闭方式可能有所不同，具体请参考设备及系统开发方说明或指引。
          </p>
        </div>

        <div class="privacy_policy_contain_seven">
          <p>七、信息的安全和保护措施</p>
          <p>
            “布客”致力于保护您的信息安全，并为此采取合理的预防措施。我们采取互联网业内标准做法来保护您的个人信息，防止您的信息遭到未经授权访问、披露、使用、修改、损坏或丢失。
          </p>
          <p>
            7.1我们会采取一切合理可行的措施，确保未收集与“布客”提供的服务无关的个人信息。
          </p>
          <p>7.2 我们采用以下措施保护我们收集到的您的个人信息：</p>
          <p>7.2.1 技术措施</p>
          <p>
            7.2.1.1我们的网络服务采取了符合行业标准的加密技术来保护您在传输过程中的数据，该技术通常被称为传输层安全（“TLS”）技术或安全套接层（“SSL”）技术，通过https等方式提供浏览服务，
            确保用户数据在传输过程中的安全。但是，互联网数据传输无法保证100%的安全，因此，我们无法保证在您和我们之间的传输过程中信息的安全；因此，请您理解，由于技术的限制以及可能存在的各种恶意手段，
            在互联网行业，不可能始终保证信息百分之百的安全。您需要了解，您接入我们的服务所用的系统和通讯网络，有可能因我们可控范围外的因素而出现问题。
          </p>
          <p>
            7.2.1.2
            一旦收到您的数据，我们将通过行政、物理以及逻辑安全相结合的方式确保其在我们服务器上的安全。
          </p>
          <p>
            7.2.1.3
            在个人信息使用时，我们会采用包括内容替换、加密脱敏等多种数据脱敏技术增强个人信息在使用中安全性。
          </p>
          <p>
            7.2.1.4
            设立严格的数据使用和访问制度，采用严格的数据访问权限控制和多重身份认证技术保护个人信息，避免数据被违规使用。
          </p>
          <p>
            7.2.1.5
            采取专门的数据和技术安全审计，设立日志审计和行为审计多项措施。
          </p>
          <p>
            7.2.1.6
            为保护您的帐户及服务功能，我们的自动系统会分析您从“布客”服务帐户中发出或接收的邮件，来监测垃圾邮件、恶意软件或其它潜在的安全问题及可能影响邮件送达的行为。
          </p>
          <p>
            7.2.1.7
            在发生“布客”停止运营或服务时，我们将及时停止继续采集您个人信息的活动，并将停止运营的通知以公告形式通知您，同时对您的个人信息进行匿名化处理。
          </p>
          <p>7.2.2 管理措施</p>
          <p>
            7.2.2.1
            建立数据安全管理规范、数据安全开发规范来管理规范个人信息的存储和使用。
          </p>
          <p>
            7.2.2.2
            加强安全意识教育；我们不定期举办安全和隐私保护培训，加强员工对于保护个人信息重要性的认识。
          </p>
          <p>
            7.2.2.3我们在中华人民共和国境内收集和产生的个人信息和重要数据将存储在中华人民共和国境内。由于我们可能通过遍布全球的资源和服务器提供产品或服务，因此在获得您的授权同意后，
            您的个人信息可能会被转移到您使用产品或服务所在国家/地区的境外管辖区，或者受到来自这些管辖区的访问，我们会确保您的个人信息得到在中国境内足够同等的保护。
          </p>
          <p>
            7.2.2.4
            我们仅会在达到本政策所述目的所必需的时限内保存您的个人信息，但为了遵守适用的法律法规、法院判决或裁定、其他有权机关的要求、维护公共利益等目的，
            我们可能会将个人信息保存时间予以适当延长。
          </p>
          <p>7.3个人信息安全事件处理</p>
          <p>
            7.3.1互联网环境并非百分之百安全，我们将尽力确保或担保您发送给我们的任何信息的安全性。如果我们的物理、技术、或管理防护设施遭到破坏，导致信息被非授权访问、公开披露、篡改、或毁坏，
            导致您的合法权益受损，我们将承担相应的法律责任。
          </p>
          <p>
            7.3.2如发生个人信息安全事件，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低风险的建议、对您的补救措施等。
            我们将及时将事件相关情况以邮件、信函、电话、推送通知等方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告，避免进一步的数据泄露。同时，我们还将按照监管部门的要求，
            主动上报个人信息安全事件的处置情况。
          </p>
          <p>
            7.4
            需要注意的是，尽管已经采取了上述合理有效措施，并已经遵守了相关法律规定要求的标准，但我们仍然无法保证您的个人信息通过不安全途径进行交流时的安全性。例如，存储于安装在您的电脑设备上
            的“布客”软件中的信息的安全取决于您对您的电脑设备的安全功能的使用。因此，我们建议您采取适当的措施，以保证使用我们应用程序和服务的电脑设备的安全性。如定期修改账号密码、不使用与其他服务相同的密码、
            不将自己的账号密码等个人信息透露给他人等。
          </p>
          <p>
            7.5
            您理解并同意，我们提供的个人信息保护措施仅适用于“布客”相关服务，一旦您离开该平台，浏览或使用其他网站、服务及内容资源，我们即没有能力及义务保护您在“布客”以外的网络环境使用的任何个人信息，
            无论您登录或浏览的网络资源是否源于“布客”的链接或引导。
          </p>
        </div>
        <div class="privacy_policy_contain_eight">
          <p>八、风险提示</p>
          <p>8.1 请您注意，任何安全系统都存在可能的及未知的风险。</p>
          <p>
            8.2
            请您知悉：包含您身份信息、财产信息、账户信息、信用信息、交易信息、行踪轨迹信息、电子设备信息、电子设备操作日志等信息为您重要且私密的个人信息。
            您充分了解并明确知悉“布客”向第三方采集并在法律法规许可范围向信息使用者提供这些信息后，虽然我们将与可能接触到您的个人信息的我们的合作方及供应商等第三方签署保密协议并尽合理的努力督促其履行保密义务，
            但我们无法保证第三方一定会按照我们的要求采取保密措施，信息被其他主体对外提供和使用的风险，包括但不限于：该等信息被“布客”依法提供给第三方后被他人不当利用的风险、第三方向您推销产品或服务而打扰您等风险；
            我们亦不对第三方的行为及后果承担任何责任。在此情况下，您同意并不可撤销地授权“布客”依照本隐私条款约定向其他机构提供您的个人信息。
          </p>
          <p>
            8.3
            作为用户，您可根据您的意愿决定是否使用“布客”服务平台中的服务，是否主动提供个人信息。同时，您可以查看您提供给我们的个人信息。如果您希望删除或更正您的个人信息，请联系“布客”的客服人员。
          </p>
          <p>8.4 外部链接</p>
          <p>
            “布客”网站及服务可能包含其他网站的链接。对于我们的网站所链接之网站所采用的隐私条款或其他做法，以及该等网站上含有的信息或内容，“布客”概不负责。本隐私条款仅适用于“布客”网站及服务收集的信息，不适用于其他网站。
          </p>
          <p>8.5 信息删除</p>
          <p>
            您可以随时删除随笔和书评内容，也可以随时停止使用“布客”服务。如果您停用帐户，您帐户中的内容将不会被删除，除非您在停用帐户之前，主动删除这些信息并同步您的帐户。如果您删除信息和资料，然后才将您的帐户同步，
            则您或其他有权存取服务的人均不再能再存取这些数据，但由于有关系统运行的特性，您删除的内容的残余副本可能继续在“布客”服务的备份和存档系统中被保留最多一年。
          </p>
          <p>
            8.6
            我们也可能与第三方，包括但不限于支援我们服务的其他服务提供商、以及广告投放相关的厂商，共享不记名信息，包括但不限于：我们网站的使用信息、经过分析汇整的统计数据、用户使用行为、流量及数量统计等。
          </p>
          <p>
            8.7
            您在我们的网站以及服务平台所公开提交的评论、分享、以及用户名，视为公开信息。我们保留重置、使用、公开及传播此公开信息的权利。请注意，若您自愿提供此讯息，则亦有可能由他人使用及收集。例如，如果您在我们的交流平台上进行发言，
            并且公开留下了联系方式，则可能收到他人所传递的广告信息。我们将无法控制您在此公开领域中发布的个人信息。您可依照您对个人信息的公开程度，选择性公布您的个人信息。
          </p>
          <p>8.8 第三方服务商</p>
          <p>
            我们可能与第三方服务商进行合作，以便提供更多的服务给我们的用户。这些第三方服务商将可能代表我们提供服务，并执行网站的维护、托管、数据库管理等服务。这些第三方服务商仅在执行此类业务时，有权使用您的个人信息，并同时保持与我们同样的隐私权承诺。
          </p>
          <p>8.9 遵守法律</p>
          <p>
            “布客”必须与政府及执法单位进行配合，以确保无违法行为的进行。在此情况下，我们将视情况与执法单位配合，提供个人信息以及使用信息，以确保“布客”或第三方的权利保障、保护公众或任何人的安危、或防止任何我们认为造成违法的可能之行为。
          </p>
          <p>8.10 商业转移</p>
          <p>
            “布客”可能因转售、转移或其他原因，售出部分或全部的资产，包括您的个人信息及使用信息，以完成并购、收购、组织重整、甚至破产所导致的资产变卖。如果收购方的使用条款及隐私权声明不符合您的标准，您将保留退出或取消帐户的权利。
          </p>
          <p>8.11 第三方链接</p>
          <p>
            我们的网站包含其他网站的链接。我们不对其他网站上的隐私政策和/或其执行承担责任。当链接到其他网站时，用户应该阅读该网站公示的隐私政策。我们所提供的第三方网站的链接不代表我们对该网站的代言，也不代表我们与该网站的联盟关系，
            亦不代表我们与其隐私条款的相关链接。这些第三方网站有可能在您的计算机上安装第三方软件，或向您索取个人信息。
            我们的隐私条款仅适用于“布客”；即便您或许已熟悉了我们的隐私条款，我们仍建议您参考该网站所公示的隐私政策。
          </p>
        </div>
        <div class="privacy_policy_contain_nine">
          <p>九、通知和修订</p>
          <p>
            9.1
            为给您提供更好的服务，“布客”的业务将不时变化，本隐私条款也将随之调整。我们会通过在我们网站、移动端上发出更新版本或以其他方式提醒您相关内容的更新，也请您访问我们以便及时了解最新的隐私条款。隐私条款条款变更后，
            您有权选择接受更新的隐私条款或停止使用我们的软件及服务。如果您继续使用“布客”提供的软件或服务，即视为您已接受修改后的隐私条款。
          </p>
          <p>
            9.2如果您对于本隐私条款或在使用我们服务时对于您的个人信息或隐私情况有任何问题，请联系我们的客服并作充分描述，我们将尽力解决。
          </p>
          <p>
            9.3
            如果您对我们的回复不满意，特别是认为我们的个人信息处理行为损害了您的合法权益，您还可以通过以下外部途径寻求解决方案：个人信息控制者所在管辖区的法院、认证个人信息控制者隐私条款的独立机构、行业自律协会或政府相关管理机构。
          </p>
        </div>
        <div class="privacy_policy_contain_ten">
          <p>十、未成年人保护</p>
          <p>
            我们非常重视对未成年人个人信息的保护。我们希望未成年人仅在接受其父母或法定监护人指导、监督并经其同意的前提下使用。若您是未成年人的监护人，当您对您所监护的未成年人的个人信息有相关疑问时，可与我们联系。
          </p>
        </div>
        <div class="privacy_policy_contain_eleven">
          <p>十一、联系我们</p>
          <p>
            欢迎您对本隐私条款和我们的服务条款提出意见。如有任何问题、意见或疑虑，请通过https://www.bookor.com/contact/support联系我们，您也可以将您的问题发送至**********************或寄信至如下地址：
          </p>
          <p>上书台科技发展（深圳）有限公司</p>
          <p>地址：深圳市</p>
          <p>邮编：</p>
        </div>
      </div>
    </div>
    <div v-show="isShowPrivacyPolicy">
      <div class="register-title-box">
        <img src="@/assets/login/back.png" alt="" @click="showRegisterBox" />
        <p style="" class="register-title">隐私条款</p>
      </div>
      <p class="register_line"></p>
      <div class="register-form-content">
        <div class="user_agreement_contain_one">
          <p>一、总则</p>
          <p>
            1.1
            本协议中所述的“布客”或“本站”含义相同，其所有权和运营权归上书台科技发展(深圳)有限公司所有。
          </p>
          <p>
            1.2
            用户在注册之前，应当仔细阅读本协议，并同意遵守本协议后方可注册成为“布客”用户。一旦注册成功，则用户与“布客”之间自动形成协议关系，
            双方应当受本协议的约束。用户在使用特殊的服务或产品时，应在同意接受相关协议后方能使用。
          </p>
          <p>
            1.3
            “布客”有权在必要时修改服务条款，用户应当及时关注并同意本站不承担通知义务。本站的通知、公告、声明或其它类似内容是本协议的一部分。
            如果用户不同意所改动的内容，可以主动取消获得的网络服务。如果用户继续享用网络服务，则视为接受服务条款的变动。
            “布客”保留随时修改或中断服务而不需知照用户的权利。“布客”行使修改或中断服务的权利，不需对用户或第三方负责。
          </p>
        </div>

        <div class="user_agreement_contain_two">
          <p>二、服务内容</p>
          <p>2.1 “布客”的具体服务内容由本站根据实际情况提供。</p>
          <p>
            2.2
            本站仅提供相关的网络服务，除此之外与相关网络服务有关的设备（如个人电脑、手机、及其他与接入互联网或移动网有关的装置）及所需的费用（
            如为接入互联网而支付的电话费及上网费、为使用移动网而支付的手机费）等均应由用户自行负担。
          </p>
        </div>

        <div class="user_agreement_contain_three">
          <p>三、用户帐号</p>
          <p>
            3.1
            经本站注册系统完成注册程序并通过身份认证的用户即成为正式用户，可以获得本站规定用户所应享有的一切权限；
            未经认证仅享有本站规定的部分用户权限。“布客”有权对用户的权限设计进行变更。
          </p>
          <p>
            3.2
            用户只能按照注册要求使用真实姓名及身份证号注册。用户有义务保证密码和帐号的安全，
            用户利用该密码和帐号所进行的一切活动引起的任何损失或损害，由用户自行承担全部责任，本站不承担任何责任。
            如用户发现帐号遭到未授权的使用或发生其他任何安全问题，应立即修改帐号密码并妥善保管，
            如有必要，请通知本站。因黑客行为或用户的保管疏忽导致帐号非法使用，本站不承担任何责任。
          </p>
        </div>

        <div class="user_agreement_contain_four">
          <p>四、使用规则</p>
          <p>
            4.1
            遵守中华人民共和国相关法律法规，包括但不限于《中华人民共和国计算机信息系统安全保护条例》、
            《计算机软件保护条例》、《最高人民法院关于审理涉及计算机网络著作权纠纷案件适用法律若干问题的解释（法释[2004]1号）》、
            《全国人大常委会关于维护互联网安全的决定》、《互联网电子公告服务管理规定》、《互联网新闻信息服务管理规定》、
            《互联网著作权行政保护办法》和《信息网络传播权保护条例》
            等有关计算机互联网规定和知识产权的法律和法规、实施办法。
          </p>
          <p>
            4.2
            用户对其自行发表、上传或传送的内容负全部责任，所有用户不得在本站任何页面发布、转载、传送含有下列内容之一的信息，
            否则本站有权自行处理并不通知用户：
          </p>
          <p>（1）违反宪法确定的基本原则的；</p>
          <p>（2）危害国家安全，泄漏国家机密，颠覆国家政权，破坏国家统一的；</p>
          <p>（3）损害国家荣誉和利益的；</p>
          <p>（4）煽动民族仇恨、民族歧视，破坏民族团结的；</p>
          <p>（5）破坏国家宗教政策，宣扬邪教和封建迷信的；</p>
          <p>（6）散布谣言，扰乱社会秩序，破坏社会稳定的；</p>
          <p>（7）散布淫秽、色情、赌博、暴力、恐怖或者教唆犯罪的；</p>
          <p>（8）侮辱或者诽谤他人，侵害他人合法权益的；</p>
          <p>（9）煽动非法集会、结社、游行、示威、聚众扰乱社会秩序的；</p>
          <p>（10）以非法民间组织名义活动的；</p>
          <p>（11）含有法律、行政法规禁止的其他内容的。</p>
          <p>
            4.3
            用户承诺对其发表或者上传于本站的所有信息（即属于《中华人民共和国著作权法》规定的作品，
            包括但不限于文字、图片、音乐、电影、表演和录音录像制品和电脑程序等）
            均享有完整的知识产权，或者已经得到相关权利人的合法授权；
            如用户违反本条规定造成本站被第三方索赔的，用户应全额补偿本站一切费用（包括但不限于各种赔偿费、
            诉讼代理费及为此支出的其它合理费用）。
            当第三方认为用户发表或者上传于本站的信息侵犯其权利，并根据《信息网络传播权保护条例》
            或者相关法律规定向本站发送权利通知书时，用户同意本站可以自行判断决定删除涉嫌侵权信息，
            除非用户提交书面证据材料排除侵权的可能性，本站将不会自动恢复上述删除的信息。
          </p>
          <p>4.4 不得为任何非法目的而使用网络服务系统。</p>
          <p>4.5 遵守所有与网络服务有关的网络协议、规定和程序。</p>
          <p>
            4.6 不得利用本站进行任何可能对互联网的正常运转造成不利影响的行为。
          </p>
          <p>4.7 不得利用本站进行任何不利于本站的行为。</p>
          <p>4.8 用户还须做到：</p>
          <p>
            （1）用户名和昵称的注册与使用应符合网络道德，遵守中华人民共和国的相关法律法规；
          </p>
          <p>
            （2）用户名和昵称中不能含有威胁、淫秽、漫骂、非法、侵害他人权益等有争议性的文字；
          </p>
          <p>
            （3）注册成功后，用户必须保护好自己的帐号和密码，因用户本人泄露而造成的任何损失由用户本人负责；
          </p>
          <p>（4）不得盗用他人帐号，由此行为造成的后果自负。</p>
          <p>
            4.9
            如用户在使用网络服务时违反上述任何规定，本站有权要求用户改正或直接采取一切必要的措施（包括但不限于删除用户张贴的内容、
            暂停或终止用户使用网络服务的权利）以减轻用户不当行为而造成的影响。
          </p>
        </div>
        <div class="user_agreement_contain_five">
          <p>五、隐私保护</p>
          <p>
            5.1用户通过注册而获得“布客”的相关服务时，根据本站要求提供相关个人信息；在使用“布客”的服务、参加本站活动、或访问本站网页时，
            本站自动接收并记录的用户浏览器上的服务器数据，包括但不限于IP地址、网站Cookie中的资料及用户要求取用的网页记录；
            本站承诺不对外公开或向第三方提供单个用户的注册资料及用户在使用网络服务时存储在本站的非公开内容，但下列情况除外：
          </p>
          <p>（1）事先获得用户的明确授权；</p>
          <p>
            （2）
            如用户是符合资格的知识产权投诉人并已提起投诉，应被投诉人要求，向被投诉人披露，以便双方处理可能的权利纠纷；
          </p>
          <p>
            （3）根据法律的有关规定，或者行政或司法机构的要求，向第三方或者行政、司法机构披露；
          </p>
          <p>
            （4）如果用户出现违反中国有关法律或者网站政策的情况，需要向第三方披露；
          </p>
          <p>
            （5）为提供用户所要求的产品和服务，而必须和第三方分享该用户的个人信息；
          </p>
          <p>（6）为维护社会公众的利益；</p>
          <p>（7）其他本网站根据法律或者网站政策认为合适的披露。</p>
          <p>
            5.2为服务用户，“布客”可能通过使用用户的个人信息，向该用户提供服务，包括但不限于向该用户发出活动和服务信息等。
          </p>
          <p>
            5.3
            本站可能会与第三方合作向用户提供相关的网络服务，在此情况下，如该第三方同意承担与本站同等的保护用户隐私的责任，
            则本站有权将该用户的注册资料等提供给该第三方。
          </p>
          <p>
            5.4
            在不透露单个用户隐私资料的前提下，本站有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。
          </p>
        </div>

        <div class="user_agreement_contain_six">
          <p>六、版权声明</p>
          <p>
            6.1
            本站的文字、图片、音频、视频等版权均归上书台科技发展(深圳)有限公司享有或与作者共同享有，未经本站许可，不得任意转载。
          </p>
          <p>
            6.2
            本站特有的标识、版面设计、编排方式等版权均属上书台科技发展(深圳)有限公司享有，未经本站许可，不得任意复制或转载。
          </p>
          <p>
            6.3
            使用本站的任何内容均应注明“来源于布客”及署上作者姓名，按法律规定需要支付稿酬的，应当通知本站及作者及支付稿酬，并独立承担一切法律责任。
          </p>
          <p>
            6.4
            本站享有所有作品用于其它用途的优先权，包括但不限于网站、电子杂志、平面出版等，但在使用前会通知作者，并按同行业的标准支付稿酬。
          </p>
          <p>6.5 恶意转载本站内容的，本站保留将其诉诸法律的权利。</p>
          <p>
            6.6
            本站所有内容仅代表作者自己的立场和观点，与本站无关，由作者本人承担一切法律责任。
          </p>
        </div>

        <div class="user_agreement_contain_seven">
          <p>七、责任声明</p>
          <p>
            7.1
            用户明确同意：其使用本站网络服务所存在的风险及一切后果将完全由用户本人承担，本站对此不承担任何责任。
          </p>
          <p>
            7.2
            本站无法保证网络服务一定能满足用户的要求，也不保证网络服务的及时性、安全性、准确性。
          </p>
          <p>
            7.3
            本站不保证为方便用户而设置的外部链接的准确性和完整性。同时，对于该等外部链接指向的不由
            本站实际控制的任何网页上的内容，本站不承担任何责任。对于任何包含、经由或链接、下载或从任何与有关
            本网站所获得的任何内容、信息或广告，本站不声明或保证其正确性或可靠性；并且对于用户经本站上的内容、
            广告、展示而购买、取得的任何产品、信息或资料，本站不负保证责任；用户自行负担使用本站的风险。
          </p>
          <p>
            7.4
            对于因不可抗力或本站不能控制的原因造成的网络服务中断或其它缺陷，本站不承担任何责任，
            但将尽力减少因此而给用户造成的损失和影响。用户自行承担一切因自身行为而直接或者间接导致的民事或刑事法律责任。
          </p>
          <p>
            7.5
            对于本站向用户提供的下列产品或者服务的质量缺陷本身及其引发的任何损失，本站无需承担任何责任：
          </p>
          <p>（1）本站向用户免费提供的各项网络服务；</p>
          <p>（2）本站向用户赠送的任何产品或者服务。</p>
          <p>
            7.6
            未经本站的授权或许可，任何用户不得借用本站的名义从事任何商业活动，也不得将本站作为从事商业活动的场所、
            平台或其他任何形式的媒介。禁止将本站用作从事各种非法活动的场所、平台或者其他任何形式的媒介。违反者若触犯法律，
            一切后果自负，本站不承担任何责任。
          </p>
          <p>7.7 本站保留删除站内各类不符合规定点评而无需通知用户的权利。</p>
          <p>
            7.8
            本站有权于任何时间暂时或永久修改或终止本服务（或其任何部分），而无论其通知与否，
            本站对用户和任何第三人均无需承担任何责任。
          </p>
          <p>7.9 本站有权但无义务，改善或更正本站任何部分之任何疏漏、错误。</p>
          <p>
            7.10
            本站内相关信息内容仅代表发布者的个人观点，并不表示本站赞同其观点或证实其描述，本站不承担由此引发的法律责任。
          </p>
        </div>

        <div class="user_agreement_contain_eight">
          <p>八、附则</p>
          <p>
            8.1 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。
          </p>
          <p>
            8.2
            如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。
          </p>
          <p>8.3 本协议解释权及修订权归上书台科技发展(深圳)有限公司所有。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['showLogin', 'showRegister'],
  data () {
    return {
      options: [
        {
          value: '86',
          label: '中国大陆+86'
        },
        {
          value: '852',
          label: '中国香港+852'
        },
        {
          value: '853',
          label: '中国澳门+853'
        },
        {
          value: '886',
          label: '中国台湾+886'
        }
      ],
      value: '86',

      registerPhoneNumber: '',
      registerCaptcha: '',
      registerEmail: '',
      regitserPassword: '',
      isChecked: true,

      sms_time: 60,
      start_interval: null,
      isCountingDown: false,

      isShowRegisterBox: true,
      isShowTermsOfService: false,
      isShowPrivacyPolicy: false
    }
  },
  computed: {
    smsButtonText () {
      if (this.isCountingDown) {
        return `${this.sms_time}S`
      }
      return '获取验证码'
    }
  },
  methods: {
    // 获取验证码
    getPhoneCaptcha () {
      const phone = this.registerPhoneNumber.trim()
      if (!phone) {
        this.$message({
          message: '手机号不能为空',
          type: 'error',
          duration: 1000
        })
      } else if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(phone)) {
        this.$message({
          message: '手机号格式不正确',
          type: 'error',
          duration: 1000
        })
      } else {
        this.apiGetPhoneCaptcha(phone)
      }
    },
    toLogin () {
      this.$emit('toLogin')
    },
    apiGetPhoneCaptcha (phone) {
      const params = {
        phone: phone,
        type: 1 // 1.为注册 2.为绑定 3.为找回登录密码 4.获取旧手机验证码 5.获取新手机验证码 6.找回支付密码
      }
      fetch(
        'https://api.bookor.com.cn/index.php/mobile/Connect/get_sms_captcha',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        }
      )
        .then((response) => {
          if (response.ok) {
            return response.json()
          }
          throw new Error('Network response was not ok.')
        })
        .then((data) => {
          if (data.code == 200) {
            if (!this.isCountingDown) {
              this.isCountingDown = true
              this.start_interval = setInterval(() => {
                this.sms_time--
                if (this.sms_time <= 0) {
                  clearInterval(this.start_interval)
                  this.isCountingDown = false
                  this.sms_time = 60
                }
              }, 1000)
            }
          } else {
            this.$message({
              message: data.message,
              type: 'error',
              duration: 1000
            })
          }
        })
        .catch((error) => {
          console.error('There was a problem with the network request:', error)
        })
    },

    // 注册
    toRegister () {
      const phone = this.registerPhoneNumber.trim()
      const capture = this.registerCaptcha.trim()
      const email = this.registerEmail.trim()
      const password = this.regitserPassword.trim()
      const isChecked = this.isChecked

      if (!phone) {
        this.$message({
          message: '手机号不能为空',
          type: 'error',
          duration: 1000
        })
      } else if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(phone)) {
        this.$message({
          message: '手机号格式不正确',
          type: 'error',
          duration: 1000
        })
      } else if (!capture) {
        this.$message({
          message: '验证码不能为空',
          type: 'error',
          duration: 1000
        })
      } else if (
        email &&
        !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/.test(email)
      ) {
        this.$message({
          message: '邮箱格式不正确',
          type: 'error',
          duration: 1000
        })
      } else if (!password) {
        this.$message({
          message: '密码不能为空',
          type: 'error',
          duration: 1000
        })
      } else if (
        !/^\S*([a-zA-Z]+\S*[0-9]+|[0-9]+\S*[a-zA-Z]+)\S*$/.test(password)
      ) {
        this.$message({
          message: '密码格式不正确',
          type: 'error',
          duration: 1000
        })
      } else if (!isChecked) {
        this.$message({
          message: '请同意用户协议',
          type: 'error',
          duration: 1000
        })
      } else {
        // 进行其他操作
        const params = {
          phone: phone,
          sms_captcha: capture,
          email: email,
          password: password
        }
        this.apiToRegister(params)
      }
    },
    apiToRegister (params) {
      fetch('https://api.bookor.com.cn/index.php/api/login/pcregister', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      })
        .then((response) => {
          if (response.ok) {
            return response.json()
          }
          throw new Error('Network response was not ok.')
        })
        .then((data) => {
          if (data.code === 200) {
            this.$message({
              message: '注册成功',
              type: 'success',
              duration: 1000
            })

            setTimeout(() => {
              this.goBack()
              this.registerPhoneNumber = ''
              this.registerCaptcha = ''
              this.registerEmail = ''
              this.regitserPassword = ''
              this.isChecked = false
            }, 600)
          } else {
            this.$message({
              message: data.message,
              type: 'error',
              duration: 1000
            })
          }
        })
        .catch((error) => {
          console.error('There was a problem with the network request:', error)
        })
    },

    // 展示用户协议和隐私条款
    showTermsOfService () {
      this.isShowRegisterBox = false
      this.isShowTermsOfService = true
    },
    showPrivacyPolicy () {
      this.isShowRegisterBox = false
      this.isShowPrivacyPolicy = true
    },

    showRegisterBox () {
      this.isShowRegisterBox = true
      this.isShowPrivacyPolicy = false
      this.isShowTermsOfService = false
    },
    // 返回登录页
    goBack () {
      // 修改 showLogin 和 showRegister 属性的值，并通过 updateShown 事件将修改后的值传递回给父组件
      this.$emit('updateShown', {
        showLogin: !this.showLogin,
        showRegister: !this.showRegister
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/login/login.scss";
.register-title-box {
  display: flex;

  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    cursor: pointer;
  }
  .register-title {
    font-size: 16px;
    font-weight: 800;
    display: inline-block;
  }
}

.register-content-box {
  .account_security_phone_number {
    @include gray-input;
  }
  .send_sms {
    @include gray-btn;
    height: 35px;
    margin-left: 5px;
    cursor: pointer;
  }
  .register_btn {
    @include black-btn;
  }
}

.register_line {
  width: 30vw;
  height: 1px;
  border-bottom: 2px solid #d9d9d9;
  margin-top: 10px;
  /* margin-right: 15px; */
  position: relative;
}
.register-phone-box {
  display: flex;
  // margin: 25px 0 20px;
  /* 下拉选框样式 */
  .el-select {
    width: 140px;
  }
}

.register_bind_email {
  width: 300px;
  height: 35px;
  text-align: center;
  border: none;
  border-radius: 5px;
  background-color: #eeeeee;
  margin-top: 15px;
}

.register-form {
  // color: #f6e926;
  color: #3acbf8;
  // margin: 0 5px;
  cursor: pointer;
  // text-decoration: underline;
}

.register-form-content {
  overflow-y: auto;
  height: 340px;
  width: 30vw;
  word-wrap: break-word;
  margin-top: 10px;
  font-size: 15px;
}
.register-form-content::-webkit-scrollbar {
  display: none;
}
.register-btn-box {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

@media (max-width: 700px) {
  .account_security_phone_number {
    width: 60vw !important;
  }
  .register-content-box {
    width: 60vw !important;
  }
  .mobile_box {
    width: 36vw !important;
  }
  .register_line {
    width: 60vw !important;
  }
  .register-form-content {
    width: 60vw !important;
  }
}
</style>
