<template>
  <div class="draft-list-con"  @scroll="handleScrollList">
    <div class="item-draft" v-for="item in list" :key="item.id" @click="handleDraft(item.id)">
      <span class="title">{{ item.title }}</span>
      <el-tooltip class="item" effect="dark" content="删除白板" placement="top">
        <el-popconfirm
          title="确定删除这个白板吗？"
          @confirm="deleteDraft(item.id)"
        >
          <i class="el-icon-delete" slot="reference" @click.stop></i>
        </el-popconfirm>
      </el-tooltip>
    </div>
    <el-empty v-if="!list.length" description="暂无数据"></el-empty>
  </div>
</template>

<script>
import request from '@/http/request'
import { EventBus } from '@/main.js'
export default {
  data () {
    return {
      page: 1,
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      isMore: true, // 是否还有更多
      list: []
    }
  },
  methods: {
    handleScrollList (event) {
      if (!this.isMore) {
        return
      }
      const elem = event.target
      if (elem.scrollTop + elem.clientHeight >= elem.scrollHeight) {
        this.page += 1
        this.getData()
      }
    },
    handleDraft (id) {
      this.$emit('chooseDraft', id)
    },
    deleteDraft (id) {
      request('/api/Draft/deleteDraft', {
        id
      }).then(() => {
        this.page = 1
        this.getData()
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      })
    },
    getData () {
      request('/api/Draft/draftList', {
        page: this.page,
        limit: 10
      }).then((result) => {
        if (!result.length) {
          this.isMore = false
          return
        }
        if (this.page === 1) {
          this.list = result
        } else {
          this.list = [...this.list, ...result]
        }
      })
    }
  },
  mounted () {
    this.getData()

    EventBus.$on('refreshList', () => {
      this.list = []
      this.page = 1
      this.getData()
    })
  }
}
</script>
<style lang="scss" scoped>
.draft-list-con {
  height: 300px;
  background-color: #eeeeee;
  padding: 0px 5px;
  overflow-y: auto;

  .item-draft{
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px;
    font-size: 14px;
    background-color: white;
    align-items: center;
    margin: 5px 0;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    transition: all 0.6s ease-in-out;
    cursor: pointer;
    &:active{
      transform: scale(1.4); /* 放大按钮 */
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
    }
  }
}
</style>
