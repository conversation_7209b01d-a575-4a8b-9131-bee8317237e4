// fetch post + form
const BASE_API = 'https://api.bookor.com.cn/index.php'

export default {
  async postData (url, params) {
    try {
      const queryParams = new URLSearchParams(params)
      const response = await fetch(BASE_API + url, {
        method: 'POST',
        body: queryParams
      })

      if (!response.ok) {
        throw new Error('Network response was not ok.')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('There was a problem with the network request:', error)
      return null
    }
  },
  async getData (url, params) {
    try {
      const queryParams = new URLSearchParams(params)
      const apiUrl = BASE_API + url
      const urlWithParams = `${apiUrl}?${queryParams.toString()}`
      const response = await fetch(urlWithParams)

      if (!response.ok) {
        throw new Error('Network response was not ok.')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('There was a problem with the network request:', error)
      return null
    }
  },

  async postFile (file, params) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      for (const key in params) {
        formData.append(key, params[key])
      }
      const response = await fetch(BASE_API, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Network response was not ok.')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('There was a problem with the network request:', error)
      return null
    }
  },

  async postDataFullUrl (url, params) {
    try {
      const queryParams = new URLSearchParams(params)
      const response = await fetch(url, {
        method: 'POST',
        body: queryParams
      })

      if (!response.ok) {
        throw new Error('Network response was not ok.')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('There was a problem with the network request:', error)
      return null
    }
  }
}
