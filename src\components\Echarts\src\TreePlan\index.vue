
<template>
  <div id="treePlan"></div>
</template>

<script>
import request from '@/http/request';
import G6 from '@antv/g6';

let graph = null
export default {
  props: {
    treeObj: {
      type: Object,
      required: true
    },
    width: {
      type: String
    },
    height: {
      type: String
    },
    isCut: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: true
    },
    zoom: {
      type: Number,
      default: 3
    },
    lineWidth: {
      type: Number,
      default: 300
    }
  },
  methods: {
    async add (item) {
      const currentId = item.getID()
      const params = {
        content: 'new Content',
        mind_map_id: Number(currentId)
      }
      request('/api/MindMap/addMindMap', params).then((result) => {
        const obj = {
          id: result.add_id,
          level: '3',
          content: 'new Content',
          mind_map_id: Number(result.add_id),
          mind_map_pid: Number(currentId),
          mind_map_tpid: Number(currentId),
          type: 'tree-node'
        }
        graph.addChild(obj, currentId)
        this.$message.success('添加成功')
      }).catch(() => {
        this.$message.success('登录失效，重新登录')
        localStorage.removeItem('userinfo')
        this.$router.push('/login')
      })
    },
    async remove (item) {
      const id = item.getID()
      const params = {
        mind_map_id: id.toString()
      }
      request('/api/MindMap/deleteMindMap', params).then((result) => {
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.success('登录失效，重新登录')
        localStorage.removeItem('userinfo')
        this.$router.push('/login')
      })
      graph.removeChild(id)
    },
    async edit (content, id) {
      const currentNode = graph.find('node', (node) => {
        return node.getID() === id
      })
      // 想不通后端的脑回路，内容一致不允许继续保存
      if (currentNode.getModel().content === content) {
        return
      }
      const params = {
        content,
        mind_map_id: id
      }
      currentNode.getModel().content = content
      request('/api/MindMap/editMindMap', params).then((res) => {
        this.$message.success('更新成功')
      }).catch(() => {
        this.$message.success('编辑失败')
      })
    }
  },
  mounted () {
    G6.registerNode(
      'tree-node',
      {
        draw: (cfg, group) => {
          const x = cfg.depth * this.lineWidth
          const rect = group.addShape('rect', {
            attrs: {
              fill: '#525252',
              name: 'tree-rect',
              stroke: '#777',
              x,
              y: 0,
              width: 1,
              height: 1,
              maxWidth: 400, // 设置最大宽度为100像素
              whiteSpace: 'wrap' // 设置 whiteSpace 样式属性为 wrap
            },
            // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
            name: 'rect-shape',
            zIndex: 1
          })
          let content = cfg.content.replace(/(.{25})/g, '$1\n') // 增加换行
          content = content.replace(/<br\/>/g, '')
          const text = group.addShape('text', {
            attrs: {
              name: 'tree-text',
              text: content.length > 51 ? `${content.substring(0, 50)}...` : content,
              x,
              textAlign: 'left',
              textBaseline: 'middle',
              fill: '#fff',
              cursor: 'pointer',
              width: 400,
              maxWidth: 400, // 设置最大宽度为100像素
              whiteSpace: 'wrap' // 设置 whiteSpace 样式属性为 wrap
            },
            // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
            name: 'text-shape',
            zIndex: 2
          })
          const bbox = text.getBBox()
          const hasChildren = cfg.children && cfg.children.length > 0
          rect.attr({
            x: x - 4,
            y: -bbox.height / 2 - 6,
            width: bbox.width + (hasChildren ? 38 : 16),
            height: bbox.height + 12
          })
          text.attr({
            x: x + (hasChildren ? 20 : 0)
          })
          if (this.isEdit) {
            group.addShape('text', {
              attrs: {
                text: '+',
                x: x + bbox.width + (hasChildren ? 22 : 0),
                y: -bbox.height / 2 + 6,
                opacity: 0,
                cursor: 'pointer',
                name: 'add-item',
                stroke: '#5B8FF9'
              },
              name: 'add-item',
              zIndex: 5
            })
            if (!hasChildren) {
              group.addShape('text', {
                attrs: {
                  text: '-',
                  x: x + bbox.width + (hasChildren ? 22 : 0),
                  y: bbox.height / 2 + 6,
                  r: 6,
                  stroke: '#f00',
                  name: 'remove-item',
                  cursor: 'pointer',
                  opacity: 0
                },
                name: 'remove-item',
                zIndex: 5
              })
            }
          }

          if (hasChildren) {
            group.addShape('marker', {
              attrs: {
                x: x + 8,
                y: 0,
                r: 6,
                symbol: cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
                stroke: '#fff',
                // fill: '#fff',
                lineWidth: 2,
                zIndex: 4,
                cursor: 'pointer'
              },
              // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
              className: 'collapse-icon'
            })
          }
          return rect
        }
      },

      'single-node'
    )

    // 自定义behavior 用以解决点击节点会同时选中和展开的问题
    // 参照 https://github.com/antvis/G6/issues/736
    G6.registerBehavior('custom-collapse-expand', {
      getDefaultCfg () {
        return {
          onChange () { }
        }
      },
      getEvents () {
        return {
          'node:click': 'onNodeClick'
        }
      },
      onNodeClick (e) {
        if (e.target.cfg.type !== 'marker') {
          return
        }
        const item = e.item
        // 如果节点进行过更新，model 会进行 merge，直接改 model 就不能改布局，所以需要去改源数据
        const sourceData = graph.findDataById(item.get('id'))
        const children = sourceData.children
        // 叶子节点的收缩和展开没有意义
        if (!children || children.length === 0) {
          return
        }
        const collapsed = !sourceData.collapsed
        if (!this.shouldBegin(e, collapsed)) {
          return
        }
        sourceData.collapsed = collapsed
        item.getModel().collapsed = collapsed
        graph.emit('itemcollapsed', { item: e.item, collapsed })
        if (!this.shouldUpdate(e, collapsed)) {
          return
        }
        try {
          this.onChange(item, collapsed)
        } catch (e) {
          console.warn('G6 自 3.0.4 版本支持直接从 item.getModel() 获取源数据(临时通知，将在之后版本清除)', e)
        }
        graph.refreshLayout()
      }
    })

    const width = window.innerWidth * 0.98
    const height = window.innerHeight * 0.98

    const tooltip = new G6.Tooltip({
      offsetX: 10,
      offsetY: 20,
      getContent (e) {
        const outDiv = document.createElement('div')
        outDiv.style.width = '180px'
        outDiv.innerHTML = `
        <ul>
          <li>${e.item.getModel().content}</li>
        </ul>`
        return outDiv
      },
      itemTypes: ['node']
    })

    graph = new G6.TreeGraph({
      container: 'treePlan',
      width,
      height,
      linkCenter: true,
      plugins: [tooltip],
      // fitView: true, // 添加上这个就会导致增加和删除重置位置
      modes: {
        default: [
          {
            type: 'custom-collapse-expand',
            onChange: function onChange (item, collapsed) {
              var data = item.get('model')
              const icon = item.get('group').findByClassName('collapse-icon')
              if (collapsed) {
                icon.attr('symbol', G6.Marker.expand)
              } else {
                icon.attr('symbol', G6.Marker.collapse)
              }
              data.collapsed = collapsed
              return true
            }
          },
          'drag-canvas',
          'zoom-canvas'
        ]
      },
      defaultNode: {
        type: 'tree-node',
        anchorPoints: [
          [1, 0.5],
          [1, 0]
        ]
      },
      defaultEdge: {
        type: 'cubic-horizontal',
        size: 2,
        color: '#FFF'
      },
      layout: {
        type: 'compactBox',
        direction: 'LR',
        getId: function getId (d) {
          return d.id
        },
        getHeight: function getHeight () {
          return 16
        },
        getWidth: function getWidth () {
          return 16
        },
        getVGap: function getVGap () {
          return 20
        },
        getHGap: function getHGap () {
          return 80
        }
      }
    })
    graph.data(this.treeObj)
    graph.render()
    graph.fitView()
    graph.zoom(this.zoom, { x: width / 2, y: height / 2 }, true, { duration: 500 })

    graph.on('node:mouseenter', evt => {
      const item = evt.item
      const group = item.get('group')

      const list = group.findAll((item) => {
        return item.cfg.zIndex === 5
      })
      list.forEach(element => {
        element.attr({
          opacity: 1
        })
      })
    })
    graph.on('node:mouseleave', evt => {
      const item = evt.item
      const group = item.get('group')

      const list = group.findAll((item) => {
        return item.cfg.zIndex === 5
      })
      list.forEach(element => {
        element.attr({
          opacity: 0
        })
      })
    })
    if (this.isEdit) {
      graph.on('node:click', async evt => {
        const { item } = evt
        if (evt.target.cfg.type === 'marker') {
          return
        }
        if (evt.target.cfg.name === 'add-item') {
          this.add(item)
          return
        }
        if (evt.target.cfg.name === 'remove-item') {
          this.remove(item)
          return
        }
        const model = item.get('model')
        const {
          x,
          y
        } = item.calculateBBox()
        const realPosition = evt.currentTarget.getClientByPoint(x, y)
        const el = document.createElement('div')
        el.style.zIndex = '10'
        el.style.fontSize = '8px'
        el.style.position = 'fixed'
        el.style.top = realPosition.y + 'px'
        el.style.left = realPosition.x + 'px'
        el.style.transformOrigin = 'top left'
        el.style.transform = `scale(${evt.currentTarget.getZoom()})`
        const textarea = document.createElement('textarea')
        textarea.style.padding = '2px'
        textarea.style.border = '1px solid #8faadc'
        textarea.style.resize = 'none'
        textarea.value = model.content
        const bbox = item.getBBox()
        textarea.style.width = `${bbox.width}px`
        textarea.style.height = `${bbox.height}px`
        textarea.className = 'dice-textarea'
        el.className = 'dice-textarea'
        el.appendChild(textarea)
        document.body.appendChild(el)
        textarea.focus()
        const destroyEl = () => {
          document.body.removeChild(el)
        }

        const clickEvt = (event) => {
          if (!(event.target && event.target.className && event.target.className.includes('dice-textarea'))) {
            window.removeEventListener('mousedown', clickEvt)
            window.removeEventListener('scroll', clickEvt)
            const group = item.get('group')
            const current = group.find((item) => {
              return item.attr('name') === 'tree-text'
            })

            const rect = group.find((item) => {
              return item.attr('name') === 'tree-rect'
            })

            const addItem = group.find((item) => {
              return item.attr('name') === 'add-item'
            })
            const removeItem = group.find((item) => {
              return item.attr('name') === 'remove-item'
            })

            const text = textarea.value.replace(/\n/g, '')
            const content = text.replace(/(.{20})/g, '$1\n') // 增加换行
            current.attr({
              text: content
            })
            const bbox = current.getBBox()

            rect.attr({
              y: -bbox.height / 2 - 6,
              width: bbox.width + 18,
              height: bbox.height + 12
            })
            addItem.attr({
              x: rect.attr('x') + bbox.width + 6
            })
            removeItem.attr({
              x: rect.attr('x') + bbox.width + 6
            })

            graph.layout(false)
            graph.off('wheelZoom', clickEvt)
            destroyEl()
            const id = item.getID()
            this.edit(content, id)
          }
        }

        graph.on('wheelZoom', clickEvt)
        window.addEventListener('mousedown', clickEvt)
        window.addEventListener('scroll', clickEvt)
        textarea.addEventListener('keyup', (event) => {
          if (event.key === 'Enter') {
            clickEvt({
              target: {}
            })
          }
        })
        textarea.addEventListener('blur', (event) => {
          event.target.remove()
        })
      })
    }
  }
}

</script>
<style lang="scss" scoped>
#treePlan{
  background-color: #fff;
}
</style>
