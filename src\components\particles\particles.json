{"particles": {"number": {"value": 100, "density": {"enable": true, "value_area": 800}}, "color": {"value": "#ffffff"}, "shape": {"type": "circle", "stroke": {"width": 3, "color": "#283152"}, "polygon": {"nb_sides": 5}, "image": {"src": "img/github.svg", "width": 100, "height": 100}}, "opacity": {"value": 0.8, "random": false, "anim": {"enable": false, "speed": 2, "opacity_min": 0.2, "sync": false}}, "size": {"value": 4, "random": false, "anim": {"enable": false, "speed": 30, "size_min": 0.1, "sync": false}}, "line_linked": {"enable": false, "distance": 150, "color": "#edd1d8", "opacity": 0.9, "width": 2}, "move": {"enable": true, "speed": 1, "direction": "none", "random": true, "straight": true, "out_mode": "bounce", "bounce": false, "attract": {"enable": true, "rotateX": 400, "rotateY": 400}}}, "interactivity": {"detect_on": "Window", "events": {"onhover": {"enable": true, "mode": "grab"}, "onclick": {"enable": true, "mode": "grab"}, "resize": true}, "modes": {"grab": {"distance": 140, "line_linked": {"opacity": 0}}, "bubble": {"distance": 4, "size": 1, "duration": 2, "opacity": 8, "speed": 5}, "repulse": {"distance": 200, "duration": 0.4}, "push": {"particles_nb": 4}, "remove": {"particles_nb": 2}}}, "retina_detect": true}