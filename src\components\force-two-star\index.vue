<template>
  <div>
    <div id="3d-graph" @click="handleClick"></div>

    <div class="node-active" v-show="activeLabel.text" :style="getStyle">
      <p>{{activeLabel.text}}</p>
    </div>
  </div>
</template>

<script lang="js">
// import ForceGraph3D from 'force-graph'
// import ForceGraph3D from 'force-graph'
import * as THREE from 'three'
import ForceGraph3D from '3d-force-graph'
import { getBondNode, getBondTitle } from './utils'
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass'
import { EventBus } from '@/main.js'
import { mapState, mapMutations } from 'vuex'
import { CSS2DRenderer, CSS2DObject } from './CSS2DRenderer'

const Graph = null
const isRotationActive = true

const isDragging = false // 是否正在拖动
const startX = 0 // 鼠标按下时的X坐标
const scrollLeft = 0 // 当前滚动的距离
const bloomPass = null

export default {
  data: function () {
    return {
      activeLabel: {
        text: '',
        x: 0,
        y: 0
      },
      lineTitle: null,
      isShowInfo: false,
      currentKeyword: '',
      currentBookId: '',
      currentNodeId: '',
      currentBondList: [] // 当前关联列表
    }
  },
  computed: {
    ...mapState('book', ['choosedBookId', 'isShowBookDetail']),
    ...mapState('indexsearch', {
      choosedSearchItem: (state) => state.chooseSearchItem
    }),
    getStyle () {
      return {
        left: `${this.activeLabel.x}px`,
        top: `${this.activeLabel.y}px`
      }
    }
  },
  props: ['forceData', 'bondList'],
  watch: {
    choosedBookId (newVal) {
      // 在这里处理 choosedBookId 的变化
      this.setShowDetails()
    },
    isShowBookDetail (newVal) {
      if (newVal === 'false') {
        // 解除锁定
        this.isLocked = false
      }
    },
    choosedSearchItem (value) {
      this.isLocked = false
      this.chooseSearchItem(value)
    }
  },
  methods: {
    ...mapMutations({
      setChosenBookId: 'book/SET_CHOSED_BOOK_ID',
      setShowDetails: 'book/showDetails'
    }),
    handleClick () {
      this.isShowInfo = false
    },
    nodeAnimation (node) {
      setTimeout(() => {
        bloomPass.strength = 2
        this.activeLabel = {
          text: node.user,
          x: window.innerWidth / 2 - (node.user.length * 8),
          y: (window.innerHeight / 2) - 50
        }
      }, 3100)
    },
    init () {
      Graph = ForceGraph3D({
        extraRenderers: [new CSS2DRenderer()]
      })
      (document.getElementById('3d-graph'))
        .graphData(this.forceData)
        .nodeAutoColorBy('group')
        .nodeThreeObject(node => {
          const nodeEl = document.createElement('div')
          nodeEl.textContent = node.user
          nodeEl.style.color = node.color
          nodeEl.className = 'node-label'
          return new CSS2DObject(nodeEl)
        })
        .nodeThreeObjectExtend(true)
    },
    // 清除页面上的截面标签
    clearObjectsLabels () {
      // 移除之前所有的标识符元素
      // isShowInfo = false;
      const visibleMarkerList =
        document.getElementsByClassName('visible-marker')
      while (visibleMarkerList.length > 0) {
        visibleMarkerList[0].parentNode.removeChild(visibleMarkerList[0])
      }
    },
    clearShow () {
      this.isShowInfo = false
      this.isShowInfoArrayLocked = false
      this.isShowInfoArray = false
      this.entryids = {
        book_keywords_id: '',
        member_keywords_id: ''
      }
      this.isShowEntryAddClassify = false
    },
    showBookDetail (id) {
      if (id) {
        this.setChosenBookId(id)
        const previousLabel = document.getElementById('object-label')
        if (previousLabel) {
          previousLabel.remove()
        }
        this.keyword = ''
        this.clearObjectsLabels()
        this.clearShow()
      } else {
        this.$message({
          message: '暂无对应图书',
          type: 'error',
          duration: 1000
        })
      }
    }
  },
  mounted () {
    this.init()
    // const container = document.getElementById('book-container')
    // container.addEventListener('mousedown', function (event) {
    //   isDragging = true
    //   startX = event.clientX
    //   scrollLeft = container.scrollLeft
    // })
    // container.addEventListener('mousemove', function (event) {
    //   if (!isDragging) return

    //   var distance = event.clientX - startX
    //   container.scrollLeft = scrollLeft - distance
    // })

    // container.addEventListener('mouseup', function (event) {
    //   isDragging = false
    // })

    // EventBus.$on('openDetail', (item) => {
    //   this.isShowInfo = true
    //   const { nodes } = Graph.graphData()
    //   let node = null
    //   if (item.type && item.type === 'KEYWORD_TITLE') {
    //     const currentBond = getBondTitle(this.bondList, item)
    //     const first = currentBond.point_list[0]
    //     item.user = first.keywords
    //     this.currentBondList = currentBond.point_list.filter(child => child.keywords !== item.user)
    //     node = nodes.find(i => {
    //       if (first.book_keywords_id) {
    //         return i.bookId === first.book_keywords_id
    //       } else {
    //         return i.nodeId === first.member_keywords_id
    //       }
    //     })
    //     this.lineTitle = {
    //       title: currentBond.title[0].lines_title,
    //       id: currentBond.title[0].mix_keywords_lines_id
    //     }
    //   } else {
    //     node = nodes.find(i => {
    //       if (item.bookId) {
    //         return i.bookId === (item.bookId)
    //       } else {
    //         return i.nodeId === item.nodeId
    //       }
    //     })
    //     const currentBond = getBondNode(this.bondList, node)
    //     if (currentBond) {
    //       this.currentBondList = currentBond.point_list.filter(child => child.keywords !== item.user)
    //     } else {
    //       this.currentBondList = []
    //     }
    //     if (node.titleId) {
    //       this.lineTitle = {
    //         title: node.title,
    //         id: node.titleId
    //       }
    //     } else {
    //       this.lineTitle = null
    //     }
    //   }
    //   this.currentKeyword = item.user
    //   this.currentBookId = node.bookId
    //   this.currentNodeId = node.nodeId
    //   this.currentKeyword = node.user
    //   this.nodeAnimation(node)
    // })
  }
}

</script>

<style lang="scss">
.book-container{
   position: absolute;
   bottom: 15px;
    /* z-index: 9999999; */
    bottom: 0;
    display: flex;
    max-width: 100%;
    overflow-x: scroll;
    padding-top: 30px;
    .keyword-title{
      position: absolute;
      color: #fff;
      top: 0;
      left: 12px;
      z-index: 9;
    }
}
.node-label {
    font-size: 12px;
    padding: 1px 4px;
    border-radius: 4px;
    background-color: rgba(149, 42, 42, 0.5);
    user-select: none;
  }
// .node-label{
//   color: rgb(234, 178, 4);
//   background-color: black;
//   border: 1px solid rgb(154, 182, 200);
//   padding: 2px 4px;
// }
.node-active{
  position: absolute;
  display: inline-block;
  padding: 2px 4px;
  color: rgb(234, 178, 4);
  border: 1px solid rgb(154, 182, 200);
}
</style>
