const indexsearch = {
  namespaced: true,
  state: {
    isNotFirstSearch: false,
    select: null,
    sceneChildren: [],

    chooseSearchItem: null
  },
  mutations: {
    setFirstSearch (state, val) {
      state.isNotFirstSearch = val
    },
    setSceneChildren (state, children) {
      state.sceneChildren = children
    },
    setChooseSearchItem (state, item) {
      state.chooseSearchItem = item
    }
  },
  actions: {}
}

export default indexsearch
