import { Graph } from '@antv/x6'
import { History } from '@antv/x6-plugin-history'
import { getId, getRectPort, getCirPort, typeLine } from './util'

export default (await import('vue')).defineComponent({
  data () {
    return {
      graph: null,
      history: null,
      canRedo: false,
      canUndo: false,
      drawer: false,
      edgeVisible: false,
      title: '',
      currentNode: null,
      currentEdge: null
    }
  },
  components: {
    Node
  },
  methods: {
    addRect () {
      this.graph.addNode({
        id: getId(),
        shape: 'rect',
        x: 100,
        y: 40,
        label: 'Hello',
        width: 100,
        height: 40,
        attrs: {
          body: {
            stroke: '#ffa940',
            strokeWidth: 1,
            fill: '#ffd591',
            rx: 5,
            ry: 5
          },
          text: {
            fill: '#000',
            fontSize: 12
          }
        },
        ports: getRectPort(100, 40)
      })
    },
    addCir () {
      this.graph.addNode({
        id: getId(),
        shape: 'circle',
        x: 300,
        y: 140,
        label: 'world',
        width: 60,
        height: 60,
        attrs: {
          body: {
            stroke: '#ffa940',
            strokeWidth: 1,
            fill: '#ffd591',
            rx: 5,
            ry: 5
          },
          text: {
            fill: '#000',
            fontSize: 12
          }
        },
        ports: getCirPort()
      })
    },
    handleCommand (command) {
      if (command === 'rectangle') {
        this.addRect()
      } else {
        this.addCir()
      }
    },
    redo () {
      this.graph.redo()
    },
    undo () {
      this.graph.undo()
    },
    updateNode (node) {
      const currentNode = this.graph.getCellById(node.id)
      currentNode.setLabel(node.label)
      currentNode.attr('text/fill', node.textColor)
      currentNode.attr('body/fill', node.backgroundColor)
      currentNode.setProp('size', { width: node.width, height: node.height })
      this.drawer = false
    },
    updateEdge (edge) {
      const currentEdge = this.graph.getCellById(edge.id)
      currentEdge.setLabelAt(0, edge.label)
      currentEdge.attr('line/stroke', edge.color)
      if ([5, 6].includes(edge.type)) {
        currentEdge.attr('line/strokeDasharray', '5 3')
      } else {
        currentEdge.attr('line/strokeDasharray', '0')
      }
      const line = typeLine(edge.type)
      if (line.sourceMarker || line.targetMarker) {
        currentEdge.attr({
          line: {
            sourceMarker: line.sourceMarker,
            targetMarker: line.targetMarker
          }
        })
      }
      this.edgeVisible = false
    },
    save () {
      const json = this.graph.toJSON()
      if (!this.title) {
        return this.$message({
          message: '标题不能为空',
          type: 'error',
          duration: 1000
        })
      }
      if (!json.cells.length) {
        return this.$message({
          message: '白板内容为空',
          type: 'error',
          duration: 1000
        })
      }
      window.localStorage.setItem('graph_data', JSON.stringify(json))
      this.$message({
        message: '保存成功',
        type: 'success',
        duration: 1000
      })
    }
  },
  mounted () {
    const w = document.querySelector('.draft-container').clientWidth
    const h = window.innerHeight * 0.85
    this.graph = new Graph({
      container: document.getElementById('container'),
      grid: true,
      width: w,
      height: h,
      background: {
        color: '#F2F7FA'
      }
    })
    this.graph.use(
      new History({
        enabled: true
      })
    )
    this.graph.on('history:change', () => {
      this.canRedo = this.graph.canRedo()
      this.canUndo = this.graph.canUndo()
    })
    this.graph.on('node:click', (e) => {
      this.drawer = true
      this.currentNode = e.node
    })
    this.graph.on('edge:click', (e) => {
      this.edgeVisible = true
      this.currentEdge = e.edge
    })
    this.graph.on('node:mouseenter', ({ node }) => {
      const { width } = node.getBBox()
      let args = {
        x: width,
        y: 0
      }
      if (node.shape === 'circle') {
        args = {
          x: width,
          y: 5
        }
      }
      node.addTools({
        name: 'button-remove',
        args
      })
      node.addTools({
        name: 'boundary',
        args: {
          attrs: {
            fill: '#7c68fc',
            stroke: '#9254de',
            strokeWidth: 1,
            fillOpacity: 0.2
          }
        }
      })
      const { ports } = getRectPort(node.width, node.height)
      node.addPorts(ports)
    })
    this.graph.on('node:mouseleave', ({ node }) => {
      node.removeTools()
    })
    this.graph.on('edge:mouseenter', ({ edge }) => {
      edge.addTools({
        name: 'button-remove'
      })
    })
    this.graph.on('edge:mouseleave', ({ edge }) => {
      edge.removeTools()
    })
    const data = window.localStorage.getItem('graph_data')
    if (data) {
      this.graph.fromJSON(JSON.parse(data))
    }
  }
})
