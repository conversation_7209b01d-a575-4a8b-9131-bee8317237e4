<template>
  <div class="basic-layout">
    <div class="top-bar">
      <div class="nav">
        <router-link class="item" to="/index">
          <span class="txt">快讯</span>
        </router-link>
        <router-link class="item" to="/index">
          <span class="txt">视频</span>
        </router-link>
        <router-link class="item" to="/index">
          <span class="txt">书评</span>
        </router-link>
        <router-link class="item" to="/index">
          <span class="txt">书单</span>
        </router-link>
        <router-link class="item" to="/topic">
          <span class="txt">话题</span>
        </router-link>
        <router-link class="item" to="/editor">
          <span class="txt">笔记</span>
        </router-link>
        <router-link class="item" to="/index" style="border: 0">
          <img class="search-icon" src="@/assets/img/icon-search.png" alt="" />
        </router-link>
      </div>

      <div class="nav-mobile">
        <div @click="handleShowAllRouter">
          <img
            style="width: 30px; position: absolute; right: 14px"
            src="./img/mobile-router.png"
            alt=""
          />
        </div>
        <div v-show="isShowAllRouter" class="nav-mobile-box">
          <router-link class="item" to="/index">
            <span class="txt">快讯</span>
          </router-link>
          <router-link class="item" to="/index">
            <span class="txt">视频</span>
          </router-link>
          <router-link class="item" to="/index">
            <span class="txt">书评</span>
          </router-link>
          <router-link class="item" to="/index">
            <span class="txt">书单</span>
          </router-link>
          <router-link class="item" to="/topic">
            <span class="txt">话题</span>
          </router-link>
          <router-link class="item" to="/editor">
            <span class="txt">笔记</span>
          </router-link>
          <router-link class="item" to="/index" style="border: 0">
            <img
              class="search-icon"
              src="@/assets/img/icon-search.png"
              alt=""
            />
          </router-link>
        </div>
      </div>
      <!-- <div class="personal-box">
        <img class="photo" src="@/assets/img/icon_robot.png" />
        <div class="list">
          <div class="item" v-for="(item, index) in list" :key="index">
            <span class="txt">{{ item.name }}</span>
          </div>
        </div>
      </div> -->
    </div>
    <router-view />
  </div>
</template>

<script>
export default {
  name: "BasicLayout",
  data() {
    return {
      list: [
        { name: "账户" },
        { name: "待租售" },
        { name: "已租售" },
        { name: "订单" },
        { name: "藏书" },
        { name: "书评" },
        { name: "笔记" },
        { name: "话题" },
      ],
      isShowAllRouter: false,
    };
  },
  methods: {
    handleShowAllRouter() {
      this.isShowAllRouter = !this.isShowAllRouter;
      this.isRotated = !this.isRotated;
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/assets/scss/top.scss";
</style>
