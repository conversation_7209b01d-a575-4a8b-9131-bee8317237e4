<template>
  <div class="section">
    <div class="page-box">
        <div class="title-part1">传统阅读方式的弊端</div>
        <div class="content-box-part1">
            <div class="content-inside content_box">
                <div class="content-inside-item" @click="threePage.currentNum = 1"
                    :class="threePage.currentNum == 1 ? 'item_box' : ''">
                    <div style="color: #fff" class="item_title">阅读被动</div>
                    <div class="item">
                        传统阅读以读懂作者，理解其思想为目的。读者已预设自己被动“受教”的地位。并不能为读者自动带来客观批判立场。
                    </div>
                </div>
                <div class="content-inside-item" @click="threePage.currentNum = 2"
                    :class="threePage.currentNum == 2 ? 'item_box' : ''">
                    <div style="color: #fff; margin-top: 5px" class="item_title">难持续专注</div>
                    <div class="item">
                        传统方式要求有相对完整、有规律的阅读时间。很难利用是生活中的碎片时间持续阅读。
                    </div>
                </div>
                <div class="content-inside-item" @click="threePage.currentNum = 3"
                    :class="threePage.currentNum == 3 ? 'item_box' : ''">
                    <div style="color: #fff; margin-top: 5px" class="item_title">
                        知识缺横向关联
                    </div>
                    <div class="item">
                        传统阅读以完整的文本为单位，缺乏和其它文本的关联和主动比较机制，知识全局观难以形成。
                    </div>
                </div>
                <div class="content-inside-item" @click="threePage.currentNum = 4"
                    :class="threePage.currentNum == 4 ? 'item_box' : ''">
                    <div style="color: #fff; margin-top: 5px" class="item_title">
                        逻辑分析能力要求高
                    </div>
                    <div class="item">
                        快速了解文本的结构和作者的思想框架依赖读者较强的综合分析能力，这在快节奏高密度信息化条件下，越来越难做到。
                    </div>
                </div>
                .
            </div>
            <div class="image-list">
                <img v-if="threePage.currentNum == 2" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/%E5%9B%B0%E6%AD%BB.d748b9e9.png"/>
                <img v-if="threePage.currentNum == 1" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/%E8%AE%B0%E4%B8%8D%E4%BD%8F.4b28ce07.png"/>
                <img v-if="threePage.currentNum == 3" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/%E8%BF%9E%E6%8E%A5.png"/>
                <img v-if="threePage.currentNum == 4" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/%E9%80%BB%E8%BE%91%E6%80%9D%E7%BB%B4.png"/>
            </div>
        </div>
    </div>
</div>
</template>

<script>
export default {
  props: ['threePage']
}
</script>

<style lang="scss" scoped>
.title-part1 {
    color: white;
    font-size: 26px;
    font-weight: 600;
    text-align: center;
    margin-top: 5%;
}
.content-box-part1{
  display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    max-width: 900px;
    margin:  0 auto;
    margin-top: 6%;
}
.content-inside{
  width: 50%;
}
.content-inside-item{
  font-size: 18px;
  font-weight: 500;
  text-align: justify;
  .item{
    color: #fff
  }
  .item_title {
    color: #a583f1;
  }
}
.image-list {
  width: 50%;
  img {
    margin-left: 60px; width: 30vw
  }
}
@media only screen and (max-width: 420px) {
  .content-box-part1{
    flex-direction: column;
    width: 100%;
  }
  .content-inside{
    width: 100%;
  }
  .image-list{
    width: 100%;
    img{
      width: 100%;
      margin-left: 0;
    }
  }
}
</style>
