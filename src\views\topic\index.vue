<template>
  <div class="topic-index">
    <topic-detail v-if="isDetail" @handleList="handleList" />
    <div v-if="!isDetail">
      <div
        ref="label"
        class="label"
        v-for="(item, index) in topicList"
        :key="index"
        :class="{ related: item.type === 1 }"
        :style="{ left: item.x, top: item.y }"
        @mouseover.stop="handleStopMove(index)"
        @mouseleave.stop="handleMove(index)"
      >
        <div v-if="item.isLabel" class="txt" @click="handleDetail(index)">
          {{ item.title }}
        </div>
        <span v-if="item.isLabel" class="arrow"></span>
        <span
          class="point"
          :class="{
            orange: item.comment_total > 30,
            red: item.comment_total > 50,
          }"
        ></span>
      </div>
    </div>
  </div>
</template>

<script>
import TopicDetail from './detail.vue'
import request from '@/http/request'

export default {
  name: 'TopicIndex',
  components: {
    TopicDetail
  },
  data () {
    return {
      topicList: [
        {
          type: 1,
          title: '相关话题1',
          messageNum: 10,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 1,
          title: '相关话题2',
          messageNum: 20,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 1,
          title: '相关话题3',
          messageNum: 30,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 1,
          title: '相关话题4',
          messageNum: 40,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 1,
          title: '相关话题5',
          messageNum: 60,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 2,
          title: '无关话题',
          messageNum: 6,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 2,
          title: '无关话题',
          messageNum: 7,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 2,
          title: '无关话题',
          messageNum: 3,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 2,
          title: '无关话题',
          messageNum: 4,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        },
        {
          type: 2,
          title: '无关话题',
          messageNum: 1,
          x: 0,
          y: 0,
          isLabel: false,
          timer: null
        }
      ],
      isDetail: false,
      clickIndex: 0
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      const _this = this
      request('/api/Topic/getBookTopicList', {
        book_id: 230029
      }).then((result) => {
        this.topicList = result
        _this.topicList.forEach((item, index) => {
          item = JSON.parse(JSON.stringify(item))
          item.y = _this.rand()
          item.x = _this.rand()
          item.isLabel = false
          item.type = 1
          let num = parseInt(item.x.substring(0, item.x.length - 1))
          item.timer = setInterval(() => {
            if (num > 0) {
              num--
            } else {
              num = 100
            }
            item.x = num + '%'
          }, 600)
          _this.$set(_this.topicList, index, item)
        })
      })
    },
    rand () {
      return Math.random() * 50 + '%'
    },
    handleLabel (index) {
      const item = this.topicList[index]
      item.isLabel = true
      this.$set(this.topicList, index, item)
    },
    handleDetail (index) {
      this.clickIndex = index
      request('/api/Topic/getTopicCommentList', {
        topic_id: 184
      })
      this.isDetail = true
    },
    handleList () {
      this.detail = false
      this.handleMove(this.clickIndex)
    },
    handleStopMove (index) {
      const item = JSON.parse(JSON.stringify(this.topicList[index]))
      item.timer = null
      clearInterval(item.timer)
      item.isLabel = true
      this.$set(this.topicList, index, item)
    },
    handleMove (index) {
      const item = JSON.parse(JSON.stringify(this.topicList[index]))
      let num = parseInt(item.x.substring(0, item.x.length - 1))
      item.timer = setInterval(() => {
        if (num > 0) {
          num--
        } else {
          num = 100
        }
        item.x = num + '%'
      }, 600)
      this.$set(this.topicList, index, item)
    },
    handleList () {
      this.isDetail = false
    }
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/scss/topic.scss";

</style>
