/* Copyright 2014 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.textLayer {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.6;
  line-height: 1;
}

.textLayer > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

.textLayer .highlight {
  margin: -1px;
  padding: 1px;
  background-color: rgba(180, 0, 170, 1);
  border-radius: 4px;
}

.textLayer .highlight.begin {
  border-radius: 4px 0px 0px 4px;
}

.textLayer .highlight.end {
  border-radius: 0px 4px 4px 0px;
}

.textLayer .highlight.middle {
  border-radius: 0px;
}

.textLayer .highlight.selected {
  background-color: rgba(0, 100, 0, 1);
}

.textLayer ::-moz-selection {
  background: rgba(0, 0, 255, 1);
}
/* // 设置选中之后的样式 */
.textLayer ::selection {
  background: rgb(4, 87, 240)
}

.textLayer .endOfContent {
  display: block;
  position: absolute;
  left: 0px;
  top: 100%;
  right: 0px;
  bottom: 0px;
  z-index: -1;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.textLayer .endOfContent.active {
  top: 0px;
}


.annotationLayer section {
  position: absolute;
}

.annotationLayer .linkAnnotation > a,
.annotationLayer .buttonWidgetAnnotation.pushButton > a {
  position: absolute;
  font-size: 1em;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.annotationLayer .linkAnnotation > a:hover,
.annotationLayer .buttonWidgetAnnotation.pushButton > a:hover {
  opacity: 0.2;
  background: rgba(255, 255, 0, 1);
  box-shadow: 0px 2px 10px rgba(255, 255, 0, 1);
}

.annotationLayer .textAnnotation img {
  position: absolute;
  cursor: pointer;
}

.annotationLayer .textWidgetAnnotation input,
.annotationLayer .textWidgetAnnotation textarea,
.annotationLayer .choiceWidgetAnnotation select,
.annotationLayer .buttonWidgetAnnotation.checkBox input,
.annotationLayer .buttonWidgetAnnotation.radioButton input {
  background-color: rgba(0, 54, 255, 0.13);
  border: 1px solid transparent;
  box-sizing: border-box;
  font-size: 9px;
  height: 100%;
  margin: 0;
  padding: 0 3px;
  vertical-align: top;
  width: 100%;
}

.annotationLayer .choiceWidgetAnnotation select option {
  padding: 0;
}

.annotationLayer .buttonWidgetAnnotation.radioButton input {
  border-radius: 50%;
}

.annotationLayer .textWidgetAnnotation textarea {
  font: message-box;
  font-size: 9px;
  resize: none;
}

.annotationLayer .textWidgetAnnotation input[disabled],
.annotationLayer .textWidgetAnnotation textarea[disabled],
.annotationLayer .choiceWidgetAnnotation select[disabled],
.annotationLayer .buttonWidgetAnnotation.checkBox input[disabled],
.annotationLayer .buttonWidgetAnnotation.radioButton input[disabled] {
  background: none;
  border: 1px solid transparent;
  cursor: not-allowed;
}

.annotationLayer .textWidgetAnnotation input:hover,
.annotationLayer .textWidgetAnnotation textarea:hover,
.annotationLayer .choiceWidgetAnnotation select:hover,
.annotationLayer .buttonWidgetAnnotation.checkBox input:hover,
.annotationLayer .buttonWidgetAnnotation.radioButton input:hover {
  border: 1px solid rgba(0, 0, 0, 1);
}

.annotationLayer .textWidgetAnnotation input:focus,
.annotationLayer .textWidgetAnnotation textarea:focus,
.annotationLayer .choiceWidgetAnnotation select:focus {
  background: none;
  border: 1px solid transparent;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before,
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after,
.annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before {
  background-color: rgba(0, 0, 0, 1);
  content: "";
  display: block;
  position: absolute;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before,
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after {
  height: 80%;
  left: 45%;
  width: 1px;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before {
  transform: rotate(45deg);
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after {
  transform: rotate(-45deg);
}

.annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before {
  border-radius: 50%;
  height: 50%;
  left: 30%;
  top: 20%;
  width: 50%;
}

.annotationLayer .textWidgetAnnotation input.comb {
  font-family: monospace;
  padding-left: 2px;
  padding-right: 0;
}

.annotationLayer .textWidgetAnnotation input.comb:focus {
  /*
   * Letter spacing is placed on the right side of each character. Hence, the
   * letter spacing of the last character may be placed outside the visible
   * area, causing horizontal scrolling. We avoid this by extending the width
   * when the element has focus and revert this when it loses focus.
   */
  width: 115%;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input,
.annotationLayer .buttonWidgetAnnotation.radioButton input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
}

.annotationLayer .popupWrapper {
  position: absolute;
  width: 20em;
}

.annotationLayer .popup {
  position: absolute;
  z-index: 200;
  max-width: 20em;
  background-color: rgba(255, 255, 153, 1);
  box-shadow: 0px 2px 5px rgba(136, 136, 136, 1);
  border-radius: 2px;
  padding: 6px;
  margin-left: 5px;
  cursor: pointer;
  font: message-box;
  font-size: 9px;
  word-wrap: break-word;
}

.annotationLayer .popup > * {
  font-size: 9px;
}

.annotationLayer .popup h1 {
  display: inline-block;
}

.annotationLayer .popup span {
  display: inline-block;
  margin-left: 5px;
}

.annotationLayer .popup p {
  border-top: 1px solid rgba(51, 51, 51, 1);
  margin-top: 2px;
  padding-top: 2px;
}

.annotationLayer .highlightAnnotation,
.annotationLayer .underlineAnnotation,
.annotationLayer .squigglyAnnotation,
.annotationLayer .strikeoutAnnotation,
.annotationLayer .freeTextAnnotation,
.annotationLayer .lineAnnotation svg line,
.annotationLayer .squareAnnotation svg rect,
.annotationLayer .circleAnnotation svg ellipse,
.annotationLayer .polylineAnnotation svg polyline,
.annotationLayer .polygonAnnotation svg polygon,
.annotationLayer .caretAnnotation,
.annotationLayer .inkAnnotation svg polyline,
.annotationLayer .stampAnnotation,
.annotationLayer .fileAttachmentAnnotation {
  cursor: pointer;
}

.pdfViewer .canvasWrapper {
  overflow: hidden;
}

.pdfViewer .page {
  direction: ltr;
  width: 816px;
  height: 1056px;
  margin: 1px auto -8px auto;
  position: relative;
  overflow: visible;
  border: 9px solid transparent;
  background-clip: content-box;
  -o-border-image: url(images/shadow.png) 9 9 repeat;
     border-image: url(images/shadow.png) 9 9 repeat;
  background-color: rgba(255, 255, 255, 1);
}

.pdfViewer.removePageBorders .page {
  margin: 0px auto 10px auto;
  border: none;
}

.pdfViewer.singlePageView {
  display: inline-block;
}

.pdfViewer.singlePageView .page {
  margin: 0;
  border: none;
}

.pdfViewer.scrollHorizontal,
.pdfViewer.scrollWrapped,
.spread {
  margin-left: 3.5px;
  margin-right: 3.5px;
  text-align: center;
}

.pdfViewer.scrollHorizontal,
.spread {
  white-space: nowrap;
}

.pdfViewer.removePageBorders,
.pdfViewer.scrollHorizontal .spread,
.pdfViewer.scrollWrapped .spread {
  margin-left: 0;
  margin-right: 0;
}

.spread .page,
.pdfViewer.scrollHorizontal .page,
.pdfViewer.scrollWrapped .page,
.pdfViewer.scrollHorizontal .spread,
.pdfViewer.scrollWrapped .spread {
  display: inline-block;
  vertical-align: middle;
}

.spread .page,
.pdfViewer.scrollHorizontal .page,
.pdfViewer.scrollWrapped .page {
  margin-left: -3.5px;
  margin-right: -3.5px;
}

.pdfViewer.removePageBorders .spread .page,
.pdfViewer.removePageBorders.scrollHorizontal .page,
.pdfViewer.removePageBorders.scrollWrapped .page {
  margin-left: 5px;
  margin-right: 5px;
}

.pdfViewer .page canvas {
  margin: 0;
  display: block;
}

.pdfViewer .page canvas[hidden] {
  display: none;
}

.pdfViewer .page .loadingIcon {
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: url("images/loading-icon.gif") center no-repeat;
}

.pdfPresentationMode .pdfViewer {
  margin-left: 0;
  margin-right: 0;
}

.pdfPresentationMode .pdfViewer .page,
.pdfPresentationMode .pdfViewer .spread {
  display: block;
}

.pdfPresentationMode .pdfViewer .page,
.pdfPresentationMode .pdfViewer.removePageBorders .page {
  margin-left: auto;
  margin-right: auto;
}

.pdfPresentationMode:-ms-fullscreen .pdfViewer .page {
  margin-bottom: 100% !important;
}

.pdfPresentationMode:-webkit-full-screen .pdfViewer .page {
  margin-bottom: 100%;
  border: 0;
}

.pdfPresentationMode:-moz-full-screen .pdfViewer .page {
  margin-bottom: 100%;
  border: 0;
}

.pdfPresentationMode:fullscreen .pdfViewer .page {
  margin-bottom: 100%;
  border: 0;
}

:root {
  --sidebar-width: 200px;
  --sidebar-transition-duration: 200ms;
  --sidebar-transition-timing-function: ease;

  --toolbar-icon-opacity: 0.7;
  --doorhanger-icon-opacity: 0.9;

  --main-color: rgba(12, 12, 13, 1);
  --body-bg-color: rgba(237, 237, 240, 1);
  --errorWrapper-bg-color: rgba(255, 74, 74, 1);
  --progressBar-color: rgba(10, 132, 255, 1);
  --progressBar-indeterminate-bg-color: rgba(221, 221, 222, 1);
  --progressBar-indeterminate-blend-color: rgba(116, 177, 239, 1);
  --scrollbar-color: auto;
  --scrollbar-bg-color: auto;

  --sidebar-bg-color: rgba(245, 246, 247, 1);
  --toolbar-bg-color: rgba(249, 249, 250, 1);
  --toolbar-border-color: rgba(204, 204, 204, 1);
  --button-hover-color: rgba(221, 222, 223, 1);
  --toggled-btn-bg-color: rgba(0, 0, 0, 0.3);
  --dropdown-btn-bg-color: rgba(215, 215, 219, 1);
  --separator-color: rgba(0, 0, 0, 0.3);
  --field-color: rgba(6, 6, 6, 1);
  --field-bg-color: rgba(255, 255, 255, 1);
  --field-border-color: rgba(187, 187, 188, 1);
  --findbar-nextprevious-btn-bg-color: rgba(227, 228, 230, 1);
  --outline-color: rgba(0, 0, 0, 0.8);
  --outline-hover-color: rgba(0, 0, 0, 0.9);
  --outline-active-color: rgba(0, 0, 0, 0.08);
  --outline-active-bg-color: rgba(0, 0, 0, 1);
  --sidebaritem-bg-color: rgba(0, 0, 0, 0.15);
  --doorhanger-bg-color: rgba(255, 255, 255, 1);
  --doorhanger-border-color: rgba(12, 12, 13, 0.2);
  --doorhanger-hover-color: rgba(237, 237, 237, 1);
  --doorhanger-separator-color: rgba(222, 222, 222, 1);
  --overlay-button-bg-color: rgba(12, 12, 13, 0.1);
  --overlay-button-hover-color: rgba(12, 12, 13, 0.3);

  --loading-icon: url(images/loading.svg);
  --treeitem-expanded-icon: url(images/treeitem-expanded.svg);
  --treeitem-collapsed-icon: url(images/treeitem-collapsed.svg);
  --toolbarButton-menuArrow-icon: url(images/toolbarButton-menuArrow.svg);
  --toolbarButton-sidebarToggle-icon: url(images/toolbarButton-sidebarToggle.svg);
  --toolbarButton-secondaryToolbarToggle-icon: url(images/toolbarButton-secondaryToolbarToggle.svg);
  --toolbarButton-pageUp-icon: url(images/toolbarButton-pageUp.svg);
  --toolbarButton-pageDown-icon: url(images/toolbarButton-pageDown.svg);
  --toolbarButton-zoomOut-icon: url(images/toolbarButton-zoomOut.svg);
  --toolbarButton-zoomIn-icon: url(images/toolbarButton-zoomIn.svg);
  --toolbarButton-presentationMode-icon: url(images/toolbarButton-presentationMode.svg);
  --toolbarButton-print-icon: url(images/toolbarButton-print.svg);
  --toolbarButton-openFile-icon: url(images/toolbarButton-openFile.svg);
  --toolbarButton-download-icon: url(images/toolbarButton-download.svg);
  --toolbarButton-bookmark-icon: url(images/toolbarButton-bookmark.svg);
  --toolbarButton-viewThumbnail-icon: url(images/toolbarButton-viewThumbnail.svg);
  --toolbarButton-viewOutline-icon: url(images/toolbarButton-viewOutline.svg);
  --toolbarButton-viewAttachments-icon: url(images/toolbarButton-viewAttachments.svg);
  --toolbarButton-viewLayers-icon: url(images/toolbarButton-viewLayers.svg);
  --toolbarButton-search-icon: url(images/toolbarButton-search.svg);
  --findbarButton-previous-icon: url(images/findbarButton-previous.svg);
  --findbarButton-next-icon: url(images/findbarButton-next.svg);
  --secondaryToolbarButton-firstPage-icon: url(images/secondaryToolbarButton-firstPage.svg);
  --secondaryToolbarButton-lastPage-icon: url(images/secondaryToolbarButton-lastPage.svg);
  --secondaryToolbarButton-rotateCcw-icon: url(images/secondaryToolbarButton-rotateCcw.svg);
  --secondaryToolbarButton-rotateCw-icon: url(images/secondaryToolbarButton-rotateCw.svg);
  --secondaryToolbarButton-selectTool-icon: url(images/secondaryToolbarButton-selectTool.svg);
  --secondaryToolbarButton-handTool-icon: url(images/secondaryToolbarButton-handTool.svg);
  --secondaryToolbarButton-scrollVertical-icon: url(images/secondaryToolbarButton-scrollVertical.svg);
  --secondaryToolbarButton-scrollHorizontal-icon: url(images/secondaryToolbarButton-scrollHorizontal.svg);
  --secondaryToolbarButton-scrollWrapped-icon: url(images/secondaryToolbarButton-scrollWrapped.svg);
  --secondaryToolbarButton-spreadNone-icon: url(images/secondaryToolbarButton-spreadNone.svg);
  --secondaryToolbarButton-spreadOdd-icon: url(images/secondaryToolbarButton-spreadOdd.svg);
  --secondaryToolbarButton-spreadEven-icon: url(images/secondaryToolbarButton-spreadEven.svg);
  --secondaryToolbarButton-documentProperties-icon: url(images/secondaryToolbarButton-documentProperties.svg);
}

@media (prefers-color-scheme: dark) {
  :root {
    --main-color: rgba(249, 249, 250, 1);
    --body-bg-color: rgba(42, 42, 46, 1);
    --errorWrapper-bg-color: rgba(199, 17, 17, 1);
    --progressBar-color: rgba(0, 96, 223, 1);
    --progressBar-indeterminate-bg-color: rgba(40, 40, 43, 1);
    --progressBar-indeterminate-blend-color: rgba(20, 68, 133, 1);
    --scrollbar-color: rgba(121, 121, 123, 1);
    --scrollbar-bg-color: rgba(35, 35, 39, 1);

    --sidebar-bg-color: rgba(50, 50, 52, 1);
    --toolbar-bg-color: rgba(56, 56, 61, 1);
    --toolbar-border-color: rgba(12, 12, 13, 1);
    --button-hover-color: rgba(102, 102, 103, 1);
    --toggled-btn-bg-color: rgba(0, 0, 0, 0.3);
    --dropdown-btn-bg-color: rgba(74, 74, 79, 1);
    --separator-color: rgba(0, 0, 0, 0.3);
    --field-color: rgba(250, 250, 250, 1);
    --field-bg-color: rgba(64, 64, 68, 1);
    --field-border-color: rgba(115, 115, 115, 1);
    --findbar-nextprevious-btn-bg-color: rgba(89, 89, 89, 1);
    --outline-color: rgba(255, 255, 255, 0.8);
    --outline-hover-color: rgba(255, 255, 255, 0.9);
    --outline-active-color: rgba(255, 255, 255, 0.08);
    --outline-active-bg-color: rgba(255, 255, 255, 1);
    --sidebaritem-bg-color: rgba(255, 255, 255, 0.15);
    --doorhanger-bg-color: rgba(74, 74, 79, 1);
    --doorhanger-border-color: rgba(39, 39, 43, 1);
    --doorhanger-hover-color: rgba(93, 94, 98, 1);
    --doorhanger-separator-color: rgba(92, 92, 97, 1);
    --overlay-button-bg-color: rgba(92, 92, 97, 1);
    --overlay-button-hover-color: rgba(115, 115, 115, 1);

    --loading-icon: url(images/loading-dark.svg);
    --treeitem-expanded-icon: url(images/treeitem-expanded-dark.svg);
    --treeitem-collapsed-icon: url(images/treeitem-collapsed-dark.svg);
    --toolbarButton-menuArrow-icon: url(images/toolbarButton-menuArrow-dark.svg);
    --toolbarButton-sidebarToggle-icon: url(images/toolbarButton-sidebarToggle-dark.svg);
    --toolbarButton-secondaryToolbarToggle-icon: url(images/toolbarButton-secondaryToolbarToggle-dark.svg);
    --toolbarButton-pageUp-icon: url(images/toolbarButton-pageUp-dark.svg);
    --toolbarButton-pageDown-icon: url(images/toolbarButton-pageDown-dark.svg);
    --toolbarButton-zoomOut-icon: url(images/toolbarButton-zoomOut-dark.svg);
    --toolbarButton-zoomIn-icon: url(images/toolbarButton-zoomIn-dark.svg);
    --toolbarButton-presentationMode-icon: url(images/toolbarButton-presentationMode-dark.svg);
    --toolbarButton-print-icon: url(images/toolbarButton-print-dark.svg);
    --toolbarButton-openFile-icon: url(images/toolbarButton-openFile-dark.svg);
    --toolbarButton-download-icon: url(images/toolbarButton-download-dark.svg);
    --toolbarButton-bookmark-icon: url(images/toolbarButton-bookmark-dark.svg);
    --toolbarButton-viewThumbnail-icon: url(images/toolbarButton-viewThumbnail-dark.svg);
    --toolbarButton-viewOutline-icon: url(images/toolbarButton-viewOutline-dark.svg);
    --toolbarButton-viewAttachments-icon: url(images/toolbarButton-viewAttachments-dark.svg);
    --toolbarButton-viewLayers-icon: url(images/toolbarButton-viewLayers-dark.svg);
    --toolbarButton-search-icon: url(images/toolbarButton-search-dark.svg);
    --findbarButton-previous-icon: url(images/findbarButton-previous-dark.svg);
    --findbarButton-next-icon: url(images/findbarButton-next-dark.svg);
    --secondaryToolbarButton-firstPage-icon: url(images/secondaryToolbarButton-firstPage-dark.svg);
    --secondaryToolbarButton-lastPage-icon: url(images/secondaryToolbarButton-lastPage-dark.svg);
    --secondaryToolbarButton-rotateCcw-icon: url(images/secondaryToolbarButton-rotateCcw-dark.svg);
    --secondaryToolbarButton-rotateCw-icon: url(images/secondaryToolbarButton-rotateCw-dark.svg);
    --secondaryToolbarButton-selectTool-icon: url(images/secondaryToolbarButton-selectTool-dark.svg);
    --secondaryToolbarButton-handTool-icon: url(images/secondaryToolbarButton-handTool-dark.svg);
    --secondaryToolbarButton-scrollVertical-icon: url(images/secondaryToolbarButton-scrollVertical-dark.svg);
    --secondaryToolbarButton-scrollHorizontal-icon: url(images/secondaryToolbarButton-scrollHorizontal-dark.svg);
    --secondaryToolbarButton-scrollWrapped-icon: url(images/secondaryToolbarButton-scrollWrapped-dark.svg);
    --secondaryToolbarButton-spreadNone-icon: url(images/secondaryToolbarButton-spreadNone-dark.svg);
    --secondaryToolbarButton-spreadOdd-icon: url(images/secondaryToolbarButton-spreadOdd-dark.svg);
    --secondaryToolbarButton-spreadEven-icon: url(images/secondaryToolbarButton-spreadEven-dark.svg);
    --secondaryToolbarButton-documentProperties-icon: url(images/secondaryToolbarButton-documentProperties-dark.svg);
  }
}

* {
  padding: 0;
  margin: 0;
}

html {
  height: 100%;
  width: 100%;
  /* Font size is needed to make the activity bar the correct size. */
  font-size: 10px;
}

body {
  height: 100%;
  width: 100%;
  background-color: rgba(237, 237, 240, 1);
  background-color: var(--body-bg-color);
}

@media (prefers-color-scheme: dark) {

  body {
  background-color: rgba(42, 42, 46, 1);
  background-color: var(--body-bg-color);
  }
}

body {
  font: message-box;
  outline: none;
  scrollbar-color: auto auto;
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  body {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  body {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  body {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  body {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

input {
  font: message-box;
  outline: none;
  scrollbar-color: auto auto;
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  input {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  input {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  input {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  input {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

button {
  font: message-box;
  outline: none;
  scrollbar-color: auto auto;
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  button {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  button {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  button {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  button {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

select {
  font: message-box;
  outline: none;
  scrollbar-color: auto auto;
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  select {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  select {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  select {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  select {
  scrollbar-color: rgba(121, 121, 123, 1) rgba(35, 35, 39, 1);
  scrollbar-color: var(--scrollbar-color) var(--scrollbar-bg-color);
  }
}

.hidden {
  display: none !important;
}
[hidden] {
  display: none !important;
}

.pdfViewer.enablePermissions .textLayer > span {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
      -ms-user-select: none !important;
          user-select: none !important;
  cursor: not-allowed;
}

#viewerContainer.pdfPresentationMode:-ms-fullscreen {
  top: 0px !important;
  overflow: hidden !important;
}

#viewerContainer.pdfPresentationMode:-ms-fullscreen::-ms-backdrop {
  background-color: rgba(0, 0, 0, 1);
}

#viewerContainer.pdfPresentationMode:-webkit-full-screen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -webkit-user-select: none;
          user-select: none;
}

#viewerContainer.pdfPresentationMode:-moz-full-screen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -moz-user-select: none;
       user-select: none;
}

#viewerContainer.pdfPresentationMode:-ms-fullscreen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -ms-user-select: none;
      user-select: none;
}

#viewerContainer.pdfPresentationMode:fullscreen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.pdfPresentationMode:-webkit-full-screen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:-moz-full-screen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:-ms-fullscreen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:fullscreen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:-webkit-full-screen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode:-moz-full-screen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode:-ms-fullscreen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode:fullscreen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode.pdfPresentationModeControls > *,
.pdfPresentationMode.pdfPresentationModeControls .textLayer > span {
  cursor: default;
}

#outerContainer {
  width: 100%;
  height: 100%;
  position: relative;
}

#sidebarContainer {
  position: absolute;
  top: 32px;
  bottom: 0;
  width: 200px;
  width: var(--sidebar-width);
  visibility: hidden;
  z-index: 100;
  border-top: 1px solid rgba(51, 51, 51, 1);
  transition-duration: 200ms;
  transition-duration: var(--sidebar-transition-duration);
  transition-timing-function: ease;
  transition-timing-function: var(--sidebar-transition-timing-function);
}
html[dir="ltr"] #sidebarContainer {
  transition-property: left;
  left: -200px;
  left: calc(0px - var(--sidebar-width));
}
html[dir="rtl"] #sidebarContainer {
  transition-property: right;
  right: -200px;
  right: calc(0px - var(--sidebar-width));
}

.loadingInProgress #sidebarContainer {
  top: 36px;
}

#outerContainer.sidebarResizing #sidebarContainer {
  /* Improve responsiveness and avoid visual glitches when the sidebar is resized. */
  transition-duration: 0s;
  /* Prevent e.g. the thumbnails being selected when the sidebar is resized. */
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

#outerContainer.sidebarMoving #sidebarContainer,
#outerContainer.sidebarOpen #sidebarContainer {
  visibility: visible;
}
html[dir="ltr"] #outerContainer.sidebarOpen #sidebarContainer {
  left: 0px;
}
html[dir="rtl"] #outerContainer.sidebarOpen #sidebarContainer {
  right: 0px;
}

#mainContainer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  min-width: 320px;
}

#sidebarContent {
  top: 32px;
  bottom: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}
html[dir="ltr"] #sidebarContent {
  left: 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
}
html[dir="rtl"] #sidebarContent {
  right: 0;
  box-shadow: inset 1px 0 0 rgba(0, 0, 0, 0.25);
}

#viewerContainer {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  top: 32px;
  right: 0;
  bottom: 0;
  left: 0;
  outline: none;
}
#viewerContainer:not(.pdfPresentationMode) {
  transition-duration: 200ms;
  transition-duration: var(--sidebar-transition-duration);
  transition-timing-function: ease;
  transition-timing-function: var(--sidebar-transition-timing-function);
}

#outerContainer.sidebarResizing #viewerContainer {
  /* Improve responsiveness and avoid visual glitches when the sidebar is resized. */
  transition-duration: 0s;
}

html[dir="ltr"]
  #outerContainer.sidebarOpen
  #viewerContainer:not(.pdfPresentationMode) {
  transition-property: left;
  left: 200px;
  left: var(--sidebar-width);
}
html[dir="rtl"]
  #outerContainer.sidebarOpen
  #viewerContainer:not(.pdfPresentationMode) {
  transition-property: right;
  right: 200px;
  right: var(--sidebar-width);
}

.toolbar {
  position: relative;
  left: 0;
  right: 0;
  z-index: 9999;
  cursor: default;
}

#toolbarContainer {
  width: 100%;
}

#toolbarSidebar {
  width: 100%;
  height: 32px;
  background-color: rgba(245, 246, 247, 1);
  background-color: var(--sidebar-bg-color);
}

@media (prefers-color-scheme: dark) {

  #toolbarSidebar {
  background-color: rgba(50, 50, 52, 1);
  background-color: var(--sidebar-bg-color);
  }
}
html[dir="ltr"] #toolbarSidebar {
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25), 0 1px 0 rgba(0, 0, 0, 0.15),
    0 0 1px rgba(0, 0, 0, 0.1);
}
html[dir="rtl"] #toolbarSidebar {
  box-shadow: inset 1px 0 0 rgba(0, 0, 0, 0.25), 0 1px 0 rgba(0, 0, 0, 0.15),
    0 0 1px rgba(0, 0, 0, 0.1);
}

html[dir="ltr"] #toolbarSidebar .toolbarButton {
  margin-right: 2px !important;
}
html[dir="rtl"] #toolbarSidebar .toolbarButton {
  margin-left: 2px !important;
}

#sidebarResizer {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 6px;
  z-index: 200;
  cursor: ew-resize;
}
html[dir="ltr"] #sidebarResizer {
  right: -6px;
}
html[dir="rtl"] #sidebarResizer {
  left: -6px;
}

#toolbarContainer {
  position: relative;
  height: 32px;
  background-color: rgba(249, 249, 250, 1);
  background-color: var(--toolbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  #toolbarContainer {
  background-color: rgba(56, 56, 61, 1);
  background-color: var(--toolbar-bg-color);
  }
}

.findbar {
  position: relative;
  height: 32px;
  background-color: rgba(249, 249, 250, 1);
  background-color: var(--toolbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  .findbar {
  background-color: rgba(56, 56, 61, 1);
  background-color: var(--toolbar-bg-color);
  }
}

.secondaryToolbar {
  position: relative;
  height: 32px;
  background-color: rgba(249, 249, 250, 1);
  background-color: var(--toolbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbar {
  background-color: rgba(56, 56, 61, 1);
  background-color: var(--toolbar-bg-color);
  }
}
html[dir="ltr"] #toolbarContainer {
  box-shadow: 0 1px 0 rgba(204, 204, 204, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
}
@media (prefers-color-scheme: dark) {

  html[dir="ltr"] #toolbarContainer {
  box-shadow: 0 1px 0 rgba(12, 12, 13, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
  }
}
.findbar {
  box-shadow: 0 1px 0 rgba(204, 204, 204, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
}
@media (prefers-color-scheme: dark) {

  .findbar {
  box-shadow: 0 1px 0 rgba(12, 12, 13, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
  }
}
.secondaryToolbar {
  box-shadow: 0 1px 0 rgba(204, 204, 204, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
}
@media (prefers-color-scheme: dark) {

  .secondaryToolbar {
  box-shadow: 0 1px 0 rgba(12, 12, 13, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
  }
}
html[dir="rtl"] #toolbarContainer {
  box-shadow: 0 1px 0 rgba(204, 204, 204, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
}
@media (prefers-color-scheme: dark) {

  html[dir="rtl"] #toolbarContainer {
  box-shadow: 0 1px 0 rgba(12, 12, 13, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
  }
}
.findbar {
  box-shadow: 0 1px 0 rgba(204, 204, 204, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
}
@media (prefers-color-scheme: dark) {

  .findbar {
  box-shadow: 0 1px 0 rgba(12, 12, 13, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
  }
}
.secondaryToolbar {
  box-shadow: 0 1px 0 rgba(204, 204, 204, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
}
@media (prefers-color-scheme: dark) {

  .secondaryToolbar {
  box-shadow: 0 1px 0 rgba(12, 12, 13, 1);
  box-shadow: 0 1px 0 var(--toolbar-border-color);
  }
}

#toolbarViewer {
  height: 32px;
}

#loadingBar {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: rgba(237, 237, 240, 1);
  background-color: var(--body-bg-color);
  border-bottom: 1px solid rgba(204, 204, 204, 1);
  border-bottom: 1px solid var(--toolbar-border-color);
}

@media (prefers-color-scheme: dark) {

  #loadingBar {
  border-bottom: 1px solid rgba(12, 12, 13, 1);
  border-bottom: 1px solid var(--toolbar-border-color);
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar {
  background-color: rgba(42, 42, 46, 1);
  background-color: var(--body-bg-color);
  }
}

#loadingBar .progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: rgba(10, 132, 255, 1);
  background-color: var(--progressBar-color);
  overflow: hidden;
  transition: width 200ms;
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress {
  background-color: rgba(0, 96, 223, 1);
  background-color: var(--progressBar-color);
  }
}

@-webkit-keyframes progressIndeterminate {
  0% {
    left: -142px;
  }
  100% {
    left: 0;
  }
}

@keyframes progressIndeterminate {
  0% {
    left: -142px;
  }
  100% {
    left: 0;
  }
}

#loadingBar .progress.indeterminate {
  background-color: rgba(221, 221, 222, 1);
  background-color: var(--progressBar-indeterminate-bg-color);
  transition: none;
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate {
  background-color: rgba(40, 40, 43, 1);
  background-color: var(--progressBar-indeterminate-bg-color);
  }
}

#loadingBar .progress.indeterminate .glimmer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: calc(100% + 150px);
  background: repeating-linear-gradient(
    135deg,
    rgba(116, 177, 239, 1) 0,
    rgba(221, 221, 222, 1) 5px,
    rgba(221, 221, 222, 1) 45px,
    rgba(10, 132, 255, 1) 55px,
    rgba(10, 132, 255, 1) 95px,
    rgba(116, 177, 239, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  -webkit-animation: progressIndeterminate 1s linear infinite;
          animation: progressIndeterminate 1s linear infinite;
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

@media (prefers-color-scheme: dark) {

  #loadingBar .progress.indeterminate .glimmer {
  background: repeating-linear-gradient(
    135deg,
    rgba(20, 68, 133, 1) 0,
    rgba(40, 40, 43, 1) 5px,
    rgba(40, 40, 43, 1) 45px,
    rgba(0, 96, 223, 1) 55px,
    rgba(0, 96, 223, 1) 95px,
    rgba(20, 68, 133, 1) 100px
  );
  background: repeating-linear-gradient(
    135deg,
    var(--progressBar-indeterminate-blend-color) 0,
    var(--progressBar-indeterminate-bg-color) 5px,
    var(--progressBar-indeterminate-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-indeterminate-blend-color) 100px
  );
  }
}

.findbar,
.secondaryToolbar {
  top: 32px;
  position: absolute;
  z-index: 10000;
  height: auto;
  min-width: 16px;
  padding: 0px 4px 0px 4px;
  margin: 4px 2px 4px 2px;
  color: rgba(217, 217, 217, 1);
  font-size: 12px;
  line-height: 14px;
  text-align: left;
  cursor: default;
}

.findbar {
  min-width: 300px;
  background-color: rgba(249, 249, 250, 1);
  background-color: var(--toolbar-bg-color);
}

@media (prefers-color-scheme: dark) {

  .findbar {
  background-color: rgba(56, 56, 61, 1);
  background-color: var(--toolbar-bg-color);
  }
}
.findbar > div {
  height: 32px;
}
.findbar.wrapContainers > div {
  clear: both;
}
.findbar.wrapContainers > div#findbarMessageContainer {
  height: auto;
}
html[dir="ltr"] .findbar {
  left: 64px;
}
html[dir="rtl"] .findbar {
  right: 64px;
}

html[dir="ltr"] .findbar .splitToolbarButton {
  margin-left: 0px;
  margin-top: 3px;
}

html[dir="rtl"] .findbar .splitToolbarButton {
  margin-right: 0px;
  margin-top: 3px;
}

.findbar .splitToolbarButton .findNext {
  width: 29px;
}

html[dir="ltr"] .findbar .splitToolbarButton .findNext {
  border-right: 1px solid rgba(187, 187, 188, 1);
  border-right: 1px solid var(--field-border-color);
}

@media (prefers-color-scheme: dark) {

  html[dir="ltr"] .findbar .splitToolbarButton .findNext {
  border-right: 1px solid rgba(115, 115, 115, 1);
  border-right: 1px solid var(--field-border-color);
  }
}

html[dir="rtl"] .findbar .splitToolbarButton .findNext {
  border-left: 1px solid rgba(187, 187, 188, 1);
  border-left: 1px solid var(--field-border-color);
}

@media (prefers-color-scheme: dark) {

  html[dir="rtl"] .findbar .splitToolbarButton .findNext {
  border-left: 1px solid rgba(115, 115, 115, 1);
  border-left: 1px solid var(--field-border-color);
  }
}

.findbar .splitToolbarButton .toolbarButton {
  background-color: rgba(227, 228, 230, 1);
  background-color: var(--findbar-nextprevious-btn-bg-color);
  border-radius: 0px;
  height: 26px;
  border-top: 1px solid rgba(187, 187, 188, 1);
  border-top: 1px solid var(--field-border-color);
  border-bottom: 1px solid rgba(187, 187, 188, 1);
  border-bottom: 1px solid var(--field-border-color);
}

@media (prefers-color-scheme: dark) {

  .findbar .splitToolbarButton .toolbarButton {
  border-bottom: 1px solid rgba(115, 115, 115, 1);
  border-bottom: 1px solid var(--field-border-color);
  }
}

@media (prefers-color-scheme: dark) {

  .findbar .splitToolbarButton .toolbarButton {
  border-top: 1px solid rgba(115, 115, 115, 1);
  border-top: 1px solid var(--field-border-color);
  }
}

@media (prefers-color-scheme: dark) {

  .findbar .splitToolbarButton .toolbarButton {
  background-color: rgba(89, 89, 89, 1);
  background-color: var(--findbar-nextprevious-btn-bg-color);
  }
}

.findbar .splitToolbarButton .toolbarButton::before {
  top: 5px;
}

html[dir="ltr"] .findbar .splitToolbarButton > .findPrevious {
  border-radius: 0;
}
html[dir="ltr"] .findbar .splitToolbarButton > .findNext {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 2px;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
}

html[dir="rtl"] .findbar .splitToolbarButton > .findPrevious {
  border-radius: 0;
}
html[dir="rtl"] .findbar .splitToolbarButton > .findNext {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 0;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
}

.findbar input[type="checkbox"] {
  pointer-events: none;
}

.findbar label {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.findbar label:hover {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
}

@media (prefers-color-scheme: dark) {

  .findbar label:hover {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

.findbar input:focus + label {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
}

@media (prefers-color-scheme: dark) {

  .findbar input:focus + label {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

html[dir="ltr"] #findInput {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

html[dir="rtl"] #findInput {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.findbar .toolbarField[type="checkbox"]:checked + .toolbarLabel {
  background-color: rgba(0, 0, 0, 0.3) !important;
  background-color: var(--toggled-btn-bg-color) !important;
}

@media (prefers-color-scheme: dark) {

  .findbar .toolbarField[type="checkbox"]:checked + .toolbarLabel {
  background-color: rgba(0, 0, 0, 0.3) !important;
  background-color: var(--toggled-btn-bg-color) !important;
  }
}

#findInput {
  width: 200px;
}
#findInput::-webkit-input-placeholder {
  color: rgba(191, 191, 191, 1);
}
#findInput::-moz-placeholder {
  font-style: normal;
}
#findInput:-ms-input-placeholder {
  font-style: normal;
}
#findInput::-ms-input-placeholder {
  font-style: normal;
}
#findInput::placeholder {
  font-style: normal;
}
#findInput[data-status="pending"] {
  background-image: url(images/loading.svg);
  background-image: var(--loading-icon);
  background-repeat: no-repeat;
  background-position: 98%;
}
@media (prefers-color-scheme: dark) {

  #findInput[data-status="pending"] {
  background-image: url(images/loading-dark.svg);
  background-image: var(--loading-icon);
  }
}
html[dir="rtl"] #findInput[data-status="pending"] {
  background-position: 3px;
}

.secondaryToolbar {
  padding: 6px 0 10px 0;
  height: auto;
  z-index: 30000;
  background-color: rgba(255, 255, 255, 1);
  background-color: var(--doorhanger-bg-color);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbar {
  background-color: rgba(74, 74, 79, 1);
  background-color: var(--doorhanger-bg-color);
  }
}
html[dir="ltr"] .secondaryToolbar {
  right: 4px;
}
html[dir="rtl"] .secondaryToolbar {
  left: 4px;
}

#secondaryToolbarButtonContainer {
  max-width: 220px;
  max-height: 400px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: -4px;
}

#secondaryToolbarButtonContainer.hiddenScrollModeButtons > .scrollModeButtons,
#secondaryToolbarButtonContainer.hiddenSpreadModeButtons > .spreadModeButtons {
  display: none !important;
}

.doorHanger {
  border-radius: 2px;
  box-shadow: 0 1px 5px rgba(12, 12, 13, 0.2),
    0 0 0 1px rgba(12, 12, 13, 0.2);
  box-shadow: 0 1px 5px var(--doorhanger-border-color),
    0 0 0 1px var(--doorhanger-border-color);
}

@media (prefers-color-scheme: dark) {

  .doorHanger {
  box-shadow: 0 1px 5px rgba(39, 39, 43, 1),
    0 0 0 1px rgba(39, 39, 43, 1);
  box-shadow: 0 1px 5px var(--doorhanger-border-color),
    0 0 0 1px var(--doorhanger-border-color);
  }
}

.doorHangerRight {
  border-radius: 2px;
  box-shadow: 0 1px 5px rgba(12, 12, 13, 0.2),
    0 0 0 1px rgba(12, 12, 13, 0.2);
  box-shadow: 0 1px 5px var(--doorhanger-border-color),
    0 0 0 1px var(--doorhanger-border-color);
}

@media (prefers-color-scheme: dark) {

  .doorHangerRight {
  box-shadow: 0 1px 5px rgba(39, 39, 43, 1),
    0 0 0 1px rgba(39, 39, 43, 1);
  box-shadow: 0 1px 5px var(--doorhanger-border-color),
    0 0 0 1px var(--doorhanger-border-color);
  }
}
.doorHanger:after,
.doorHanger:before,
.doorHangerRight:after,
.doorHangerRight:before {
  bottom: 100%;
  border: solid rgba(0, 0, 0, 0);
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
.doorHanger:after,
.doorHangerRight:after {
  border-width: 8px;
}
.doorHanger:after {
  border-bottom-color: rgba(249, 249, 250, 1);
  border-bottom-color: var(--toolbar-bg-color);
}
@media (prefers-color-scheme: dark) {

  .doorHanger:after {
  border-bottom-color: rgba(56, 56, 61, 1);
  border-bottom-color: var(--toolbar-bg-color);
  }
}
.doorHangerRight:after {
  border-bottom-color: rgba(255, 255, 255, 1);
  border-bottom-color: var(--doorhanger-bg-color);
}
@media (prefers-color-scheme: dark) {

  .doorHangerRight:after {
  border-bottom-color: rgba(74, 74, 79, 1);
  border-bottom-color: var(--doorhanger-bg-color);
  }
}
.doorHanger:before {
  border-bottom-color: rgba(12, 12, 13, 0.2);
  border-bottom-color: var(--doorhanger-border-color);
  border-width: 9px;
}
@media (prefers-color-scheme: dark) {

  .doorHanger:before {
  border-bottom-color: rgba(39, 39, 43, 1);
  border-bottom-color: var(--doorhanger-border-color);
  }
}
.doorHangerRight:before {
  border-bottom-color: rgba(12, 12, 13, 0.2);
  border-bottom-color: var(--doorhanger-border-color);
  border-width: 9px;
}
@media (prefers-color-scheme: dark) {

  .doorHangerRight:before {
  border-bottom-color: rgba(39, 39, 43, 1);
  border-bottom-color: var(--doorhanger-border-color);
  }
}

html[dir="ltr"] .doorHanger:after,
html[dir="rtl"] .doorHangerRight:after {
  left: 10px;
  margin-left: -8px;
}

html[dir="ltr"] .doorHanger:before,
html[dir="rtl"] .doorHangerRight:before {
  left: 10px;
  margin-left: -9px;
}

html[dir="rtl"] .doorHanger:after,
html[dir="ltr"] .doorHangerRight:after {
  right: 10px;
  margin-right: -8px;
}

html[dir="rtl"] .doorHanger:before,
html[dir="ltr"] .doorHangerRight:before {
  right: 10px;
  margin-right: -9px;
}

#findResultsCount {
  background-color: rgba(217, 217, 217, 1);
  color: rgba(82, 82, 82, 1);
  text-align: center;
  padding: 3px 4px;
  margin: 5px;
}

#findMsg {
  color: rgba(251, 0, 0, 1);
}
#findMsg:empty {
  display: none;
}

#findInput.notFound {
  background-color: rgba(255, 102, 102, 1);
}

#toolbarViewerMiddle {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

html[dir="ltr"] #toolbarViewerLeft,
html[dir="rtl"] #toolbarViewerRight {
  float: left;
}
html[dir="ltr"] #toolbarViewerRight,
html[dir="rtl"] #toolbarViewerLeft {
  float: right;
}
html[dir="ltr"] #toolbarViewerLeft > *,
html[dir="ltr"] #toolbarViewerMiddle > *,
html[dir="ltr"] #toolbarViewerRight > *,
html[dir="ltr"] .findbar * {
  position: relative;
  float: left;
}
html[dir="rtl"] #toolbarViewerLeft > *,
html[dir="rtl"] #toolbarViewerMiddle > *,
html[dir="rtl"] #toolbarViewerRight > *,
html[dir="rtl"] .findbar * {
  position: relative;
  float: right;
}

html[dir="ltr"] .splitToolbarButton {
  margin: 2px 2px 0;
  display: inline-block;
}
html[dir="rtl"] .splitToolbarButton {
  margin: 2px 2px 0;
  display: inline-block;
}
html[dir="ltr"] .splitToolbarButton > .toolbarButton {
  border-radius: 2px;
  float: left;
}
html[dir="rtl"] .splitToolbarButton > .toolbarButton {
  border-radius: 2px;
  float: right;
}

.toolbarButton,
.secondaryToolbarButton,
.overlayButton {
  border: 0 none;
  background: none;
  width: 28px;
  height: 28px;
}
.overlayButton {
  background-color: rgba(12, 12, 13, 0.1);
  background-color: var(--overlay-button-bg-color);
}
@media (prefers-color-scheme: dark) {

  .overlayButton {
  background-color: rgba(92, 92, 97, 1);
  background-color: var(--overlay-button-bg-color);
  }
}

.overlayButton:hover {
  background-color: rgba(12, 12, 13, 0.3);
  background-color: var(--overlay-button-hover-color);
}

@media (prefers-color-scheme: dark) {

  .overlayButton:hover {
  background-color: rgba(115, 115, 115, 1);
  background-color: var(--overlay-button-hover-color);
  }
}

.overlayButton:focus {
  background-color: rgba(12, 12, 13, 0.3);
  background-color: var(--overlay-button-hover-color);
}

@media (prefers-color-scheme: dark) {

  .overlayButton:focus {
  background-color: rgba(115, 115, 115, 1);
  background-color: var(--overlay-button-hover-color);
  }
}

.toolbarButton > span {
  display: inline-block;
  width: 0;
  height: 0;
  overflow: hidden;
}

.toolbarButton[disabled],
.secondaryToolbarButton[disabled],
.overlayButton[disabled] {
  opacity: 0.5;
}

.splitToolbarButton.toggled .toolbarButton {
  margin: 0;
}

.splitToolbarButton > .toolbarButton:hover {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
  z-index: 199;
}

@media (prefers-color-scheme: dark) {

  .splitToolbarButton > .toolbarButton:hover {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

.splitToolbarButton > .toolbarButton:focus {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
  z-index: 199;
}

@media (prefers-color-scheme: dark) {

  .splitToolbarButton > .toolbarButton:focus {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

.dropdownToolbarButton:hover {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
  z-index: 199;
}

@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton:hover {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

.toolbarButton.textButton:hover {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
  z-index: 199;
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.textButton:hover {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

.toolbarButton.textButton:focus {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
  z-index: 199;
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.textButton:focus {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}
.splitToolbarButton > .toolbarButton {
  position: relative;
}
html[dir="ltr"] .splitToolbarButton > .toolbarButton:first-child,
html[dir="rtl"] .splitToolbarButton > .toolbarButton:last-child {
  position: relative;
  margin: 0;
}
html[dir="ltr"] .splitToolbarButton > .toolbarButton:last-child,
html[dir="rtl"] .splitToolbarButton > .toolbarButton:first-child {
  position: relative;
  margin: 0;
}
.splitToolbarButtonSeparator {
  padding: 10px 0;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--separator-color);
  z-index: 99;
  display: inline-block;
  margin: 4px 0;
}
@media (prefers-color-scheme: dark) {

  .splitToolbarButtonSeparator {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--separator-color);
  }
}

.findbar .splitToolbarButtonSeparator {
  background-color: rgba(187, 187, 188, 1);
  background-color: var(--field-border-color);
  margin: 0;
  padding: 13px 0;
}

@media (prefers-color-scheme: dark) {

  .findbar .splitToolbarButtonSeparator {
  background-color: rgba(115, 115, 115, 1);
  background-color: var(--field-border-color);
  }
}

html[dir="ltr"] .splitToolbarButtonSeparator {
  float: left;
}
html[dir="rtl"] .splitToolbarButtonSeparator {
  float: right;
}

.toolbarButton {
  min-width: 16px;
  padding: 2px 6px 0;
  border: none;
  border-radius: 2px;
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  font-size: 12px;
  line-height: 14px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  /* Opera does not support user-select, use <... unselectable="on"> instead */
  cursor: default;
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {

  .toolbarButton {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}

.dropdownToolbarButton {
  min-width: 16px;
  padding: 2px 6px 0;
  border: none;
  border-radius: 2px;
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  font-size: 12px;
  line-height: 14px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  /* Opera does not support user-select, use <... unselectable="on"> instead */
  cursor: default;
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}

.secondaryToolbarButton {
  min-width: 16px;
  padding: 2px 6px 0;
  border: none;
  border-radius: 2px;
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  font-size: 12px;
  line-height: 14px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  /* Opera does not support user-select, use <... unselectable="on"> instead */
  cursor: default;
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}

.overlayButton {
  min-width: 16px;
  padding: 2px 6px 0;
  border: none;
  border-radius: 2px;
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  font-size: 12px;
  line-height: 14px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  /* Opera does not support user-select, use <... unselectable="on"> instead */
  cursor: default;
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {

  .overlayButton {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}

html[dir="ltr"] .toolbarButton,
html[dir="ltr"] .overlayButton,
html[dir="ltr"] .dropdownToolbarButton {
  margin: 2px 1px;
}
html[dir="rtl"] .toolbarButton,
html[dir="rtl"] .overlayButton,
html[dir="rtl"] .dropdownToolbarButton {
  margin: 2px 1px;
}

html[dir="ltr"] #toolbarViewerLeft > .toolbarButton:first-child,
html[dir="rtl"] #toolbarViewerRight > .toolbarButton:last-child {
  margin-left: 2px;
}

html[dir="ltr"] #toolbarViewerRight > .toolbarButton:last-child,
html[dir="rtl"] #toolbarViewerLeft > .toolbarButton:first-child {
  margin-right: 2px;
}
.toolbarButton:hover {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
}
@media (prefers-color-scheme: dark) {

  .toolbarButton:hover {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}
.toolbarButton:focus {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
}
@media (prefers-color-scheme: dark) {

  .toolbarButton:focus {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}
.secondaryToolbarButton:hover {
  background-color: rgba(237, 237, 237, 1);
  background-color: var(--doorhanger-hover-color);
}
@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton:hover {
  background-color: rgba(93, 94, 98, 1);
  background-color: var(--doorhanger-hover-color);
  }
}
.secondaryToolbarButton:focus {
  background-color: rgba(237, 237, 237, 1);
  background-color: var(--doorhanger-hover-color);
}
@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton:focus {
  background-color: rgba(93, 94, 98, 1);
  background-color: var(--doorhanger-hover-color);
  }
}

.toolbarButton.toggled {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--toggled-btn-bg-color);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.toggled {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--toggled-btn-bg-color);
  }
}

.splitToolbarButton.toggled > .toolbarButton.toggled {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--toggled-btn-bg-color);
}

@media (prefers-color-scheme: dark) {

  .splitToolbarButton.toggled > .toolbarButton.toggled {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--toggled-btn-bg-color);
  }
}

.secondaryToolbarButton.toggled {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--toggled-btn-bg-color);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.toggled {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--toggled-btn-bg-color);
  }
}

.toolbarButton.toggled:hover:active,
.splitToolbarButton.toggled > .toolbarButton.toggled:hover:active,
.secondaryToolbarButton.toggled:hover:active {
  background-color: rgba(0, 0, 0, 0.4);
}

.dropdownToolbarButton {
  width: 140px;
  padding: 0;
  overflow: hidden;
  background-color: rgba(215, 215, 219, 1);
  background-color: var(--dropdown-btn-bg-color);
  margin-top: 2px !important;
}

@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton {
  background-color: rgba(74, 74, 79, 1);
  background-color: var(--dropdown-btn-bg-color);
  }
}
.dropdownToolbarButton::after {
  position: absolute;
  display: inline-block;
  top: 6px;
  content: url(images/toolbarButton-menuArrow.svg);
  content: var(--toolbarButton-menuArrow-icon);
  pointer-events: none;
  max-width: 16px;
}
@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton::after {
  content: url(images/toolbarButton-menuArrow-dark.svg);
  content: var(--toolbarButton-menuArrow-icon);
  }
}
html[dir="ltr"] .dropdownToolbarButton::after {
  right: 7px;
}
html[dir="rtl"] .dropdownToolbarButton::after {
  left: 7px;
}

.dropdownToolbarButton > select {
  width: 162px;
  height: 28px;
  font-size: 12px;
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  margin: 0;
  padding: 1px 0 2px;
  border: none;
  background-color: rgba(215, 215, 219, 1);
  background-color: var(--dropdown-btn-bg-color);
}

@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton > select {
  background-color: rgba(74, 74, 79, 1);
  background-color: var(--dropdown-btn-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton > select {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}
html[dir="ltr"] .dropdownToolbarButton > select {
  padding-left: 4px;
}
html[dir="rtl"] .dropdownToolbarButton > select {
  padding-right: 4px;
}
.dropdownToolbarButton > select:hover {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
}
@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton > select:hover {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

.dropdownToolbarButton > select:focus {
  background-color: rgba(221, 222, 223, 1);
  background-color: var(--button-hover-color);
}

@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton > select:focus {
  background-color: rgba(102, 102, 103, 1);
  background-color: var(--button-hover-color);
  }
}

.dropdownToolbarButton > select > option {
  background: rgba(255, 255, 255, 1);
  background: var(--doorhanger-bg-color);
}

@media (prefers-color-scheme: dark) {

  .dropdownToolbarButton > select > option {
  background: rgba(74, 74, 79, 1);
  background: var(--doorhanger-bg-color);
  }
}

#customScaleOption {
  display: none;
}

#pageWidthOption {
  border-bottom: 1px rgba(255, 255, 255, 0.5) solid;
}

.toolbarButtonSpacer {
  width: 30px;
  display: inline-block;
  height: 1px;
}

html[dir="ltr"] #findPrevious {
  margin-left: 0;
}
html[dir="ltr"] #findNext {
  margin-right: 3px;
}

html[dir="rtl"] #findPrevious {
  margin-right: 0;
}
html[dir="rtl"] #findNext {
  margin-left: 3px;
}

.toolbarButton::before {
  opacity: 0.7;
  opacity: var(--toolbar-icon-opacity);
  top: 6px;
}

.secondaryToolbarButton::before {
  opacity: 0.9;
  opacity: var(--doorhanger-icon-opacity);
  top: 5px;
}

.toolbarButton::before,
.secondaryToolbarButton::before {
  /* All matching images have a size of 16x16
   * All relevant containers have a size of 28x28 */
  position: absolute;
  display: inline-block;
  left: 6px;
  max-width: 16px;
}

html[dir="ltr"] .secondaryToolbarButton::before {
  left: 12px;
}
html[dir="rtl"] .secondaryToolbarButton::before {
  right: 12px;
}

.toolbarButton#sidebarToggle::before {
  content: url(images/toolbarButton-sidebarToggle.svg);
  content: var(--toolbarButton-sidebarToggle-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton#sidebarToggle::before {
  content: url(images/toolbarButton-sidebarToggle-dark.svg);
  content: var(--toolbarButton-sidebarToggle-icon);
  }
}
html[dir="rtl"] .toolbarButton#sidebarToggle::before {
  transform: scaleX(-1);
}

.toolbarButton#secondaryToolbarToggle::before {
  content: url(images/toolbarButton-secondaryToolbarToggle.svg);
  content: var(--toolbarButton-secondaryToolbarToggle-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton#secondaryToolbarToggle::before {
  content: url(images/toolbarButton-secondaryToolbarToggle-dark.svg);
  content: var(--toolbarButton-secondaryToolbarToggle-icon);
  }
}
html[dir="rtl"] .toolbarButton#secondaryToolbarToggle::before {
  transform: scaleX(-1);
}

.toolbarButton.findPrevious::before {
  content: url(images/findbarButton-previous.svg);
  content: var(--findbarButton-previous-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.findPrevious::before {
  content: url(images/findbarButton-previous-dark.svg);
  content: var(--findbarButton-previous-icon);
  }
}
html[dir="rtl"] .toolbarButton.findPrevious::before {
  transform: scaleX(-1);
}

.toolbarButton.findNext::before {
  content: url(images/findbarButton-next.svg);
  content: var(--findbarButton-next-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.findNext::before {
  content: url(images/findbarButton-next-dark.svg);
  content: var(--findbarButton-next-icon);
  }
}
html[dir="rtl"] .toolbarButton.findNext::before {
  transform: scaleX(-1);
}

.toolbarButton.pageUp::before {
  content: url(images/toolbarButton-pageUp.svg);
  content: var(--toolbarButton-pageUp-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.pageUp::before {
  content: url(images/toolbarButton-pageUp-dark.svg);
  content: var(--toolbarButton-pageUp-icon);
  }
}
html[dir="rtl"] .toolbarButton.pageUp::before {
  transform: scaleX(-1);
}

.toolbarButton.pageDown::before {
  content: url(images/toolbarButton-pageDown.svg);
  content: var(--toolbarButton-pageDown-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.pageDown::before {
  content: url(images/toolbarButton-pageDown-dark.svg);
  content: var(--toolbarButton-pageDown-icon);
  }
}
html[dir="rtl"] .toolbarButton.pageDown::before {
  transform: scaleX(-1);
}

.toolbarButton.zoomOut::before {
  content: url(images/toolbarButton-zoomOut.svg);
  content: var(--toolbarButton-zoomOut-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.zoomOut::before {
  content: url(images/toolbarButton-zoomOut-dark.svg);
  content: var(--toolbarButton-zoomOut-icon);
  }
}

.toolbarButton.zoomIn::before {
  content: url(images/toolbarButton-zoomIn.svg);
  content: var(--toolbarButton-zoomIn-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.zoomIn::before {
  content: url(images/toolbarButton-zoomIn-dark.svg);
  content: var(--toolbarButton-zoomIn-icon);
  }
}

.toolbarButton.presentationMode::before {
  content: url(images/toolbarButton-presentationMode.svg);
  content: var(--toolbarButton-presentationMode-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.presentationMode::before {
  content: url(images/toolbarButton-presentationMode-dark.svg);
  content: var(--toolbarButton-presentationMode-icon);
  }
}

.secondaryToolbarButton.presentationMode::before {
  content: url(images/toolbarButton-presentationMode.svg);
  content: var(--toolbarButton-presentationMode-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.presentationMode::before {
  content: url(images/toolbarButton-presentationMode-dark.svg);
  content: var(--toolbarButton-presentationMode-icon);
  }
}

.toolbarButton.print::before {
  content: url(images/toolbarButton-print.svg);
  content: var(--toolbarButton-print-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.print::before {
  content: url(images/toolbarButton-print-dark.svg);
  content: var(--toolbarButton-print-icon);
  }
}

.secondaryToolbarButton.print::before {
  content: url(images/toolbarButton-print.svg);
  content: var(--toolbarButton-print-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.print::before {
  content: url(images/toolbarButton-print-dark.svg);
  content: var(--toolbarButton-print-icon);
  }
}

.toolbarButton.openFile::before {
  content: url(images/toolbarButton-openFile.svg);
  content: var(--toolbarButton-openFile-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.openFile::before {
  content: url(images/toolbarButton-openFile-dark.svg);
  content: var(--toolbarButton-openFile-icon);
  }
}

.secondaryToolbarButton.openFile::before {
  content: url(images/toolbarButton-openFile.svg);
  content: var(--toolbarButton-openFile-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.openFile::before {
  content: url(images/toolbarButton-openFile-dark.svg);
  content: var(--toolbarButton-openFile-icon);
  }
}

.toolbarButton.download::before {
  content: url(images/toolbarButton-download.svg);
  content: var(--toolbarButton-download-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.download::before {
  content: url(images/toolbarButton-download-dark.svg);
  content: var(--toolbarButton-download-icon);
  }
}

.secondaryToolbarButton.download::before {
  content: url(images/toolbarButton-download.svg);
  content: var(--toolbarButton-download-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.download::before {
  content: url(images/toolbarButton-download-dark.svg);
  content: var(--toolbarButton-download-icon);
  }
}

.secondaryToolbarButton.bookmark {
  padding-top: 6px;
  text-decoration: none;
}

.bookmark[href="#"] {
  opacity: 0.5;
  pointer-events: none;
}

.toolbarButton.bookmark::before {
  content: url(images/toolbarButton-bookmark.svg);
  content: var(--toolbarButton-bookmark-icon);
}

@media (prefers-color-scheme: dark) {

  .toolbarButton.bookmark::before {
  content: url(images/toolbarButton-bookmark-dark.svg);
  content: var(--toolbarButton-bookmark-icon);
  }
}

.secondaryToolbarButton.bookmark::before {
  content: url(images/toolbarButton-bookmark.svg);
  content: var(--toolbarButton-bookmark-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.bookmark::before {
  content: url(images/toolbarButton-bookmark-dark.svg);
  content: var(--toolbarButton-bookmark-icon);
  }
}

#viewThumbnail.toolbarButton::before {
  content: url(images/toolbarButton-viewThumbnail.svg);
  content: var(--toolbarButton-viewThumbnail-icon);
}

@media (prefers-color-scheme: dark) {

  #viewThumbnail.toolbarButton::before {
  content: url(images/toolbarButton-viewThumbnail-dark.svg);
  content: var(--toolbarButton-viewThumbnail-icon);
  }
}

#viewOutline.toolbarButton::before {
  content: url(images/toolbarButton-viewOutline.svg);
  content: var(--toolbarButton-viewOutline-icon);
}

@media (prefers-color-scheme: dark) {

  #viewOutline.toolbarButton::before {
  content: url(images/toolbarButton-viewOutline-dark.svg);
  content: var(--toolbarButton-viewOutline-icon);
  }
}
html[dir="rtl"] #viewOutline.toolbarButton::before {
  transform: scaleX(-1);
}

#viewAttachments.toolbarButton::before {
  content: url(images/toolbarButton-viewAttachments.svg);
  content: var(--toolbarButton-viewAttachments-icon);
}

@media (prefers-color-scheme: dark) {

  #viewAttachments.toolbarButton::before {
  content: url(images/toolbarButton-viewAttachments-dark.svg);
  content: var(--toolbarButton-viewAttachments-icon);
  }
}

#viewLayers.toolbarButton::before {
  content: url(images/toolbarButton-viewLayers.svg);
  content: var(--toolbarButton-viewLayers-icon);
}

@media (prefers-color-scheme: dark) {

  #viewLayers.toolbarButton::before {
  content: url(images/toolbarButton-viewLayers-dark.svg);
  content: var(--toolbarButton-viewLayers-icon);
  }
}

#viewFind.toolbarButton::before {
  content: url(images/toolbarButton-search.svg);
  content: var(--toolbarButton-search-icon);
}

@media (prefers-color-scheme: dark) {

  #viewFind.toolbarButton::before {
  content: url(images/toolbarButton-search-dark.svg);
  content: var(--toolbarButton-search-icon);
  }
}

.toolbarButton.pdfSidebarNotification::after {
  position: absolute;
  display: inline-block;
  top: 1px;
  /* Create a filled circle, with a diameter of 9 pixels, using only CSS: */
  content: "";
  background-color: rgba(112, 219, 85, 1);
  height: 9px;
  width: 9px;
  border-radius: 50%;
}
html[dir="ltr"] .toolbarButton.pdfSidebarNotification::after {
  left: 17px;
}
html[dir="rtl"] .toolbarButton.pdfSidebarNotification::after {
  right: 17px;
}

.secondaryToolbarButton {
  position: relative;
  margin: 0;
  padding: 0 0 1px 0;
  height: auto;
  min-height: 26px;
  width: auto;
  min-width: 100%;
  white-space: normal;
  border-radius: 0;
  box-sizing: border-box;
}
html[dir="ltr"] .secondaryToolbarButton {
  padding-left: 36px;
  text-align: left;
}
html[dir="rtl"] .secondaryToolbarButton {
  padding-right: 36px;
  text-align: right;
}

html[dir="ltr"] .secondaryToolbarButton > span {
  padding-right: 4px;
}
html[dir="rtl"] .secondaryToolbarButton > span {
  padding-left: 4px;
}

.secondaryToolbarButton.firstPage::before {
  content: url(images/secondaryToolbarButton-firstPage.svg);
  content: var(--secondaryToolbarButton-firstPage-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.firstPage::before {
  content: url(images/secondaryToolbarButton-firstPage-dark.svg);
  content: var(--secondaryToolbarButton-firstPage-icon);
  }
}

.secondaryToolbarButton.lastPage::before {
  content: url(images/secondaryToolbarButton-lastPage.svg);
  content: var(--secondaryToolbarButton-lastPage-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.lastPage::before {
  content: url(images/secondaryToolbarButton-lastPage-dark.svg);
  content: var(--secondaryToolbarButton-lastPage-icon);
  }
}

.secondaryToolbarButton.rotateCcw::before {
  content: url(images/secondaryToolbarButton-rotateCcw.svg);
  content: var(--secondaryToolbarButton-rotateCcw-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.rotateCcw::before {
  content: url(images/secondaryToolbarButton-rotateCcw-dark.svg);
  content: var(--secondaryToolbarButton-rotateCcw-icon);
  }
}

.secondaryToolbarButton.rotateCw::before {
  content: url(images/secondaryToolbarButton-rotateCw.svg);
  content: var(--secondaryToolbarButton-rotateCw-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.rotateCw::before {
  content: url(images/secondaryToolbarButton-rotateCw-dark.svg);
  content: var(--secondaryToolbarButton-rotateCw-icon);
  }
}

.secondaryToolbarButton.selectTool::before {
  content: url(images/secondaryToolbarButton-selectTool.svg);
  content: var(--secondaryToolbarButton-selectTool-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.selectTool::before {
  content: url(images/secondaryToolbarButton-selectTool-dark.svg);
  content: var(--secondaryToolbarButton-selectTool-icon);
  }
}

.secondaryToolbarButton.handTool::before {
  content: url(images/secondaryToolbarButton-handTool.svg);
  content: var(--secondaryToolbarButton-handTool-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.handTool::before {
  content: url(images/secondaryToolbarButton-handTool-dark.svg);
  content: var(--secondaryToolbarButton-handTool-icon);
  }
}

.secondaryToolbarButton.scrollVertical::before {
  content: url(images/secondaryToolbarButton-scrollVertical.svg);
  content: var(--secondaryToolbarButton-scrollVertical-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.scrollVertical::before {
  content: url(images/secondaryToolbarButton-scrollVertical-dark.svg);
  content: var(--secondaryToolbarButton-scrollVertical-icon);
  }
}

.secondaryToolbarButton.scrollHorizontal::before {
  content: url(images/secondaryToolbarButton-scrollHorizontal.svg);
  content: var(--secondaryToolbarButton-scrollHorizontal-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.scrollHorizontal::before {
  content: url(images/secondaryToolbarButton-scrollHorizontal-dark.svg);
  content: var(--secondaryToolbarButton-scrollHorizontal-icon);
  }
}

.secondaryToolbarButton.scrollWrapped::before {
  content: url(images/secondaryToolbarButton-scrollWrapped.svg);
  content: var(--secondaryToolbarButton-scrollWrapped-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.scrollWrapped::before {
  content: url(images/secondaryToolbarButton-scrollWrapped-dark.svg);
  content: var(--secondaryToolbarButton-scrollWrapped-icon);
  }
}

.secondaryToolbarButton.spreadNone::before {
  content: url(images/secondaryToolbarButton-spreadNone.svg);
  content: var(--secondaryToolbarButton-spreadNone-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.spreadNone::before {
  content: url(images/secondaryToolbarButton-spreadNone-dark.svg);
  content: var(--secondaryToolbarButton-spreadNone-icon);
  }
}

.secondaryToolbarButton.spreadOdd::before {
  content: url(images/secondaryToolbarButton-spreadOdd.svg);
  content: var(--secondaryToolbarButton-spreadOdd-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.spreadOdd::before {
  content: url(images/secondaryToolbarButton-spreadOdd-dark.svg);
  content: var(--secondaryToolbarButton-spreadOdd-icon);
  }
}

.secondaryToolbarButton.spreadEven::before {
  content: url(images/secondaryToolbarButton-spreadEven.svg);
  content: var(--secondaryToolbarButton-spreadEven-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.spreadEven::before {
  content: url(images/secondaryToolbarButton-spreadEven-dark.svg);
  content: var(--secondaryToolbarButton-spreadEven-icon);
  }
}

.secondaryToolbarButton.documentProperties::before {
  content: url(images/secondaryToolbarButton-documentProperties.svg);
  content: var(--secondaryToolbarButton-documentProperties-icon);
}

@media (prefers-color-scheme: dark) {

  .secondaryToolbarButton.documentProperties::before {
  content: url(images/secondaryToolbarButton-documentProperties-dark.svg);
  content: var(--secondaryToolbarButton-documentProperties-icon);
  }
}

.verticalToolbarSeparator {
  display: block;
  padding: 11px 0;
  margin: 5px 2px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--separator-color);
}

@media (prefers-color-scheme: dark) {

  .verticalToolbarSeparator {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--separator-color);
  }
}
html[dir="ltr"] .verticalToolbarSeparator {
  margin-left: 2px;
}
html[dir="rtl"] .verticalToolbarSeparator {
  margin-right: 2px;
}

.horizontalToolbarSeparator {
  display: block;
  margin: 6px 0 5px 0;
  height: 1px;
  width: 100%;
  border-top: 1px solid rgba(222, 222, 222, 1);
  border-top: 1px solid var(--doorhanger-separator-color);
}

@media (prefers-color-scheme: dark) {

  .horizontalToolbarSeparator {
  border-top: 1px solid rgba(92, 92, 97, 1);
  border-top: 1px solid var(--doorhanger-separator-color);
  }
}

.toolbarField {
  padding: 4px 7px;
  margin: 3px 0 3px 0;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 1);
  background-color: var(--field-bg-color);
  background-clip: padding-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(187, 187, 188, 1);
  border-color: var(--field-border-color);
  box-shadow: none;
  color: rgba(6, 6, 6, 1);
  color: var(--field-color);
  font-size: 12px;
  line-height: 16px;
  outline-style: none;
}

@media (prefers-color-scheme: dark) {

  .toolbarField {
  color: rgba(250, 250, 250, 1);
  color: var(--field-color);
  }
}

@media (prefers-color-scheme: dark) {

  .toolbarField {
  border-color: rgba(115, 115, 115, 1);
  border-color: var(--field-border-color);
  }
}

@media (prefers-color-scheme: dark) {

  .toolbarField {
  background-color: rgba(64, 64, 68, 1);
  background-color: var(--field-bg-color);
  }
}

.toolbarField[type="checkbox"] {
  opacity: 0;
  position: absolute !important;
  left: 0;
}

html[dir="ltr"] .toolbarField[type="checkbox"] {
  margin: 10px 0 3px 7px;
}

html[dir="rtl"] .toolbarField[type="checkbox"] {
  margin: 10px 7px 3px 0;
}

.toolbarField.pageNumber {
  -moz-appearance: textfield; /* hides the spinner in moz */
  min-width: 16px;
  text-align: right;
  width: 40px;
}

.toolbarField.pageNumber.visiblePageIsLoading {
  background-image: url(images/loading.svg);
  background-image: var(--loading-icon);
  background-repeat: no-repeat;
  background-position: 3px;
}

@media (prefers-color-scheme: dark) {

  .toolbarField.pageNumber.visiblePageIsLoading {
  background-image: url(images/loading-dark.svg);
  background-image: var(--loading-icon);
  }
}

.toolbarField.pageNumber::-webkit-inner-spin-button,
.toolbarField.pageNumber::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.toolbarField:focus {
  border-color: #0a84ff;
}

.toolbarLabel {
  min-width: 16px;
  padding: 6px;
  margin: 2px;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 2px;
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  font-size: 12px;
  line-height: 14px;
  text-align: left;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  cursor: default;
}

@media (prefers-color-scheme: dark) {

  .toolbarLabel {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}

html[dir="ltr"] #numPages.toolbarLabel {
  padding-left: 2px;
}
html[dir="rtl"] #numPages.toolbarLabel {
  padding-right: 2px;
}

#thumbnailView {
  position: absolute;
  width: calc(100% - 60px);
  top: 0;
  bottom: 0;
  padding: 10px 30px 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

#thumbnailView > a:active,
#thumbnailView > a:focus {
  outline: 0;
}

.thumbnail {
  margin: 0 10px 5px 10px;
}
html[dir="ltr"] .thumbnail {
  float: left;
}
html[dir="rtl"] .thumbnail {
  float: right;
}

#thumbnailView > a:last-of-type > .thumbnail {
  margin-bottom: 10px;
}

#thumbnailView > a:last-of-type > .thumbnail:not([data-loaded]) {
  margin-bottom: 9px;
}

.thumbnail:not([data-loaded]) {
  border: 1px dashed rgba(132, 132, 132, 1);
  margin: -1px 9px 4px 9px;
}

.thumbnailImage {
  border: 1px solid rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5), 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
  z-index: 99;
  background-color: rgba(255, 255, 255, 1);
  background-clip: content-box;
}

.thumbnailSelectionRing {
  border-radius: 2px;
  padding: 7px;
}

a:focus > .thumbnail > .thumbnailSelectionRing > .thumbnailImage,
.thumbnail:hover > .thumbnailSelectionRing > .thumbnailImage {
  opacity: 0.9;
}

a:focus > .thumbnail > .thumbnailSelectionRing {
  background-color: rgba(0, 0, 0, 0.15);
  background-color: var(--sidebaritem-bg-color);
  background-clip: padding-box;
  color: rgba(255, 255, 255, 0.9);
}

@media (prefers-color-scheme: dark) {

  a:focus > .thumbnail > .thumbnailSelectionRing {
  background-color: rgba(255, 255, 255, 0.15);
  background-color: var(--sidebaritem-bg-color);
  }
}

.thumbnail:hover > .thumbnailSelectionRing {
  background-color: rgba(0, 0, 0, 0.15);
  background-color: var(--sidebaritem-bg-color);
  background-clip: padding-box;
  color: rgba(255, 255, 255, 0.9);
}

@media (prefers-color-scheme: dark) {

  .thumbnail:hover > .thumbnailSelectionRing {
  background-color: rgba(255, 255, 255, 0.15);
  background-color: var(--sidebaritem-bg-color);
  }
}

.thumbnail.selected > .thumbnailSelectionRing > .thumbnailImage {
  opacity: 1;
}

.thumbnail.selected > .thumbnailSelectionRing {
  background-color: rgba(0, 0, 0, 0.15);
  background-color: var(--sidebaritem-bg-color);
  background-clip: padding-box;
  color: rgba(255, 255, 255, 1);
}

@media (prefers-color-scheme: dark) {

  .thumbnail.selected > .thumbnailSelectionRing {
  background-color: rgba(255, 255, 255, 0.15);
  background-color: var(--sidebaritem-bg-color);
  }
}

#outlineView,
#attachmentsView,
#layersView {
  position: absolute;
  width: calc(100% - 8px);
  top: 0;
  bottom: 0;
  padding: 4px 4px 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

html[dir="ltr"] .treeWithDeepNesting > .treeItem,
html[dir="ltr"] .treeItem > .treeItems {
  margin-left: 20px;
}

html[dir="rtl"] .treeWithDeepNesting > .treeItem,
html[dir="rtl"] .treeItem > .treeItems {
  margin-right: 20px;
}

.treeItem > a {
  text-decoration: none;
  display: inline-block;
  min-width: 95%;
  /* Subtract the right padding (left, in RTL mode) of the container: */
  min-width: calc(100% - 4px);
  height: auto;
  margin-bottom: 1px;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.8);
  color: var(--outline-color);
  font-size: 13px;
  line-height: 15px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: normal;
  cursor: pointer;
}

@media (prefers-color-scheme: dark) {

  .treeItem > a {
  color: rgba(255, 255, 255, 0.8);
  color: var(--outline-color);
  }
}
html[dir="ltr"] .treeItem > a {
  padding: 2px 0 5px 4px;
}
html[dir="rtl"] .treeItem > a {
  padding: 2px 4px 5px 0;
}

#layersView .treeItem > a > * {
  cursor: pointer;
}
html[dir="ltr"] #layersView .treeItem > a > label {
  padding-left: 4px;
}
html[dir="rtl"] #layersView .treesItem > a > label {
  padding-right: 4px;
}

.treeItemToggler {
  position: relative;
  height: 0;
  width: 0;
  color: rgba(255, 255, 255, 0.5);
}
.treeItemToggler::before {
  content: url(images/treeitem-expanded.svg);
  content: var(--treeitem-expanded-icon);
  display: inline-block;
  position: absolute;
  max-width: 16px;
}
@media (prefers-color-scheme: dark) {

  .treeItemToggler::before {
  content: url(images/treeitem-expanded-dark.svg);
  content: var(--treeitem-expanded-icon);
  }
}
.treeItemToggler.treeItemsHidden::before {
  content: url(images/treeitem-collapsed.svg);
  content: var(--treeitem-collapsed-icon);
  max-width: 16px;
}
@media (prefers-color-scheme: dark) {

  .treeItemToggler.treeItemsHidden::before {
  content: url(images/treeitem-collapsed-dark.svg);
  content: var(--treeitem-collapsed-icon);
  }
}
html[dir="rtl"] .treeItemToggler.treeItemsHidden::before {
  transform: scaleX(-1);
}
.treeItemToggler.treeItemsHidden ~ .treeItems {
  display: none;
}
html[dir="ltr"] .treeItemToggler {
  float: left;
}
html[dir="rtl"] .treeItemToggler {
  float: right;
}
html[dir="ltr"] .treeItemToggler::before {
  right: 4px;
}
html[dir="rtl"] .treeItemToggler::before {
  left: 4px;
}

.treeItemToggler:hover {
  background-color: rgba(0, 0, 0, 0.15);
  background-color: var(--sidebaritem-bg-color);
  background-clip: padding-box;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.9);
  color: var(--outline-hover-color);
}

@media (prefers-color-scheme: dark) {

  .treeItemToggler:hover {
  color: rgba(255, 255, 255, 0.9);
  color: var(--outline-hover-color);
  }
}

@media (prefers-color-scheme: dark) {

  .treeItemToggler:hover {
  background-color: rgba(255, 255, 255, 0.15);
  background-color: var(--sidebaritem-bg-color);
  }
}

.treeItemToggler:hover + a {
  background-color: rgba(0, 0, 0, 0.15);
  background-color: var(--sidebaritem-bg-color);
  background-clip: padding-box;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.9);
  color: var(--outline-hover-color);
}

@media (prefers-color-scheme: dark) {

  .treeItemToggler:hover + a {
  color: rgba(255, 255, 255, 0.9);
  color: var(--outline-hover-color);
  }
}

@media (prefers-color-scheme: dark) {

  .treeItemToggler:hover + a {
  background-color: rgba(255, 255, 255, 0.15);
  background-color: var(--sidebaritem-bg-color);
  }
}

.treeItemToggler:hover ~ .treeItems {
  background-color: rgba(0, 0, 0, 0.15);
  background-color: var(--sidebaritem-bg-color);
  background-clip: padding-box;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.9);
  color: var(--outline-hover-color);
}

@media (prefers-color-scheme: dark) {

  .treeItemToggler:hover ~ .treeItems {
  color: rgba(255, 255, 255, 0.9);
  color: var(--outline-hover-color);
  }
}

@media (prefers-color-scheme: dark) {

  .treeItemToggler:hover ~ .treeItems {
  background-color: rgba(255, 255, 255, 0.15);
  background-color: var(--sidebaritem-bg-color);
  }
}

.treeItem > a:hover {
  background-color: rgba(0, 0, 0, 0.15);
  background-color: var(--sidebaritem-bg-color);
  background-clip: padding-box;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.9);
  color: var(--outline-hover-color);
}

@media (prefers-color-scheme: dark) {

  .treeItem > a:hover {
  color: rgba(255, 255, 255, 0.9);
  color: var(--outline-hover-color);
  }
}

@media (prefers-color-scheme: dark) {

  .treeItem > a:hover {
  background-color: rgba(255, 255, 255, 0.15);
  background-color: var(--sidebaritem-bg-color);
  }
}

.treeItem.selected {
  background-color: rgba(0, 0, 0, 1);
  background-color: var(--outline-active-bg-color);
  background-clip: padding-box;
  color: rgba(0, 0, 0, 0.08);
  color: var(--outline-active-color);
}

@media (prefers-color-scheme: dark) {

  .treeItem.selected {
  color: rgba(255, 255, 255, 0.08);
  color: var(--outline-active-color);
  }
}

@media (prefers-color-scheme: dark) {

  .treeItem.selected {
  background-color: rgba(255, 255, 255, 1);
  background-color: var(--outline-active-bg-color);
  }
}

.noResults {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
  cursor: default;
}

/* TODO: file FF bug to support ::-moz-selection:window-inactive
   so we can override the opaque grey background when the window is inactive;
   see https://bugzilla.mozilla.org/show_bug.cgi?id=706209 */
::-moz-selection {
  background: rgba(0, 0, 255, 0.3);
}
::selection {
  background: rgba(0, 0, 255, 0.3);
}

#errorWrapper {
  background: none repeat scroll 0 0 rgba(255, 74, 74, 1);
  background: none repeat scroll 0 0 var(--errorWrapper-bg-color);
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  left: 0;
  position: absolute;
  right: 0;
  z-index: 1000;
  padding: 3px 6px;
}

@media (prefers-color-scheme: dark) {

  #errorWrapper {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}

@media (prefers-color-scheme: dark) {

  #errorWrapper {
  background: none repeat scroll 0 0 rgba(199, 17, 17, 1);
  background: none repeat scroll 0 0 var(--errorWrapper-bg-color);
  }
}
.loadingInProgress #errorWrapper {
  top: 37px;
}

#errorMessageLeft {
  float: left;
}

#errorMessageRight {
  float: right;
}

#errorMoreInfo {
  background-color: rgba(255, 255, 255, 1);
  background-color: var(--field-bg-color);
  color: rgba(6, 6, 6, 1);
  color: var(--field-color);
  border: 1px solid rgba(187, 187, 188, 1);
  border: 1px solid var(--field-border-color);
  padding: 3px;
  margin: 3px;
  width: 98%;
}

@media (prefers-color-scheme: dark) {

  #errorMoreInfo {
  border: 1px solid rgba(115, 115, 115, 1);
  border: 1px solid var(--field-border-color);
  }
}

@media (prefers-color-scheme: dark) {

  #errorMoreInfo {
  color: rgba(250, 250, 250, 1);
  color: var(--field-color);
  }
}

@media (prefers-color-scheme: dark) {

  #errorMoreInfo {
  background-color: rgba(64, 64, 68, 1);
  background-color: var(--field-bg-color);
  }
}

.overlayButton {
  width: auto;
  margin: 3px 4px 2px 4px !important;
  padding: 2px 11px 2px 11px;
}

#overlayContainer {
  display: table;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 40000;
}
#overlayContainer > * {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

#overlayContainer > .container {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}

#overlayContainer > .container > .dialog {
  display: inline-block;
  padding: 15px;
  border-spacing: 4px;
  color: rgba(12, 12, 13, 1);
  color: var(--main-color);
  font-size: 12px;
  line-height: 14px;
  background-color: rgba(255, 255, 255, 1);
  background-color: var(--doorhanger-bg-color);
  border: 1px solid rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

@media (prefers-color-scheme: dark) {

  #overlayContainer > .container > .dialog {
  background-color: rgba(74, 74, 79, 1);
  background-color: var(--doorhanger-bg-color);
  }
}

@media (prefers-color-scheme: dark) {

  #overlayContainer > .container > .dialog {
  color: rgba(249, 249, 250, 1);
  color: var(--main-color);
  }
}

.dialog > .row {
  display: table-row;
}

.dialog > .row > * {
  display: table-cell;
}

.dialog .toolbarField {
  margin: 5px 0;
}

.dialog .separator {
  display: block;
  margin: 4px 0 4px 0;
  height: 1px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--separator-color);
}

@media (prefers-color-scheme: dark) {

  .dialog .separator {
  background-color: rgba(0, 0, 0, 0.3);
  background-color: var(--separator-color);
  }
}

.dialog .buttonRow {
  text-align: center;
  vertical-align: middle;
}

.dialog :link {
  color: rgba(255, 255, 255, 1);
}

#passwordOverlay > .dialog {
  text-align: center;
}
#passwordOverlay .toolbarField {
  width: 200px;
}

#documentPropertiesOverlay > .dialog {
  text-align: left;
}
#documentPropertiesOverlay .row > * {
  min-width: 100px;
}
html[dir="ltr"] #documentPropertiesOverlay .row > * {
  text-align: left;
}
html[dir="rtl"] #documentPropertiesOverlay .row > * {
  text-align: right;
}
#documentPropertiesOverlay .row > span {
  width: 125px;
  word-wrap: break-word;
}
#documentPropertiesOverlay .row > p {
  max-width: 225px;
  word-wrap: break-word;
}
#documentPropertiesOverlay .buttonRow {
  margin-top: 10px;
}

.clearBoth {
  clear: both;
}

.fileInput {
  background: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  margin-top: 5px;
  visibility: hidden;
  position: fixed;
  right: 0;
  top: 0;
}

#PDFBug {
  background: none repeat scroll 0 0 rgba(255, 255, 255, 1);
  border: 1px solid rgba(102, 102, 102, 1);
  position: fixed;
  top: 32px;
  right: 0;
  bottom: 0;
  font-size: 10px;
  padding: 0;
  width: 300px;
}
#PDFBug .controls {
  background: rgba(238, 238, 238, 1);
  border-bottom: 1px solid rgba(102, 102, 102, 1);
  padding: 3px;
}
#PDFBug .panels {
  bottom: 0;
  left: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  right: 0;
  top: 27px;
}
#PDFBug .panels > div {
  padding: 5px;
}
#PDFBug button.active {
  font-weight: bold;
}
.debuggerShowText {
  background: none repeat scroll 0 0 rgba(255, 255, 0, 1);
  color: rgba(0, 0, 255, 1);
}
.debuggerHideText:hover {
  background: none repeat scroll 0 0 rgba(255, 255, 0, 1);
}
#PDFBug .stats {
  font-family: courier;
  font-size: 10px;
  white-space: pre;
}
#PDFBug .stats .title {
  font-weight: bold;
}
#PDFBug table {
  font-size: 10px;
}

#viewer.textLayer-visible .textLayer {
  opacity: 1;
}

#viewer.textLayer-visible .canvasWrapper {
  background-color: rgba(128, 255, 128, 1);
}

#viewer.textLayer-visible .canvasWrapper canvas {
  mix-blend-mode: screen;
}

#viewer.textLayer-visible .textLayer > span {
  background-color: rgba(255, 255, 0, 0.1);
  color: rgba(0, 0, 0, 1);
  border: solid 1px rgba(255, 0, 0, 0.5);
  box-sizing: border-box;
}

#viewer.textLayer-hover .textLayer > span:hover {
  background-color: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
}

#viewer.textLayer-shadow .textLayer > span {
  background-color: rgba(255, 255, 255, 0.6);
  color: rgba(0, 0, 0, 1);
}

.grab-to-pan-grab {
  cursor: url("images/grab.cur"), move !important;
  cursor: -webkit-grab !important;
  cursor: grab !important;
}
.grab-to-pan-grab
  *:not(input):not(textarea):not(button):not(select):not(:link) {
  cursor: inherit !important;
}
.grab-to-pan-grab:active,
.grab-to-pan-grabbing {
  cursor: url("images/grabbing.cur"), move !important;
  cursor: -webkit-grabbing !important;
  cursor: grabbing !important;
  position: fixed;
  background: rgba(0, 0, 0, 0);
  display: block;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 50000; /* should be higher than anything else in PDF.js! */
}

@page {
  margin: 0;
}

#printContainer {
  display: none;
}

@media print {
  /* General rules for printing. */
  body {
    background: rgba(0, 0, 0, 0) none;
  }

  /* Rules for browsers that don't support mozPrintCallback. */
  #sidebarContainer,
  #secondaryToolbar,
  .toolbar,
  #loadingBox,
  #errorWrapper,
  .textLayer {
    display: none;
  }
  #viewerContainer {
    overflow: visible;
  }

  #mainContainer,
  #viewerContainer,
  .page,
  .page canvas {
    position: static;
    padding: 0;
    margin: 0;
  }

  .page {
    float: left;
    display: none;
    border: none;
    box-shadow: none;
    background-clip: content-box;
    background-color: rgba(255, 255, 255, 1);
  }

  .page[data-loaded] {
    display: block;
  }

  .fileInput {
    display: none;
  }

  /* Rules for browsers that support PDF.js printing */
  body[data-pdfjsprinting] #outerContainer {
    display: none;
  }
  body[data-pdfjsprinting] #printContainer {
    display: block;
  }
  #printContainer {
    height: 100%;
  }
  /* wrapper around (scaled) print canvas elements */
  #printContainer > div {
    position: relative;
    top: 0;
    left: 0;
    width: 1px;
    height: 1px;
    overflow: visible;
    page-break-after: always;
    page-break-inside: avoid;
  }
  #printContainer canvas,
  #printContainer img {
    direction: ltr;
    display: block;
  }
}

.visibleLargeView,
.visibleMediumView,
.visibleSmallView {
  display: none;
}

@media all and (max-width: 900px) {
  #toolbarViewerMiddle {
    display: table;
    margin: auto;
    left: auto;
    position: inherit;
    transform: none;
  }
}

@media all and (max-width: 840px) {
  #sidebarContent {
    background-color: rgba(0, 0, 0, 0.7);
  }

  html[dir="ltr"] #outerContainer.sidebarOpen #viewerContainer {
    left: 0px !important;
  }
  html[dir="rtl"] #outerContainer.sidebarOpen #viewerContainer {
    right: 0px !important;
  }

  #outerContainer .hiddenLargeView,
  #outerContainer .hiddenMediumView {
    display: inherit;
  }
  #outerContainer .visibleLargeView,
  #outerContainer .visibleMediumView {
    display: none;
  }
}

@media all and (max-width: 770px) {
  #outerContainer .hiddenLargeView {
    display: none;
  }
  #outerContainer .visibleLargeView {
    display: inherit;
  }
}

@media all and (max-width: 700px) {
  #outerContainer .hiddenMediumView {
    display: none;
  }
  #outerContainer .visibleMediumView {
    display: inherit;
  }
}

@media all and (max-width: 640px) {
  .hiddenSmallView,
  .hiddenSmallView * {
    display: none;
  }
  .visibleSmallView {
    display: inherit;
  }
  .toolbarButtonSpacer {
    width: 0;
  }
  html[dir="ltr"] .findbar {
    left: 34px;
  }
  html[dir="rtl"] .findbar {
    right: 34px;
  }
}

@media all and (max-width: 535px) {
  #scaleSelectContainer {
    display: none;
  }
}

.highlighted{
  border-bottom: 2px dashed #F00;
  position: relative;
  top: -6px;
}

.highlighted-tag{
  position: absolute;
  font-size: 12px;
  left: -20px;
  top: -5px;
  z-index: 9999000099;
  opacity: 1;
  padding: 2px 4px;
  color: #fff;
  background: rgb(4, 87, 240);
}

