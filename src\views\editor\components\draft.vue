<template>
  <div class="draft-container">
    <div class="func-box">
      <div class="left">
        <el-tooltip class="item" effect="dark" content="添加矩形" placement="top">
          <img class="func-img" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/juxing.png"
            @click="handleCommand('rectangle')" />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="添加六边形" placement="top">
          <img class="func-img" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/assets/img/six.png"
            @click="addHexagon" />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="添加圆形" placement="top">
          <img class="func-img" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/cir.png"
            @click="handleCommand('cir')" />
        </el-tooltip>
        <el-tooltip  class="item" effect="dark" content="添加文字" placement="top">
          <img class="func-img" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/text.png" @click="handleCommand('text')">
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="添加图片" placement="top">
          <i class="el-icon-picture-outline" @click="addPhoto"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="添加组" placement="top">
          <img class="func-img" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/Group_1.png"
            @click="addGroup" />
        </el-tooltip>
        <input type="file" ref="imageFile" accept="image/*" id="file" @change="handleFileChange"
          style="display: none" />
        <el-tooltip class="item" effect="dark" content="撤销" placement="top">
          <i class="el-icon-arrow-left" :class="!canUndo ? 'disable' : ''" @click="undo"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="重做" placement="top">
          <i class="el-icon-arrow-right" :class="!canRedo ? 'disable' : ''" @click="redo"></i>
        </el-tooltip>
      </div>
      <div class="center">
        <el-input placeholder="请输入白板标题" class="title" v-model="title"></el-input>
      </div>
      <div class="right">
        <el-popover placement="top-start" title="帮助" width="400" trigger="hover">
          <i slot="reference" class="el-icon-upload" @click="uploadFile"></i>
          <div>
            <p>
              ●
              导入功能：本地上传txt文档，将创建一个白板图；如果已经打开的白板图上点击导入，那么新导入的内容将被合并在一起。
            </p>
            <p>● 图形代号：”1-“ 代表圆形，”2-“代表方形，</p>
            <p>
              ●
              txt文档格式：【1-元素名称】子元素内容；句号为句子的分割符号。例如：【1-元素】子元素a。子元素b....。
            </p>
          </div>
        </el-popover>
        <input type="file" accept=".txt" ref="textFile" id="textFile" style="display: none"
          @change="handleFileSelect" />
        <el-popover placement="top-start" title="快捷键" width="200" trigger="hover">
          <i class="el-icon-help" slot="reference"></i>
          <div>
            <p>1. Del 删除</p>
            <p>2. Ctrl + C 复制</p>
            <p>3. Ctrl + V 粘贴</p>
            <p>4. Ctrl + X 删除</p>
            <p>5. Ctrl + Z 撤销</p>
            <p>6. Ctrl + Y 重做</p>
            <p>7. 空格 + 拖拽</p>
          </div>
        </el-popover>
        <el-dropdown @command="toShare">
          <i class="el-icon-share"></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="0">好友分享</el-dropdown-item>
            <el-dropdown-item :command="1">站外分享</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <p id="copyBtn" ref="copyBtn" style="display: none" :data-clipboard-text="copyText"></p>
        <el-tooltip class="item" effect="dark" content="下载" placement="top">
          <i class="el-icon-download" @click="downloadImage"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="保存" placement="top">
          <img class="save-img" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/save.png" @click="save" />
        </el-tooltip>
      </div>
    </div>
    <div class="draft-con">
      <div id="graph-container"></div>
      <div class="draft-menu" v-show="menuShow" :style="{ left: `${menu.x}px`, top: `${menu.y}px` }">
        <div class="item" @click="nodeEdit">编辑</div>
        <div class="item" v-if="currentNode?.data?.isGroup" @click="openAddGroup">
          添加子元素
        </div>
        <div class="item" @click="nodeRemoveColor">加边框</div>
        <div class="item" @click="nodeRemove">删除</div>
      </div>
      <div class="more-tooltip" v-show="tooltipMore" :style="{ left: tooltipObj.x + 'px', top: tooltipObj.y + 'px' }">
        <p>{{ tooltipText }}</p>
      </div>
    </div>
    <Node :drawer="drawer" :currentNode="currentNode" @update:drawer="drawer = false" @update:node="updateNode"
      @add:node="addChildNode"></Node>
    <Edge :edgeVisible="edgeVisible" :currentEdge="currentEdge" @update:visible="edgeVisible = false"
      @update:edge="updateEdge"></Edge>
    <el-dialog title="分享白板" :visible.sync="shareDialog" width="50%" :before-close="handleShareClose">
      <div class="friend-list">
        <el-checkbox-group v-model="checkList">
          <div class="item-friend" v-for="item in friendList" :key="item.bei_member_id">
            <div class="left">
              <el-checkbox :label="item.bei_member_id"></el-checkbox>
            </div>
            <div class="right">
              <img :src="item.member_avatar" />
              <span class="nick">{{ item.member_name }}</span>
            </div>
          </div>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="shareDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitShare">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="添加组元素" :visible.sync="addGroupVisible" width="50%">
      <div class="add-group-from">
        <div class="item">
          组件名称:
          <el-input v-model="groupObj.name" placeholder="请输入组名称" style="width: 300px" />
        </div>
        <div class="item">
          组件类型:
          <el-select v-model="groupObj.type" placeholder="请选择">
            <el-option label="矩形" :value="1"></el-option>
            <el-option label="圆形" :value="2"></el-option>
            <el-option label="六边形" :value="3"></el-option>
            <el-option label="图片" :value="4"></el-option>
          </el-select>
        </div>
        <div class="item" v-if="groupObj.type === 4">
          上传文件
          <el-input id="updateFile" v-model="groupObj.file" type="file" accept="image/*" style="width: 300px" />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addGroupVisible = false">取 消</el-button>
        <el-button type="primary" @click="addGroupNode">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/http/request'
import { Graph } from '@antv/x6'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { Export } from '@antv/x6-plugin-export'
import { History } from '@antv/x6-plugin-history'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Transform } from '@antv/x6-plugin-transform'
import ClipboardJS from 'clipboard'
import { Loading } from 'element-ui'
import Edge from './edge.vue'
import GraphOnEvent from './event'
import Group from './group'
import Node from './node.vue'
import NodeEvent from './nodeEvent'
import {
childNode,
cirNode,
getChildNodePositions,
getId,
getRectPort,
hexagonNode,
rectNode,
textNode,
typeLine
} from './util'

let isSubmit = false
// 获取空格键的按键代码
const SPACE_KEY = 32
const sizeLimit = 4 * 1024 * 1024
export default {
  data () {
    return {
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      checkList: [],
      graph: null,
      history: null,
      canRedo: false,
      canUndo: false,
      drawer: false,
      edgeVisible: false,
      title: '',
      currentNode: null,
      currentEdge: null,
      menuShow: false,
      menu: {
        x: 0,
        y: 0
      },
      isDownload: false,
      exportInstance: null,
      selection: null,
      friendList: [],
      copyText: '',
      select: true,
      shareDialog: false,
      tooltipMore: false,
      tooltipText: 'text', // 用户hover 子节点，内容过多的时候展示tooltip
      tooltipObj: {
        x: 0,
        y: 0
      },
      group: null,
      addGroupVisible: false,
      groupObj: {
        name: '',
        type: 1,
        file: null
      }
    }
  },
  components: {
    Node,
    Edge
  },
  props: ['draftId'],
  watch: {
    draftId (val) {
      if (!val) {
        this.title = ''
        this.graph?.clearCells()
        this.history?.cleanHistory()
        return
      }
      this.getDetail()
    }
  },
  methods: {
    addText () {
      console.log('addText')
    },
    openAddGroup () {
      this.addGroupVisible = true
      this.menuShow = false
    },
    addGroup () {
      this.group.addGroup()
    },
    addGroupNode () {
      if (!this.groupObj.name) {
        this.$message.info('请填写组件名称')
      }
      const { x, y } = this.currentNode.getPosition()
      let node = rectNode({
        parentId: this.currentNode.id,
        label: this.groupObj.name,
        x: x + 5,
        y: y + 5,
        width: 80,
        height: 40
      })
      if (this.groupObj.type === 2) {
        node = cirNode({
          label: this.groupObj.name,
          x: x + 5,
          y: y + 5,
          width: 60,
          height: 60
        })
      }
      if (this.groupObj.type === 3) {
        node = hexagonNode({
          label: this.groupObj.name,
          x: x + 5,
          y: y + 5,
          width: 100,
          height: 80
        })
      }
      if (this.groupObj.type === 4) {
        const file = document.querySelector('#updateFile').files[0]
        const imageUrl = URL.createObjectURL(file)
        const image = new Image()
        image.src = imageUrl
        image.onload = () => {
          const canvas = document.createElement('canvas')
          const context = canvas.getContext('2d')
          canvas.width = image.width
          canvas.height = image.height
          context.drawImage(image, 0, 0)
          const base64Data = canvas.toDataURL('image/webp', 0.6)
          let width = image.width
          let height = image.height
          if (width > 200) {
            width = 200
            height = height / (image.width / 200)
          }
          node = this.graph.addNode({
            id: getId(),
            shape: 'image',
            x: 10,
            y: 10,
            width,
            height,
            imageUrl: base64Data,
            ports: getRectPort(width, height)
          })
        }
      }
      const newNode = this.graph.addNode(node)
      this.currentNode.addChild(newNode)
      this.addGroupVisible = false
      this.groupObj = {
        name: '',
        type: 1,
        file: null
      }
    },
    handleFileSelect () {
      const loading = this.$loading({
        lock: true,
        text: '文件导入中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (this.draftId) {
        this.save()
      }
      const fileInput = this.$refs.textFile
      const file = fileInput.files[0]

      if (file.size > sizeLimit) {
        fileInput.value = ''
        this.$message.error('文件大小超过限制')
        return
      }

      const formData = new FormData()
      formData.append('file', file)
      formData.append('member_id', this.userinfo.userid)
      formData.append('draft_id', this.draftId)
      // 使用XMLHttpRequest或Fetch API发送数据
      var xhr = new XMLHttpRequest()
      const authorization = localStorage.getItem('authorization')
      xhr.open('POST', '/api/Draft/batchDraftContent', true)
      xhr.setRequestHeader('Authorization', authorization)
      xhr.onload = () => {
        if (xhr.status === 200) {
          const result = JSON.parse(xhr.responseText).result
          const id = result.id.toString()
          if (this.draftId.toString() === id) {
            this.getDetail()
          } else {
            this.$router.push({
              name: 'draft',
              query: {
                id: id
              }
            })
          }
          loading.close()
        } else {
          loading.close()
          this.$message.error('文件上传失败')
        }
        fileInput.value = ''
      }
      xhr.send(formData)
    },
    uploadFile () {
      const inputRef = this.$refs.textFile
      inputRef.click()
    },
    search () {
      // 遍历所有节点，根据搜索内容匹配来动态改变节点标签的颜色
      this.graph.getNodes().forEach((node) => {
        const label = node.getLabel()
        if (label) {
          const text = label
          const htmlLabel = document.createElement('div')

          // 根据搜索内容匹配来动态改变文字颜色
          const coloredText = text.replace(
            new RegExp('新', 'g'),
            '<span style="color: #00FF00;">新</span>'
          )
          htmlLabel.innerHTML = coloredText

          label.attr('html', htmlLabel)
        }
      })
    },
    addVerticalLine () {
      this.graph.addEdge({
        source: { x: 40, y: 40 },
        target: { x: 40, y: 180 }
      })
    },
    handleShareClose () {
      this.shareDialog = false
    },
    nodeRemoveColor () {
      this.currentNode.attr('body/fill', 'transparent')
      this.currentNode.attr('body/strokeWidth', 1)
      this.currentNode.attr('body/stroke', '#000')
      this.currentNode.attr('text/fill', '#000')
      this.menuShow = false
    },
    toShare (command) {
      if (command) {
        if (!this.title) {
          return this.$message.info('文档名称不能为空')
        }
        if (this.userinfo) {
          this.copyText = `你的好友分享了${this.title} 请复制链接在浏览器打开\n https://www.bookor.com.cn/#/editor/draft?id=${this.draftId}&userId=${this.userinfo.userid}`
        }
        this.$nextTick(() => {
          this.$refs.copyBtn.click()
        })
      } else {
        request('/mobile/member/myFans', {
          key: this.userinfo?.key,
          type: '0'
        })
          .then((result) => {
            const list = result
            this.shareDialog = true
            this.friendList = list
          })
          .catch(() => {
            this.$message.error('获取好友失败')
          })
      }
    },
    submitShare () {
      if (!this.checkList.length) {
        return this.$message.info('请选择分享人')
      }
      request('/api/Draft/draftShare', {
        draft_id: this.draftId,
        member_ids: this.checkList.join()
      }).then(() => {
        this.$message.success('分享成功')
        this.shareDialog = false
      })
    },
    addRect () {
      const rect = rectNode()
      this.graph.addNode(rect)
    },
    addCir () {
      const cir = cirNode()
      this.graph.addNode(cir)
    },
    addHexagon () {
      const node = hexagonNode()
      this.graph.addNode(node)
    },
    addText () {
      const text = textNode()
      this.graph.addNode(text)
    },

    addChildNode (obj) {
      // 添加字元素
      const { idx, data, total, ids } = obj
      const arr = getChildNodePositions(data, total)
      // 字元素位置重新排列
      if (idx > 0) {
        for (let i = 0; i < idx; i += 1) {
          const currentNode = this.graph.getCellById(ids[i].toString())
          if (currentNode) {
            currentNode.setPosition({ x: arr[i].x, y: arr[i].y })
          }
        }
      }
      const child = childNode(obj, arr[total - 1].x, arr[total - 1].y)
      this.graph.addNode(child)
      this.$nextTick(() => {
        const source = this.graph.getCellById(child.id)
        // const point = this.newVertices(source, this.currentNode)
        this.graph.addEdge({
          source: {
            cell: this.currentNode.id,
            port: this.currentNode.ports.items[1].id
          },
          target: { cell: child.id, port: child.ports.items[0].id },
          attrs: {
            line: {
              stroke: '#8f8f8f',
              strokeWidth: 2,
              targetMarker: '',
              sourceMarker: '',
              strokeDasharray: '5 3'
            }
          },
          // vertices: [],
          zIndex: 0,
          connector: 'normal'
        })
      })
    },
    addPhoto () {
      const inputRef = this.$refs.imageFile
      inputRef.click()
    },
    handleFileChange (e) {
      const file = e.target.files[0]
      const imageUrl = URL.createObjectURL(file)
      const image = new Image()
      image.src = imageUrl
      image.onload = () => {
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        canvas.width = image.width
        canvas.height = image.height
        context.drawImage(image, 0, 0)
        const base64Data = canvas.toDataURL('image/webp', 0.6)
        let width = image.width
        let height = image.height
        if (width > 200) {
          width = 200
          height = height / (image.width / 200)
        }
        this.graph.addNode({
          id: getId(),
          shape: 'image',
          x: 10,
          y: 10,
          width,
          height,
          label: '图片',
          attrs: {
            label: {
              fill: '#f00',
              fontSize: 14,
              refX: 0.5, // 水平锚点，0.5 表示标签居中
              refY: 0.9 // 垂直锚点，0.5 表示标签
            }
          },
          imageUrl: base64Data,
          ports: getRectPort(width, height)
        })
      }
    },
    handleCommand (command) {
      if (command === 'rectangle') {
        this.addRect()
      } else if (command === 'six') {
        this.addSix()
      } else if (command === 'text') {
        this.addText()
      } else {
        this.addCir()
      }
    },
    handleSelect () {
      this.select = !this.select
      this.graph.togglePanning(!this.select)
      this.selection.toggleRubberband(this.select)
    },
    redo () {
      this.history.redo()
    },
    undo () {
      this.history.undo()
    },
    updatePort (node) {
      const { width, height } = node.getBBox()
      const currentNode = this.graph.getCellById(node.id)
      currentNode.setPortProp('port1', 'args', {
        x: 0,
        y: height / 2
      })
      currentNode.setPortProp('port2', 'args', {
        x: width,
        y: height / 2
      })
      currentNode.setPortProp('port3', 'args', {
        x: width / 2,
        y: 0
      })
      currentNode.setPortProp('port4', 'args', {
        x: width / 2,
        y: height
      })
    },
    updateNode (node) {
      const currentNode = this.graph.getCellById(node.id)
      currentNode.setLabel(node.label)
      if (currentNode.shape === 'image') {
        currentNode.attr('label/refY', 0.9)
        currentNode.attr('label/refX', 0.5)
        currentNode.attr('label/fill', '#f00')
        currentNode.attr('label/fontSize', 14)
      }
      currentNode.attr('text/fill', node.textColor)
      currentNode.attr('body/fill', node.backgroundColor)
      currentNode.attr('body/stroke', node.textColor)
      currentNode.attr('body/strokeWidth', node.border ? 1 : 0)
      currentNode.setProp('size', { width: node.width, height: node.height })
      this.updatePort(currentNode)
      this.drawer = false
    },
    nodeEdit () {
      this.menuShow = false
      this.drawer = true
    },
    nodeRemove () {
      this.menuShow = false
      this.graph.removeCell(this.currentNode)
      const childrenList = this.graph.getCells().filter((item) => {
        return item?.data?.id.includes(this.currentNode.id)
      })
      if (childrenList.length) {
        childrenList.forEach((item) => {
          this.graph.removeCell(item)
        })
      }
    },
    newVertices (target, source) {
      // 计算中点坐标
      const midX = (source.position().x + target.position().x + 50) / 2
      const midY = (source.position().y + target.position().y + 50) / 2
      // 计算控制点坐标
      return [{ x: midX, y: midY }]
    },
    updateEdge (edge) {
      const currentEdge = this.graph.getCellById(edge.id)
      currentEdge.setLabelAt(0, edge.label)
      currentEdge.attr('line/stroke', edge.color)
      currentEdge.attr('line/vertices', edge.color)
      const point = this.newVertices(
        currentEdge.getTargetNode(),
        currentEdge.getSourceNode()
      )

      if (edge.category) {
        currentEdge.vertices = point
        currentEdge.connector = 'smooth'
      } else {
        currentEdge.vertices = []
        currentEdge.connector = undefined
      }

      if ([2, 4, 6].includes(edge.type)) {
        currentEdge.attr('line/strokeDasharray', '5 3')
      } else {
        currentEdge.attr('line/strokeDasharray', '0')
      }
      const line = typeLine(edge.type)
      currentEdge.attr({
        line: {
          sourceMarker: line.sourceMarker,
          targetMarker: line.targetMarker
        }
      })
      this.edgeVisible = false
    },
    addDraft (json) {
      if (!isSubmit) {
        isSubmit = true
        request('/api/Draft/addDraft', {
          title: this.title,
          content: JSON.stringify(json)
        })
          .then((result) => {
            isSubmit = false
            this.$emit('editDraftId', result)
            this.$message({
              message: '保存成功',
              type: 'success',
              duration: 1000
            })
          })
          .catch(() => {
            this.$message({
              message: '保存失败，系统错误',
              type: 'error',
              duration: 1000
            })
          })
      }
    },
    editDraft (json) {
      request('/api/Draft/editDraft', {
        id: this.draftId,
        title: this.title,
        content: JSON.stringify(json)
      })
        .then((result) => {
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 1000
          })
        })
        .catch(() => {
          this.$message({
            message: '保存失败，系统错误',
            type: 'error',
            duration: 1000
          })
        })
    },
    save () {
      const json = this.graph.toJSON()
      if (!this.title) {
        return this.$message({
          message: '标题不能为空',
          type: 'error',
          duration: 1000
        })
      }
      if (!json.cells.length) {
        return this.$message({
          message: '白板内容为空',
          type: 'error',
          duration: 1000
        })
      }
      if (this.draftId) {
        this.editDraft(json)
      } else {
        this.addDraft(json)
      }
    },
    downloadImage () {
      this.isDownload = true
      this.exportInstance.exportPNG(this.title || 'download', {
        width: 4500,
        height: 4500,
        backgroundColor: '#fff',
        padding: 1,
        quality: 1
      })
      setTimeout(() => {
        this.isDownload = false
        this.$message.success('下载成功')
      }, 2000)
    },
    getDetail () {
      this.loadingInstance = Loading.service({
        fullscreen: true, // 是否全屏遮罩
        text: '加载中...', // 显示的文本
        background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
      })
      request('/api/Draft/draftDetail', {
        id: this.draftId
      })
        .then((result) => {
          this.loadingInstance.close()
          const data = result.content
          this.title = result.title
          if (data) {
            this.graph.fromJSON(JSON.parse(data))
            setTimeout(() => {
              this.showPorts(false)
            }, 200)
          }
        })
        .catch(() => {
          this.loadingInstance.close()
        })
    },
    showPorts (show) {
      const container = document.getElementById('graph-container')
      const ports = container.querySelectorAll('.x6-port-body')
      for (let i = 0, len = ports.length; i < len; i += 1) {
        ports[i].style.visibility = show ? 'visible' : 'hidden'
      }
    },
    hideSnapline () {
      // 因为辅助线官方有点bug，同时出现2条辅助线的话会无法消失，这里自己写方法解决一下
      setTimeout(() => {
        const dom = document.querySelector(
          '.x6-widget-snapline .x6-widget-snapline-horizontal'
        )
        if (dom) {
          dom.style.display = 'none'
          dom.style.display = 'none'
        }

        setTimeout(() => {
          const dom = document.querySelector(
            '.x6-widget-snapline .x6-widget-snapline-vertical'
          )
          if (dom) {
            dom.style.display = ''
            dom.style.display = ''
          }
        }, 2000)
      }, 2000)
    }
  },
  mounted () {
    const copyBtn = new ClipboardJS('#copyBtn')

    copyBtn.on('success', (e) => {
      this.$message.success('站外分享链接复制成功, 您可以发送给你的好友')
    })
    const w = document.querySelector('.draft-container').clientWidth
    const h = window.innerHeight * 0.9
    this.graph = new Graph({
      container: document.getElementById('graph-container'),
      grid: true,
      width: w,
      height: h,
      connecting: {
        snap: {
          radius: 50
        },
        enabled: true, // 开启连线交互
        allowMulti: true, // 是否允许在相同的起始节点和终止之间创建多条边
        allowNode: false, // 是否允许边链接到节点（非节点上的链接桩）
        allowBlank: false, // 是否允许连接到空白点
        allowLoop: true, // 是否允许创建循环连线，即边的起始节点和终止节点为同一节点，
        allowEdge: false, // 是否允许边链接到另一个边
        highlight: true, // 拖动边时，是否高亮显示所有可用的连接桩或节点
        connectionPoint: 'anchor', // 指定连接点
        anchor: 'midSide', // 指定被连接的节点的锚点
        createEdge () {
          return this.createEdge({
            attrs: {
              line: {
                stroke: '#8f8f8f',
                strokeWidth: 2
              }
            },
            zIndex: 0,
            connector: 'smooth'
          })
        }
      },
      background: {
        color: '#F2F7FA'
      },
      highlighting: {
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              strokeWidth: 20,
              stroke: 'rgba(223,234,255)'
            }
          }
        }
      },
      interacting: {
        // node 是否可以拖动
        nodeMovable: ({ cell }) => {
          if (cell.getData()?.selectable) {
            return false
          }
          return true
        }
      },
      // 开启画布缩放
      mousewheel: {
        enabled: true,
        modifiers: ['ctrl', 'meta'],
        minScale: 0.5,
        maxScale: 2
      },
      // // 开启拖拽平移（防止冲突，按下修饰键并点击鼠标才能触发画布拖拽）
      panning: {
        enabled: false
      },
      moving: {
        enabled: false
      },
      resizing: {
        enabled: true
      },
      rotating: true
    })
    window.graph = this.graph
    const keyboard = new Keyboard({
      enabled: true,
      global: true
    })
    this.selection = new Selection({
      enabled: true, // 是否开启
      multiple: true, // 是否启动多选
      rubberband: true, // 是否启用框选节点功能
      movable: false, // 拖动选框时框选的节点是否一起移动
      showNodeSelectionBox: true,
      showEdgeSelectionBox: false,
      pointerEvents: 'none'
    })
    const clipboard = new Clipboard({
      enabled: true
    })
    const snapline = new Snapline({
      enabled: true,
      clean: false
    })
    const transform = new Transform({
      resizing: {
        enabled: (node) => {
          // TODO 禁止父元素的修改图形变化，这个已经忘记为什么这样了，需要看看
          // 这只是要控制子元素的修改图形变化，结果也控制了组元素，需要放开
          if (node.getData()?.selectable) {
            return false
          }
          return true
        },
        orthogonal: true
      }
    })
    this.history = new History({
      enabled: true,
      beforeAddCommand (event, args) {
        if (args.key === 'tools') {
          return false
        }
      }
    })
    this.exportInstance = new Export()
    this.graph.use(keyboard)
    this.graph.use(this.selection)
    this.graph.use(clipboard)
    this.graph.use(snapline)
    this.graph.use(transform)
    this.graph.use(this.exportInstance)
    this.graph.use(this.history)
    // 过滤选中
    this.selection.setSelectionFilter((cell) => {
      return !cell.getData()?.selectable
    })
    this.graph.on('history:change', () => {
      this.canRedo = this.history?.canRedo()
      this.canUndo = this.history?.canUndo()
    })
    this.graph.on('edge:click', (e) => {
      this.edgeVisible = true
      this.currentEdge = e.edge
    })
    this.graph.on('blank:click', ({ e }) => {
      this.$emit('move')
      this.menuShow = false
    })
    // 菜单
    this.graph.on('node:contextmenu', ({ e, node }) => {
      if (node.getData()?.selectable) {
        return
      }
      const { clientX, clientY } = e
      this.menuShow = true
      // 总宽度 - 画布宽度 / 2
      const w = window.innerWidth * 0.15
      this.menu = {
        x: clientX - w,
        y: clientY - 60
      }
      this.currentNode = node
    })
    this.graph.on('node:mouseenter', ({ node, e }) => {
      if (node?.data?.isMore && !this.tooltipMore) {
        const x = e.offsetX
        const y = e.offsetY
        this.tooltipObj = {
          x,
          y
        }
        this.tooltipMore = true
        this.tooltipText = node.data.originLabel
      }
      if (!node.getData()?.selectable) {
        this.showPorts(true)
      }
    })
    this.graph.on('node:mouseleave', ({ node }) => {
      this.showPorts(false)
      if (this.tooltipMore) {
        this.tooltipMore = false
      }
    })
    this.graph.on('node:mouseup', ({ node }) => {
      this.hideSnapline()
    })
    this.graph.on('edge:mouseenter', ({ edge, e }) => {
      edge.addTools([
        {
          name: 'button-remove'
        }
      ])
    })
    this.graph.on('node:resized', ({ node }) => {
      this.updatePort(node)
    })
    this.graph.on('node:click', ({ node }) => {
      this.$emit('move')
    })
    this.graph.on('edge:mouseleave', ({ edge }) => {
      edge.removeTools()
    })
    // this.graph.on('node:dblclick', ({ node, e }) => {
      // node.addTools({
      //   name: 'node-editor',
      //   args: {
      //     event: e,
      //   },
      // })
    // })
    this.graph.on('node:changed', ({ node }) => {
      const targetEdge = this.graph.getIncomingEdges(node.id) || []
      const sourceEdge = this.graph.getOutgoingEdges(node.id) || []
      const edgeList = [...targetEdge, ...sourceEdge]
      for (const item of edgeList) {
        // 直线不需要处理
        if (item.connector === 'normal') {
          return
        }
        const point = this.newVertices(
          item.getTargetNode(),
          item.getSourceNode()
        )
        item.vertices = point
      }
    })
    this.graph.on('node:selected', ({ e }) => {
      this.showPorts(false)
    })
    const event = new GraphOnEvent(this.graph, this.history)
    event.init(keyboard, this.selection, clipboard)

    const nodeEvent = new NodeEvent(this.graph)
    nodeEvent.init()
    this.graph.on('edge:connected', ({ edge, previous }) => {
      const point = this.newVertices(edge.getTargetNode(), edge.getSourceNode())
      edge.vertices = point
    })
    if (this.draftId) {
      this.getDetail()
    }
    const that = this
    document.addEventListener('keydown', function (event) {
      if (event.keyCode === SPACE_KEY) {
        that.graph.togglePanning(true)
        // 可以拖动画布
        that.selection.toggleRubberband(false)
      }
    })

    // 监听释放空格键的事件
    document.addEventListener('keyup', function (event) {
      if (event.keyCode === SPACE_KEY) {
        that.graph.togglePanning(false)
        that.selection.toggleRubberband(true)
      }
    })

    this.group = new Group(this.graph)
  }
}
</script>

<style lang="scss">
.draft-container {
  width: 80vw;
  height: calc(100% - 80px);
  position: relative;
  background-color: #fff;
  margin: 20px auto !important;
  border: 1px solid #ddd;
  overflow: hidden;

  :deep(.func-box .el-input__inner) {
    // border: none;
    // border-bottom: 1px solid #;
    height: 30px;
  }

  .draft-menu {
    position: absolute;
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
    z-index: 99;

    .item {
      font-size: 13px;
      width: 70px;
      height: 20px;
      text-align: center;
      padding: 8px;
      cursor: pointer;
    }

    .item:hover {
      background-color: #ecf5ff;
      color: #66b1ff;
    }
  }

  .more-tooltip {
    background: #303133;
    color: #fff;
    position: absolute;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    line-height: 1.2;
    min-width: 10px;
    max-width: 420px;
    word-wrap: break-word;
    z-index: 100;
  }

  .func-box {
    width: calc(100% - 10px);
    height: 30px;
    box-shadow: 0 2px 5px 1px rgba(52, 51, 53, 0.16);
    padding: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;

    .el-icon-picture-outline {
      position: relative;
      top: -2px;
    }

    .func-img {
      height: 20px;
      margin-right: 10px;
      cursor: pointer;
    }

    i {
      font-size: 22px;
      margin-right: 10px;
      cursor: pointer;
    }

    .el-icon-upload {
      font-size: 24px;
      margin-right: 16px;
    }

    .disable {
      color: #ccc;
    }

    .center .title {
      border: none;
      width: 250px;
    }

    .center input {
      border: none;
    }
  }

  .draft-con {
    width: 80vw;
  }
}

.x6-graph-svg {
  z-index: 2;
}

.x6-widget-selection {
  z-index: 1;
}

.x6-node-tool-editor {
  z-index: 3;
}

.save-img {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.friend-list {
  max-height: 350px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  /* 定义滚动条轨道的颜色 */
  &::-webkit-scrollbar-track {
    background: #409eff;
  }

  /* 定义滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background: #409eff;
  }

  /* 鼠标悬停在滚动条上时滑块的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background: #409eff;
  }

  .item-friend {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    border-bottom: 1px solid #ddd;
    padding: 10px 0;

    .el-checkbox__label {
      display: none;
    }

    .right {
      margin-left: 12px;
      display: flex;
      align-items: center;
    }

    .nick {
      color: #000;
      font-weight: 600;
      font-size: 16px;
      margin-left: 12px;
    }
  }

  img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}

// .friend{
// width: 19px;
// height: 19px;
// position: relative;
// top: 1px;
// right: 8px;
// cursor: pointer;
// }

.x6-widget-transform {
  margin: -1px 0 0 -1px;
  padding: 0;
  border: 1px solid #239edd;
  border-radius: 6px;
  z-index: 10;
}

.x6-widget-transform>div {
  border: 1px solid #239edd;
}

.x6-widget-transform>div:hover {
  background-color: #3dafe4;
}

.x6-widget-transform-active-handle {
  background-color: #3dafe4;
}

.x6-widget-transform-resize {
  border-radius: 0;
}

.ant-card {
  box-shadow: 0 0 10px 1px #e9e9e9;
}

.ant-card-head-title {
  text-align: center;
}

.ant-row {
  margin: 16px 0;
  text-align: left;
}

.x6-widget-transform-resize {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.add-group-from {
  .item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
  }
}
</style>
