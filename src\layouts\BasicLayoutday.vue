<template>
  <div class="basic-layout">
    <div class="top-bar">
      <router-link class="item" to="/">
        <div class="top-bar-title-box">
          <div class="left-B">B</div>
          <div class="right-B">B</div>
        </div>
      </router-link>

      <div style="display: flex">
        <div class="nav-box">
          <div class="nav-item" @click="openVision(0)">愿景</div>
          <router-link class="item" to="/">
            <div class="nav-item" @click="openVision(1)">使用说明</div>
          </router-link>
          <router-link class="item" to="/">
            <div class="nav-item">合作</div>
          </router-link>
          <router-link class="item" to="/login" v-if="!isMini">
            <div class="nav-item" style="background-color: #c6c6c6">
              登录/注册
            </div>
          </router-link>
          <div
            class="nav-item"
            style="background-color: #c6c6c6"
            @click="wxlogin()"
            v-if="isMini"
          >
            登录/注册
          </div>
        </div>
      </div>
    </div>
    <router-view />
    <el-dialog :title="title" custom-class="vision-dialog"  width="1000px" :visible.sync="dialogTableVisible" @close="closeVision">
      <div class="video-con">
        <video id="visionDom" controls autoplay :src="url" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BasicLayoutday',
  data () {
    return {
      isMini: false,
      dialogTableVisible: false,
      url: 'https://bookor-application.oss-cn-beijing.aliyuncs.com/public_video.mp4',
      title: '企业愿景'
    }
  },
  mounted () {
    this.isMiniProgramEnv()
  },
  methods: {
    openVision (type) {
      if (type) {
        this.title = '使用说明'
        this.url = 'https://oss.bookor.com.cn/video/article.mp4'
      } else {
        this.title = '企业愿景'
        this.url = 'https://bookor-application.oss-cn-beijing.aliyuncs.com/public_video.mp4'
      }
      if (this.$parent.$el.id === 'app') {
        this.dialogTableVisible = true
      } else {
        this.$parent.openVision(type)
      }
    },
    closeVision () {
      document.querySelector('#visionDom').pause()
    },
    // 不显示注册功能
    goToLogin () {
      sessionStorage.setItem('from', 'noReg')
    },

    // 判断环境是否为小程序
    isMiniProgramEnv () {
      // 通过navigator.userAgent 判断
      const userAgent = navigator.userAgent
      this.isMini = /miniProgram/i.test(userAgent)
    },

    wxlogin () {
      setTimeout(() => {
        wx.miniProgram.navigateBack()
        wx.miniProgram.postMessage({ data: '获取成功' })
      }, 2000)
    }
  }
}
</script>

<style scoped lang="scss">
@import "@/assets/scss/global.scss";
.top-bar {
  z-index: 10;
  @include flex-between;
  width: 100vw;
  height: 10vh;
  background-color: white;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.6); /* 在这里添加阴影 */
  margin: 0 0 10px 0;
  position: absolute;
  top: 0;
  padding: 0 3vw;
  box-sizing: border-box;
  .item {
    text-decoration: none; /* 去掉下划线 */
    color: black; /* 修改链接字体颜色 */
  }
  .top-bar-title-box {
    display: flex;
    position: relative;

    .left-B {
      font-size: 43px;
      font-weight: bolder;
      background-color: rgb(255, 255, 255);
      position: absolute;
      right: 8px;
      text-shadow: 4px 0 0 white;
    }
    .right-B {
      font-size: 43px;
      font-weight: bolder;
    }
  }

  .nav-box {
    display: flex;
    .item {
      text-decoration: none; /* 去掉下划线 */
      color: black; /* 修改链接字体颜色 */
    }
    .nav-item {
      margin: 0 0 0 1vw !important;
      padding: 0 1.3vw !important;
      border: none !important;
      border-radius: 5px;
      font-size: 14px;
      cursor: pointer;
    }
  }
}
.video-con{
  width: 100%;
  display: flex;
  justify-content: center;
  video{
    width: 850px;
  }
}
.vision-dialog{
  background-color: #000;
  box-shadow: 0 0 10px 4px rgba(255, 255, 255, 1);
}
.el-dialog__title{
  color: #fff;
}
</style>
