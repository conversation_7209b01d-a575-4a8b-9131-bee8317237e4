<template>
  <div>
    <div v-if="mindMapList.length > 0">
      <div class="mind-list">
        <div v-for="(item, index) in mindMapList" :key="index">
          <div
            class="ming-level-item"
            :class="['mind-level-' + item.level]"
            v-if="item.isShow"
            style="position: relative"
          >
            <!-- line  -->
            <div
              v-for="(idxitem, idx) in item.level"
              :key="idx"
              class="mind-level-line"
              :style="{
                'border-left': item.lineShow[idx + 1]
                  ? '1px dashed #65818f'
                  : 'none',
                opacity:
                  idx + 1 == item.level &&
                  item.open == false &&
                  item.ishasChild == true
                    ? 0
                    : 1,
                position: 'absolute',
                left: idx * 10 + 'px',
                top: '7px',
              }"
            ></div>
            <!-- icon -->
            <div v-if="item.open == true && item.ishasChild == true">
              <div
                :style="{ 'margin-left': (item.level - 1) * 10 + 'px' }"
                class="mind-map-open-icon-box"
              >
                <img
                  :style="{
                    top: item.level == 1 ? '-7px' : '-2px',
                  }"
                  class="mind-map-open-icon-img"
                  src="./img/icon-open.png"
                  @click.stop="
                    handleCloseChild($event, item.mind_map_id, index)
                  "
                />
                <div class="mind-map-open-icon-touch"></div>
                <div class="mind-map-horizontal-line"></div>
              </div>
            </div>
            <div v-if="item.open == false && item.ishasChild == true">
              <div
                :style="{ 'margin-left': (item.level - 1) * 10 + 'px' }"
                class="mind-map-close-icon-box"
              >
                <img
                  :style="{ top: item.level == 1 ? '-4px' : '0' }"
                  class="mind-map-close-icon-img"
                  src="./img/icon-close.png"
                  @click.stop="handleOpenChild($event, item.mind_map_id, index)"
                />
                <div class="mind-map-open-close-touch"></div>
                <div class="mind-map-horizontal-line"></div>
              </div>
            </div>
            <div v-if="item.ishasChild == false">
              <div
                :style="{ 'margin-left': (item.level - 1) * 10 + 'px' }"
                class="mind-map-dot-icon-box"
              >
                <img
                  :style="{ top: item.level === 1 ? '-2px' : '-5px' }"
                  class="mind-map-dot-icon-img"
                  src="./img/icon-lone.png"
                />
                <div
                  :style="{ top: item.level == 1 ? '9px' : '7px' }"
                  class="mind-map-horizontal-line"
                ></div>
              </div>
            </div>

            <div
              v-html="item.content"
              :style="{
                'font-size': '14px',
                'font-weight': item.level == 1 ? 'bold' : 'normal',
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>暂无导图</div>
  </div>
</template>

<script>
import request from '@/http/request'
export default {
  data () {
    return {
      mindMapTitle: '',
      mindMapList: [] // 树形结构转换后的扁平化数据
    }
  },
  props: {
    book_id: {
      type: Number
    }
  },
  mounted () {
    this.getMindDetail()
  },
  methods: {
    getMindDetail (isFisrtLoad) {
      const that = this
      const params = {
        book_id: this.book_id
      }
      request('/api/MindMap/getMindMapContentList', params, 'get')
        .then((result) => {
          if (result?.length > 0) {
            const list = result
            that.initList(list[0].children)
          }
        })
    },
    initList (list) {
      const newList = this.flatten(list, [], {})
      this.mindMapList = newList
    },
    flatten (tree, arr = [], parentLinesShow = {}, parentChildsNum = 0) {
      const linesMap = {}
      tree.forEach((item, index) => {
        const { children, ...props } = item
        const curLinesShow = JSON.parse(JSON.stringify(parentLinesShow))

        // 处理根节点
        if (props.level === 0) {
          props.oldContent = props.content
          linesMap[props.id] = {}
          this.mindMapTitle = props
        } else {
          props.open = true
          props.level = parseInt(props.level)
          props.isShow = true
          props.oldContent = props.content
          // 是否有孩子节点
          props.ishasChild = !!(children && children.length > 0)
          // 显示对应的线条
          if (children) {
            curLinesShow[props.level] = true
          } else {
            curLinesShow[props.level] = false
          }
          if (parentChildsNum == 1 && props.level > 1) {
            curLinesShow[props.level - 1] = false
          }
          if (index === tree.length - 1) {
            if (props.level > 1) {
              curLinesShow[props.level - 1] = false
            }
          }
          props.lineShow = curLinesShow
          arr.push(props)
        }
        if (children) {
          this.flatten(children, arr, curLinesShow, children.length)
        }
      })
      return arr
    },

    // 层级打开关闭
    handleOpenChild (e, id, index) {

      const cur_id = id
      const cur_index = index
      const mindMapList = this.mindMapList
      mindMapList[cur_index].open = true
      let i = cur_index
      while (i < mindMapList.length) {
        if (mindMapList[i].mind_map_pid == cur_id) {
          mindMapList[i].isShow = true
        }
        i++
      }
      this.mindMapList = mindMapList
    },

    handleCloseChild (e, id, index) {
      const cur_id = id
      const cur_index = index
      const mindMapList = this.mindMapList
      mindMapList[cur_index].open = false
      const pidArr = []
      pidArr.push(cur_id)
      let i = cur_index
      while (i < mindMapList.length) {
        if (this.IsInArray(pidArr, mindMapList[i].mind_map_pid)) {
          mindMapList[i].open = false
          mindMapList[i].isShow = false
          pidArr.push(mindMapList[i].mind_map_id)
        }
        i++
      }
      this.mindMapList = mindMapList
    },

    // 判断某元素是否属于某数组
    IsInArray (arr, val) {
      const testStr = ',' + arr.join(',') + ','
      return testStr.indexOf(',' + val + ',') != -1
    }
  }
}
</script>

<style lang="scss" scoped>
.mind-list {
  font-size: 14px;
  border-left: 1px dashed #65818f;
  padding-left: 10px;
  .mind-level-line {
    box-sizing: content-box;
    min-height: 100%;
    /* position: absolute; */
  }
  .mind-map-open-icon-box {
    position: relative;
    .mind-map-open-icon-img {
      width: 10px;
      height: 10px;
      position: relative;
      left: -5px;
      top: -2px;
      z-index: 1;
      cursor: pointer;
    }
    .mind-map-open-icon-touch {
      width: 25px;
      height: 25px;
      position: absolute;
      left: -15px;
      top: 10%;
    }
  }

  .mind-map-close-icon-box {
    position: relative;
    .mind-map-close-icon-img {
      width: 10px;
      height: 10px;
      margin-right: 2px;
      position: relative;
      left: -5px;
      top: 0;
      z-index: 1;
      cursor: pointer;
    }
    .mind-map-open-close-touch {
      width: 25px;
      height: 25px;
      position: absolute;
      left: -15px;
      top: 0;
    }
  }

  .mind-map-dot-icon-box {
    position: relative;
    width: 10px;
    height: 10px;
    .mind-map-dot-icon-img {
      width: 10px;
      height: 10px;
      position: relative;
      left: -4px;
      top: -5px;
      // z-index: 1;
    }
  }

  .mind-level-name {
    display: block;
    width: auto;
    margin-right: 5px;
    flex: none;
  }

  .mind-level-1 {
    font-size: 16px;
    padding-top: 7px;
  }
  .mind-dot {
    display: inline-block;
    width: 5px;
    height: 5px;
    background-color: #fff;
    margin-right: 5px;
    margin-top: 7px;
  }
  .ming-level-item {
    align-items: flex-start;
    display: flex;
    padding-bottom: 10px;
  }
  .mind-map-open-close-touch {
    width: 25px;
    height: 25px;
    position: absolute;
    left: -15px;
    top: 0;
  }
  .mind-map-horizontal-line {
    position: absolute;
    height: 1px;
    width: 7px;
    background: #65818f;
    left: -10px;
    top: 7px;
  }
}
</style>
