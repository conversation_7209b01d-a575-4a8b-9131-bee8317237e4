<template>
  <div style="height: 100vh;width:100vw;">
    <div
      v-if="pdfFileUrl"
      class="change-to-word"
      @click="changeToWord()"
    >
      转为WORD
    </div>
    <iframe
      :src="'pdfjs/web/viewer.html?file=' + pdfFileUrl"
      class="pdf-viewer pdf-viewer-local"
      ref="pdfViewer"
    />
    <notePop
      v-if="paperId"
      :selectText="selectText"
      :chapterInformation="chapterInformation"
      :isShowNotePop="isShowNotePop"
      :notePopTop="notePopTop"
      :notePopLeft="notePopLeft"
      :paper_id="paperId"
      :entry_position="entry_position"
    />
  </div>
</template>

<script>
import request from '@/http/request'
import { Loading } from 'element-ui'
import notePop from '@/components/editor/note-editor'
export default {
  data () {
    return {
      pdfFileUrl: '',
      title: '',
      paperId: 0,
      isShowNotePop: false,
      selectText: '',
      notePopTop: null,
      notePopLeft: null,
      entry_position: '', // 当前划线在文档中的位置
      chapterInformation: '' // 完整信息：书名+大纲
    }
  },
  components: {
    notePop
  },
  created () {
    this.$watch('$route.query', (newQuery) => {
      const pdfFileUrl = newQuery.url
      this.pdfFileUrl = pdfFileUrl
      this.paperId = Number(newQuery.paperId)
      this.title = newQuery.title || ''
    })
  },
  methods: {
    // 上传到个人数据库里
    apiAddFilesandWord (title, url, changeToWord) {
      if (changeToWord) {
        title = '[word]' + title
      }
      const params = {
        paper_title: title,
        paper_url: url,
        paper_type: 'others' // 区分上传的类型 other表示上传的文档和word
      }
      request('/api/Paper/addPaper', params).then((result) => {
        this.currentInfo = {
          id: result.paper_url,
          type: 'others',
          pdfUrl: url
        }
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 1000
        })
        // 清空页面
        this.editBookorNoteContent = ''
        // 刷新docswitch列表
        this.$refs.docswitch.refreshFileFileList()
      }).catch((result) => {
        this.$message.error({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },
    async pool (wordId) {
      const timer = setInterval(async () => {
        try {
          const result = await request('api/Paper/getAliyunWord', {
            id: wordId
          })
          if (result.status === 0) {
            clearInterval(timer)
            this.hideLoading()
            this.$message({
              message: '转换失败',
              type: 'error',
              duration: 1000
            })
          }
          // 成功
          if (result.status === 2) {
            clearInterval(timer)
            const url = result.url
            this.apiAddFilesandWord(this.title, url, true)
            this.hideLoading()
          }
        } catch (err) {
          clearInterval(timer)
          this.hideLoading()
          this.$message({
            message: '转换失败',
            type: 'error',
            duration: 1000
          })
        }
      }, 4000)
    },
    // 转换为word时的loading
    showLoading () {
      this.loadingInstance = Loading.service({
        fullscreen: true, // 是否全屏遮罩
        text: '加载中...', // 显示的文本
        background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
      })
    },
    hideLoading () {
      this.loadingInstance.close() // 关闭 Loading 实例
    },
    // 转换为word
    async changeToWord () {
      // 使用原来的api
      this.showLoading()
      const result = await request('api/Paper/aliyunPdfToWord', {
        fileUrl: this.pdfFileUrl
      })
      if (result.statusCode === 200) {
        const wordId = result.body.Data.Id
        await this.pool(wordId)
      } else {
        this.$message({
          message: '转换失败',
          type: 'error',
          duration: 1000
        })
      }
    },
    // 本地pdf滑选事件注册： 获取鼠标选中的文本
    getSelectText () {
      const _this = this
      const iframe = document.getElementsByClassName('pdf-viewer-local')[0]
      let x = ''
      let y = ''
      let _x = ''
      let _y = ''
      // iframe 加载完成后执行并将双击事件过滤掉，因为双击事件可能也触发滑选，所以为了不误操作，将其剔除
      iframe.onload = function () {
        // 鼠标点击监听
        iframe.contentDocument.addEventListener(
          'mousedown',
          function (e) {
            x = e.pageX
            y = e.pageY
            _this.isShowNotePop = false
          },
          true
        )
        // 鼠标抬起监听
        iframe.contentDocument.addEventListener(
          'mouseup',
          function (e) {
            _x = e.pageX
            _y = e.pageY
            if (x == _x && y == _y) return // 判断点击和抬起的位置，如果相同，则视为没有滑选，不支持双击选中
            var selection = iframe.contentWindow.getSelection()
            var range = selection.getRangeAt(0)
            var choose = selection.toString()

            _this.selectText = choose
            _this.chapterInformation = _this.chapterInformation_bookTitle + ' '
            if (choose) {
              _this.notePopTop = range.getBoundingClientRect().top
              _this.notePopLeft = range.getBoundingClientRect().left
              _this.isShowNotePop = true
            } else {
              _this.isShowNotePop = false
            }
            // 当前页码
            const currentPageIndex =
              iframe.contentWindow.PDFViewerApplication.page

            this.entry_position = {}
            // 当前页数据
            const currentPage =
              iframe.contentWindow.PDFViewerApplication.pdfViewer.getPageView(
                currentPageIndex
              )
            // 当前页码对应的num索引
            const num = currentPage.pdfPage.ref.num
            // 根据索引查找大纲项 如果存在大纲的话。
            iframe.contentWindow.PDFViewerApplication.pdfDocument
              .getOutline()
              .then(function (outline) {
                if (outline) {
                  var currentPageOutline = _this.findOutlineByPageIndex(
                    outline,
                    num
                  )
                  if (currentPageOutline) {
                    _this.chapterInformation =
                      _this.chapterInformation_bookTitle +
                      currentPageOutline.title
                  }
                } else {
                  _this.chapterInformation =
                    _this.chapterInformation_bookTitle +
                    '第' +
                    currentPageIndex +
                    '页'
                }
              })
          },
          true
        )
      }
    },

    searchKeyword (keyword) {
      const iframe = document.getElementsByClassName('pdf-viewer-local')[0]
      const PDFViewer = iframe.contentWindow.PDFViewerApplication

      PDFViewer.findBar.open()
      PDFViewer.findBar.findField.value = keyword
      PDFViewer.findController.executeCommand('find', {
        query: keyword,
        phraseSearch: false,
        caseSensitive: false,
        entireWord: false,
        highlightAll: true,
        findPrevious: undefined
      })
      PDFViewer.findBar.dispatchEvent('')
    }
  },
  destroyed () {
    this.isShowNotePop = false
  },
  mounted () {
    const pdfFileUrl = this.$route.query.url
    this.pdfFileUrl = pdfFileUrl
    this.title = this.$route.query.title || ''
    this.paperId = Number(this.$route.query.paperId)
    this.getSelectText()

    // 跳转到pdf 固定页
    // iframe.contentWindow.PDFViewerApplication.page
    const keyword = this.$route.query.keyword
    if (keyword) {
      setTimeout(() => {
        this.searchKeyword(keyword)

        this.$router.replace({
          query: {
            url: pdfFileUrl,
            title: this.title,
            paperId: this.paperId,
            keyword: ''
          }
        })
      }, 3000)
    }
  }
}
</script>

<style lang="scss" scoped>
.pdf-viewer-local{
  width: 100%;
  height: 100%;
}
.change-to-word {
  position: absolute;
  color: white;
  top: 10px;
  right: 227px;
  background-color: gray;
  border-radius: 5px;
  padding: 0 4px;
  cursor: pointer;
}
</style>
