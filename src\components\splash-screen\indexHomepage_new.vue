<template>
  <div class="container_box" style="position: relative; " @wheel="mouseScrolling">
    <!-- <div style="position: relative"> -->
    <div class="container" v-if="curPage == 1">
      <div>
        <!-- 矩形条 -->
        <div class="rectBox">
          <div class="rect-group rect-group-left">
            <div v-for="(rect, index) in rectanglesLeft" :key="index" class="rect animated fadeInLeft" :style="{
              transform: `translateX(${rect.translateX * 0.25}vw)`,
              opacity: rect.opacity,
              height: `${rect.height}px`,
            }"></div>
          </div>

          <div class="rect-group">
            <div v-for="(rect, index) in rectanglesRight" :key="index" class="rect animated fadeInRight" :style="{
              transform: `translateX(${rect.translateX * 0.25}vw)`,
              opacity: rect.opacity,
              height: `${rect.height}px`,
            }"></div>
          </div>
        </div>
        <!-- 头部标题 -->
        <div class="header">
          <div v-if="isShowTitle" style="margin-right: 12px; display: inline-flex">
            <div class="door" id="bookor" v-for="(letter, index) in lettersBookor" :key="index" :style="{
              animationDelay: `${(lettersBookor.length - index - 1) * 0.2}s`,
            }" :class="{ fadeOut: index !== 0 && isShowFade }">
              <span class="left-panel">{{ letter }}</span>
            </div>
          </div>
          <div v-if="isShowTitle" style="display: inline-flex">
            <div class="door" id="brain" style="width: 27px" v-for="(letter, index) in lettersBrain" :key="index" :style="{
              animationDelay: `${(lettersBrain.length - index - 1) * 0.2}s`,
            }" :class="{ fadeOut: index !== 0 && isShowFade }">
              <span class="left-panel">{{ letter }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 口号 -->
      <transition name="fade">
        <div v-show="isShowSlogan" key="isShowSlogan" class="slogan animate__animated" style="" :class="{
          animate__fadeIn: isShowSlogan,
          animate__fadeOut: !isShowSlogan,
        }">
          看得见的大脑知识库
        </div>
      </transition>
      <!--animate__animated  :class="{
            animate__fadeIn: isShowSloganRoad,
            animate__fadeOut: !isShowSloganRoad,
          }" -->
      <transition name="fade">
        <div v-if="isShowSloganRoad" key="isShowSloganRoad" class="sloganRoad">
          通向新阅读
        </div>
      </transition>
      <transition name="fade">
        <div v-show="isShowSloganSecond" key="isShowSloganSecond" class="isShowSloganSecond animate__animated" :class="{
          animate__fadeIn: isShowSloganSecond,
          animate__fadeOut: !isShowSloganSecond,
        }">
          从阅读到系统知识，一套高效知识管理系统
        </div>
      </transition>

      <!-- <div
        v-if="isShowNextPage"
        style="
          color: white;
          position: absolute;
          top: 90%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 200px;
          height: 40px;
          text-align: center;
          line-height: 40px;

          display: none;
        "
        @wheel="handleMouseWheel"
      >
        滑动查看更多
      </div> -->
      <div v-if="isShowNextPage" class="down-arrow" @click="handleMouseWheel">
        <svg id="more-arrows">
          <polygon class="arrow-top" points="37.6,27.9 1.8,1.3 3.3,0 37.6,25.3 71.9,0 73.7,1.3 " />
          <polygon class="arrow-middle" points="37.6,45.8 0.8,18.7 4.4,16.4 37.6,41.2 71.2,16.4 74.5,18.7 " />
          <polygon class="arrow-bottom" points="37.6,64 0,36.1 5.1,32.8 37.6,56.8 70.4,32.8 75.5,36.1 " />
        </svg>
      </div>

      <!-- 页脚 -->
      <div class="footer-copyright">
        <p>
          <a id="publication_business_license_p"
            href="https://oss.bookor.com.cn/uploads/20210811/8c45f7ed37aef9633b160196e5cf9dc1.jpeg" target="_blank">
            出版社经营许可证
            <span id="publication_business_license">
              新出发京批字第直200369号
            </span>
          </a>
          <a id="business_license_p"
            href="https://oss.bookor.com.cn/uploads/20210811/6ef2747348e564c9f3d4f1686faea408.jpeg" target="_blank">
            <span id="business_license"> 上书台科技（北京）有限公司 </span>
          </a>
          <a href="https://beian.miit.gov.cn/" target="_blank">
            <span id="beian"> 京ICP备**********号-1 </span> Copyright ©
            <span id="year"></span> bookor
          </a>
        </p>
        <p>
          <a href="https://beian.miit.gov.cn/" target="_blank"><img style="vertical-align: middle"
              src="https://img60.ddimg.cn/assets/pc_image/jinggongwanganbei.png" alt="京公网安备11022802220906" /><span
              id="public_security_beian" style="margin-left: 5px">
              京公网安备11022802220906
            </span></a>
        </p>
      </div>
    </div>
    <!-- @click="changePage" -->
    <div class="container" v-if="curPage == 2">
      <Introduce ref="introduce" @toPrevPage="toPrevPage" :setCurPage="setCurPage" :setcurrentPage="setcurrentPage"
        :curSubPage="curSubPage" :onePage="onePage" :threePage="threePage" :clickNext="clickNext" />
    </div>
  </div>
</template>

<script>
import Introduce from '@/components/homepage/introduces_new.vue'
export default {
  // watch: {
  //   curSubPage(n, o) {
  //     if (n >= 2) {
  //       isShowNextPage = false
  //     }

  //   }

  // },
  data () {
    return {
      curSubPage: 1,
      rectanglesLeft: [],
      rectanglesRight: [],
      defaultHeight: 80, // 默认的矩形高度，以vw为单位

      isShowTitle: false,
      isShowSlogan: false,

      isShowSloganSecond: false,
      isShowSloganRoad: false,

      isShowNextPage: false,
      // isShowSearchBox: false,
      isShowHeaderFade: false,
      isShowFade: false,
      lettersBookor: ['B', 'o', 'o', 'k', 'o', 'r'],
      lettersBrain: ['B', 'r', 'a', 'i', 'n'],

      keyword: '',
      isMobile: false, // 移动端适配

      curPage: 1,
      // 第一页翻页配置
      onePage: {
        isShow: true,
        currentNum: 1,
        currentNum2Show: false,
        throttleBoo: true
      },
      // 第三页翻页配置
      threePage: {
        currentNum: 1
      }
    }
  },
  components: {
    Introduce
  },
  mounted () {
    this.handleResize()
    if (this.curPage == 1) {
      setTimeout(() => {
        this.createRectangles()
      }, 1000)
    }
  },
  methods: {
    // 设置子组件的页码值
    setcurrentPage (val) {
      if (val) {
        this.curSubPage++
      } else {
        this.curSubPage--
      }
    },
    oneSectionScrollWheel (event) {
      if (this.onePage.currentNum <= 1) {
        event.preventDefault()

        if (this.onePage.throttleBoo) {
          this.onePage.throttleBoo = false

          if (this.onePage.isShow) {
            this.onePage.isShow = false
          }
          this.onePage.currentNum++
          this.onePage.throttleBoo = true
        }
      }
    },

    // 鼠标滚动
    mouseScrolling (e) {
      if (e.deltaY < 0 && this.curSubPage > 0) {
        if (this.curSubPage == 1) {
          // this.oneSectionScrollWheel(e);
          if (this.onePage.currentNum > 1) {
            if (this.onePage.throttleBoo) {
              this.onePage.isShow = true

              this.onePage.currentNum--

              this.onePage.currentNum2Show = false

              // this.setcurrentPage(true);
              return
            }
          }
        }

        if (this.curSubPage == 3) {
          if (this.threePage.currentNum > 1) {
            this.threePage.currentNum--
            // this.setcurrentPage(true)

            return
          }
        }

        if (this.curSubPage > 1) {
          this.setcurrentPage(false)
        }
      }
    },

    // 向下点击
    clickNext () {
      if (this.curSubPage == 1) {
        // this.oneSectionScrollWheel(e);
        if (this.onePage.currentNum <= 1) {
          this.onePage.isShow = false

          this.onePage.currentNum++

          setTimeout(() => {
            this.onePage.currentNum2Show = true
          }, 250)

          return
        }
      }

      if (this.curSubPage == 3) {
        if (this.threePage.currentNum < 4) {
          this.threePage.currentNum++
          return
        }
      }

      this.setcurrentPage(true)
    },

    createRectangles () {
      const centerX = 0 // 矩形左边距离容器居中的vw值

      for (let i = 0; i < 6; i++) {
        const rect = {
          translateX: centerX,
          opacity: 0
        }

        setTimeout(() => {
          rect.opacity = 1
          rect.height = this.defaultHeight
          if (i > 3) {
            rect.translateX = centerX - (i + 0.5) * 5 + i * 1
          } else {
            rect.translateX = centerX - (i + 0.5) * 5 // 向目标位置移动
          }
        }, i * 300)

        this.rectanglesLeft.push(rect)
      }

      for (let i = 1; i < 6; i++) {
        const rect = {
          translateX: centerX,
          opacity: 0
        }

        setTimeout(() => {
          rect.opacity = 1
          rect.height = this.defaultHeight
          if (i > 3) {
            rect.translateX = centerX + (i + 0.5) * 5 - i * 1
          } else {
            rect.translateX = centerX + (i + 0.5) * 5 // 向目标位置移动
          }
        }, i * 300)

        this.rectanglesRight.push(rect)
      }

      // 延迟执行的函数，实现矩形高度减半动画
      setTimeout(() => {
        this.rectanglesLeft.forEach((rect, index) => {
          if (index != this.rectanglesLeft.length - 1) {
            this.$set(this.rectanglesLeft, index, {
              ...rect,
              height: rect.height * 0.6
            })
          }
        })
        this.rectanglesRight.forEach((rect, index) => {
          if (index !== 0) {
            this.$set(this.rectanglesRight, index, {
              ...rect,
              height: rect.height * 0.6
            })
          }
        })
      }, 3000)

      // 透明度为0
      setTimeout(() => {
        this.rectanglesLeft.forEach((rect, index) => {
          this.$set(this.rectanglesLeft, index, {
            ...rect,
            opacity: 0
          })
        })
        this.rectanglesRight.forEach((rect, index) => {
          this.$set(this.rectanglesRight, index, {
            ...rect,
            opacity: 0
          })
        })
        this.isShowTitle = true
        setTimeout(() => {
          this.isShowSlogan = true
          this.isShowSloganSecond = true
        }, 2000)
      }, 3800)

      // 隐藏slogan，搜索框出现

      setTimeout(() => {
        this.isShowSlogan = false
      }, 8000) // 在淡入完成后2秒触发淡出动画

      setTimeout(() => {
        this.isShowSloganRoad = true
        this.isShowNextPage = true
      }, 10000)

      setTimeout(() => {
        sessionStorage.setItem('finishedSplash', 'true')
        this.$store.commit('SETISSHOWSPLASH', false)
      }, 13000)
    },

    handleResize () {
    },

    changePage () {
      this.curPage++
    },

    handleMouseWheel (event) {
      if (this.curPage == 1) {
        this.curPage++
      } else {
        this.curPage--
      }
    },

    reset () {
      this.rectanglesLeft = []
      this.rectanglesRight = []
      this.defaultHeight = 80
      this.isShowTitle = false
      this.isShowSlogan = false

      this.isShowSloganSecond = false
      this.isShowSloganRoad = false

      this.isShowNextPage = false

      this.isShowHeaderFade = false
      this.isShowFade = false
    },
    toPrevPage () {
      this.curPage = 1
    },
    setCurPage (val) {
      this.curPage = val
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;

  .down-arrow {
    // color: white;
    position: absolute;
    top: 90%;
    left: 50%;
    transform: translate(-50%, -50%);
    // // width: 200px;
    // // height: 40px;
    // text-align: center;
    // line-height: 40px;

    /* Arrow & Hover Animation */
    #more-arrows {
      width: 75px;
      height: 65px;
      transform: scale(0.4);
    }

    @keyframes arrow-animation {
      0% {
        fill: #d9dadb;
        opacity: 0.5;
      }

      33.33% {
        fill: #d9dadb;
        opacity: 0.75;
      }

      66.66% {
        fill: #d9dadb;
        opacity: 1;
      }

      100% {
        fill: #d9dadb;
        opacity: 0.75;
      }
    }

    polygon.arrow-top {
      animation: arrow-animation 1s linear infinite;
    }

    polygon.arrow-middle {
      animation: arrow-animation 1s linear infinite 1.3s;
    }

    polygon.arrow-bottom {
      animation: arrow-animation 1s linear infinite 2.5s;
    }
  }

  .footer-copyright {
    color: white;
    position: absolute;
    bottom: 0px;
    text-align: center;
    font-size: 14px;

    p {
      color: #e5e6e7;
    }

    a {
      text-decoration: none;
      /* 去掉下划线 */
      color: inherit;
      /* 继承父元素的颜色（或设置为你想要的链接颜色）*/
      cursor: pointer;
      /* 将鼠标指针改为手型 */
      outline: none;
      /* 去掉点击时的虚线框 */
    }

    a:visited {
      color: inherit;
      /* 继承父元素的颜色（或设置为你想要的已访问链接颜色）*/
    }
  }
}

.rectBox {
  display: flex;
  position: absolute;
  top: 38%;
  left: 50%;
  transform: translate(-50%, -50%);

  .rect-group-left {
    flex-direction: row-reverse;
  }

  .rect-group {
    display: flex;
    align-items: baseline;
  }

  .rect {
    width: 12px;
    background-color: #8faadc;
    margin-right: 2px;
    transition: transform 0.5s, opacity 0.5s, height 0.5s;
  }
}

.header {
  display: flex;
  position: absolute;
  top: 38%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.5s ease;

  .door {
    width: 32px;
    height: 40px;
    perspective: 1000px;
    /* 透视效果，用于增强 3D 效果 */
    transition: all 0.5s ease;
  }

  .left-panel {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    transform-origin: left center;
    /* 左侧为变换参考点 */
    transition: transform 2s ease;
    /* 添加过渡效果，使动画平滑 */
    animation: rotateIn 5s forwards;
    /* 应用动画效果 */
    font-size: 55px;
    font-weight: 600;
    color: #8faadc;
    // color: #f4f6f9;
    align-items: center;
    display: flex;
    justify-content: center;
    font-family: math;
    font-weight: bolder;
    // text-shadow: 0 0 9px rgba(106, 187, 239, 0.5);
  }
}

@keyframes rotateIn {
  0% {
    transform: rotateY(-90deg);
  }

  100% {
    transform: rotateY(0deg);
  }
}

.fadeOut {
  animation: fadeOut 0.5s ease-in-out;
  animation-fill-mode: forwards;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(-30px);
  }
}

.bottom-line {

  // position: relative;
  // display: flex;
  .search-box-input-splash {
    transition: all 1s ease;
  }

  .search-btn-box {
    color: rgb(255, 255, 255);
    width: auto;
    border-radius: 20px;
    min-width: 25px;
    height: 25px;
    line-height: 25px;
    display: inline-block;
    position: relative;
    overflow: hidden;
    border: 3px solid #8faadc;
    background-size: 104% 104%;
    cursor: pointer;
    transition: all 1s ease;
  }

  .search-btn-box input {
    display: inline-block;
    background: 0 0;
    /* background: red; */
    border: none;
    color: #8faadc;
    padding-left: 25px;
    line-height: 25px !important;
    height: 25px;
    box-sizing: border-box;
    vertical-align: 4px;
    font-size: 16px;
    width: 25px;
    transition: all 0.5s ease-in-out;
  }

  .search-btn-box:hover input {
    display: inline-block;
    width: 325px;
    padding-right: 15px;
  }

  .search-btn-box input:not(:placeholder-shown) {
    display: inline-block;
    width: 325px;
    padding-right: 15px;
  }

  .search-btn-box:hover+.search-line {
    opacity: 0;
    height: 15px;
    transition: opacity 0.5s ease-out;
  }

  .search-line {
    height: 20px;
    width: 3px;
    background-color: #8faadc;
    position: absolute;
    left: 32px;
    bottom: -5px;
    transform: rotate(135deg);
    opacity: 1;
    transition: opacity 0.5s ease-in;
  }
}

.slogan {
  color: #bc8d02;
  position: absolute;
  top: 40%;
  font-size: 22px;
}

.isShowSloganSecond {
  color: #9ab6c8;
  font-size: 17px;
  // margin-top: 60px;
  position: absolute;
  top: 65%;
}

.sloganRoad {
  color: white;
  font-size: 35px;
  position: absolute;
  top: 45%;
  font-weight: 600;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 1s;
}

.fade-enter,
.fade-leave-to {
  // transition: opacity 0.5s ease-out;
  opacity: 0;
}
</style>
