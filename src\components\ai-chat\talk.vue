<template>

  <div>
    <div class="talk-list">
      <div v-for="(item, index) in messageList" :key="index" :class="item.type === 'user' ? 'user_con' : 'teacher_con'">
        <div class="ai ai_teacher item" v-if="item.type === 'teacher'">
          <!-- <img class="avatar avatar-server" src="./assets/ai-avatar-day.png" /> -->
          <div class="answer-box">
            {{ item.content }}
          </div>
          <img v-if="currentAudioIdx === index" class="volume"
            src="https://bookor-application.oss-cn-beijing.aliyuncs.com/sound_play.gif" />
        </div>
        <div class="ai ai_student item" v-else-if="item.type === 'student'">
          <!-- <img class="avatar avatar-server" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/student_male.png" /> -->
          <div class="answer-box">
            {{ item.content }}
          </div>
          <img class="volume" v-if="currentAudioIdx === index"
            src="https://bookor-application.oss-cn-beijing.aliyuncs.com/sound_play.gif" />
        </div>

        <div class="ai ai_user item" v-else>
          <img v-if="currentAudioIdx === index" class="volume"
            src="https://bookor-application.oss-cn-beijing.aliyuncs.com/sound_play.gif" />
          <div class="answer-box">
            {{ item.content }}
          </div>
          <!-- <img class="avatar avatar-server" src="./assets/ai-avatar-day.png" /> -->
        </div>
      </div>
      <div class="preset-list">
        <div class="item-preset" v-for="(item, index) in recommendList" :key="index"
          @click="handlePreset(item.content)">
          {{ item.content }}
        </div>
      </div>
    </div>
    <div class="bottom-input">
      <el-input v-model="textInput" placeholder="请输入你想要的所有问题" @keyup.native="onKeyUp"></el-input>
      <el-button type="primary" size="small" @click="toSendMessage">提问</el-button>
    </div>
  </div>
</template>

<script>
import AudioUtil from './AudioUtil';
import { clearTimer, createTalk, getAudioUrl, pollDetail, pollList } from './talkUtil';
export default {
  data() {
    return {
      loading: false,
      conversationId: '', // 会话id
      textInput: '',
      recommendList: [],
      defaultAudio: document.createElement('audio'), // 默认的音频
      audioList: [],
      currentAudioIdx: null, // 当前播放的audio
      isChatIng: true, // 单次问题是否结束
      messageList: [
        {
          type: 'teacher',
          content: '回答中...',
          loading: 1
        }
      ],
      audioUtil: null
    }
  },
  props: ['currentInfo', 'rightExpanded'],
  watch: {
    rightExpanded(val) {
      if (!val) {
        clearTimer()
        this.audioUtil?.pause()
      }
    }
  },
  methods: {
    toSendMessage(con) {
      if (!this.isChatIng) {
        return this.$message.error({
          message: '请等待当前问题回答结束',
          duration: 600
        })
      }
      let content = ''
      if (typeof con === 'string') {
        content = con
      } else {
        content = this.textInput
      }
      if (!content) {
        return
      }
      this.messageList.push({
        type: 'user',
        content: content
      })
      this.pollAudio([''])

      this.messageList.push({
        type: 'teacher',
        content: '回答中...',
        loading: 1
      })
      this.textInput = ''
      this.isChatIng = false
      createTalk(content, this.conversationId).then((res) => {
        this.conversationId = res.conversation_id

        pollDetail(res.id, res.conversation_id).then((pollRes) => {
          if (pollRes) {
            this.isChatIng = true
            pollList(res.id, res.conversation_id).then((listRes) => {
              const current = listRes.filter((item) => ['assistant', 'answer'].includes(item.type))
              const con = current[current.length - 1].content

              const list = this.formatData(con)
              if (list.length) {
                list.forEach((item, idx) => {
                  if (idx === 0) {
                    this.messageList[this.messageList.length - 1] = {
                      type: 'teacher',
                      content: item,
                      status: 1 //  回答中，1，回答完成
                    }
                  } else if (idx % 2 !== 0) {
                    this.messageList.push({
                      type: 'student',
                      content: item,
                      status: 1 //  回答中，1，回答完成
                    })
                  } else {
                    this.messageList.push({
                      type: 'teacher',
                      content: item,
                      status: 1 //  回答中，1，回答完成
                    })
                  }
                })
                this.pollAudio(list)
              } else {
                this.messageList[this.messageList.length - 1] = {
                  type: 'teacher',
                  content: con,
                  status: 1 //  回答中，1，回答完成
                }
              }
              const recommendList = listRes?.filter((item) => item.type === 'follow_up')
              this.recommendList = recommendList
              this.$forceUpdate()
            })
          }
        })
      })
    },
    onKeyUp() {
      if (event.key === 'Enter' && this.textInput) {
        this.toSendMessage()
      }
    },
    handlePreset(con) {
      this.toSendMessage(con)
    },
    containsAny(str, substrings) {
      return substrings.some((substring) => str.includes(substring))
    },
    formatData(str) {
      const arr = str.split(/\n/)
      const result = arr.filter((item) => this.containsAny(item, ['老师：', '学生：']))
      const list = result.map((item) => {
        return item.replace(/^(?:老师|学生)：/, '')
      })
      return list
    },
    pollAudio(list, callback) {
      let idx = 0
      let count = 0 // 判断是不是第一段语音
      const poll = () => {
        let voice = 'zhishuo'
        if (idx % 2 === 0) {
          voice = 'aixia'
        }
        getAudioUrl(list[idx], voice).then((audio) => {
          this.audioUtil.add(audio)
          if (idx < list.length) {
            poll()
          }
          this.audioUtil.play(this.playAudio)
          if (!count) {
            callback && callback()
          }
          count += 1
        })
        idx += 1
      }
      poll()
    },
    playAudio(idx) {
      this.currentAudioIdx = idx
    }
  },
  mounted() {
    this.audioUtil = new AudioUtil(this.playAudio)
    let url = ''
    if (this.$route.query?.url) {
      url = this.$route.query.url
    } else {
      url = this.currentInfo.url
    }

    const con = `讨论一下这个链接中的论文 ${url}`
    this.isChatIng = false
    createTalk(con, '').then((res) => {
      this.conversationId = res.conversation_id
      pollDetail(res.id, res.conversation_id).then((detailRes) => {
        if (detailRes) {
          this.isChatIng = true
          pollList(res.id, res.conversation_id).then((listRes) => {
            const current = listRes.find((item) => item.type === 'answer')
            // current.content = '**主播 A**：今天我们来聊一聊一篇很有深度的论文。这篇论文呢，链接在此，https://oss.bookor.com.cn/uploads/semanticscholar_paper/2024-06-10/23892cae5f831fad7065ad1613687c672fca499d.pdf。不知道你读完有何感想呢？\n**主播 B**：我觉得这篇论文的研究角度颇为新颖。它从多个层面探讨了一个复杂的问题，确实给人以深刻的启发。首先，它的理论框架构建得非常严谨，每一个观点都有充分的论据支持。你觉得呢？\n**主播 A**：确实如此。论文中对于相关概念的界定也十分清晰，让人一目了然。而且，作者在论证过程中运用了大量的实例，使得理论更加具有说服力。不过，我也在思考，其中的一些观点是否具有普适性呢？\n**主播 B**：这是一个很值得探讨的问题。有些观点可能在特定的情境下才成立，而在其他情况下则需要进一步的调整和完善。比如说，论文中提到的某个案例，在不同的文化背景下可能会有不同的解读。\n**主播 A**：对呀，文化背景的差异确实会对论文中的观点产生影响。那么，我们应该如何在不同的文化背景下应用这些观点呢？\n**主播 B**：我认为，我们需要充分考虑当地的文化特点和实际情况，进行有针对性的分析和应用。不能简单地生搬硬套论文中的观点，而应该根据具体情况进行灵活调整。\n**主播 A**：嗯，有道理。此外，我还注意到论文中的研究方法也很有特色。它采用了多种研究方法相结合的方式，使得研究结果更加全面和准确。\n**主播 B**：没错，这种多方法的研究方式可以相互印证，提高研究的可靠性。同时，也为我们今后的研究提供了一种新的思路和方法。\n**主播 A**：那么，你觉得这篇论文对于我们的实际工作和生活有哪些启示呢？\n**主播 B**：我觉得它可以让我们更加深入地理解一些复杂的问题，并且提供了一些解决问题的思路和方法。比如，在面对类似的问题时，我们可以借鉴论文中的研究方法和观点，进行更加科学和有效的分析和决策。\n**主播 A**：非常赞同。这篇论文确实值得我们深入思考和探讨。希望我们以后能看到更多这样有价值的研究成果。'
            const list = this.formatData(current.content)
            const con = current.content
            if (list.length > 1) {
              this.pollAudio(list, () => {
                setTimeout(() => {
                  list.forEach((item, idx) => {
                    if (idx === 0) {
                      this.messageList[this.messageList.length - 1] = {
                        type: 'teacher',
                        content: item,
                        status: 1 //  回答中，1，回答完成
                      }
                    } else if (idx % 2 !== 0) {
                      this.messageList.push({
                        type: 'student',
                        content: item,
                        status: 1 //  回答中，1，回答完成
                      })
                    } else {
                      this.messageList.push({
                        type: 'teacher',
                        content: item,
                        status: 1 //  回答中，1，回答完成
                      })
                    }
                  })
                }, 2000)
              })
            } else {
              getAudioUrl(con, 'aixia').then((audio) => {
                this.defaultAudio.src = audio
                this.audioUtil.add(audio)
                this.audioUtil.play(this.playAudio)
              })
              this.messageList[this.messageList.length - 1] = {
                type: 'teacher',
                content: con,
                status: 1 //  回答中，1，回答完成
              }
            }
            const recommendList = listRes?.filter((item) => item.type === 'follow_up')
            this.recommendList = recommendList
          })
          this.$forceUpdate()
        }
      })
    })
  }
}
</script>

<style lang="scss" scoped>
.talk-list {
  height: 80vh;
  overflow: auto;
  padding-bottom: 40px;

  .student_con,
  .teacher_con {
    width: 90%;
  }

  .user_con {
    display: flex;
    justify-content: flex-end;
  }

  .item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    width: 90%;
    position: relative;

    .volume {
      position: absolute;
      width: 28px;
      right: -34px;
      bottom: 0;
    }
  }

  .answer-box {
    font-size: 16px;
    color: #444;
    padding: 1vw;
    background-color: #fff2cc;
    border-radius: 5px;
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
    word-wrap: break-word;
    word-break: break-all;
    white-space: pre-line;
    box-sizing: border-box;
  }

  .ai_teacher {
    .answer-box {
      background-color: #fff2cc;
    }
  }

  .ai_student {
    .answer-box {
      background-color: #bdd7ee;
      color: #444;
    }
  }

  .ai_user {
    justify-content: flex-end;

    .answer-box {
      background-color: #409eff;
      color: #fff;
    }

    .volume {
      position: absolute;
      width: 28px;
      left: -34px;
      bottom: 0;
      transform: rotate(180deg);
    }
  }

  .avatar {
    height: 40px;
    width: 40px;
    background-color: #fff2cc;
    border-radius: 50%;
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
  }
}

.preset-list {

  // position: absolute;
  // bottom: 60px;
  // left: 34px;
  .item-preset {
    background-color: #fff;
    border: 1px solid #ccc;
    height: 30px;
    line-height: 30px;
    margin-top: 5px;
    border-radius: 4px;
    padding: 0px 6px;
    cursor: pointer;
  }
}

.bottom-input {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 4px;
  gap: 8px;

  .el-icon-time {
    font-size: 22px;
  }

  :deep(.el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
</style>
