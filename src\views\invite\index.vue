<template>
  <div class="main">
    <h3 v-if="!isSubmitted">内测邀请</h3>
    <div class="afterSubmit" v-else>已提交</div>
    <div class="form-box" v-if="!isSubmitted">
      <div class="form-group">
        <label class="label">
          <span>用户昵称</span>
          <span class="asterisk"> *</span>
        </label>
        <input
          type="text"
          v-model="loginForm.name"
          class="input"
          maxLength="15"
          placeholder="请输入用户昵称"
        />
        <span class="check-message">{{ errorMessage.name }}</span>
      </div>

      <div class="form-group">
        <label class="label">
          <span>邮箱</span>
          <span class="asterisk">&nbsp;*</span>
        </label>
        <input
          type="text"
          v-model="loginForm.postbox"
          class="input"
          maxlength="50"
          placeholder="请输入邮箱"
        />
        <span class="check-message">{{ errorMessage.postbox }}</span>
      </div>

      <div class="form-group">
        <label class="label">
          <span>手机号</span>
          <span class="asterisk">&nbsp;*</span>
        </label>
        <input
          type="text"
          v-model="loginForm.phone"
          class="input"
          placeholder="请输入手机号"
          onkeyup="this.value=this.value.replace(/\D|^0/g,'')"
        />
        <span class="check-message">{{ errorMessage.phone }}</span>
      </div>

      <div class="form-group">
        <label class="label">
          <span>微信账号</span>
          <span class="asterisk">&nbsp;*</span>
        </label>
        <input
          type="text"
          v-model="loginForm.wechat"
          class="input"
          minlength="6"
          maxlength="20"
          placeholder="请输入微信账号"
        />
        <span class="check-message">{{ errorMessage.wechat }}</span>
      </div>

      <div class="form-group">
        <label class="label">
          <span>邀请人ID</span>
          <span class="asterisk">&nbsp;*</span>
        </label>
        <input
          type="text"
          v-model="loginForm.invite_member_id"
          class="input"
          maxLength="4"
          placeholder="请输入邀请人ID"
          onkeyup="this.value=this.value.replace(/\D|^0/g,'')"
        />
        <span class="check-message">{{ errorMessage.invite_member_id }}</span>
      </div>
      <div class="form-group">
        <button class="btn btn-reset" @click="reset()">重置</button>
        <button class="btn" @click="submit()">提交</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import Schema from 'async-validator'
import { reactive, ref } from 'vue'
import Vrouter from '@/router'

const id = Vrouter.history.current.params.id

// 表单对象
const loginForm = reactive({
  name: '',
  postbox: '',
  phone: '',
  wechat: '',
  invite_member_id: id
})
// 校验规则
const descriptor = reactive({
  name: {
    required: true,
    min: 1,
    max: 15,
    message: '用户昵称不能为空'
  },
  postbox: [
    {
      required: true,
      pattern:
        /^([a-zA-Z0-9]+[||-|.]?)[a-zA-Z0-9]+@([a-zA-Z0-9]+[||.]?)[a-zA-Z0-9]+.[a-zA-Z]{2,6}/,
      message: '邮箱账号不能为空'
    }
  ],
  phone: [{ required: true, pattern: /^1\d{10}/, message: '手机号码不能为空' }],
  wechat: {
    required: true,
    message: '微信账号不能为空'
  },
  invite_member_id: [{ required: true, message: '邀请人ID不能为空' }]
})
// 错误提示
const errorMessage = reactive({
  name: '',
  postbox: '',
  phone: '',
  wechat: '',
  invite_member_id: ''
})
const validator = reactive(new Schema(descriptor))
const isSubmitted = ref(false)
const submit = () => {
  clearMessage()
  validator
    .validate(loginForm, {
      firstFields: true
    })
    .then(() => {
      // 校验通过
      isSubmitted.value = true
      sendForm(loginForm)
    })
    .catch(({ errors }) => {
      // 校验未通过
      // 显示错误信息
      for (const { field, message } of errors) errorMessage[field] = message
    })
}
// 重置表单
const reset = () => {
  clearMessage()
  Object.keys(loginForm).forEach((key) => {
    loginForm[key] = ''
  })
}
// 清空校验错误提示
const clearMessage = () => {
  for (const key in errorMessage) {
    errorMessage[key] = ''
  }
}

// 提交表单
const sendForm = (form) => {
  const { name, postbox, phone, wechat, invite_member_id } = form
  const params = new URLSearchParams({
    name,
    postbox,
    phone,
    wechat,
    invite_member_id: parseInt(invite_member_id)
  })
  fetch(
    'https://api.bookor.com.cn/index.php/mobile/Nokeyinterface/inviteMember',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    }
  )
    .then((response) => {
      if (response.ok) {
        return response.json()
      }
      throw new Error('Network response was not ok.')
    })
}
</script>

<style scoped>
.main {
  text-align: center;
}
.btn {
  margin: 0;
  line-height: 1;
  padding: 15px;
  height: 30px;
  width: 60px;
  font-size: 14px;
  border-radius: 4px;
  color: #fff;
  background-color: #2080f0;
  white-space: nowrap;
  outline: none;
  position: relative;
  border: none;
  display: inline-flex;
  flex-wrap: nowrap;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  user-select: none;
  text-align: center;
  cursor: pointer;
  text-decoration: none;
}

.btn-reset {
  background-color: #a6a6a6;
  margin-right: 15px;
}

.form-box {
  width: 500px;
  max-width: 100%;
  margin: 0 auto;
  padding: 10px;
  /* border: 5px solid rgb(171 174 186); */
}
.form-group {
  height: 30px;
  margin: 10px;
  padding: 10px 15px 10px 0;
}
.label {
  padding-right: 10px;
  padding-left: 10px;
  display: inline-block;
  box-sizing: border-box;
  width: 110px;
  text-align: right;
}

.input {
  width: calc(100% - 120px);
  height: 28px;
}
.check-message {
  color: #d03050;
  position: relative;
  left: -70px;
  font-size: 12px;
}
.asterisk {
  color: #d03050;
}

.afterSubmit {
  margin-top: 10vh;
  font-size: 30px;
}
</style>
