<template>
    <div class="login-container">
      <div class="color"></div>
      <div class="color"></div>
      <div class="color"></div>
      <div class="login-box">
        <div class="circle" style="--x:0"></div>
        <div class="circle" style="--x:1"></div>
        <div class="circle" style="--x:2"></div>
        <div class="circle" style="--x:3"></div>
        <div class="circle" style="--x:4"></div>
      </div>
      <div class="mind-list-container">
        <div class="mind-list-header">
          <el-tooltip content="返回首页" placement="top">
            <i class="el-icon-arrow-left" style="font-size: 20px;" @click="handleToHomePage" />
          </el-tooltip>
          <el-input type="text"
            prefix-icon="el-icon-search"
            class="search-input"
            v-model:value="keyword"
            clearable
            @clear="clearKeyword"
            @keyup.enter.native="handleSearch"
            placeholder="输入关键词进行搜素"></el-input>
          <el-tooltip content="添加脑图" placement="top">
            <el-button type="primary" circle icon="el-icon-plus" @click="addMind"></el-button>
          </el-tooltip>
        </div>
        <div class="mind-list infinite-list" v-infinite-scroll="getMindList">
           <div class="mind-list-item" v-for="item in mindList" :key="item.mind_map_id" @click="handleMindList(item.mind_map_id)">
            <p>{{ item.content }}</p>
            <p class="time">{{ format(item.add_time) }}</p>
          </div>
          <el-empty :image-size="200" v-if="!mindList.length"></el-empty>
        </div>
      </div>
      <el-dialog title="新增脑图" :visible.sync="addMindVisible" width="40%">
        <el-input v-model="mindTitle" placeholder="请输入脑图名称" maxlength="20" show-word-limit clearable />
        <span slot="footer" class="dialog-footer">
          <el-button @click="addMindVisible = false">取 消</el-button>
          <el-button type="primary" @click="addMindMap">确 定</el-button>
        </span>
      </el-dialog>
  </div>
</template>

<script>
import axios from "@/http";
import dayjs from 'dayjs'
export default {
  name: 'MindList',
  data() {
    return {
      keyword: '',
      mindList: [],
      addMindVisible: false,
      mindTitle: '',
      page: 1,
      size: 20
    }
  },
  methods: {
    clearKeyword() {
      this.keyword = ''
      this.page = 1
      this.getMindList()
    },

    handleSearch() {
      if(!this.keyword) {
        this.clearKeyword()
        return
      }
      this.page = 1;
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      axios.post('ai/mind/search', {
        memberId: userInfo.userid,
        keyword: this.keyword,
        page: this.page,
        size: this.size
      }).then(res => {
        this.mindList = res.data;
      })
    },
    addMindMap() {
      if(!this.mindTitle) {
        this.$message.error('请输入脑图名称')
        return
      }
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      this.addMindVisible = false
      axios.post('/ai/mind/add', {
          title: this.mindTitle,
          memberId: userInfo.userid
      }).then(res => {
        if(res.code === 200) {
          this.$message.success('添加成功')
          window.open(`#/mind/${res.data}`, '_blank');
        } else {
          this.$message.error('添加失败')
        }
      })
    },
    addMind() {
      this.addMindVisible = true
    },
    format(val) {
      if (!val) return '';
      return dayjs(val * 1000).format('YYYY-MM-DD HH:mm');
    },
    handleToHomePage() {
      this.$router.push({
        path: '/index'
      })
    },
    handleMindList(id) {
      window.open(`#/mind/${id}`, '_blank');
    },
    getMindList() {
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      axios({
        url: `/ai/mind/list?memberId=${userInfo.userid}&page=${this.page}&size=${this.size}`,
        method: "get",
      }).then(res => {
        if(res.code === 200) {
          if(this.page === 1) {
            this.mindList = res.data
          } else {
            this.mindList = this.mindList.concat(res.data)
          }
          this.page +=1
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@keyframes animate {
  // 0%, 100%, {
  //     transform: translateY(-50px);
  // }
  // 50% {
  //     transform: translateY(50px);
  // }
}
.login-container {
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(to bottom, #f1f4f9, #dff1ff);
  .color {
    /* 绝对定位 */
    position: absolute;
    /* 使用filter(滤镜) 属性，给图像设置高斯模糊*/
    filter: blur(200px);
  }
  .color:nth-child(1) {
    top: -350px;
    width: 600px;
    height: 600px;
    background: #ff359b;
  }

  .color:nth-child(2) {
    bottom: -150px;
    left: 100px;
    width: 500px;
    height: 500px;
    background: #fffd87;
  }

  .color:nth-child(3) {
    bottom: 50px;
    right: 100px;
    width: 500px;
    height: 500px;
    background: #00d2ff;
  }
  .login-box{
    position: relative;
    width: 70vw;
    min-height: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
    .circle{
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      /* backdrop-filter属性为一个元素后面区域添加模糊效果 */
      backdrop-filter: blur(5px);
      box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.5);
      border-right: 1px solid rgba(255, 255, 255, 0.2);
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      /* 使用filter(滤镜) 属性，改变颜色。
      hue-rotate(deg)  给图像应用色相旋转
      calc() 函数用于动态计算长度值
      var() 函数调用自定义的CSS属性值x*/
      filter: hue-rotate(calc(var(--x) * 70deg));
      animation: animate 10s linear infinite;
      animation-delay: calc(var(--x) * -1s);
    }
    .circle:nth-child(1) {
      top: -50px;
      right: -60px;
      width: 100px;
      height: 100px;
    }

    .circle:nth-child(2) {
      top: 150px;
      left: -100px;
      width: 120px;
      height: 120px;
      z-index: 2;
    }

    .circle:nth-child(3) {
      bottom: 50px;
      right: -60px;
      width: 80px;
      height: 80px;
      z-index: 2;
    }

    .circle:nth-child(4) {
      bottom: -80px;
      left: 100px;
      width: 60px;
      height: 60px;
    }

    .circle:nth-child(5) {
      top: -80px;
      left: 140px;
      width: 60px;
      height: 60px;
    }
    .inputBox{
      width: 100%;
      margin-top: 20px;
    }
    :deep(.ant-input){
      width: 100%;
      padding: 10px 20px;
      background: rgba(255, 255, 255, 0.2);
      outline: none;
      border: none;
      border-radius: 30px;
      border: 1px solid rgba(255, 255, 255, 0.5);
      border-right: 1px solid rgba(255, 255, 255, 0.2);
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      font-size: 16px;
      letter-spacing: 1px;
      color: #000;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05)
    }
  }
}

.mind-list-container{
  position: absolute;
  top: 80px;
  left: 15vw;
  width: 70vw;
  z-index: 3;
  .mind-list-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-input{
      border-radius: 20px;
      width: 60%;
    }
    .el-icon-arrow-left{
      cursor: pointer;
    }
  }
  .mind-list{
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: auto;
    height: calc(100vh - 160px);
    .mind-list-item{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      border-radius: 10px;
      background: #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      &:hover{
        background: #f0f2f5;
      }
      .time{
        color: #999;
      }
    }
  }
}
</style>
