<template>
<div class="content-inside">
    <div class="content-inside-item" style="">
        <span class="content-sub-title-part1" style="color: white; font-size: 25px; ">联想</span>
        <div class="sub_title_item" :class="onePage.currentNum2Show ? 'opa1' : ''">
            关联不同观点和知识
        </div>
    </div>
    <div class="content-inside-item">
        <span class="content-sub-title-part1" style="color: white; font-size: 25px; ">洞见</span>

        <div class="sub_title_item" :class="onePage.currentNum2Show ? 'opa1' : ''">
            在关联中深入洞察新见解
        </div>
    </div>
    <div class="content-inside-item">
        <span class="content-sub-title-part1" style="color: white; font-size: 25px; ">创见</span>
        <div class="sub_title_item" :class="onePage.currentNum2Show ? 'opa1' : ''" >
            激发创造性思维
        </div>
    </div>
</div>
</template>
<script>
export default {
  props: ['onePage']
}
</script>
<style lang="scss" scoped>
.content-inside{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  width: 900px;
  margin: 0 auto;
  margin-top: 60px!important;
  .content-inside-item{
    flex: 1
  }
}
.content-inside-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

@media only screen and (max-width: 420px) {
  .content-inside{
    width: 100%;
    display: block;
    .content-inside-item{
      text-align: center;
      margin-top: 30px;
    }
  }
}
</style>
