<template>
  <div class="topic-detail" @click.stop="handleList($event)">
    <div class="wrap-user" ref="wrap">
      <div
        class="user-box"
        v-for="(item, index) in topicData"
        :key="index"
        :id="'user-' + index"
        :style="{'left': item.x, 'top': item.y}">
        <div class="user" @click.once="handleUser(index)">
          <img class="photo" :src="item.photo" />
          <span class="name">{{ item.name }}</span>
        </div>
        <div v-show="item.isTheme" class="theme">
          <div class="title" @click="handleTitle(index)">{{ item.title }}</div>
          <div v-show="item.isThemeContent" class="content">
            <div class="label">话题内容</div>
            <div class="txt">{{ item.content }}</div>
          </div>
        </div>
      </div>
      <!-- <canvas ref="canvas" id="canvas" style="width: 100vw; height: 100vh;"></canvas> -->
      <div v-if="messageData&&messageData.length">
        <div
          v-for="(item, index) in messageData"
          :key="index"
          class="msg-box"
          :class="{'white': item.type === 1, 'blue': item.type === 2, 'left': item.type === 2, 'right': item.type === 1}"
          :style="{'left': item.x, 'top': item.y}">
          <input class="input" type="text" v-model="item.content" @blur="handleMessage(index)" />
          <span class="arrow"></span>
          <span class="point" :class="{'red': messageData.length > 1, 'blue': messageData.length === 1}"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { jsPlumb } from 'jsplumb'

export default {
  name: 'TopicDetail',
  data () {
    return {
      topicData: [
        {
          photo: require('@/assets/img/icon.png'),
          name: '用户1头像',
          title: '话题1标题',
          content: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          x: this.rand(),
          y: this.rand(),
          isTheme: false,
          isThemeContent: false,
          messages: [
            {target: '', content: '', } //
          ]
        },
        {
          photo: require('@/assets/img/icon.png'),
          name: '用户2头像',
          title: '话题2标题',
          content: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          x: this.rand(),
          y: this.rand(),
          isTheme: false,
          isThemeContent: false
        },
        {
          photo: require('@/assets/img/icon.png'),
          name: '用户3头像',
          title: '话题3标题',
          content: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          x: this.rand(),
          y: this.rand(),
          isTheme: false,
          isThemeContent: false
        },
        {
          photo: require('@/assets/img/icon.png'),
          name: '用户4头像',
          title: '话题4标题',
          content: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          x: this.rand(),
          y: this.rand(),
          isTheme: false,
          isThemeContent: false
        },
        {
          photo: require('@/assets/img/icon.png'),
          name: '用户5头像',
          title: '话题5标题',
          content: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          x: this.rand(),
          y: this.rand(),
          isTheme: false,
          isThemeContent: false
        }
      ],
      messageData: [],
      storageIndex: -1,
      plumbIns: jsPlumb.getInstance(),
      xloop: [],
      yloop: []
    }
  },
  methods: {
    rand () {
      return Math.random() * 50 + '%'
    },
    handleUser (index) {
      const item = this.topicData[index]
      item.isTheme = !item.isTheme
      this.$set(this.topicData, index, item)
      this.handleLine(index)
    },
    handleTitle (index) {
      const item = this.topicData[index]
      item.isThemeContent = !item.isThemeContent
      this.$set(this.topicData, index, item)
    },
    handleLine (index) {
      this.plumbIns.draggable('user-' + index)
      if (this.storageIndex > -1) {
        this.plumbIns.ready(() => {
          this.plumbIns.connect({
            source: 'user-' + this.storageIndex,
            target: 'user-' + index,
            anchor: ['Left', 'Right', 'Top', 'Bottom', [0.3, 0, 0, -1], [0.7, 0, 0, -1], [0.3, 1, 0, 1], [0.7, 1, 0, 1]],
            connector: ['Straight'],
            maxConnections: -1,
            endpoint: 'Blank',
            overlays: [
              ['Arrow', { width: 8, length: 8, location: 1, foldback: 0.25 }],
              [
                'Custom',
                {
                  create: function (component) {
                    return $(
                      '<div class="box" style="'
                      +'height: 14px;'
                      +'border: 1px dashed rgb(126, 126, 126);'
                      +'border-radius: 3px;'
                      +'margin-top: 10px;'
                      +'box-shadow: 0 8px 25px 0 #000;'
                      +'position: relative;'
                      +'"><input style="'
                      +'width: 120px;'
                      +'border: none;'
                      +'padding: 5px 10px 5px 0;'
                      +'border-radius: 3px;'
                      +'text-align: center;'
                      +'outline: none;'
                      +'" type="text" /><span style="'
                      +'box-sizing: border-box;'
                      +'width: 17px;'
                      +'height: 17px;'
                      +'border-top: 1px dashed rgb(126, 126, 126);'
                      +'background: #fff;'
                      +'position: absolute;'
                      +'right: 0;'
                      +'top: 50%;'
                      +'transform: translate(50%, -3px) rotate(45deg);'
                      +'transform-origin: center;'
                      +'"></span></div>')
                  },
                  location: 0.5,
                  id: 'cus'
                }
              ]
            ],
            paintStyle: { stroke: '#4472C4', strokeWidth: 1.5, dashstyle: '3' }
          })
        })
      }
      this.storageIndex = index
    },
    handleMessageLabel (e) {
    },
    switchX (ox, tx, num) {
      const x = parseInt((ox > tx ? tx : ox) + Math.abs(ox - tx) * num) + '%'
      if (this.xloop.indexOf(x) === -1) {
        this.xloop.push(x)
        return x
      } else {
        this.switchPos(ox, tx)
      }
    },
    switchY (oy, ty, num) {
      const y = parseInt((oy > ty ? ty : oy) + Math.abs(oy - ty) * num) + '%'
      if (this.yloop.indexOf(y) === -1) {
        this.yloop.push(y)
        return y
      } else {
        this.switchPos(oy, ty)
      }
    },
    updateMessagePos (ox, oy, tx, ty) {
      ox = parseInt(ox.substring(0, ox.length - 1))
      oy = parseInt(oy.substring(0, oy.length - 1))
      tx = parseInt(tx.substring(0, tx.length - 1))
      ty = parseInt(ty.substring(0, ty.length - 1))
      const num = Math.random()
      return { x: this.switchX(ox, tx, num), y: this.switchY(tx, ty, num) }
    },
    handleMessage (index) {
      if (!this.messageData[index].content) {
        this.messageData.splice(index)
      }
    },
    handleList (e) {
      if (e.target._prevClass === 'wrap-user') {
        this.$emit('handleList')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/scss/topic.scss';
</style>
