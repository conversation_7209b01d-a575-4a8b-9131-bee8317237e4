<template>
  <div class="particles-js-box">
    <div id="particles-js"></div>
  </div>
</template>

<script>
  import particlesJs from "particles.js"
  import particlesConfig from "./particles.json"
  export default {
    data() {
      return {}
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        particlesJS("particles-js", particlesConfig)
        document.body.style.overflow = "hidden";
      }
    }
}
</script>

<style scoped>
.particles-js-box{
  position:fixed;
  width: 100%;
  height: 100%;
  top:0;
  left:0;
}
#particles-js{
  background-color: rgb(40, 49, 82);
  width: 100%;
  height: 100%;
}
</style>
