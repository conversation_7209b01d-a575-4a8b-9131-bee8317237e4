
const getUrlExtension = (url) => {
  // 提取URL中的路径部分
  try {
    const path = new URL(url).pathname
    // 获取路径中最后一个'/'之后的部分，即文件名
    const filename = path.split('/').pop()
    // 检查文件名中是否有'.'，如果有，返回最后一个'.'之后的部分
    const extension = filename.lastIndexOf('.') > 0 ? filename.substring(filename.lastIndexOf('.') + 1) : ''
    return extension
  } catch (error) {
    return ''
  }
}
export default getUrlExtension
