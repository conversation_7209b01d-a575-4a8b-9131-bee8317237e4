import * as THREE from 'three'
import { BLOOM_LAYER, STAR_MAX, STAR_MIN } from '../config/renderConfig.js'
import { starTypes, starLinksTypes } from '../config/starDistributions.js'
import { clamp } from '../utils.js'

const texture = new THREE.TextureLoader().load(
  require('../resources/sprite120.png')
)

let materials
materials = starTypes.color.map(
  (color) =>
    new THREE.SpriteMaterial({
      map: texture,
      color: color
    })
)

let materialsStarLink
materialsStarLink = starLinksTypes.color.map(
  (color) =>
    new THREE.SpriteMaterial({
      map: texture,
      color: color
    })
)

export class Star {
  constructor (position, data) {
    this.position = position
    this.starType = this.generateStarType()
    this.obj = null
    this.userData = data
    this.userData.isClickable = true
  }

  generateStarType () {
    let num = Math.random() * 100.0
    const pct = starTypes.percentage
    for (let i = 0; i < pct.length; i++) {
      num -= pct[i]
      if (num < 0) {
        return i
      }
    }
    return 0
  }

  updateScale (camera) {
    const dist = this.position.distanceTo(camera.position) / 250

    // update star size
    let starSize = dist * starTypes.size[this.starType]
    starSize = clamp(starSize, STAR_MIN, STAR_MAX)
    this.obj?.scale.copy(new THREE.Vector3(starSize, starSize, starSize))
  }

  toThreeObject (scene, type) {
    let sprite
    if (type == 'isStarLink') {
      sprite = new THREE.Sprite(materialsStarLink[this.starType])
    } else {
      sprite = new THREE.Sprite(materials[this.starType])
    }

    sprite.layers.set(BLOOM_LAYER)

    sprite.scale.multiplyScalar(starTypes.size[this.starType])
    sprite.position.copy(this.position)

    this.obj = sprite
    sprite.userData = this.userData // 在 sprite 对象中存储 userData 属性

    // 添加 click 事件监听器
    sprite.addEventListener('click', () => {
  
    })
    sprite.intersectable = true
    scene.add(sprite)
  }
}
