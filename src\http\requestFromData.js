import axios from 'axios'
import router from '../router'

const service = axios.create({
  baseURL: '/',
  timeout: 30000 // 设置请求超时时间
})
// let loading = "";
// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求发送之前做一些处理
    if (!(config.headers['Content-Type'])) {
      if (config.method === 'post') {
        config.headers['Content-Type'] = 'multipart/form-data'
        for (var key in config.data) {
          if (config.data[key] === '') {
            delete config.data[key]
          }
        }
      } else {
        config.headers['Content-Type'] =
     'application/x-www-form-urlencoded;charset=UTF-8'
        config.data = JSON.stringify(config.data)
      }
    }
    const authorization = localStorage.getItem('authorization')
    // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    if (authorization) {
      config.headers.Authorization = authorization
    } else {
      router.push({ path: '/login' })
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const dataAxios = response.data
    return dataAxios
  },
  (error) => {
    return Promise.reject(error)
  }
)

const requestFromData = (url, params, method = 'post') => {
  // const memberId = JSON.parse(localStorage.getItem('userinfo'))?.userid
  const data = {
    ...params
  }
  const axiosConfig = {
    url: url,
    method: method
  }

  // 根据请求方法添加 params 或 data
  if (method.toLowerCase() === 'get') {
    axiosConfig.params = data
  } else if (method.toLowerCase() === 'post') {
    axiosConfig.data = data
  }
  return new Promise((resolve, reject) => {
    service(axiosConfig).then((result) => {
      if (result.code === 200) {
        resolve(result.result)
      } else {
        reject(result)
      }
    }).catch((err) => {
      reject(err)
    })
  })
}

export default requestFromData
