.top-bar {
  position: fixed;
  top: 20px;
  left: 0;
  width: 100vw;
  z-index: 999999;

  .nav {
    display: inline-block;
    vertical-align: middle;

    .item {
      display: inline-flex;
      align-items: baseline;
      box-sizing: border-box;
      width: 45px;
      height: 45px;
      // background: rgb(255, 242, 204);
      // border: 2rem solid #8faadc;
      border-radius: 10px;
      border: 3px solid #8faadc;
      margin-left: 20px;
      position: relative;

      // padding: 2px;
      .txt {
        display: block;
        // width: 50px;
        text-align: center;
        font-size: 12px;
        color: #bdd7ee;
        text-decoration: none;
        position: absolute;
        top: 45%;
        left: 50%;
        font-weight: bold;
        transform: translate(-50%, -50%);
      }

      .search-icon {
        width: 2.7rem;
        height: 2.8rem;
      }
    }
  }

  .personal-box {
    float: right;
    position: relative;
    margin-right: 15px;

    .photo {
      width: 53px;
      height: 53px;
      border-radius: 50%;
      box-shadow: 2px 2px 2px 3px rgba(100, 100, 100, .6);
      position: relative;
      cursor: pointer;
      z-index: 60;
    }

    .list {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 100%);
      display: none;

      .item {
        display: block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        box-shadow: 2px 2px 2px 3px rgba(100, 100, 100, .6);
        margin-top: -5px;
        background: rgb(255, 242, 204);
        position: relative;
        cursor: pointer;

        .txt {
          width: 40px;
          text-align: center;
          font-size: 14px;
          color: #000;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    &:hover {
      .list {
        display: block;
      }
    }
  }

  //移动端的导航
  .nav-mobile {
    display: none;
  }
}

@media (max-width: 700px) {
  .top-bar {
    top: 0;
  }

  .nav {
    display: none !important;
  }

  .nav-mobile {
    display: inline-block !important;
    vertical-align: middle !important;

    .nav-mobile-box {
      align-items: baseline;
      box-sizing: border-box;
      width: 60px;
      height: 45px;
      right: 0;
      position: absolute;
      top: 43px;

      .item {
        display: inline-flex;
        align-items: baseline;
        box-sizing: border-box;
        width: 60px;
        height: 17px;
        justify-content: center;
        border-radius: 10px;
        /* border: 3px solid #8faadc; */
        /* margin-left: 20px; */
        position: relative;

        .txt {
          display: block;
          // width: 50px;
          text-align: center;
          font-size: 14px;
          color: #bdd7ee;
          text-decoration: none;
          position: absolute;
          top: 45%;
          left: 50%;
          font-weight: bold;
          transform: translate(-50%, -50%);
        }

        .search-icon {
          width: 1.7rem;
          height: 1.8rem;
        }
      }

      .item:hover {
        // background-color: white;

        .txt {
          color: #639ba8;
        }
      }
    }
  }
}