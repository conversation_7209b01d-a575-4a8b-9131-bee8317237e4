import {
  backgroundPositionList as backgroundPositionListEn,
  backgroundRepeatList as backgroundRepeatListEn,
  backgroundSizeList as backgroundSizeListEn,
  borderDasharrayList as borderDasharrayListEn,
  downTypeList as downTypeListEn,
  fontFamily<PERSON>ist as fontFamilyListEn,
  linearGradientDirList as linearGradientDirListEn,
  lineStyleList as lineStyleListEn,
  numberLevelList as numberLevelListEn,
  numberTypeList as numberTypeListEn,
  rootLineKeepSameInCurveList as rootLineKeepSameInCurveListEn,
  shapeList as shapeListEn,
  shortcutKeyList as shortcutKeyListEn,
  sidebarTriggerList as sidebarTriggerListEn
} from './en'
import {
  backgroundPositionList as backgroundPositionListZh,
  backgroundRepeatList as backgroundRepeatListZh,
  backgroundSizeList as backgroundSizeListZh,
  borderDasharrayList as borderDasharrayListZh,
  borderRadius<PERSON>ist,
  borderWidthList,
  colorList,
  downTypeList as downType<PERSON>ist<PERSON>h,
  fontFamily<PERSON>ist as fontFamily<PERSON>ist<PERSON>h,
  fontSizeList,
  langList,
  linearGradientDirList as linearGradientDirListZh,
  lineStyleList as lineStyleListZh,
  lineStyleMap as lineStyleMapZh,
  lineWidthList,
  numberLevelList as numberLevelListZh,
  numberTypeList as numberTypeListZh,
  rootLineKeepSameInCurveList as rootLineKeepSameInCurveListZh,
  shapeListMap as shapeListMapZh,
  shapeList as shapeListZh,
  shortcutKeyList as shortcutKeyListZh,
  sidebarTriggerList as sidebarTriggerListZh,
  store
} from './zh'
import {
  backgroundPositionList as backgroundPositionListZhtw,
  backgroundRepeatList as backgroundRepeatListZhtw,
  backgroundSizeList as backgroundSizeListZhtw,
  borderDasharrayList as borderDasharrayListZhtw,
  downTypeList as downTypeListZhtw,
  fontFamilyList as fontFamilyListZhtw,
  linearGradientDirList as linearGradientDirListZhtw,
  lineStyleList as lineStyleListZhtw,
  numberLevelList as numberLevelListZhtw,
  numberTypeList as numberTypeListZhtw,
  rootLineKeepSameInCurveList as rootLineKeepSameInCurveListZhtw,
  shapeList as shapeListZhtw,
  shortcutKeyList as shortcutKeyListZhtw,
  sidebarTriggerList as sidebarTriggerListZhtw
} from './zhtw'

const fontFamilyList = {
  zh: fontFamilyListZh,
  en: fontFamilyListEn,
  zhtw: fontFamilyListZhtw
}

const borderDasharrayList = {
  zh: borderDasharrayListZh,
  en: borderDasharrayListEn,
  zhtw: borderDasharrayListZhtw
}

const lineStyleList = {
  zh: lineStyleListZh,
  en: lineStyleListEn,
  zhtw: lineStyleListZhtw
}

const lineStyleMap = {
  zh: lineStyleMapZh,
  en: lineStyleMapZh,
  zhtw: lineStyleMapZh
}

const rootLineKeepSameInCurveList = {
  zh: rootLineKeepSameInCurveListZh,
  en: rootLineKeepSameInCurveListEn,
  zhtw: rootLineKeepSameInCurveListZhtw
}

const backgroundRepeatList = {
  zh: backgroundRepeatListZh,
  en: backgroundRepeatListEn,
  zhtw: backgroundRepeatListZhtw
}

const backgroundPositionList = {
  zh: backgroundPositionListZh,
  en: backgroundPositionListEn,
  zhtw: backgroundPositionListZhtw
}

const backgroundSizeList = {
  zh: backgroundSizeListZh,
  en: backgroundSizeListEn,
  zhtw: backgroundSizeListZhtw
}

const shortcutKeyList = {
  zh: shortcutKeyListZh,
  en: shortcutKeyListEn,
  zhtw: shortcutKeyListZhtw
}

const shapeList = {
  zh: shapeListZh,
  en: shapeListEn,
  zhtw: shapeListZhtw
}

const shapeListMap = {
  zh: shapeListMapZh,
  en: shapeListMapZh,
  zhtw: shapeListMapZh
}

const sidebarTriggerList = {
  zh: sidebarTriggerListZh,
  en: sidebarTriggerListEn,
  zhtw: sidebarTriggerListZhtw
}

const downTypeList = {
  zh: downTypeListZh,
  en: downTypeListEn,
  zhtw: downTypeListZhtw
}

const numberTypeList = {
  zh: numberTypeListZh,
  en: numberTypeListEn,
  zhtw: numberTypeListZhtw
}

const numberLevelList = {
  zh: numberLevelListZh,
  en: numberLevelListEn,
  zhtw: numberLevelListZhtw
}

const linearGradientDirList = {
  zh: linearGradientDirListZh,
  en: linearGradientDirListEn,
  zhtw: linearGradientDirListZhtw
}

export {
  backgroundPositionList, backgroundRepeatList, backgroundSizeList, borderDasharrayList, borderRadiusList, borderWidthList, colorList, downTypeList, fontFamilyList, fontSizeList, langList, linearGradientDirList, lineStyleList,
  lineStyleMap, lineWidthList, numberLevelList, numberTypeList, rootLineKeepSameInCurveList, shapeList,
  shapeListMap, shortcutKeyList, sidebarTriggerList, store
}

