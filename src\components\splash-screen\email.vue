<template>
    <!-- 第十页 输入邮件页面 -->
    <div class="section">
        <div class="page-box">
            <div style="
                text-align: center;
                color: #ffc000;
                margin-top: 10%;
                margin-left: 5px;
              ">
                您将通过电子邮件收到确认。
                <img src="../../components/homepage/img/电子邮件确认.png" style="width: 1.5%; margin-bottom: -5px" />
                <!-- <CircleCheck
                style="width: 100em; height: 100em; margin-right: 8px"
              /> -->
            </div>
            <div>
                <div class="bottomInfo">
                    <div>
                        <el-input type="text" placeholder="名字" v-model="name" class="info-box"
                            style="color: black; font-weight: 500" />
                    </div>
                    <div>
                        <el-select v-model="profession_area" placeholder="专业领域" class="info-box">
                            <el-option v-for="item in profession_area_options" :label="item.text" :key="item.value"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select v-model="read_habit" placeholder="阅读习惯" class="info-box">
                            <el-option v-for="item in read_habit_options" :label="item.text" :key="item.value"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select v-model="is_note" placeholder="是否做阅读笔记" class="info-box" @change="noteToolsChange">
                            <el-option v-for="item in is_note_options" :label="item.text" :key="item.value"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div v-show="noteToolsShow">
                        <el-select v-model="note_tools" placeholder="笔记工具" class="info-box">
                            <el-option v-for="item in note_tools_options" :label="item.text" :key="item.value"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div style="margin-top: 3%; text-align: center">
                <el-button @click.once="sendEmail" round color="#D6A557"
                    style="color: white; background-color: transparent">
                    <el-icon>
                        <pointer />
                    </el-icon>
                    发送
                </el-button>
            </div>
        </div>
    </div>

</template>

<script >
// import axios from 'axios'
import request from '@/http/request.js'
export default {
  props: {
    // 返回第一页
    back: {
      type: Function
    },
    // 邮箱
    email: {
      type: String
    }

  },
  data () {
    return {
      curSubPage: '',
      // email: "", //邮箱
      name: '', // 名字
      // 专业领域
      profession_area: '',
      profession_area_options: [
        { value: 1, text: '学生' },
        { value: 2, text: '研究人员' },
        { value: 3, text: '职场从业者' },
        { value: 4, text: '自雇及知识博主' },
        { value: 5, text: '企业主' },
        { value: 6, text: '其他' }
      ],
      // 阅读习惯
      read_habit: '',
      read_habit_options: [
        { value: 1, text: '按顺序逐页阅读' },
        { value: 2, text: '随机选读有兴趣的章节' },
        { value: 3, text: '快读先了解文章结构' },
        { value: 4, text: '只关注有用的知识点' }
      ],
      // 是否做读书笔记
      is_note: '',
      is_note_options: [
        { value: 1, text: '每次阅读都做笔记' },
        { value: 2, text: '重要的会做笔记' },
        { value: 3, text: '偶尔随手记录' },
        { value: 4, text: '从不做笔记' }
      ],
      // 笔记工具
      note_tools: '',
      note_tools_options: [
        { value: 1, text: '书页空白处手写记录' },
        { value: 2, text: '纸质笔记本' },
        { value: 3, text: '电脑笔记本工具' },
        { value: 4, text: '手机记录工具' }
      ],
      noteToolsShow: false
    }
  },
  methods: {

    noteToolsChange () {
      if (this.is_note == 4) {
        this.noteToolsShow = false
      } else {
        this.noteToolsShow = true
      }
    },
    // 发送邮箱

    sendEmail () {
      if (!this.name || !this.is_note || !this.profession_area || !this.read_habit) {
        this.$message({
          message: '信息暂未填写完毕',
          type: 'warning'
        })
        return
      }

      // 调用接口
      request('/mobile/Nokeyinterface/addApply', {
        name: this.name,
        postbox: this.email,
        specialty: this.profession_area,
        use_to: '1',
        reading_habit: this.read_habit,
        reading_note: this.is_note,
        note_tool: this.note_tools
      }).then((result) => {
        this.$message({
          dangerouslyUseHTMLString: true,
          message: `
                            <div>提交成功！</div>
                            <div>欢迎加入“新阅读计划”</div>
                        `,
          type: 'success'
        })
        this.back(1)
      }).catch((data) => {
        this.$message({
          message: data.message,
          type: 'warning'
        })
      })
    }

  }

}

</script>

<style scoped lang='scss'>
.page-box {
  width: 99vw;
  height: 100vh;
  // display: inline-block;
  padding: 20px;
  box-sizing: border-box;

  ::placeholder {
      // text-align: center; /* 让 placeholder 居中 */
      color: #3b3d42;
  }
}
.bottomInfo{
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  margin: 50px auto;
  width: 230px;
}
</style>
