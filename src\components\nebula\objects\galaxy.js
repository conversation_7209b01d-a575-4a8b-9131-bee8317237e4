import * as THREE from 'three'
import { Star } from './star.js'
import {
  CORE_X_DIST,
  CORE_Y_DIST,
  GALAXY_THICKNESS
} from '../config/galaxyConfig.js'
import { gaussianRandom } from '../utils.js'
import { Haze } from './haze.js'
// import { Interactive } from "three.interactive";

export class Galaxy {
  constructor (scene, data, list) {
    this.scene = scene
    // 过滤掉data数组中与list数组中的point_list关键词相同的项
    data.pointData = data.pointData.filter((item) => {
      // 判断是否存在于list数组中的point_list关键词中
      const exist = list.some((obj) =>
        obj.point_list.some(
          (keywordObj) => keywordObj.keywords === item.keywords
        )
      )
      return !exist // 返回不重复的项
    })
    const star_num = data.pointData.length
    this.stars = this.generateObject(
      star_num,
      (pos, idx) => new Star(pos, data.pointData[idx])
    )

    this.haze = this.generateObject(
      star_num,
      (pos, idx) => new Haze(pos, data.pointData[idx])
    )

    this.stars.forEach((star) => {
      star.toThreeObject(scene)
    })
    this.haze.forEach((haze) => haze.toThreeObject(scene))

    // 星链
    for (let i = 0; i < list.length; i++) {
      const mylist = list[i].point_list

      mylist.forEach((item) => {
        item.group = `starList-${i}`
      })

      const originPos = new THREE.Vector3(
        gaussianRandom(0, 100),
        gaussianRandom(0, 100),
        gaussianRandom(0, 5)
      )
      const mylistStar = this.generateObjectStarLink(
        mylist.length,
        (pos, idx) => new Star(pos, mylist[idx]),
        originPos
      )

      const spriteMaterial = new THREE.SpriteMaterial({
        color: 0xffffff
      })
      const sprite = new THREE.Sprite(spriteMaterial)
      sprite.position.set(
        mylistStar[0].position.x,
        mylistStar[0].position.y,
        mylistStar[0].position.z + 10
      )
      sprite.scale.set(40, 20, 1)

      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      canvas.width = 128
      canvas.height = 128
      ctx.fillStyle = '#ffffff'
      ctx.font = '10px 微软雅黑'
      ctx.fillText('测试', 0, 80)

      const texture = new THREE.Texture(canvas)
      texture.needsUpdate = true
      sprite.material.map = texture

      // scene.add(sprite);

      mylistStar.forEach((star) => star.toThreeObject(scene, 'isStarLink'))
    }
  }

  updateScale (camera) {
    this.stars.forEach((star) => {
      star.updateScale(camera)
    })

    this.haze.forEach((haze) => {
      haze.updateScale(camera)
    })
  }

  generateObject (numStars, generator) {
    const objects = []

    let idx = 0 // 初始化 idx

    for (let i = 0; i < numStars; i++) {
      const pos = new THREE.Vector3(
        gaussianRandom(0, CORE_X_DIST * 4),
        gaussianRandom(0, CORE_Y_DIST * 4),
        gaussianRandom(0, GALAXY_THICKNESS * 4)
      )
      const obj = generator(pos, idx) // 传入 idx
      objects.push(obj)
      idx++ // 自增 idx
    }

    // for (let i = 0; i < numStars / 4; i++) {
    //   let pos = new THREE.Vector3(
    //     gaussianRandom(0, OUTER_CORE_X_DIST),
    //     gaussianRandom(0, OUTER_CORE_Y_DIST),
    //     gaussianRandom(0, GALAXY_THICKNESS)
    //   );
    //   let obj = generator(pos, idx); // 传入 idx
    //   objects.push(obj);
    //   idx++; // 自增 idx
    // }

    return objects
  }

  generateObjectStarLink (numStars, generator, originPos) {
    const objects = []

    let idx = 0 // 初始化 idx

    for (let i = 0; i < numStars; i++) {
      const pos = new THREE.Vector3(
        gaussianRandom(originPos.x, 10),
        gaussianRandom(originPos.y, 10),
        gaussianRandom(originPos.z, 10)
      )
      const obj = generator(pos, idx) // 传入 idx
      objects.push(obj)
      idx++ // 自增 idx
    }

    return objects
  }
}
