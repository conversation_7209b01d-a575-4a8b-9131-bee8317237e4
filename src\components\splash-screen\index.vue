<template>
  <div class="container">
    <div>
      <!-- 矩形条 -->
      <div class="rectBox">
        <div class="rect-group rect-group-left">
          <div
            v-for="(rect, index) in rectanglesLeft"
            :key="index"
            class="rect animated fadeInLeft"
            :style="{
              transform: `translateX(${rect.translateX * 0.25}vw)`,
              opacity: rect.opacity,
              height: `${rect.height}px`,
            }"
          ></div>
        </div>

        <div class="rect-group">
          <div
            v-for="(rect, index) in rectanglesRight"
            :key="index"
            class="rect animated fadeInRight"
            :style="{
              transform: `translateX(${rect.translateX * 0.25}vw)`,
              opacity: rect.opacity,
              height: `${rect.height}px`,
            }"
          ></div>
        </div>
      </div>
      <!-- 头部标题 -->
      <div class="header">
        <div
          v-if="isShowTitle"
          style="margin-right: 12px; display: inline-flex"
        >
          <div
            class="door"
            id="bookor"
            v-for="(letter, index) in lettersBookor"
            :key="index"
            :style="{
              animationDelay: `${(lettersBookor.length - index - 1) * 0.2}s`,
            }"
            :class="{ fadeOut: index !== 0 && isShowFade }"
          >
            <span class="left-panel">{{ letter }}</span>
          </div>
        </div>
        <div v-if="isShowTitle" style="display: inline-flex">
          <div
            class="door"
            id="brain"
            style="width: 27px"
            v-for="(letter, index) in lettersBrain"
            :key="index"
            :style="{
              animationDelay: `${(lettersBrain.length - index - 1) * 0.2}s`,
            }"
            :class="{ fadeOut: index !== 0 && isShowFade }"
          >
            <span class="left-panel">{{ letter }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-line">
      <!-- 搜索放大镜 -->
      <div
        class="search-box-input-splash"
        v-if="isShowSearchBox"
        key="isShowSearchBox"
        style="margin-top: 10px; position: relative"
      >
        <div
          class="search-btn-box animate__animated animate__fadeIn animate__delay-3s"
        >
          <input type="text" placeholder=" " v-model="keyword" />
        </div>
        <div
          class="search-line animate__animated animate__fadeIn animate__delay-3s"
          v-show="!keyword"
        ></div>
      </div>
      <!-- 口号 -->
      <transition name="fade">
        <div
          v-show="isShowSlogan"
          key="isShowSlogan"
          class="slogan animate__animated"
          :class="{
            animate__fadeIn: isShowSlogan,
            animate__fadeOut: !isShowSlogan,
          }"
        >
          看得见的大脑知识库
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      rectanglesLeft: [],
      rectanglesRight: [],
      defaultHeight: 80, // 默认的矩形高度，以vw为单位

      isShowTitle: false,
      isShowSlogan: false,
      isShowSearchBox: false,
      isShowHeaderFade: false,
      isShowFade: false,
      lettersBookor: ['B', 'o', 'o', 'k', 'o', 'r'],
      lettersBrain: ['B', 'r', 'a', 'i', 'n'],

      keyword: '',
      isMobile: false // 移动端适配
    }
  },
  mounted () {
    this.handleResize()
    setTimeout(() => {
      this.createRectangles()
    }, 2000)
  },
  methods: {
    createRectangles () {
      const centerX = 0 // 矩形左边距离容器居中的vw值

      for (let i = 0; i < 6; i++) {
        const rect = {
          translateX: centerX,
          opacity: 0
        }

        setTimeout(() => {
          rect.opacity = 1
          rect.height = this.defaultHeight
          if (i > 3) {
            rect.translateX = centerX - (i + 0.5) * 5 + i * 1
          } else {
            rect.translateX = centerX - (i + 0.5) * 5 // 向目标位置移动
          }
        }, i * 300)

        this.rectanglesLeft.push(rect)
      }

      for (let i = 1; i < 6; i++) {
        const rect = {
          translateX: centerX,
          opacity: 0
        }

        setTimeout(() => {
          rect.opacity = 1
          rect.height = this.defaultHeight
          if (i > 3) {
            rect.translateX = centerX + (i + 0.5) * 5 - i * 1
          } else {
            rect.translateX = centerX + (i + 0.5) * 5 // 向目标位置移动
          }
        }, i * 300)

        this.rectanglesRight.push(rect)
      }

      // 延迟执行的函数，实现矩形高度减半动画
      setTimeout(() => {
        this.rectanglesLeft.forEach((rect, index) => {
          if (index != this.rectanglesLeft.length - 1) {
            this.$set(this.rectanglesLeft, index, {
              ...rect,
              height: rect.height * 0.6
            })
          }
        })
        this.rectanglesRight.forEach((rect, index) => {
          if (index !== 0) {
            this.$set(this.rectanglesRight, index, {
              ...rect,
              height: rect.height * 0.6
            })
          }
        })
      }, 3000)

      // 透明度为0
      setTimeout(() => {
        this.rectanglesLeft.forEach((rect, index) => {
          this.$set(this.rectanglesLeft, index, {
            ...rect,
            opacity: 0
          })
        })
        this.rectanglesRight.forEach((rect, index) => {
          this.$set(this.rectanglesRight, index, {
            ...rect,
            opacity: 0
          })
        })
        this.isShowTitle = true
        setTimeout(() => {
          this.isShowSlogan = true
        }, 2000)

        this.isShowSearchBox = true
      }, 3800)

      // 隐藏slogan，搜索框出现

      setTimeout(() => {
        this.isShowSlogan = false
      }, 8000) // 在淡入完成后2秒触发淡出动画
      setTimeout(() => {
        this.keyword = ' '
      }, 11000) // 在淡入完成后2秒触发淡出动画

      // 移动标题搜索框、修改标题大小&间距
      setTimeout(() => {
        const header = document.querySelector('.header')
        header.style.transform = 'initial'
        header.style.top = '5px'
        header.style.left = '20px'

        const doors = document.querySelectorAll('.door')
        doors.forEach((door) => {
          door.style.width = '24px'
        })

        const leftPanels = document.querySelectorAll('.left-panel')
        leftPanels.forEach((leftPanel) => {
          leftPanel.style.fontSize = '43px'
        })

        // let searchBox = document.querySelector(".search-box-input-splash");

        // searchBox.style.top = "0";
        // searchBox.style.position = "absolute";
        // searchBox.style.left = "50%";
        // searchBox.style.transform = "translateX(-50%)";
      }, 12000)

      setTimeout(() => {
        this.isShowFade = true
        const brain = document.querySelector('#brain')
        brain.style.transform = 'translateX(-151px)'
        brain.style.opacity = '0.6'
        const bookor = document.querySelector('#bookor')
      }, 12500)

      setTimeout(() => {
        sessionStorage.setItem('finishedSplash', 'true')
        this.$store.commit('SETISSHOWSPLASH', false)
      }, 13000)
    },

    handleResize () {
      this.isMobile = window.innerWidth < 768 // 这里只是读取 isMobile 的值
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.rectBox {
  display: flex;
  .rect-group-left {
    flex-direction: row-reverse;
  }

  .rect-group {
    display: flex;
    align-items: baseline;
  }

  .rect {
    width: 12px;
    background-color: #8faadc;
    margin-right: 2px;
    transition: transform 0.5s, opacity 0.5s, height 0.5s;
  }
}

.header {
  display: flex;
  position: absolute;
  top: 43%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.5s ease;
  .door {
    width: 32px;
    height: 40px;
    perspective: 1000px; /* 透视效果，用于增强 3D 效果 */
    transition: all 0.5s ease;
  }

  .left-panel {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    transform-origin: left center; /* 左侧为变换参考点 */
    transition: transform 2s ease; /* 添加过渡效果，使动画平滑 */
    animation: rotateIn 5s forwards; /* 应用动画效果 */
    font-size: 55px;
    font-weight: 600;
    color: #8faadc;
    // color: #f4f6f9;
    align-items: center;
    display: flex;
    justify-content: center;
    font-family: math;
    font-weight: bolder;
    // text-shadow: 0 0 9px rgba(106, 187, 239, 0.5);
  }
}

@keyframes rotateIn {
  0% {
    transform: rotateY(-90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

.fadeOut {
  animation: fadeOut 0.5s ease-in-out;
  animation-fill-mode: forwards;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-30px);
  }
}

.bottom-line {
  // position: relative;
  display: flex;
  .search-box-input-splash {
    transition: all 1s ease;
  }

  .search-btn-box {
    color: rgb(255, 255, 255);
    width: auto;
    border-radius: 20px;
    min-width: 25px;
    height: 25px;
    line-height: 25px;
    display: inline-block;
    position: relative;
    overflow: hidden;
    border: 3px solid #8faadc;
    background-size: 104% 104%;
    cursor: pointer;
    transition: all 1s ease;
  }

  .search-btn-box input {
    display: inline-block;
    background: 0 0;
    /* background: red; */
    border: none;
    color: #8faadc;
    padding-left: 25px;
    line-height: 25px !important;
    height: 25px;
    box-sizing: border-box;
    vertical-align: 4px;
    font-size: 16px;
    width: 25px;
    transition: all 0.5s ease-in-out;
  }

  .search-btn-box:hover input {
    display: inline-block;
    width: 325px;
    padding-right: 15px;
  }

  .search-btn-box input:not(:placeholder-shown) {
    display: inline-block;
    width: 325px;
    padding-right: 15px;
  }

  .search-btn-box:hover + .search-line {
    opacity: 0;
    height: 15px;
    transition: opacity 0.5s ease-out;
  }

  .search-line {
    height: 20px;
    width: 3px;
    background-color: #8faadc;
    position: absolute;
    left: 32px;
    bottom: -5px;
    transform: rotate(135deg);
    opacity: 1;
    transition: opacity 0.5s ease-in;
  }

  .slogan {
    color: #bc8d02;
    font-size: 28px;
    margin-left: 13px;
    margin-top: 9px;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 3s;
  }
  .fade-enter,
  .fade-leave-to {
    // transition: opacity 0.5s ease-out;
    opacity: 0;
  }
}
</style>
