<template>
  <el-tooltip content="白板圆形">
    <img class="func-img" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/cir.png" @click="onClick" />
  </el-tooltip>

</template>

<script>
export default {
  name: 'BoldMenuButton',

  props: ['isActive'],

  methods: {
    onClick (e) {
      this.$emit('click')
    }
  }
}
</script>
<style lang="scss" scoped>
.func-img{
  width: 20px;
  height: 20px;
  cursor: pointer;
  // position: relative;
  margin-left: 10px;
  margin-top: 8px;
}
</style>
