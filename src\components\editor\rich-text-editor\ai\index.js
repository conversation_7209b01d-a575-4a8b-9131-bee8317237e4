import { Bold as OriginalBold } from 'element-tiptap'
import CustomBoldMenuButton from './MenuButton'

export default class Ai extends OriginalBold {
  menuBtnView ({ isActive, commands, focus, editor }) {
    return {
      component: CustomBoldMenuButton,
      componentProps: {
        isActive: isActive.bold()
      },
      componentEvents: {
        click: (callback) => {
          const selection = window.getSelection()
          const selectedText = selection.toString().trim()
          callback(selectedText)
        }
      }
    }
  }
}
