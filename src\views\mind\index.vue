<template template>
  <div v-if="treeObj" class="mind-container">
    <Mind :treeObj="treeObj" :readonly="readonly"/>
    <el-dialog
      title="温馨提示"
      custom-class="mind-dialog"
      :visible.sync="dialogVisible"
      width="66%"
      :close-on-click-modal="false"
      :show-close="false">
      <span>感谢您之前的关注！该作者暂时关闭了该脑图的分享功能，若您仍有需要，欢迎随时联系该作者～</span>
    </el-dialog>

  </div>
</template>
<script>
import axios from "@/http";
import Mind from '@/components/Echarts/src/TreePlan/mind.vue';
import request from '@/http/request';
export default {
  data() {
    return {
      treeObj: null,
      dialogVisible: false,
      readonly: false
    }
  },
  components: {
      Mind
  },
  mounted() {
    const route = this.$route
    if(route?.query?.share) {
      this.readonly = true
    }
    const mindId = this.$route.params.id; // 获取 URL 中的 id 参数
    if(!mindId) {
      return
    }
    if(route?.query?.share) {
      axios({
        url: `/ai/mind/get?id=${mindId}`,
        method: "get",
      }).then((res) => {
        if(res.code === 200){
          const data = res.data
          if(!!data.is_share) {
            this.dialogVisible = true
          }
          this.treeObj = {
            detail: data?.detail,
            mind_map_id: mindId
          }
        }
      })
    } else {
      request('/mobile/Nokeyinterface/getMindMapContentList', {mind_map_id: mindId}, 'get').then(res => {
      if(res && res.length) {
        this.treeObj = res[0]
      } else {
        this.treeObj = {
          detail: null,
          mind_map_id: mindId
        }
      }
    })
    }

  }
}
</script>

<style lang="scss" scoped>
.mind-container{
  :deep(.mind-dialog .el-dialog__body){
    height: 500px;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.mind-dialog .el-dialog__body span){
    position: relative;
    top: -40px;
  }
}
.v-modal{
  opacity: 0.8;
}
</style>
