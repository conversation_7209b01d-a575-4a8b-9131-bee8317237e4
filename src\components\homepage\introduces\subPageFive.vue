<template>
  <div class="section">
    <div class="page-box">
        <div class="title-part1">跨文本阅读 —— 实现新阅读的路径</div>
        <div class="content-title-part1">
            <span style="color: #a78bfb">跨文本阅读</span><span
                style="color: #808080">是指基于单篇文本的主题和核心概念，发掘多篇文本之间的关联，激发以新知识单元为导向的联想，建立与已有知识建立的新逻辑关联，最终融合到个人脑的知识系统中的过程。
            </span>
        </div>
        <div class="content-box-part1">
            <div class="video-cover">
                <video ref="video" class="video-js vjs-default-skin"
                  controls autoplay>
                </video>
            </div>
            <div class="content-inside">
                <div class="content-inside-item">
                    这种主动解构重构知识的过程超越了以单一文本为知识单元的传统阅读方式。它能够将不同文本中的同类知识和观点有机地组织和联动起来，在对比不同解释中，激发读者的主动分析和思考。
                </div>
                <div class="content-inside-item">
                    这种知识的比对过程超越了简单的信息获取,
                    帮助读者转向主动自建知识系统。
                </div>
                <div class="content-inside-item">
                    跨文本阅读真正体现了新阅读的本质——在新旧<span style="color: #a78bfb">元知识</span>的连接中，扩大知识深度和广度，从而能激发创意。
                </div>
            </div>
        </div>
    </div>
</div>
</template>
<style lang="scss" scoped>
.title-part1 {
    color: white;
    font-size: 30px;
    font-weight: 600;
    text-align: center;
    margin-top: 5%;
}
.content-title-part1{
  font-size: 18px;
  font-weight: 600;
  width: 70vw;
  margin: 40px auto;
}
.content-box-part1{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  width: 1100px;
  margin: 0 auto;
  margin-top: 6%;
  video{
    margin-right: 20px;
    width: 100%;
    height: 100%
  }
}
.video-cover{
  width: 55%
}
.content-inside{
  width: 40%;
  color: #808080;
  font-size: 18px;
  font-weight: 600;
  font-style: italic;
}
@media only screen and (max-width: 420px) {
  .title-part1 {
    font-size: 26px;
  }
  .content-title-part1{
    width: 100%;
  }
  .content-box-part1{
    flex-direction: column;
    font-size: 16px;
    width: 100%;
    .content-inside{
      width: 100%;
      font-size: 16px;
    }
  }
  .video-cover{
    width: 100%;
  }
}
</style>
