import { Bold as OriginalBold } from 'element-tiptap'
import CustomBoldMenuButton from './MenuButton'

export default class Bold extends OriginalBold {
  menuBtnView ({ isActive, commands, focus, editor }) {
    return {
      component: CustomBoldMenuButton,
      componentProps: {
        isActive: isActive.bold()
      },
      componentEvents: {
        click: () => {
          const { state, dispatch } = editor.view
          const { $from, $to } = state.selection
          const doc = state.doc
          const prefix = '【2-】'
          const tr = state.tr
          let total = 0
          // Check if there's already content at the start position
          // if ($from.parentOffset === 0) {
          //   // Insert prefix directly at the start of the current node
          //   tr.insertText(prefix, $from.start(), $from.start())
          // } else {
          //   // Create a new text node with the prefix and insert it
          //   const textNode = state.schema.text(prefix)
          //   tr = tr.insert($from.start(), textNode)
          // }
          doc.nodesBetween($from.pos, $to.pos, (node, pos) => {
            if (node.isText) {
              const textNode = state.schema.text(prefix)
              // 在每个文本节点的开始处插入前缀
              tr.insert(pos + total, textNode)
              total += 4
            }
            return true // 继续遍历
          })

          dispatch(tr)
        }
      }
    }
  }
}
