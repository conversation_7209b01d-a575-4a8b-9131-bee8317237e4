<template>
  <div @click.stop class="desc-box">
    <div class="desc-content">
      {{ curTagInfo.description }}
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data () {
    return {}
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      curTagInfo: (state) => state.curTagInfo
    })
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
.desc-box {
  position: absolute;
  bottom: 267px;
  left: 101px;
  max-height: 180px;
  background-color: black;
  overflow-y: scroll;
  .desc-content {
    color: white;
    width: 300px;
    font-size: 14px;
  }
  @include no-scrollbar;
}
</style>
