<template>
  <div @click.stop></div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data () {
    return {}
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      curTagInfo: (state) => state.curTagInfo
    })
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
</style>
