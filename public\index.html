<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta
      charset="utf-8"
      http-equiv="Content-Security-Policy"
      content="upgrade-insecure-requests"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>

  <body>
    <script>
      if(!location.host.includes('localhost')) {
        !(function(c,b,d,a){c[a]||(c[a]={});c[a]={
          "pid": "es6sg3p6vl@346b81406f1b2e8",
          "endpoint": "https://es6sg3p6vl-default-cn.rum.aliyuncs.com"
        };
        with(b)with(body)with(insertBefore(createElement("script"),firstChild))setAttribute("crossorigin","",src=d)
        })(window, document, "https://es6sg3p6vl-sdk.rum.aliyuncs.com/v2/browser-sdk.js", "__rum");
      }
    </script>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
<!-- <script src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script> -->
<script type="importmap">{ "imports": { "three": "https://unpkg.com/three/build/three.module.js" }}</script>
<script
  type="text/javascript"
  src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"
></script>
<script>
  !(function (a, b, c) {
    function d(a) {
      var c = "default";
      a.self_redirect === !0
        ? (c = "true")
        : a.self_redirect === !1 && (c = "false");
      var d = b.createElement("iframe"),
        e =
          "https://open.weixin.qq.com/connect/qrconnect?appid=" +
          a.appid +
          "&scope=" +
          a.scope +
          "&redirect_uri=" +
          a.redirect_uri +
          "&state=" +
          a.state +
          "&login_type=jssdk&self_redirect=" +
          c +
          "&styletype=" +
          (a.styletype || "") +
          "&sizetype=" +
          (a.sizetype || "") +
          "&bgcolor=" +
          (a.bgcolor || "") +
          "&rst=" +
          (a.rst || "");
      (e += a.style ? "&style=" + a.style : ""),
        (e += a.href ? "&href=" + a.href : ""),
        (d.src = e),
        (d.frameBorder = "0"),
        (d.allowTransparency = "true"),
        (d.sandbox = "allow-scripts allow-top-navigation allow-same-origin"),
        (d.scrolling = "no"),
        (d.width = "300px"),
        (d.height = "400px");
      var f = b.getElementById(a.id);
      (f.innerHTML = ""), f.appendChild(d);
    }
    a.WxLogin = d;
  })(window, document);
</script>
