import { Bold as OriginalBold } from 'element-tiptap'
import CustomBoldMenuButton from './MenuButton'

export default class Create extends OriginalBold {
  menuBtnView ({ isActive, commands, focus, editor }) {
    return {
      component: CustomBoldMenuButton,
      componentProps: {
        isActive: isActive.bold()
      },
      componentEvents: {
        click: (callback) => {
          const { state, dispatch } = editor.view
          const { $from, $to } = state.selection
          const doc = state.doc

          const arr = []
          let idx = 0
          const start = $from
          const end = $to
          let lastPos = 0
          doc.nodesBetween(start.pos, end.pos, (node, pos) => {
            if (node.isText) {
              let text = node.text
              if (idx === 0) {
                let begin = 0
                if (start.parentOffset) {
                  begin = start.parentOffset
                }
                text = text.slice(begin)
              }
              arr.push(text)
              idx += 1
              lastPos = pos
            }
            return true // 继续遍历
          })
          if (idx === 1) {
            lastPos = start.pos
          }
          arr[arr.length - 1] = arr[arr.length - 1].slice(0, end.pos - lastPos)
          callback(arr, (val) => {
            const tr = state.tr
            let total = 0
            let idx = 0
            doc.nodesBetween($from.pos, $to.pos, (node, pos) => {
              if (node.isText) {
                const item = val[idx]
                if (item.type && !item.hide) {
                  let prefix = ''
                  if (item.type === 2) {
                    prefix = `【2-${val[idx].title}】`
                  }
                  if (item.type === 1) {
                    prefix = `【1-${val[idx].title}】`
                  }
                  const textNode = state.schema.text(prefix)
                  tr.insert(pos + total, textNode)
                  total += prefix.length
                }
                idx += 1
              }
              return true // 继续遍历
            })
            dispatch(tr)
          })
        }
      }
    }
  }
}
