<template>
  <div class="pdf-wrapper">
    <!-- PDF展示部分 -->
    <div class="pdf-content">
      <div class="pdf-contain">
        <pdf
          class="pdf-show"
          :src="pdfUrl"
          :page="pageNum"
          @progress="loadedRatio = $event"
          @page-loaded="pageLoaded($event)"
          @num-pages="pageTotalNum = $event"
          @error="pdfError($event)"
          @link-clicked="page = $event"
        />
      </div>
    </div>
    <!-- 按钮部分 -->
    <div class="tools">
      <button @click="prePage" class="pdf-button">上一页</button>
      <!-- 页码 -->
      <div class="page">{{ pageNum }}/{{ pageTotalNum }}</div>
      <button @click="nextPage" class="pdf-button">下一页</button>
    </div>
    <pdf
      v-for="i in pageNum"
      :key="i"
      :src="pdfUrl"
      :page="i"
      style="display: inline-block; width: 150px; height: 300px"
    ></pdf>
  </div>
</template>

<script>
import request from '@/http/request'
import pdf from 'vue-pdf'
export default {
  name: 'PDF',
  components: { pdf },
  data () {
    return {
      userinfo: localStorage.getItem('userinfo'),
      pdfUrl: '',

      pageTotalNum: 1,
      pdfUrl: '',
      pageNum: 1,
      // 加载进度
      loadedRatio: 0,
      curPageNum: 0,
      numPages: 0
    }
  },
  inject: ['book_id'],
  mounted () {
    this.getBookCover()
  },
  methods: {
    getBookCover () {
      request('/api/book/bookPreview', {
        book_id: this.book_id
      }).then((result) => {
        this.pdfUrl = result.book_document
      })
    },

    // 计算pdf页码总数
    getNumPages (url) {
      const loadingTask = pdf.createLoadingTask(url)
      loadingTask.then((pdf) => {
        this.pdfUrl = loadingTask
        this.pageTotalNum = pdf.numPages
      })
    },
    // 上一页函数，
    prePage () {
      let page = this.pageNum
      page = page > 1 ? page - 1 : this.pageTotalNum
      this.pageNum = page
    },
    // 下一页函数
    nextPage () {
      let page = this.pageNum
      page = page < this.pageTotalNum ? page + 1 : 1
      this.pageNum = page
    },
    // 页面加载回调函数，其中e为当前页数
    pageLoaded (e) {
      this.curPageNum = e
    },
    // 其他的一些回调函数。
    pdfError (error) {
      console.error(error)
    }
  }
}
</script>

<style lang="scss" scoped>
.pdf-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.pdf-wrapper span {
  width: 100%;
}
.tools {
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 999;
  margin-bottom: -3%;
  padding-top: 2%;
}
.pdf-content {
  display: flex;
  justify-content: center;
  width: 150px;
  height: 300px;

  overflow-x: hidden;
  .pdf-contain {
    width: 100%;
    max-width: 100%;
    max-height: 100%;
    overflow-x: hidden;
    .pdf-show {
      width: 100%;
    }
  }
}
.pdf-button {
  width: 240px;
  height: 60px;
  font-size: 30px;
  border-radius: 20px;
}
.pdf-button:hover {
  cursor: pointer;
  transform: scale(1.2);
}
.page {
  font-size: 48px;
  color: #fff;
}
</style>
