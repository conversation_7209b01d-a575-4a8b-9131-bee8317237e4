<template>
  <div id="monitorOffice"></div>
</template>

<script>
import { handleDocType } from './util'
export default {
  props: {
    option: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      doctype: '',
      docEditor: null // 文档编辑器实例对象
    }
  },
  mounted () {
    if (this.option.url) this.setEditor(this.option)
  },

  beforeDestroy () {
    // 销毁文档编辑器实例
    this.docEditor.destroyEditor()
  },

  watch: {
    option: {
      handler: function (n, o) {
        // 判断是否已经存在文档编辑器实例
        if (this.docEditor) {
          // 销毁文档编辑器实例
          this.docEditor.destroyEditor()
          // 清空文档编辑器实例对象
          this.docEditor = null
        }
        this.setEditor(n)
        // this.$nextTick(() => {
        //   this.$forceUpdate();
        // });
      },
      deep: true
    }
  },
  methods: {
    setEditor (option) {
      this.doctype = handleDocType(option.fileType)
      // office配置参数
      const config = {
        document: {
          fileType: option.fileType,
          key: '',
          title: option.title,
          permissions: {
            comment: false,
            download: false,
            modifyContentControl: true,
            modifyFilter: true,
            print: false,
            fillForms: true,
            edit: true,
            review: false,
            protect: false
          },
          url: option.url
        },
        documentType: this.doctype,
        editorConfig: {
          callbackUrl: option.editUrl,
          lang: 'zh',
          mode: 'edit', // 允许全部编辑
          customization: {
            commentAuthorOnly: false,
            comments: false,
            compactHeader: false,
            compactToolbar: true,
            feedback: false,
            plugins: false,
            autosave: false,
            chat: false,
            help: false,
            anonymous: {
              request: false
            },
            features: {
              spellcheck: {
                mode: false
              }
            },
            forcesave: true,
            hideRightMenu: true, // 隐藏整个右侧菜单栏
            hideRulers: true, // 隐藏标尺
            hiddenFeatures: ['ins', 'layout', 'links', 'view'] // 隐藏指定的功能按钮
          },
          user: {
            // id: ,
            name: '用户'
          },
          mode: option.model ? option.model : 'edit',
          events: {
            onDocumentStateChange: this.onDocumentStateChange.bind(this), // 绑定 this
            onDocumentSaved: () => {
              return this.onDocumentSaved()
            } // 添加输出信息
          }
        },
        width: '100%',
        height: '100%',
        token: option.token
      }
      this.docEditor = new DocsAPI.DocEditor('monitorOffice', config)
    },
    onDocumentStateChange (event) {
      // 处理文档状态改变事件
    },
    onDocumentSaved () {
      // 处理文档保存事件
    }
  }
}
</script>
