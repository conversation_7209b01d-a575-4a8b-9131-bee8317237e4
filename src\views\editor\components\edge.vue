<template>
  <el-drawer title="编辑连线" direction="rtl" :visible.sync="edgeVisible" :before-close="handleClose">
    <div class="edge-editor">
      <div class="item">
        连线名称：
        <el-input class="input" placeholder="请输入节点内容" v-model="data.label"></el-input>
      </div>
      <div class="item">
        连线颜色：
        <el-color-picker v-model="data.color"></el-color-picker>
      </div>
      <div class="item">
        连线类型：
        <el-radio-group v-model="data.category">
          <el-radio :label="0">直线</el-radio>
          <el-radio :label="1">曲线</el-radio>
        </el-radio-group>
      </div>
      <div class="item">
        连线种类：
        <el-select v-model="data.type" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div class="bottom-con">
        <el-button type="primary" size="small" @click="handleUpdate">确定</el-button>
        <el-button class="cancel" size="small" @click="handleClose">取消</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getType } from './util'
export default {
  data () {
    return {
      edgeDrawer: false,
      options: [{
        value: 1,
        label: '双箭头实线'
      }, {
        value: 2,
        label: '双箭头虚线'
      }, {
        value: 3,
        label: '单箭头实线'
      }, {
        value: 4,
        label: '单箭头虚线'
      }, {
        value: 5,
        label: '实线无箭头'
      }, {
        value: 6,
        label: '虚线无箭头'
      }],
      data: {
        label: '',
        color: '',
        type: 3,
        category: 0
      }
    }
  },
  props: ['edgeVisible', 'currentEdge'],
  watch: {
    edgeVisible (val) {
      if (val) {
        const type = getType(this.currentEdge)
        this.data = {
          id: this.currentEdge.id,
          label: this.currentEdge.getLabelAt(0)?.attrs?.label?.text || '',
          color: this.currentEdge.attr().line.stroke,
          type,
          category: this.currentEdge.connector ? 1 : 0
        }
      }
    }
  },
  methods: {
    handleUpdate () {
      this.$emit('update:edge', this.data)
    },
    handleClose () {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.edge-editor {
  padding: 0 20px;

  .item {
    display: flex;
    align-items: center;
    margin-top: 10px;
  }

  .input {
    width: 240px;
  }

  .bottom-con {
    margin-top: 30px;

    .cancel {
      margin-left: 30px;
    }
  }
}
</style>
