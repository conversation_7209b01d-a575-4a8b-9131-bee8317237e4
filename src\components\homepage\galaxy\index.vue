<template>
  <div style="position: relative">
    <canvas id="canvas"></canvas>
  </div>
</template>
<script>
// import { onMounted, ref, defineComponent, onBeforeUnmount } from "vue";
import * as THREE from 'three'
import { mapState, mapMutations } from 'vuex'

// Data and visualization
import { CompositionShader } from './shaders/CompositionShader.js'
import {
  BASE_LAYER,
  BLOOM_LAYER,
  BLOOM_PARAMS,
  OVERLAY_LAYER
} from './config/renderConfig.js'

// Rendering
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js'
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js'
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js'

import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js'
import { Galaxy } from './objects/galaxy.js'

export default {
  data () {
    return {
      state: {
        canvas: null,
        renderer: null,
        camera: null,
        scene: null,
        orbit: null,
        baseComposer: null,
        bloomComposer: null,
        overlayComposer: null,
        startX: 0, // 鼠标按下时的位置
        startScrollLeft: 0, // 鼠标按下时容器的位置（scrollLeft）
        curScrollLeft: 0, // 当前容器的位置（scrollLeft）
        scrollTimer: null // 定时器
      },

      galaxy: null
    }
  },

  methods: {
    initThree () {
      // grab canvas
      this.state.canvas = document.querySelector('#canvas')
      // scene
      this.state.scene = new THREE.Scene()
      this.state.scene.fog = new THREE.FogExp2(0xebe2db, 0.00003)

      // camera
      this.state.camera = new THREE.PerspectiveCamera(
        60,
        window.innerWidth / window.innerHeight,
        0.1,
        5000000
      )
      this.state.camera.position.set(0, 100, 100)
      this.state.camera.up.set(0, 0, 1)
      this.state.camera.lookAt(0, 0, 0)
      // map orbit
      this.state.orbit = new OrbitControls(this.state.camera, this.state.canvas)
      this.state.orbit.enableDamping = true // an animation loop is required when either damping or auto-rotation are enabled
      this.state.orbit.dampingFactor = 0.05
      this.state.orbit.screenSpacePanning = false
      this.state.orbit.minDistance = 80 // 设置最小距离
      this.state.orbit.maxDistance = 600 // 设置最大距离
      this.state.orbit.maxPolarAngle = Math.PI / 2 - Math.PI / 360
      this.initRenderPipeline()
    },

    initRenderPipeline () {
      // Assign Renderer
      this.state.renderer = new THREE.WebGLRenderer({
        antialias: true,
        canvas: this.state.canvas,
        logarithmicDepthBuffer: true,
        alpha: true
      })
      this.state.renderer.setPixelRatio(window.devicePixelRatio)
      this.state.renderer.setSize(window.innerWidth, window.innerHeight)
      this.state.renderer.outputEncoding = THREE.SRGBColorSpace
      this.state.renderer.toneMapping = THREE.ACESFilmicToneMapping
      this.state.renderer.toneMappingExposure = 0.5
      if (this.isLightMode) {
        this.state.renderer.setClearColor(0xa6a6a6, 0.4)
      }
      // General-use rendering pass for chaining
      const renderScene = new RenderPass(this.state.scene, this.state.camera)
      // Rendering pass for bloom
      const bloomPass = new UnrealBloomPass(
        new THREE.Vector2(window.innerWidth, window.innerHeight),
        1.5,
        0.4,
        0.85
      )
      bloomPass.threshold = BLOOM_PARAMS.bloomThreshold
      bloomPass.strength = BLOOM_PARAMS.bloomStrength
      bloomPass.radius = BLOOM_PARAMS.bloomRadius

      // bloom composer
      this.state.bloomComposer = new EffectComposer(this.state.renderer)
      this.state.bloomComposer.renderToScreen = false
      this.state.bloomComposer.addPass(renderScene)
      this.state.bloomComposer.addPass(bloomPass)

      // overlay composer
      this.state.overlayComposer = new EffectComposer(this.state.renderer)
      this.state.overlayComposer.renderToScreen = false
      this.state.overlayComposer.addPass(renderScene)

      // Shader pass to combine base layer, bloom, and overlay layers
      const finalPass = new ShaderPass(
        new THREE.ShaderMaterial({
          uniforms: {
            baseTexture: { value: null },
            bloomTexture: {
              value: this.state.bloomComposer.renderTarget2.texture
            },
            overlayTexture: {
              value: this.state.overlayComposer.renderTarget2.texture
            }
          },
          vertexShader: CompositionShader.vertex,
          fragmentShader: CompositionShader.fragment,
          defines: {}
        }),
        'baseTexture'
      )
      finalPass.needsSwap = true

      // base layer composer
      this.state.baseComposer = new EffectComposer(this.state.renderer)
      this.state.baseComposer.addPass(renderScene)
      this.state.baseComposer.addPass(finalPass)
    },

    resizeRendererToDisplaySize (renderer) {
      const canvas = renderer.domElement
      const width = canvas.clientWidth
      const height = canvas.clientHeight
      const needResize = canvas.width !== width || canvas.height !== height
      if (needResize) {
        renderer.setSize(width, height, false)
      }
      return needResize
    },

    renderPipeline () {
      // Render bloom
      this.state.camera.layers.set(BLOOM_LAYER)
      this.state.bloomComposer.render()

      // Render overlays
      this.state.camera.layers.set(OVERLAY_LAYER)
      this.state.overlayComposer.render()

      // Render normal
      this.state.camera.layers.set(BASE_LAYER)
      this.state.baseComposer.render()
    },

    async render () {
      this.state.orbit.update()

      // fix buffer size
      if (this.resizeRendererToDisplaySize(this.state.renderer)) {
        const canvas = this.state.renderer.domElement
        this.state.camera.aspect = canvas.clientWidth / canvas.clientHeight
        this.state.camera.updateProjectionMatrix()
      }

      // fix aspect ratio
      const canvas = this.state.renderer.domElement
      this.state.camera.aspect = canvas.clientWidth / canvas.clientHeight
      this.state.camera.updateProjectionMatrix()

      this.galaxy.updateScale(this.state.camera)
      // Run each pass of the render pipeline
      this.renderPipeline()

      requestAnimationFrame(this.render)
    },

    // 点击事件处理函数
    onClick () {
      clearInterval(this.moveCameraInterval)
    }
  },
  mounted () {
    this.initThree()

    this.galaxy = new Galaxy(this.state.scene)
    requestAnimationFrame(this.render)

    // move camera animation
    const duration = 4000
    const startPosition = new THREE.Vector3(0, 0, -700) // 初始位置
    const endPosition = this.state.camera.position.clone() // 目标位置
    let currentTime = 0

    this.moveCameraInterval = setInterval(() => {
      currentTime += 16
      const progress = currentTime / duration
      this.state.camera.position.lerpVectors(
        startPosition,
        endPosition,
        progress
      )
      this.state.camera.lookAt(0, 0, 0)
      if (progress >= 1) {
        clearInterval(this.moveCameraInterval)
      }
    }, 16)

    this.state.canvas.addEventListener('click', this.onClick, false)
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
html,
body {
  margin: 0;
  height: 100%;
}

#canvas {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
