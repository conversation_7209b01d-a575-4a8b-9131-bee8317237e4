<template>
    <div style="overflow-y: scroll; height: 100vh" class="introduce-box">

        <div>
            <Transition>
                <!-- 第一页 “人机一体”的新阅读 wheel滚轮事件-->
                <div class="section" v-if="curSubPage == 1" @wheel="oneSectionScrollWheel">
                    <div class="page-box">
                        <div class="title-part1" style="color: #a78bfb">
                            “人机一体”的新阅读
                        </div>
                        <div class="content-box-part1">
                            <div class="content-title-part1" style="text-align: center">
                                借助“外脑”，实现三个层次的思维跃升
                            </div>

                            <div class="transition_box">
                                <div class="transition_item" v-if="onePage.isShow" :class="!onePage.isShow ? 'opa0' : ''"
                                    style="
                  margin-top: 5%;
                  margin-left: 30%;
                  margin-right: 30%;
                  color: #747576;
                  font-size: 20px;
                  font-weight: 600;
                ">
                                    <span style="color: #40dbff; font-size: 25px">“外脑”</span>

                                    <span>肩负储存元知识信息的电脑系统，让人脑只专注于以元知识之间的连接的创新思维活动(联想、洞见和创造)。人机一体化目的让人高效建立起系统化知识体系。</span>
                                </div>

                                <div class="content-inside transition_item" :class="onePage.isShow ? 'opa0' : ''" style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  flex-wrap: wrap;
                  width: 900px;
                  margin: 0 auto;
                  margin-top: 6%;
                ">
                                    <div class="content-inside-item" style="width: 33.3%;">

                                        <span class="content-sub-title-part1"
                                            style="color: white; font-size: 25px; ">联想</span>
                                        <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 10px;
                    ">
                                            关联不同观点和知识
                                        </div>
                                    </div>
                                    <div class="content-inside-item" style="width: 33.3%;">
                                        <span class="content-sub-title-part1"
                                            style="color: white; font-size: 25px; ">洞见</span>
                                        <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 10px;
                    ">
                                            在关联中深入洞察新见解
                                        </div>
                                    </div>
                                    <div class="content-inside-item" style="width: 33.3%;">
                                        <span class="content-sub-title-part1"
                                            style="color: white; font-size: 25px; ">创见</span>
                                        <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 10px;
                    ">
                                            激发创造性思维
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- 第二页 阅读的误区-->
                <div class="section" v-if="curSubPage == 2">
                    <div class="page-box">
                        <div class="title-part1">阅读的误区</div>
                        <div class="content-box-part1">
                            <div class="content-title-part1"></div>
                            <div class="content-inside" style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  flex-wrap: wrap;
                  width: 1100px;
                  margin-left: 20%;
                  margin-right: 15%;
                  margin-top: 8%;
                ">
                                <div class="content-inside-item" style="width: 50%; text-align: left;">
                                    <div>
                                        <span class="blue-dot" style="color: #a78bfb">·</span>
                                        <span class="content-sub-title-part1"
                                            style="color: #a78bfb; font-weight: 600">误区一：</span><span>阅读多少，就能记多少</span>
                                    </div>
                                    <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 5px;
                    ">
                                        人脑就是信息储存器，可以无限存储。
                                    </div>
                                </div>
                                <div class="content-inside-item" style="width: 50%; text-align: left;">
                                    <div>
                                        <span class="blue-dot" style="color: #a78bfb">·</span>
                                        <span class="content-sub-title-part1"
                                            style="color: #a78bfb; font-weight: 600">误区三：</span><span>阅读就是按文章顺序逐字“扫描”的过程</span>
                                    </div>
                                    <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 5px;
                    ">
                                        按照作者的行文逻辑，从头读到尾，才能完整理解作者意图。
                                    </div>
                                </div>
                                <div class="content-inside-item" style="width: 50%; text-align: left;">
                                    <div>
                                        <span class="blue-dot" style="color: #a78bfb">·</span>
                                        <span class="content-sub-title-part1"
                                            style="color: #a78bfb; font-weight: 600">误区二：</span><span>眼睛“扫”得快，阅读就快</span>
                                    </div>
                                    <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 5px;
                    ">
                                        只要刻意练习，阅读速度就能提高，甚至“一目十行”。
                                    </div>
                                </div>
                                <div class="content-inside-item" style="width: 50%; text-align: left;">
                                    <div>
                                        <span class="blue-dot" style="color: #a78bfb">·</span>
                                        <span class="content-sub-title-part1" style="color: #a78bfb; font-weight: 600">误区四：
                                        </span>
                                        <span>作者天然正确</span>
                                    </div>
                                    <div style="
                                    width: 100%;
                                    color: #68696a;
                                    font-weight: 600;
                                    font-size: 17px;
                                    margin-top: 5px;
                                    ">
                                        预设作者的观点天然正确，不主动甄别，难以形成独立思考能力。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第三页 传统阅读方式的弊端s-->
                <div class="section" v-if="curSubPage == 3">
                    <div class="page-box">
                        <div class="title-part1">传统阅读方式的弊端</div>
                        <div class="content-box-part1" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                width: 900px;
                margin:  0 auto;
                margin-top: 6%;
              ">
                            <!-- <div class="content-title-part1" ></div> -->
                            <div class="content-inside content_box" style="width: 50%">
                                <div class="content-inside-item" @click="threePage.currentNum = 1"
                                    :class="threePage.currentNum == 1 ? 'item_box' : ''"
                                    style="font-size: 18px; font-weight: 500; text-align: justify">
                                    <div style="color: #fff" class="item_title">阅读被动</div>
                                    <div class="item">
                                        传统阅读以读懂作者，理解其思想为目的。读者已预设自己被动“受教”的地位。并不能为读者自动带来客观批判立场。
                                    </div>
                                </div>
                                <div class="content-inside-item" @click="threePage.currentNum = 2"
                                    :class="threePage.currentNum == 2 ? 'item_box' : ''"
                                    style="font-size: 18px; font-weight: 500; text-align: justify">
                                    <div style="color: #fff; margin-top: 5px" class="item_title">难持续专注</div>
                                    <div class="item">
                                        传统方式要求有相对完整、有规律的阅读时间。很难利用是生活中的碎片时间持续阅读。
                                    </div>
                                </div>
                                <div class="content-inside-item" @click="threePage.currentNum = 3"
                                    :class="threePage.currentNum == 3 ? 'item_box' : ''"
                                    style="font-size: 18px; font-weight: 500; text-align: justify">
                                    <div style="color: #fff; margin-top: 5px" class="item_title">
                                        知识缺横向关联
                                    </div>
                                    <div class="item">
                                        传统阅读以完整的文本为单位，缺乏和其它文本的关联和主动比较机制，知识全局观难以形成。
                                    </div>
                                </div>
                                <div class="content-inside-item" @click="threePage.currentNum = 4"
                                    :class="threePage.currentNum == 4 ? 'item_box' : ''"
                                    style="font-size: 18px; font-weight: 500; text-align: justify">
                                    <div style="color: #fff; margin-top: 5px" class="item_title">
                                        逻辑分析能力要求高
                                    </div>
                                    <div class="item">
                                        快速了解文本的结构和作者的思想框架依赖读者较强的综合分析能力，这在快节奏高密度信息化条件下，越来越难做到。
                                    </div>
                                </div>
                                .
                            </div>
                            <div style="width: 50%">
                                <img v-if="threePage.currentNum == 2" src="./img/困死.png"
                                    style="margin-left: 60px; width: 30vw" />
                                <img v-if="threePage.currentNum == 1" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/%E8%AE%B0%E4%B8%8D%E4%BD%8F.4b28ce07.png"
                                    style="margin-left: 60px; width: 30vw" />
                                <img v-if="threePage.currentNum == 3" src="./img/连接.png"
                                    style="margin-left: 60px; width: 30vw" />
                                <img v-if="threePage.currentNum == 4" src="./img/逻辑思维.png"
                                    style="margin-left: 60px; width: 30vw" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第四页 拥有系统知识的4个标准-->
                <div class="section" v-if="curSubPage == 4">
                    <div class="page-box">
                        <div class="title-part1" style="text-align: center;">
                            拥有系统知识的4个标准
                        </div>
                        <div class="content-box-part1" style="
                display: flex;
                justify-content: space-between;
                /* align-items: center; */
                flex-wrap: wrap;
                width: 1000px;
                margin:  0 auto;
                margin-top: 6%;
              ">
                            <!-- <div class="content-title-part1"></div> -->
                            <div class="content-inside" style="width: 30%;margin-top: -10px;">
                                <div class="content-inside-item"
                                    style="color: #ffc000; font-size: 23px; font-weight: 600; text-align: left;">
                                    ➢ 掌握该专题的基本概念
                                </div>
                                <div class="content-inside-item"
                                    style="color: #ffc000; font-size: 23px; font-weight: 600;text-align: left;">
                                    ➢ 阅读过专题知识的必读文本
                                </div>
                                <div class="content-inside-item"
                                    style="color: #ffc000; font-size: 23px; font-weight: 600;text-align: left;">
                                    ➢ 理解关键概念之间的逻辑和推导关系
                                </div>
                                <div class="content-inside-item"
                                    style="color: #ffc000; font-size: 23px; font-weight: 600;text-align: left;">
                                    ➢ 能就核心概念提问并表达创新想法
                                </div>
                            </div>
                            <div style="width: 70%">
                                <video ref="video" class="video-js vjs-default-skin"
                                    style="margin-left: 10px; width: 100%; height: 100%" controls autoplay>
                                    <!-- <source src="./video/视频1.mp4" /> -->
                                </video>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第五页 跨文本阅读 -->
                <div class="section" v-if="curSubPage == 5">
                    <div class="page-box">
                        <div class="title-part1">跨文本阅读 —— 实现新阅读的路径</div>
                        <div class="content-title-part1" style="
                margin-left: 24%;
                margin-right: 24%;
                margin-top: 1%;
                font-size: 16px;
                font-weight: 600;
              ">
                            <span style="color: #a78bfb">跨文本阅读</span><span
                                style="color: #808080">是指基于单篇文本的主题和核心概念，发掘多篇文本之间的关联，激发以新知识单元为导向的联想，建立与已有知识建立的新逻辑关联，最终融合到个人脑的知识系统中的过程。
                            </span>
                        </div>
                        <div class="content-box-part1" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                width: 1100px;
                margin: 0 auto;
                margin-top: 6%;
              ">
                            <!-- <div class="content-title-part1"></div> -->
                            <div style="width: 55%">
                                <video ref="video" class="video-js vjs-default-skin"
                                    style="margin-right: 20px; width: 100%; height: 100%" controls autoplay>
                                    <!-- <source src="./video/视频1.mp4" /> -->
                                </video>
                            </div>
                            <div class="content-inside" style="
                  width: 40%;
                  color: #808080;
                  font-size: 18px;
                  font-weight: 600;
                  font-style: italic;
                ">
                                <div class="content-inside-item">
                                    这种主动解构重构知识的过程超越了以单一文本为知识单元的传统阅读方式。它能够将不同文本中的同类知识和观点有机地组织和联动起来，在对比不同解释中，激发读者的主动分析和思考。
                                </div>
                                <div class="content-inside-item">
                                    这种知识的比对过程超越了简单的信息获取,
                                    帮助读者转向主动自建知识系统。
                                </div>
                                <div class="content-inside-item">
                                    跨文本阅读真正体现了新阅读的本质——在新旧<span style="color: #a78bfb">元知识</span>的连接中，扩大知识深度和广度，从而能激发创意。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第六页 什么是元知识 -->
                <div class="section" v-if="curSubPage == 6">
                    <div class="page-box">
                        <div class="title-part1">什么是元知识</div>

                        <div class="content-box-part1">
                            <div class="content-title-part1"
                                style="margin-left: 24%; margin-right: 24%; text-align: center;">
                                <span
                                    style="color: #a78bfb">元知识</span><span>——原著的最小知识单元，是反映作者思想和原创性的载体。元知识之间严密的推导关系和逻辑联系能支撑作者的整个思想架构。包括以下四个方面：
                                </span>
                            </div>
                            <div class="content-inside" style="
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    flex-wrap: wrap;
                                    width: 1000px;
                                    margin: 6% auto
                ">
                                <div class="content-inside-item" style="width: 18%">
                                    <span class="content-sub-title-part1"
                                        style="color: #40dbff; font-size: 25px; ">核心概念</span>
                                    <div style="
                      color: white;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 10px;
                      text-align: left;
                    ">
                                        论述所涉及到的专业概念、理论及主张。它构成全书的框架和主旨。
                                    </div>
                                </div>
                                <div class="content-inside-item" style="width: 18%">
                                    <span class="content-sub-title-part1" style="color: #40dbff; font-size: 25px; ">理论起承
                                    </span>
                                    <div style="
                      color: white;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 10px;
                      text-align: left;
                    ">
                                        作者的思想起源和发展脉络，涉及其他理论、概念或理论。
                                    </div>
                                </div>
                                <div class="content-inside-item" style="width: 18%">
                                    <span class="content-sub-title-part1" style="color: #40dbff; font-size: 25px; ">证据与论据
                                    </span>
                                    <div style="
                      color: white;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 10px;
                      text-align: left;
                    ">
                                        作者为支撑观点或论证过程而采用的数据、案例和信息等。
                                    </div>
                                </div>
                                <div class="content-inside-item" style="width: 18%">
                                    <span class="content-sub-title-part1" style="color: #40dbff; font-size: 25px; ">结论主张
                                    </span>
                                    <div style="
                      color: white;
                      font-weight: 600;
                      font-size: 17px;
                      margin-top: 10px;
                      text-align: left;
                    ">
                                        作者的结论性意见。能代表其立场和对所探讨问题的最终看法。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第七页 Bookor Brain系统的理论依据 -->
                <div class="section" v-if="curSubPage == 7">
                    <div class="page-box">
                        <div class="title-part1">Bookor Brain系统的理论依据</div>
                        <div class="content-box-part1" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                width: 900px;
                margin: 0 auto;
                margin-top: 6%;
              ">
                            <!-- <div class="content-title-part1" ></div> -->
                            <div class="content-inside" style="width: 40%">
                                <div class="content-inside-item"
                                    style="font-size: 18px; font-weight: 500; text-align: justify">
                                    <span class="blue-dot" style="color: #40dbff">·</span>
                                    <span
                                        style="color: #40dbff">大脑的认知机制论</span><span>——所依据或提及的其他理论、概念或学说。这有助于理解作者的思想起源和发展脉络。
                                    </span>
                                </div>
                                <div class="content-inside-item"
                                    style="font-size: 18px; font-weight: 500; text-align: justify">
                                    <span class="blue-dot" style="color: #40dbff">·</span>
                                    <span
                                        style="color: #40dbff; margin-top: 5px">跨文本阅读如何促进大脑认知神经网络的优化呢？</span><span>——作者为支撑观点或论证过程而采用的例子、数据、案例和信息等证据。它反映出作者获取和使用知识的方式。
                                    </span>
                                </div>
                                <div class="content-inside-item"
                                    style="font-size: 18px; font-weight: 500; text-align: justify">
                                    <span class="blue-dot" style="color: #40dbff">·</span>
                                    <span
                                        style="color: #40dbff; margin-top: 5px">BookorBrain系统如何体现大脑的认知机制？</span><span>——作者最终想要传达给读者的结论性意见。这代表了作者的立场和对所探讨问题的最终看法。
                                    </span>
                                </div>
                            </div>
                            <div style="width: 60%">
                                <video ref="video" class="video-js vjs-default-skin"
                                    style="margin-left: 100px; width: 100%; height: 100%" controls autoplay>
                                    <!-- <source src="./video/视频1.mp4" /> -->
                                </video>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第八页 实现学习的良性循环 -->
                <div class="section" v-if="curSubPage == 8">
                    <div class="page-box">
                        <div class="title-part1">实现学习的良性循环</div>
                        <div class="arc-box" style="margin-top: 20%; margin-left: 48%">
                            <div class="arc-container">
                                <img src="./img/arc.png" alt="" class="arcarrow" />
                            </div>
                            <div style="top: 31%; left: 90%" class="arc-title">元知识</div>
                            <div style="top: 31%; left: 11%" class="arc-title">文本</div>
                            <div style="top: 83%; left: 48%" class="arc-title">高效工具</div>
                            <div style="top: 16%; left: 49%" class="arc-content">总结提炼</div>
                            <div style="top: 57%; left: 31%" class="arc-content">加速阅读</div>
                            <div style="top: 58%; left: 77%" class="arc-content">共享</div>
                        </div>
                    </div>
                </div>

                <!-- 第九页 “新阅读”用户享有的优质服务 -->
                <div class="section" v-if="curSubPage >= 9 && !isShowEmail">
                    <div class="page-box">
                        <div class="title-part1">“新阅读”用户享有的优质服务</div>
                        <div class="content-box-part1">
                            <div class="content-inside" style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  flex-wrap: wrap;
                  width: 1000px;
                  margin: 6% auto
                ">
                                <div class="content-inside-item"
                                    style="width: 23%; display: flex; flex-direction: column; align-items: center;">
                                    <div style="
                      width: 30%;
                      height: 0;
                      padding-bottom: 30%;
                      background: #005878;
                      border-radius: 10px;
                    ">
                                        <img src="./img/pdf doc.png"
                                            style="width: 200%; margin-left: -53%; margin-top: -40%" />
                                    </div>
                                    <div class="content-sub-title-part1" style="
                      color: #a78bfb;
                      font-size: 22px;
                      margin-top: 10%;
                    ">
                                        多文本编辑下载权
                                    </div>
                                    <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 15px;
                      margin-top: 10px;
                    ">
                                        PDF/Doc/Epub 跨格式文本摘抄、编辑和下载权限
                                    </div>
                                </div>
                                <div class="content-inside-item"
                                    style="width: 22%; display: flex; flex-direction: column; align-items: center;">
                                    <div style="
                      width: 30%;
                      height: 0;
                      padding-bottom: 30%;
                      background: #005878;
                      border-radius: 10px;
                    ">
                                        <img src="./img/doctor.png"
                                            style="width: 180%; margin-left: -30%; margin-top: -30%" />
                                    </div>
                                    <div class="content-sub-title-part1" style="
                      color: #a78bfb;
                      font-size: 22px;
                      margin-top: 10%;
                    ">
                                        “解读专家”称号
                                    </div>
                                    <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 15px;
                      margin-top: 10px;
                    ">
                                        该称号与图书永久绑定，扩大影响力，享有流量扶持
                                    </div>
                                </div>
                                <div class="content-inside-item"
                                    style="width: 22%;display: flex; flex-direction: column; align-items: center;">
                                    <div style="
                      width: 30%;
                      height: 0;
                      padding-bottom: 30%;
                      background: #005878;
                      border-radius: 10px;
                    ">
                                        <img src="./img/佣金.png" style="width: 85%; margin-left: 10%; margin-top: 10%" />
                                    </div>
                                    <div class="content-sub-title-part1" style="
                      color: #a78bfb;
                      font-size: 22px;
                      margin-top: 10%;
                    ">
                                        分享赚佣金
                                    </div>
                                    <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 15px;
                      margin-top: 10px;
                    ">
                                        分享到微信的已上架的图书链接，享有分销佣金
                                    </div>
                                </div>
                                <div class="content-inside-item"
                                    style="width: 22%;display: flex; flex-direction: column; align-items: center;">
                                    <div style="
                      width: 30%;
                      height: 0;
                      padding-bottom: 30%;
                      background: #005878;
                      border-radius: 10px;
                    ">
                                        <img src="./img/ai doctor.png" style="width: 100%" />
                                    </div>
                                    <div class="content-sub-title-part1" style="
                      color: #a78bfb;
                      font-size: 22px;
                      margin-top: 10%;
                    ">
                                        AI知识顾问
                                    </div>
                                    <div style="
                      color: #68696a;
                      font-weight: 600;
                      font-size: 15px;
                      margin-top: 10px;
                    ">
                                        享有ChatGPT AI的知识解答，文本修改的权力
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bottom-section">
                            <div style="z-index: 1; margin-top: 10%">
                                <input type="text" placeholder="输入邮箱地址" v-model="email" @keyup.enter="handleEnter"
                                    class="email-box" />
                            </div>
                        </div>
                    </div>
                </div>

                <email v-if="isShowEmail" :back="setCurPage" :email="email"></email>
            </Transition>
        </div>
        <div class="down-arrow" @click="handleMouseWheel" v-if="isShowArrow">
            <svg id="more-arrows">
                <polygon class="arrow-top" points="37.6,27.9 1.8,1.3 3.3,0 37.6,25.3 71.9,0 73.7,1.3 " />
                <polygon class="arrow-middle" points="37.6,45.8 0.8,18.7 4.4,16.4 37.6,41.2 71.2,16.4 74.5,18.7 " />
                <polygon class="arrow-bottom" points="37.6,64 0,36.1 5.1,32.8 37.6,56.8 70.4,32.8 75.5,36.1 " />
            </svg>
        </div>
    </div>
</template>
<script>
import CommitInfo from '@/components/homepage/commit-info.vue';
import Video from '@/components/public/Video.vue';
import email from '@/components/splash-screen/email.vue';
export default {
  props: {
    setCurPage: {
      type: Function
    },
    setcurrentPage: {
      type: Function
    },
    curSubPage: {
      type: Number
    },
    onePage: {
      type: Object
    },
    threePage: {
      type: Object
    }

  },

  data () {
    return {
      // homePage: '',
      // 点击箭头的显示隐藏
      isShowArrow: true,
      isShowEmail: false,
      src: 'http://oss.bookor.com.cn/uploads/20221013/69c1ae538aba5daf90c9a494c1b98deb.mp4',

      fadeInElements: [],

      email: '', // 邮箱
      name: '', // 名字
      // 专业领域
      profession_area: '',
      profession_area_options: [
        { value: 1, text: '学生' },
        { value: 2, text: '研究人员' },
        { value: 3, text: '职场从业者' },
        { value: 4, text: '自雇及知识博主' },
        { value: 5, text: '企业主' },
        { value: 6, text: '其他' }
      ],
      // 阅读习惯
      read_habit: '',
      read_habit_options: [
        { value: 1, text: '按顺序逐页阅读' },
        { value: 2, text: '随机选读有兴趣的章节' },
        { value: 3, text: '快读先了解文章结构' },
        { value: 4, text: '只关注有用的知识点' }
      ],
      // 是否做读书笔记
      is_note: '',
      is_note_options: [
        { value: 1, text: '每次阅读都做笔记' },
        { value: 2, text: '重要的会做笔记' },
        { value: 3, text: '偶尔随手记录' },
        { value: 4, text: '从不做笔记' }
      ],
      // 笔记工具
      note_tools: '',
      note_tools_options: [
        { value: 1, text: '书页空白处手写记录' },
        { value: 2, text: '纸质笔记本' },
        { value: 3, text: '电脑笔记本工具' },
        { value: 4, text: '手机记录工具' }
      ],
      noteToolsShow: false

      // 第一页翻页配置
      // onePage: {
      //     isShow: true,
      //     currentNum: 0,

      //     throttleBoo: true
      // },

      // 第三页翻页配置
      // threePage: {
      //     currentNum: 1
      // }
    }
  },
  components: {
    Video,
    CommitInfo,
    email
  },
  mounted () {
  },

  methods: {
    oneSectionScrollWheel (event) {
      if (this.onePage.currentNum <= 1) {
        event.preventDefault()

        if (this.onePage.throttleBoo) {
          this.onePage.throttleBoo = false

          if (this.onePage.isShow) {
            this.onePage.isShow = false
          }
          this.onePage.currentNum++
          this.onePage.throttleBoo = true
        }
      }
    },

    beforeChange (prev, next) {
      // 默认锁定
      if (next === 2 && this.isLock) return false
    },
    afterChange (prev, next) {
    },
    noteToolsChange () {
      if (this.is_note == 4) {
        this.noteToolsShow = false
      } else {
        this.noteToolsShow = true
      }
    },
    // 滚动的逻辑改为了点击
    handleMouseWheel (event) {
      if (this.curSubPage == 3) {
        if (this.threePage.currentNum < 4) {
          this.threePage.currentNum++
          return
        }
      }
      if (this.onePage.currentNum >= 1) {
        this.setcurrentPage(true) // 鼠标向下滚动时，页数增加
        // } else if (event.deltaY < 0 && this.curSubPage > 1) {
      } else if (event.deltaY < 0 && this.curSubPage > 1) {
        this.setcurrentPage(false) // 鼠标向上滚动时，页数减少，但不会小于1
        if (this.curSubPage == 1) {
          this.$emit('toPrevPage')
        }
      }

      if (this.curSubPage == 1) {
        this.oneSectionScrollWheel(event)
        return
      }
      // // 箭头判断显示与隐藏
      if (this.curSubPage >= 9) {
        this.isShowArrow = false
      }
    },

    // 向上滚动

    handleEnter () {
      const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/
      if (regex.test(this.email)) {
        this.curSubPage++
        this.isShowEmail = true
      } else {
        this.$message.error({
          message: '请输入正确的邮箱地址',
          duration: 1500
        })
        this.email = ''
      }
    },

    handleRefresh () {
      this.curSubPage = 10
      setTimeout(() => {
        this.$router.push('/index')
      }, 2000)
    }

  }
}
</script>

<style lang="scss" scoped>
.v-enter-active,
.v-leave-active {
    opacity: 1;
    transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
    opacity: 0;
}

.content_box {
    .content-inside-item {
        cursor: pointer;
        position: relative;
    }

    .content-inside-item::before {
        content: '';
        position: absolute;
        left: -10px;
        width: 5px;
        height: 100%;
        background-color: transparent;
    }

    .item_box::before {
        background-color: #a583f1 !important;
    }
}

.item_box {
    .item_title {
        color: #a583f1 !important;
    }

    .item {
        color: #fff !important;
    }
}

div.content_box.content-inside {
    color: #ccc;
}

.transition_item {
    opacity: 1;
    max-height: 100px;
    overflow: hidden;
    transition: all 2s ease;
}

.transition_item.opa0 {
    overflow: hidden;
    transition: all 2s ease;
    opacity: 0;
    max-height: 0;
}

.out-box {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100vw;
    height: 100vh;
}

.title-part1 {
    color: white;
    font-size: 30px;
    font-weight: 600;
    text-align: center;
    margin-top: 5%;
    // position: absolute;
    // left: 70px;
    // top: 12%;
}

.content-box-part1 {
    margin-top: 10px;

    .content-title-part1 {
        color: #68696a;
        font-weight: 600;
        font-size: 17px;
    }

    .content-inside {
        font-size: 18px;
        margin-top: 16px;
        color: white;

        .content-inside-item {
            margin-top: 10px;
            text-align: center;
            opacity: 1;
            // transition: all 0.5s ease;
        }

        .opa0 {
            transition: all 0.5s ease;
            opacity: 0;
        }

        .blue-dot {
            color: #40dbff;
            margin-right: 17px;
        }

        .content-sub-title-part1 {
            color: #37dbff;
        }
    }
}

.content-add-top {
    position: absolute;
    left: 41%;
    top: 6%;
    width: 500px;
    color: white;
    font-size: 18px;
}

// part2样式
.title-part2 {
    color: white;
    font-size: 21px;
    // position: absolute;
    top: 21%;
    left: 5%;
}

.content-box-part2 {
    color: #ffc001;
    font-size: 18px;
    margin-left: 15px;

    // position: absolute;
    .content-item {
        .arc-arrow {
            font-size: 20px;
            margin-right: 20px;
        }

        margin: 13px 0;
    }
}

.arc-box {
    width: 400px;
    height: 400px;
    // position: absolute;
    margin: 0 45vw;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .arc-container {
        // position: absolute;
        // top: 35%;
        // left: 50%;
        // transform: translate(-50%, -35%);
    }

    .arcarrow {
        // position: absolute;
        // top: 50%;
        // left: 50%;
        width: 450px;
        // transform: translate(-50%, -35%);
    }

    // .arcarrow:nth-child(1) {
    //   transform-origin: bottom center;
    //   transform: translate(-22%, 15%) rotate(120deg);
    // }

    // .arcarrow:nth-child(2) {
    //   transform-origin: bottom center;
    //   transform: translate(-70%, 18%) rotate(240deg);
    // }

    // .arcarrow:nth-child(3) {
    //   transform-origin: bottom center;
    //   transform: translate(-50%, -50%) rotate(360deg);
    // }
    .arc-title {
        color: white;
        font-size: 23px;
        position: absolute;
        width: 100px;
    }
    .bottom-section{
      .input{
        margin-top: 10%;
      }
    }

    .arc-content {
        color: #d9d9d9;
        font-size: 18px;
        position: absolute;
        width: 40px;
    }
}

.success-tips {
    text-align: center;
    //position: absolute;
    // left: 40%;
    color: #ffc000;
    margin-top: 20%;
    font-size: 18px;
}

.fade-enter-active,
.fade-leave-active {
    transition: all 3s ease-in-out;
    // transition: opacity 5s;
}

.fade-enter,
.fade-leave-to {
    transition: all 0.5s ease-out;
    // opacity: 0;
}

.introduce-box {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        // display: none;
    }

    // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
    &::-webkit-scrollbar-button {
        display: none;
    }

    // 滚动条的轨道（里面装有Thumb）
    &::-webkit-scrollbar-track {
        background: transparent;
    }

    // 滚动条的轨道（里面装有Thumb）
    &::-webkit-scrollbar-track-piece {
        background-color: transparent;
    }

    // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
    &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        cursor: pointer;
        border-radius: 4px;
    }

    // 边角，即两个滚动条的交汇处
    &::-webkit-scrollbar-corner {
        display: none;
    }

    // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
    &::-webkit-resizer {
        display: none;
    }
}

.page-box {
    width: 99vw;
    height: 100vh;
    // display: inline-block;
    padding: 20px;
    box-sizing: border-box;
    // transition: 0.3s all ease-out;
    // opacity: 0;
    // transform: scale(0.8);
    // transform: translate(-50%, -50%);
    // left: 50%;
    // top: 50%;
}

.info-box {

    // background-color: red;
    ::placeholder {
        color: #0f0f0f;
        // color: brown;

    }
}

.down-arrow {
    // color: white;
    position: absolute;
    top: 90%;
    left: 50%;
    transform: translate(-50%, -50%);
    // // width: 200px;
    // // height: 40px;
    // text-align: center;
    // line-height: 40px;

    /* Arrow & Hover Animation */
    #more-arrows {
        width: 75px;
        height: 65px;
        transform: scale(0.4);
    }

    @keyframes arrow-animation {
        0% {
            fill: #d9dadb;
            opacity: 0.5;
        }

        33.33% {
            fill: #d9dadb;
            opacity: 0.75;
        }

        66.66% {
            fill: #d9dadb;
            opacity: 1;
        }

        100% {
            fill: #d9dadb;
            opacity: 0.75;
        }
    }

    polygon.arrow-top {
        animation: arrow-animation 1s linear infinite;
    }

    polygon.arrow-middle {
        animation: arrow-animation 1s linear infinite 1.3s;
    }

    polygon.arrow-bottom {
        animation: arrow-animation 1s linear infinite 2.5s;
    }
}

::v-deep .bottomInfo .el-input__inner {
    // background-color: #d9d9d9 !important;
    // background-color: #ccc !important;
    height: 35px;
    border-radius: 10px;
    // text-align: center;
    width: 250px;
    // margin-left: 40%;
}
</style>
