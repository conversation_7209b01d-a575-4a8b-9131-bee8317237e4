<template>
  <div @click.stop>
    <div class="author-box">
      <div class="author-name">{{ author.author_name }}</div>
      <div style="overflow-y: scroll; height: 25vh" class="no-scroll-bar">
        <img
          :src="fullImageUrl(author.author_img)"
          alt=""
          style="width: 120px; height: 120px; float: left; margin: 0 4px 4px 0"
        />
        <div
          v-if="processedAuthorSign"
          v-html="processedAuthorSign"
          style="font-size: 14px; color: #929292"
        ></div>
        <div v-else style="font-size: 14px; color: #929292">暂无简介</div>
      </div>
      <div class="list-box">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="list-item"
          @click="handleClickBook(item)"
        >
          <!-- @click="$store.commit('book/closeBookAuthor')" -->
          <div style="height: 86px">
            <img :src="item.book_image" alt="" class="list-img" style="" />
          </div>

          <div class="list-info">
            <div class="list-info-title">{{ item.book_name }}</div>
            <div class="list-info-time">{{ item.publishing_time }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import axios from '@/http'
export default {
  data () {
    return {
      list: []
    }
  },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      author: (state) => state.curAuthorInfo
    }),
    ...mapGetters('book', ['processedAuthorSign']),
    fullImageUrl () {
      const baseUrl = 'https://oss.bookor.com.cn'
      return (imagePath) => {
        if (!imagePath) {
          return 'https://oss.bookor.com.cn/uploads/20191123/8d134c52225868b83013fa42ee065a27.jpg'
        }
        if (imagePath.startsWith('http')) {
          return imagePath
        }
        return baseUrl + imagePath
      }
    }
  },
  methods: {
    ...mapMutations({
      setChosenBookId: 'book/SET_CHOSED_BOOK_ID'
    }),
    apiGetBookList () {
      const params = {
        author_id: this.author.author_id,
        page: 1,
        limit: 10
      }
      axios
        .get('/api/Recommend/getBookListByAuthor', { params })
        .then((res) => {
          if (res.code === 200) {
            this.list = this.list.concat(res.result)
          }
        })
    },
    handleClickBook (item) {
      this.setChosenBookId(item.book_id)
    }
  },
  mounted () {
    this.apiGetBookList()
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
.author-box {
  color: white;
  width: 400px;
  z-index: 2;
  background-color: black;
  position: relative;
  z-index: 2;
  padding: 5px 10px;
  // height: 500px;
  .author-name {
    // font-weight: 600;
    margin-bottom: 5px;
  }
}
.list-box {
  display: flex;
  overflow-x: scroll;
  padding: 5px 0;
  // width: 400px;
  .list-item {
    margin-left: 5px;
    height: 140px;
    width: 100px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 5px 0px #ddd;
    margin-right: 10px;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    cursor: pointer;
    .list-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .list-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0 5px;
      padding-top: 3px;
      height: 51px;
      .list-info-title {
        font-size: 14px;
        /* color: #333; */
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 1.2;
      }
      .list-info-time {
        font-size: 12px;
        margin-top: 5px;
      }
    }
  }
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // display: none;
  }
  // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
  &::-webkit-scrollbar-button {
    display: none;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  // 滚动条的轨道（里面装有Thumb）
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }
  // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    cursor: pointer;
    border-radius: 4px;
  }
  // 边角，即两个滚动条的交汇处
  &::-webkit-scrollbar-corner {
    display: none;
  }
  // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
  &::-webkit-resizer {
    display: none;
  }
}
// /* 滚动条轨道的样式 */
// .list-box::-webkit-scrollbar-track {
//   background-color: #f5f5f5;
//   border-radius: 2px;
// }

// /* 滚动条滑块的样式 */
// .list-box::-webkit-scrollbar-thumb {
//   background-color: #999;
//   border-radius: 2px;
// }

// /* 滚动条滑块被悬停时的样式 */
// .list-box::-webkit-scrollbar-thumb:hover {
//   background-color: #666;
// }
.no-scroll-bar::-webkit-scrollbar {
  display: none;
}
</style>
