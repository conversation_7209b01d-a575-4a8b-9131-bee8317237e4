<template>
<div style="height: 100vh; width: 100vw;position: relative">
  <div
    class="change-to-word"
    @click="changeToWord(pdfUrl, pdfPaperTitle)"
  >
    转为WORD
  </div>
  <embed
    :src="pdfUrl"
    type="application/pdf"
    height="100%"
    width="100%"
  />
</div>
</template>
<script>
export default {
  data () {
    return {
      pdfUrl: ''
    }
  },
  created () {
    this.$watch('$route.query', (newQuery) => {
      this.pdfUrl = newQuery.url
    })
  },
  mounted () {
    const url = this.$route.query.url
    this.pdfUrl = url
  }
}

</script>
<style lang="scss" scoped>
.change-to-word {
    position: absolute;
    color: #fff;
    right: 132px;
    background-color: grey;
    top: 17px;
    border-radius: 5px;
    padding: 0 4px;
    cursor: pointer;
}
</style>
