
<template>
  <div class="infinite-list list-con" v-infinite-scroll="load">
    <div class="item" v-for="(item, idx) in list" :key="item.paper_id" @click="toDetail(item)">
      <div class="item-title">
        <img v-if="activeName === 'paper'" class="icon-pdf" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/Word-Local-hover.png"/>
        <img v-else-if="activeName === 'article'" class="icon-pdf" src="../../assets/img/article.png"/>
        <img v-else-if="activeName === 'note'" class="icon-pdf" src="../../assets/img/note.png"/>
        <img v-else-if="activeName === 'comment'" class="icon-pdf" src="../../assets/img/article_comment.png"/>
        <img v-else class="icon-pdf" src="../../assets/img/pdf.png" />
        {{ item.title }}
      </div>

      <div class="right-func">
        <!-- {{  !!item?.loading }} -->
        <el-tooltip v-if="activeName === 'paper' && item?.paper_mp3" content="重新生成mp3" placement="top">
           <i class="el-icon-refresh"  @click="downloadMp3(item)"></i>
        </el-tooltip>
        <el-tooltip v-else-if="activeName === 'paper'" content="生成mp3" placement="top">
          <i class="el-icon-mic" @click="downloadMp3(item)"></i>
        </el-tooltip>
        <span v-if="activeName !== 'draft'">
          <el-tooltip v-if="item?.loading" effect="dark" content="总结中" placement="top">
            <i class="el-icon-loading"></i>
          </el-tooltip>
          <el-tooltip v-else effect="dark" content="总结脑图" placement="top">
            <img class="mind-btn" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/mind.png" @click.stop="createdMind(item, idx)"/>
          </el-tooltip>
        </span>
        <el-popconfirm
          title="确认删除文档吗"
          @confirm="deleteDoc(item, idx)"
        >
          <i slot="reference" class="el-icon-delete" @click.stop></i>
        </el-popconfirm>
      </div>
    </div>
    <p v-if="!list.length" class="empty">暂无内容</p>
    <div class="preview-box" v-if="isShowFull">
      <img src="../../assets/img/icon_close.png" class="close" @click="() => {
        this.isShowFull = !this.isShowFull
      }"/>
      <Mind :treeObj="mindInfo" :readonly="true"></Mind>
    </div>
  </div>
</template>
<script lang="ts">
import request from '@/http/request';
import { searchEmu } from './util';
// import { TreePlan } from '@/components/Echarts'
import Mind from '@/components/Echarts/src/TreePlan/mind.vue';

const typeConfig = {
  [searchEmu.PAPER]: 3,
  [searchEmu.ARTICLE]: 2,
  [searchEmu.THESIS]: 4,
  [searchEmu.BOOK_COMMENT]: 5,
  [searchEmu.NOTE]: 6
}

export default {
  data () {
    return {
      isMore: true,
      page: 1,
      size: 20,
      list: [],
      isShowFull: false,
      downloadVisible: false,
      mindInfo: null // 脑图
    }
  },
  components: {
    Mind
  },
  props: ['activeName'],
  watch: {
    activeName (val) {
      this.page = 1
      this.list = []
      this.isMore = true
      this.getList()
    }
  },
  methods: {
    handleShareClose() {
      this.downloadVisible = false
    },
    downloadMp3(item) {
      request('/api/Paper/getPaperInfo', {
        paper_id: item.paper_id,
      }).then((info) => {
        const content = info.paper_url
        request('/api/Paper/generateTalkMp3', {
        paper_id: item.paper_id,
        content,
      }).then(() => {
        this.$message.success('生成成功，2分钟之后可以下载')
      })
      })
    },
    // 删除林文
    deletePaper (item, idx) {
      const params = {
        paper_id: item.id
      }
      request('/api/Paper/deletePaper', params).then((res) => {
        this.list.splice(idx, 1)
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: '论文删除失败',
          type: 'error',
          duration: 1000
        })
      })
    },
    createdMind (item, idx) {
      // this.list[idx].loading = true
      let count = 0
      if (this.list[idx].mind) {
        this.mindInfo = this.list[idx].mind
        this.isShowFull = true
        return
      }
      const type = typeConfig[this.activeName]
      let id = item.paper_id
      if ([searchEmu.ARTICLE, searchEmu.BOOK_COMMENT, searchEmu.NOTE].includes(this.activeName)) {
        id = item.id
      }
      const poll = () => {
        count += 1
        request('/api/Mindmap/getMindMapByInfo', {
          id,
          type
        }).then((result) => {
          if (Array.isArray(result)) {
            this.list[idx].loading = true
            this.$forceUpdate()
            if (count > 10) {
              this.list[idx].loading = false
              this.$forceUpdate()
              this.$message.error('脑图生成失败')
              return
            }
            setTimeout(() => {
              poll()
            }, 5000)
          } else {
            this.list[idx].loading = false
            this.mindInfo = result
            this.isShowFull = true
            this.list[idx].mind = result
            this.$forceUpdate()
          }
        }).catch(() => {
          this.$message.error('系统错误')
        })
      }
      poll()
    },
    // 删除收藏文章
    async deleteCollectArticle (item, idx) {
      const params = {
        id: item.id
      }
      request('/api/wechatarticle/deleteArticle', params).then(() => {
        this.list.splice(idx, 1)
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      }).catch((res) => {
        this.$message({
          message: res.message,
          type: 'error',
          duration: 1000
        })
      })
    },
    // 删除白板
    deleteDraft (item, idx) {
      request('/api/Draft/deleteDraft', {
        id: item.id
      }).then(() => {
        this.list.splice(idx, 1)
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      })
    },
    deleteNote (item, idx) {
      request('/mobile/Bookwrite/delNote', {
        note_id: item.id
      }).then(() => {
        this.list.splice(idx, 1)
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      })
    },
    deleteBookComment (item, idx) {
      request('/api/Comment/deleteComment', {
        comment_id: item.id
      }).then(() => {
        this.list.splice(idx, 1)
        this.$message({
          message: '删除成功',
          type: 'success',
          duration: 1000
        })
      })
    },
    deleteDoc (item, idx) {
      if ([searchEmu.PAPER, searchEmu.THESIS].includes(this.activeName)) {
        this.deletePaper(item, idx)
      } else if (this.activeName === searchEmu.ARTICLE) {
        this.deleteCollectArticle(item, idx)
      } else if (this.activeName === searchEmu.DRAFT) {
        this.deleteDraft(item, idx)
      } else if (this.activeName === searchEmu.NOTE) {
        this.deleteNote(item, idx)
      } else if (this.activeName === searchEmu.BOOK_COMMENT) {
        this.deleteBookComment(item, idx)
      }
    },

    getParerInfo (id) {
      request('/api/Paper/getPaperInfo', {
        paper_id: id
      }).then((result) => {
        const url = result.paper_url
        const title = result.paper_title
        const dotIndex = url.lastIndexOf('.')
        const extension = url.slice(dotIndex + 1)
        this.$emit('openUrl', {
          ...result,
          url: result.paper_url
        })
        if (extension === 'pdf') {
          this.$router.push({
            name: 'pdf',
            query: {
              paperId: id,
              title: title,
              url
            }
          })
        } else if (extension === 'epub') {
          this.$router.push({
            name: 'epub',
            query: {
              paperId: id,
              title: title,
              url
            }
          })
        } else if (extension === 'docx') {
          this.$router.push({
            name: 'docx',
            query: {
              paperId: id,
              title: title,
              url
            }
          })
        } else {
          this.$router.push({
            name: 'note',
            query: {
              paperId: id
            }
          })
        }
      })
    },
    toDetail (item) {
      if ([searchEmu.LOCAL, searchEmu.PAPER].includes(this.activeName)) {
        this.getParerInfo(item.id)
        return
      }
      const { id } = item
      if (this.activeName === searchEmu.DRAFT) {
        this.$router.push({
          name: 'draft',
          query: {
            id
          }
        })
      } else if (this.activeName === searchEmu.ARTICLE) {
        this.$router.push({
          name: 'article',
          query: {
            id: id
          }
        })
      } else if (this.activeName === searchEmu.THESIS) {
        request('/api/Paper/getPaperInfo', {
          paper_id: id
        }).then((result) => {
          let url = ''
          if (result.paper_url.includes('https://oss.bookor.com.cn')) {
            url = result.paper_url
          } else {
            url = `https://oss.bookor.com.cn/${result.paper_url}`
          }
          this.$emit('openUrl', {
            ...result,
            url: result.paper_url
          })
          this.$router.push({
            name: 'pdfEmbed',
            query: {
              url,
              id,
              title: item.title
            }
          })
        })
      } else if (this.activeName === searchEmu.BOOK_COMMENT) {
        this.$router.push({
          name: 'bookComment',
          query: {
            id
          }
        })
      } else if (this.activeName === searchEmu.NOTE) {
        this.$router.push({
          name: 'bookNote',
          query: {
            id
          }
        })
      }
    },
    getLocalList (id) {
      request('/api/Paper/getPaperList', {
        limit: this.size,
        page: this.page,
        paper_type: 'others'
      }).then((res) => {
        if (res.length < this.size) {
          this.isMore = false
        }
        const list = res.map((item) => {
          return {
            ...item,
            id: item.paper_id,
            title: item.paper_title
          }
        })
        if (this.page === 1) {
          this.list = list
        } else {
          this.list = [...this.list, ...list]
        }
      })
    },
    getList () {
      if (!this.isMore) {
        return
      }
      const val = this.activeName
      if (val === searchEmu.PAPER) {
        this.getLocalList()
      } else if (val === searchEmu.THESIS) {
        this.getPaperList()
      } else if (val === searchEmu.ARTICLE) {
        this.getArticle()
      } else if (val === searchEmu.BOOK_COMMENT) {
        this.getBookComment()
      } else if (val === searchEmu.NOTE) {
        this.getNoteList()
      } else if (val === searchEmu.DRAFT) {
        this.getDraftList()
      }
      this.page += 1
    },
    load () {
      if (this.isMore) {
        this.getList()
      }
    },
    getDraftList () {
      request('/api/Draft/draftList', {
        limit: this.size,
        page: this.page
      }).then((res) => {
        if (res.length < this.size) {
          this.isMore = false
        }
        const list = res
        if (this.page === 1) {
          this.list = list
        } else {
          this.list = [...this.list, ...list]
        }
      })
    },
    getNoteList () {
      request('/mobile/member/reviewsList', {
        page: this.page,
        limit: this.size,
        search_status: 'off',
        data_type: 'self',
        article_type: 'note',
        member_type: 'self'
      }).then((res) => {
        if (res.list.length < this.size) {
          this.isMore = false
        }
        const list = res.list.map((item) => {
          return {
            ...item,
            id: item.article_id
          }
        })
        if (this.page === 1) {
          this.list = list
        } else {
          this.list = [...this.list, ...list]
        }
      })
    },
    getBookComment () {
      request('mobile/member/reviewsList', {
        article_type: 'comment',
        data_type: 'self',
        limit: this.size,
        member_type: 'self',
        page: this.page,
        search_status: 'off'
      }).then((res) => {
        if (res.list.length < this.size) {
          this.isMore = false
        }
        const list = res.list.map((item) => {
          return {
            ...item,
            id: item.article_id
          }
        })
        if (this.page === 1) {
          this.list = list
        } else {
          this.list = [...this.list, ...list]
        }
      })
    },
    getArticle () {
      request('api/wechatarticle/articleList', {
        limit: this.size,
        page: this.page
      }).then((res) => {
        if (res.length < this.size) {
          this.isMore = false
        }
        if (this.page === 1) {
          this.list = res
        } else {
          this.list = [...this.list, ...res]
        }
      })
    },
    getPaperList () {
      request('/api/Paper/getPaperList', {
        limit: this.size,
        page: this.page,
        paper_type: 'paper'
      }).then((res) => {
        if (res.length < this.size) {
          this.isMore = false
        }
        const list = res.map((item) => {
          return {
            ...item,
            id: item.paper_id,
            title: item.paper_title
          }
        })
        if (this.page === 1) {
          this.list = list
        } else {
          this.list = [...this.list, ...list]
        }
      })
    }
  },
  mounted () {
    // this.getPaperList()
  }
}
</script>

<style lang="scss" scoped>
.list-con{
  background-color: #fff;
  margin: 0 20px 40px;
  height: 75vh;
  overflow-y: auto;
  .item{
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #ddd;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    i{
      cursor: pointer;
    }
  }
  .right-func{
    display: flex;
    align-items: center;
    gap: 6px;
    .mind-btn{
      cursor: pointer;
      width: 14px;
      height: auto;
    }
    .el-icon-mic{
      font-size: 18px;
    }
  }

}
.empty{
  color: #999;
  text-align: center;
  padding: 40px 0;
}
.item-title{
  white-space: nowrap;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis; /* 显示省略号 */
  width: 350px;
}
.icon-pdf{
  width: 20px;
  height: 20px;
  position: relative;
  top: 5px;
  margin-right: 4px;
}
.preview-box {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  background-color: #333333;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  .close {
    cursor: pointer;
    position: absolute;
    right: 12px;
    width: 24px;
    height: 24px;
    top: 12px;
    z-index: 9999;
  }
}
</style>
