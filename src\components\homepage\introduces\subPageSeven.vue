<template>
<div class="section">
      <div class="page-box">
          <div class="title-part1">Bookor Brain系统的理论依据</div>
          <div class="content-box-part1">
              <div class="content-inside">
                  <div class="content-inside-item">
                      <span class="blue-dot">·</span>
                      <span class="text">大脑的认知机制论</span><span>——所依据或提及的其他理论、概念或学说。这有助于理解作者的思想起源和发展脉络。
                      </span>
                  </div>
                  <div class="content-inside-item">
                      <span class="blue-dot">·</span>
                      <span class="text top">跨文本阅读如何促进大脑认知神经网络的优化呢？</span><span>——作者为支撑观点或论证过程而采用的例子、数据、案例和信息等证据。它反映出作者获取和使用知识的方式。
                      </span>
                  </div>
                  <div class="content-inside-item">
                      <span class="blue-dot">·</span>
                      <span class="text">BookorBrain系统如何体现大脑的认知机制？</span><span>——作者最终想要传达给读者的结论性意见。这代表了作者的立场和对所探讨问题的最终看法。
                      </span>
                  </div>
              </div>
              <div class="right-con">
                  <video ref="video" src="https://bookor-formal.oss-cn-beijing.aliyuncs.com/output2.mp4" class="video-js vjs-default-skin"
                       controls autoplay>
                  </video>
              </div>
          </div>
      </div>
  </div>
</template>

<style lang="scss" scoped>
.title-part1 {
  color: white;
  font-size: 26px;
  font-weight: 600;
  text-align: center;
  margin-top: 5%;
}
.content-box-part1{
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  width: 900px;
  margin: 0 auto;
  margin-top: 6%;
}
.content-inside-item{
  color: #fff;
  font-size: 18px;
  font-weight: 500;
  text-align: justify;
  margin-bottom: 10px;
}
.blue-dot{
  color: #40dbff;
}
.text{
  color: #40dbff;
  &.top{
    margin-top: 5px;
  }
}
.content-inside{
  width: 40%;
}
.right-con {
  width: 60%;
  video{
    margin-left: 100px;
    width: 100%;
    height: 100%;
  }
}
@media only screen and (max-width: 420px) {
  .content-box-part1{
    width: 100%;
    display: flex;
    flex-direction: column;
    .content-inside{
      width: 100%;
    }
    .right-con{
      width: 100%;
      margin-top: 40px;
      video{
        margin: 0;
      }
    }
  }
}
</style>
