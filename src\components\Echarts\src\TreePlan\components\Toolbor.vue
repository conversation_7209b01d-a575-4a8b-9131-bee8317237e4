<template>
  <div class="mind-map-toolbar">
    <el-tooltip v-if="!readonly" content="脑图列表" placement="top">
      <el-button type="text" plain class="el-icon-notebook-1" @click="openList"></el-button>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="返回" placement="top">
      <el-button v-if="!readonly" type="text" plain size="mini" class="el-icon-arrow-left" @click="backSide" :disabled="isStart"></el-button>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="前进" placement="top">
      <el-button  type="text" plain size="mini" class="el-icon-arrow-right" @click="forward" :disabled="isEnd"></el-button>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="新增脑图" placement="top">
      <el-button type="text" plain size="mini" class="el-icon-plus" @click="addMindMap"></el-button>
    </el-tooltip>

    <el-tooltip content="当前：左键拖动画布，右键框选节点" placement="top">
      <el-button  type="text" plain class="el-icon-mouse" @click="showContextMenu"></el-button>
    </el-tooltip>
    <el-tooltip content="回到根节点" placement="top">
      <i class="el-icon-location-outline" @click="backToRoot"></i>
    </el-tooltip>
    <el-tooltip content="搜索节点" placement="top">
      <i class="el-icon-search" @click="showSearch"></i>
    </el-tooltip>
    <el-tooltip content="全屏" placement="top">
      <i class="el-icon-full-screen" @click="fullScreen"></i>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="概要" placement="top">
      <el-button type="text" :disabled="!activeNodes.length" plain size="small" class="el-icon-s-grid" @click="addGeneralization"></el-button>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="快捷键" placement="top">
      <i class="el-icon-key" @click="openShortcutKey"></i>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="结构" placement="top">
      <i class="el-icon-view" @click="openStructure"></i>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="添加图片">
      <el-button type="text" :disabled="!activeNodes.length" plain class="el-icon-picture-outline" @click="openPhoto"></el-button>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="公式" placement="top">
      <img v-if="!activeNodes.length" class="icon-gs" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/%E5%85%AC%E5%BC%8F%20%281%29.png">
      <img v-else class="icon-gs" src="https://bookor-application.oss-cn-beijing.aliyuncs.com/%E5%85%AC%E5%BC%8F.png" @click="openFormula"/>
    </el-tooltip>
    <input type="file" hidden ref="imageFile" accept="image/*" @change="addPhoto"/>
    <el-tooltip v-if="!readonly" content="导入" placement="top">
      <i class="el-icon-upload2" @click="importImage"></i>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="导出" placement="top">
      <i class="el-icon-download" @click="exportImage"></i>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="设置" placement="top">
      <i class="el-icon-setting" @click="toSetting"></i>
    </el-tooltip>
    <el-tooltip v-if="!readonly" content="分享" placement="top">
      <i class="el-icon-share" @click="toShare"></i>
    </el-tooltip>
    <el-tooltip v-if="!readonly" :content="shareTooltip" placement="top">
      <el-switch
        v-model="isShare"
        active-color="#13ce66"
        @change="shareChange"
        >
      </el-switch>
    </el-tooltip>
    <p id="copyBtn" ref="copyBtn" style="display: none" :data-clipboard-text="copyText"></p>
    <el-input-number size="small" v-model="scaleNum" @change="handleChange" :min="5" :max="200" :step="5"></el-input-number>
  </div>
</template>
<script>
import { fullScreen } from '@/utils/full';
import axiosMe from 'axios'
import axios from "@/http";
import ClipboardJS from 'clipboard'
export default {
  data() {
    return {
      userinfo: JSON.parse(localStorage.getItem('userinfo')),
      activeNodes: [],
      scaleNum: 80,
      isShare: true,
      copyText: '',
      useLeftKeySelectionRightKeyDrag: false
    }
  },
  props: ['isStart', 'treeObj', 'readonly', 'isEnd', 'mindMap', 'currentNode'],
  computed: {
    shareTooltip() {
      if(this.isShare) {
        return '关闭分享'
      } else {
        return '开启分享'
      }
    }
  },
  watch: {
    mindMap(val, oldVal) {
      if (val && !oldVal) {
        this.mindMap.on('scale', this.onScale)
        this.scaleNum = this.toPer(this.mindMap.view.scale)
      }
    }
  },
  beforeDestroy() {
    this.mindMap.off('scale', this.onScale)
  },
  methods: {
    toShare() {
      const route = this.$route
      const id = route.params.id
      const title = this.treeObj.content
      this.copyText = `你的好友分享了${title} 请复制链接在浏览器打开\n https://www.bookor.com.cn/#/mind/${id}?share=1&userId=${this.userinfo.userid}`
      this.$nextTick(() => {
        this.$refs.copyBtn.click()
      })
    },
    openPhoto() {
      this.$refs.imageFile.click()
    },
    addPhoto(e) {
      const file = e.target.files[0];
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'];
      if (!allowedTypes.includes(file.type)) {
        this.$message.error('请选择有效的图片文件（JPEG, PNG, GIF, BMP）');
        return;
      }
      const formData = new FormData()
      const userInfo = JSON.parse(localStorage.getItem('userinfo'))
      formData.append('file', file)
      formData.append('upload_type', 'image')
      axiosMe({
        method: 'POST',
        url: '/api/upload/index',
        data: formData,
        headers: {
          'authorization': userInfo?.key,
          'Content-Type': 'multipart/form-data'
        }
      }).then(async res => {
        const result = res.data.result.complete_url
        this.activeNodes[0].setImage({
          url: result,
          width: 100,// 图片的宽高也不能少
          height: 100
        })
      })
    },
    openFormula() {
      this.$store.commit('mind/setActiveSidebar', 'formulaSidebar');
    },
    openList() {
      this.$bus.$emit('showMindList', this.getList)
    },
    toSetting() {
      this.$store.commit('mind/setActiveSidebar', 'setting');
    },
    toPer(scale) {
      return (scale * 100).toFixed(0)
    },
    addMindMap() {
      this.$bus.$emit('openAddMindMap')
    },
    onScale(scale) {
      this.scaleNum = this.toPer(scale)
    },
    fullScreen() {
      const dom = document.getElementById('mindMapContainer')
      fullScreen(dom)
    },
    backSide() {
      this.mindMap.execCommand('BACK')
    },
    forward() {
      this.mindMap.execCommand('FORWARD')
    },
    backToRoot() {
      this.mindMap.renderer.setRootNodeCenter()
    },
    handleChange(value) {
      // this.scaleNum = value
      this.mindMap.view.setScale(value / 100)
    },
    showSearch() {
      this.$bus.$emit('show_search')
    },
    importImage() {
      this.$bus.$emit('showImport')
    },
    exportImage() {
      this.$bus.$emit('showExport')
    },
    openShortcutKey() {
      this.$store.commit('mind/setActiveSidebar', 'shortcutKey');
    },
    addGeneralization() {
      this.mindMap.execCommand('ADD_GENERALIZATION', {
        text: '概要内容'
      })
      // this.$bus.$emit('execCommand', 'ADD_GENERALIZATION')
    },
    onNodeActive(...args) {
      this.activeNodes = [...args[1]]
    },
    showContextMenu() {
    },
    openStructure() {
      this.$store.commit('mind/setActiveSidebar', 'structure');
    },
    shareChange () {
      axios.post('/ai/mind/updateShare', {
        mindId: this.treeObj.mind_map_id,
        isShare: this.isShare ? 0 : 1
      }).then(res => {
        this.$message.success('设置成功')
      })
    }
  },
  mounted() {
    this.$bus.$on('node_active', this.onNodeActive)

    const copyBtn = new ClipboardJS('#copyBtn')

    copyBtn.on('success', (e) => {
      this.$message.success('站外分享链接复制成功, 您可以发送给你的好友')
    })
    this.isShare = !this.treeObj.is_share
  }
}
</script>
<style lang="scss" scoped>
  .mind-map-toolbar{
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: #fff;
    padding: 8px 12px 8px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 16px 0 rgba(0, 0, 0, .06);
    border: 1px solid rgba(239, 97, 97, .06);
    i{
      cursor: pointer;
      font-size: 16px;
      color: #666;
    }
    .icon-gs{
      width: 18px;
      cursor: pointer;
    }
    .el-icon-plus{
      font-size: 18px;
    }
    .el-icon-mouse{
      font-size: 18px;
    }
    .el-icon-s-grid {
      font-size: 18px;
      color: #666;
    }
    :deep(.el-button){
      border: none;
      margin: 0;
    }
  }
</style>
