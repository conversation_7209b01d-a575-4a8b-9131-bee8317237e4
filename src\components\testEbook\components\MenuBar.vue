<template>
  <div class="menu-bar">
    <div class="menu-operation">
      <div class="icon-wrapper">
      <i class="el-icon-arrow-left" @click="prevPage"/>
      <input
          class="input-wrapper"
          type="text"
          v-model="currentPageLocal"
          @keydown.enter="onPageChange"
          @input="onInputPageChange"
          :disabled="!bookAvailable"
          maxlength="6"
        />
        <span class="line">/</span>
        <span style="font-size: 14px" v-if="totalPages != 0">{{
          totalPages
        }}</span>
      <i class="el-icon-arrow-right" @click="nextPage"/>
    </div>

    <div class="icon-wrapper content-btn">
      <i class="el-icon-tickets"/>
      <span class="icon-menu icon" @click="showSetting(3)">目录</span>
    </div>
    </div>
    <content-view
      :ifShowContent="ifShowContent"
      v-show="ifShowContent"
      :navigation="navigation"
      :bookAvailable="bookAvailable"
      @jumpTo="jumpTo"
    >
    </content-view>
  </div>
</template>
<script>
import ContentView from './Content'
export default {
  components: {
    ContentView
  },
  props: {
    bookAvailable: {
      type: Boolean,
      default: false
    },
    navigation: Object,
    parentProgress: Number,
    totalPages: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      ifSettingShow: false,
      showTag: 0,
      progress: 0,
      ifShowContent: false,
      currentPageLocal: this.currentPage // 定义一个同名的 data 属性并初始化为 prop 值
    }
  },
  watch: {
    bookAvailable: {
      handler: function () {
        // this.getCurrentLocation();
      }
    },
    parentProgress: {
      handler: function (value) {
        this.progress = value
        if (this.bookAvailable && this.$refs.progress) {
          this.$refs.progress.style.backgroundSize = `${this.progress}% 100%`
        }
      },
      deep: true
    },
    currentPage: {
      handler: function (value) {
        this.currentPageLocal = value
      }
    }
  },
  methods: {
    jumpTo (target) {
      this.$emit('jumpTo', target)
    },
    onInputPageChange () {
      // 在输入框上添加 input 事件监听器
      const validNumber = this.currentPageLocal.replace(/\D/g, '') // 将非数字字符替换为空字符串
      this.currentPageLocal = Number(validNumber)
    },
    onPageChange () {
      const pageNumber = parseInt(this.currentPageLocal) // 将用户输入的内容转换为整数
      if (pageNumber > 0 && pageNumber <= this.totalPages) {
      } else {
        this.currentPageLocal = 1
      }
      this.$emit('onPageChange', this.currentPageLocal)
    },
    prevPage () {
      this.$emit('prevPage')
    },
    nextPage () {
      this.$emit('nextPage')
    },
    showSetting (tag) {
      this.showTag = tag
      if (this.showTag === 3) {
        this.ifShowContent = !this.ifShowContent
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../styles/global";

.menu-operation {
  position: absolute;
  display: flex;
  top: 10px;
  left: -12vw;
  width: 13vw;
  height: 30px;
  background: white;
  min-width: 240px;
  .icon-wrapper{
    display: flex;
    align-items: center;
    gap: 4px;
  }
  .input-wrapper{
    width: 30px;
    height: 22px;
    padding: 0 4px;
    border-radius: 2px;
    border: 1px solid #ccc;
  }
  i{
    font-size: 16px;
    padding: 4px;
    cursor: pointer;
  }
  .setting-wrapper {
    position: absolute;
    bottom: px2rem(48);
    left: 0;
    z-index: 11;
    width: 100%;
    height: px2rem(70);
    background: white;
    box-shadow: 0 px2rem(-8) px2rem(8) rgba(0, 0, 0, 0.15);
    .setting-font-size {
      display: flex;
      height: 100%;
      padding: 0 5px;
      .preview {
        flex: 0 0 px2rem(40);
        @include center;
      }
      .select {
        display: flex;
        flex: 1;
        .select-wrapper {
          flex: 1;
          display: flex;
          align-items: center;
          cursor: pointer;
          &:first-child {
            .line {
              &:first-child {
                border-top: none;
              }
            }
          }

          &:last-child {
            .line {
              &:last-child {
                border-top: none;
              }
            }
          }
          .line {
            flex: 1;
            height: 0;
            border-top: px2rem(1) solid #ccc;
          }
          .point-wrapper {
            position: relative;
            flex: 0 0 0;
            width: 0;
            height: px2rem(7);
            border-left: px2rem(1) solid #ccc;
            .point {
              position: absolute;
              top: px2rem(-8);
              left: px2rem(-10);
              width: px2rem(20);
              height: px2rem(20);
              border-radius: 50%;
              background: white;
              border: px2rem(1) solid #ccc;
              box-shadow: 0 px2rem(4) px2rem(4) rgba(0, 0, 0, 0.15);
              @include center;
              .small-point {
                width: px2rem(5);
                height: px2rem(5);
                background: black;
                border-radius: 50%;
              }
            }
          }
        }
      }
    }

    .setting-theme {
      height: 100%;
      display: flex;
      .setting-theme-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: px2rem(5);
        box-sizing: border-box;
        cursor: pointer;
        .preview {
          flex: 1;
          border: px2rem(1) solid #ccc;
          box-sizing: border-box;
          &.no-border {
            border: none;
          }
        }

        .text {
          flex: 0 0 px2rem(20);
          font-size: px2rem(14);
          color: #ccc;
          @include center;
          &.selected {
            color: #333;
          }
        }
      }
    }

    .setting-progress {
      position: relative;
      width: 100%;
      height: 100%;
      .progress-wrapper {
        width: 100%;
        height: 100%;
        @include center;
        padding: 0 px2rem(30);
        box-sizing: border-box;
        .progress {
          width: 100%;
          -webkit-appearance: none;
          height: px2rem(2);
          background: -webkit-linear-gradient(#999, #999) no-repeat, #ddd;
          background-size: 0 100%;
          cursor: pointer;
          &:focus {
            outline: none;
          }
          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            height: px2rem(20);
            width: px2rem(20);
            border-radius: 50%;
            background: white;
            box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.15);
            border: px2rem(1) solid #ddd;
          }
        }
      }
      .text-wrapper {
        position: absolute;
        width: 100%;
        bottom: 0;
        color: #333;
        font-size: px2rem(12);
        span {
          @include center;
        }
      }
    }
  }
  .content-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 11;
    display: flex;
    width: 100%;
    height: 100%;
  }
}
</style>
