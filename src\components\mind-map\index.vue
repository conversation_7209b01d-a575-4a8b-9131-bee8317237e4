<template>
  <div
    @click.stop="$store.commit('dropwelt/closeMindMap')"
    v-if="isShowMindMap"
    class="container"
  >
    <div class="box" @click.stop>
      <div class="tag-box" v-if="myMemberKeywordsTagList">
        <div
          v-for="(item, index) in myMemberKeywordsTagList"
          :key="index"
          :class="{
            'tag-item': true,
            'tag-item-active': item.id === currentSearchTagId,
          }"
          @click="handleChooseTag(item)"
        >
          #{{ item.tag_name }}
        </div>
      </div>
      <div class="mindMapList" ref="mindMapListRef">
        <div
          class="mindMapItem"
          v-for="(item, index) in mindMaptitleList"
          :key="index"
        >
          <div class="mindMapTitle">{{ item.content }}</div>
          <div class="time">
            <div>
              {{ mindMapListDetailData[item.mind_map_id] && mindMapListDetailData[item.mind_map_id]?.add_time_format }}
            </div>
          </div>
          <div @click="handlePreview(item)" v-if="isShowPreview">
            <TreeEchart
              :treeObj="mindMapListDetailData[item.mind_map_id]"
              :treeId="'tree-echart-' + index"
              :width="'200px'"
              :height="'160px'"
              :isCut="true"
            />
          </div>
        </div>
      </div>
    </div>
    <el-drawer
      title="脑图"
      :append-to-body="true"
      size="95%"
      :visible.sync="isShowFull"
      direction="rtl"
      :before-close="handleClose">
      <Mind v-if="isShowFull" :treeObj="mindMapListDetailData[curMindMapId]" :isCut="false"></Mind>
    </el-drawer>
  </div>
</template>

<script>
import { Mind, TreeEchart } from '@/components/Echarts';
import request from '@/http/request';
import { mapState } from 'vuex';
export default {
  data () {
    return {
      myMemberKeywordsTagList: [],
      mindMaptitleList: [],

      isLoadingTitleList: false,
      curPage: 1,
      pageLimit: 5,

      mindMapListDetailData: {},
      isShowFull: false,
      isShowPreview: false,
      // 选中某个tag
      currentSearchTagId: '',
      currentSearchTagName: '',

      isRefreshMindDetailLoading: false
    }
  },
  components: { TreeEchart, Mind },
  computed: {
    ...mapState('user', {
      userInfoMore: (state) => state.userInfoMore
    }),
    ...mapState('book', {
      curTagInfo: (state) => state.curTagInfo
    }),
    ...mapState('dropwelt', {
      isShowMindMap: (state) => state.isShowMindMap
    })
  },
  watch: {
    isShowMindMap: function (newValue, oldValue) {
      // 在这里执行变量改变时的逻辑
      if (newValue) {
        // this.getAllMindMapDetail()
      } else {
        this.isShowFull = false
      }
    }
  },
  methods: {
    handleClose() {
      this.isShowFull = false
    },
    // 获取脑图标签
    async getMemberKeywordsTagList () {
      const params = {
        page: 1,
        limit: 100000
      }
      const url = '/mobile/Nokeyinterface/tagList'
      request(url, params, 'get').then((result) => {
        if (result.length > 0) {
          this.myMemberKeywordsTagList = result
        }
      })
    },

    // 获取脑图标题
    async getTitleList () {
      if (this.isLoadingTitleList) {
        return false
      }
      this.isLoadingTitleList = true
      const params = {
        page: this.curPage,
        limit: this.pageLimit
      }

      if (this.currentSearchTagId !== '') {
        params.search_words = ''
        params.tag_id = this.currentSearchTagId
      }
      const url = '/api/MindMap/getMindMapTitleList'
      request(url, params, 'get').then((result) => {
        if (result?.list?.length) {
          this.mindMaptitleList = [
            ...this.mindMaptitleList,
            ...result.list
          ]

          this.totalMindMap = result.total
          // 先不请求
          this.getAllMindMapDetail()
          this.setMaxPageNum(result.total)
        }
      }).catch((result) => {
        this.$message({
          message: result.message + '，请刷新重试',
          type: 'error',
          duration: 1000
        })
        this.curPage = 1
        this.setMaxPageNum(0)
      })
      this.isLoadingTitleList = false
    },

    getAllMindMapDetail () {
      this.isShowPreview = false
      const mindMaptitleList = this.mindMaptitleList
      const promises = [] // 存储所有 setMindMapItemDetail 方法的 Promise 对象

      for (let i = 0; i < mindMaptitleList.length; i++) {
        const mind_map_id = mindMaptitleList[i].mind_map_id
        const mindMapListDetailData = this.mindMapListDetailData
        mindMapListDetailData[mind_map_id] = {}
        this.mindMapListDetailData = mindMapListDetailData
        promises.push(this.setMindMapItemDetail(mind_map_id))
      }

      Promise.all(promises).then(() => {
        if (!this.isShowPreview) {
          setTimeout(() => {
            this.isShowPreview = true
          }, 1500)
        }
      })
    },
    async setMindMapItemDetail (mindMapId, type) {
      const params = {
        mind_map_id: mindMapId
      }
      if (type == 1) {
        this.isRefreshMindDetailLoading = true
      }
      const url = '/mobile/Nokeyinterface/getMindMapContentList'
      request(url, params, 'get').then((result) => {
        if (result?.length) {
          const list = result
          const mindMapListDetailData = this.mindMapListDetailData
          mindMapListDetailData[mindMapId] = list[0]
          const addTime = mindMapListDetailData[mindMapId].add_time
          if (addTime) {
            mindMapListDetailData[mindMapId].add_time_format =
              this.formatDate(addTime * 1000, 'yyyy-MM-dd')
          } else {
            mindMapListDetailData[mindMapId].add_time_format = ''
          }

          this.mindMapListDetailData[mindMapId] =
            mindMapListDetailData[mindMapId]

          if (type == 1) {
            this.mindMaptitleList.splice(this.curEditMindMapIndex, 1, {
              ...this.mindMaptitleList[this.curEditMindMapIndex],
              content: list[0].content,
              status: list[0].status
            })
            this.isRefreshMindDetailLoading = false
            this.curMindMapStatus = list[0].status


          }
        }
      }).catch(() => {
        if (type == 1) {
          this.isRefreshMindDetailLoading = false
        }
      })
    },

    setMaxPageNum (total) {
      let maxPageNum = 1
      const pageLimit = this.pageLimit
      if (total == 0) {
        maxPageNum = 1
      } else {
        maxPageNum = Math.ceil(total / pageLimit)
      }
      this.maxPageNum = maxPageNum
    },
    formatDate (date, fmt) {
      const d = new Date(date)
      const o = {
        'M+': d.getMonth() + 1, // 月份
        'd+': d.getDate(), // 日
        'h+': d.getHours(), // 小时
        'm+': d.getMinutes(), // 分
        's+': d.getSeconds(), // 秒
        'q+': Math.floor((d.getMonth() + 3) / 3), // 季度
        S: d.getMilliseconds() // 毫秒
      }

      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          String(d.getFullYear()).substr(4 - RegExp.$1.length)
        )
      }

      for (const k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length === 1
              ? o[k]
              : `00${o[k]}`.substr(String(o[k]).length)
          )
        }
      }

      return fmt
    },

    handlePreview (item) {
      window.open(`#/mind/${item.mind_map_id}`, '_blank');
      // this.$router.push({
      //   path: `/mind/${item.mind_map_id}`
      // })
    },

    handleChooseTag (item) {
      // 重复点击就是取消
      if (this.currentSearchTagId == item.id) {
        this.currentSearchTagId = ''
      } else {
        this.currentSearchTagId = item.id
        this.currentSearchTagName = item.tag_name
      }

      this.isShowPreview = false
      this.onRefreshTitleList()
    },

    onRefreshTitleList () {
      if (this.isLoadingTitleList == true) {
        return false
      }
      this.curPage = 1
      this.mindMaptitleList = []
      this.mindMapListDetailData = {}

      this.getTitleList()
    }
  },
  mounted () {
    this.getMemberKeywordsTagList()
    this.getTitleList()
  },
  updated () {
    // 滚动
    this.$nextTick(() => {
      const container = this.$refs.mindMapListRef
      let isDown = false
      let startX
      let scrollLeft
      if (container) {
        container.addEventListener('mousedown', (e) => {
          isDown = true
          container.classList.add('active')
          startX = e.pageX - container.offsetLeft
          scrollLeft = container.scrollLeft
        })

        container.addEventListener('mouseleave', () => {
          isDown = false
          container.classList.remove('active')
        })

        container.addEventListener('mouseup', () => {
          isDown = false
          container.classList.remove('active')
          if (
            container.scrollLeft + container.offsetWidth >=
            container.scrollWidth
          ) {
            this.curPage++
            this.getTitleList()
          }
        })

        container.addEventListener('mousemove', (e) => {
          if (!isDown) return
          e.preventDefault()
          const x = e.pageX - container.offsetLeft
          const walk = (x - startX) * 3
          container.scrollLeft = scrollLeft - walk
        })
      }
    })
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
:deep(.el-drawer__header){
    padding: 10px 20px 0;
    margin-bottom: 10px;
}
:deep(.el-drawer__body){
  padding: 0;
}
.container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2;
  .box {
    position: absolute;
    // height: 40vh;
    bottom: 0;
    // margin: 0 10px;
    // padding: 0 5px;
    .tag-box {
      .tag-item {
        background-color: #4f4f4f;
        color: white;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        padding: 3px;
        margin-right: 5px;
        display: inline-block;
        margin-bottom: 5px;
      }
      .tag-item-active {
        border: 1px solid #ffc000;
        background-color: #333333;
      }
    }
    .mindMapList {
      display: flex;
      height: 35vh;
      overflow-x: scroll;
      width: 100vw;
      .mindMapItem {
        display: inline-block;
        background: #333;
        /* width: 90%; */
        // min-width: 110px;
        // max-width: 300px;
        width: 220px;
        /* margin: 0 auto 5px auto; */
        padding: 0px 10px;
        border-radius: 10px;
        position: relative;
        // height: 175px;
        border: 1px solid #627b87;
        margin-right: 5px;
        margin-bottom: 5px;
        .mindMapTitle {
          color: white;
          font-size: 16px;
          font-weight: 600;
          padding-bottom: 2px;
          border-bottom: 1px dotted white;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-all;
        }
        .time {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          color: rgb(248, 180, 33);
          font-size: 12px;
        }
      }
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .preview-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #333333;
    box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    .close {
      cursor: pointer;
      position: absolute;
      right: 12px;
      width: 24px;
      height: 24px;
      top: 12px
    }
  }
}
</style>
