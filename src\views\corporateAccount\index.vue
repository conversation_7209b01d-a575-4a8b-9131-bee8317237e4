<template>
  <div class="container" @click="closeClassify">
    <!-- 背景&个人信息 -->
    <div class="top-box">
      <img class="background-cover" :src="mini_personal_bg" alt="" />

      <div class="avator-box">
        <img class="avator" :src="avator" alt="" />
        <div class="user-name">{{ user_name }}</div>
      </div>
    </div>
    <!-- 分类 -->
    <div class="content-box">
      <div class="left">
        <!-- 关于我们 -->
        <div
          class="level1-box"
          @mouseover="showBtn('aboutus')"
          @mouseout="hideBtn"
        >
          <div @click="handleShowAboutus">关于我们</div>
          <div
            v-show="isShowBtnType == 'aboutus'"
            class="add-btn"
            @click="showPopup"
          >
            +
          </div>
        </div>
        <div v-show="isShowAboutus" class="aboutus-content">
          {{ introductionText }}
        </div>
        <!-- 新增&编辑 -->
        <el-dialog
          :visible="isShowPopup"
          @close="closePopup"
          class="aboutus-dialog"
          title="关于我们"
        >
          <!-- 弹窗内容 -->
          <el-input type="textarea" v-model="inputText"></el-input>
          <el-button type="primary" size="small" @click="save">保存</el-button>
        </el-dialog>
        <!-- 图书分类 -->
        <div class="bookClassify" @click.stop>
          <div
            class="level1-box"
            @mouseover="showBtn('classifyFirst')"
            @mouseout="hideBtn"
          >
            <div @click="showAllSellBooks">图书分类</div>
            <div
              v-show="isShowBtnType == 'classifyFirst'"
              class="add-btn"
              @click="handleShowClassifyFirst"
            >
              +
            </div>
          </div>
          <!-- 添加一级分类 -->
          <div v-if="showClassifyFirst" class="classifyPopup" style="top: 32px">
            <Classify classifyTitle="图书" @closeClassify="closeClassify" />
          </div>
          <!-- 一级分类 -->
          <div v-for="(item, index) in bookClassifyList" :key="index">
            <div
              class="bookClassifyItem1"
              @mouseover="showBtn('classifySecond', item.firstCategory.id)"
              @mouseout="hideBtn"
            >
              <div class="bookClassifyItem1-title">
                <div style="display: flex; align-items: center">
                  <img
                    src="./icon-lone.png"
                    alt=""
                    style="width: 9px; height: 9px; margin-right: 2px"
                  />
                  {{ item.firstCategory.name }}
                </div>
                <!-- 添加二级分类 -->
                <div
                  style="display: flex"
                  v-show="
                    isShowBtnType == 'classifySecond' &&
                    pid == item.firstCategory.id
                  "
                >
                  <img
                    src="./icon-edit.png"
                    alt=""
                    style="width: 20px; height: 20px; margin-right: 5px"
                    @click="handleShowEditClassify(item)"
                  />
                  <div
                    class="add-btn add-btn-sub2"
                    @click="handleShowClassifySecond(item.firstCategory.id)"
                  >
                    +
                  </div>
                </div>
                <div
                  v-if="showClassifySecond && pid == item.firstCategory.id"
                  class="classifyPopup"
                  style="top: 22px; left: -12px"
                >
                  <Classify
                    :pid="pid"
                    classifyTitle="次级"
                    @closeClassify="closeClassify"
                  />
                </div>
              </div>
            </div>

            <div
              v-for="(itemSub, indexSub) in item.secondCategory"
              @mouseover="showBtn('classifyBook', itemSub.id)"
              @mouseout="hideBtn"
              :key="indexSub"
              style="
                padding: 0px 9px 0 25px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
              "
            >
              <div
                style="display: flex; align-items: center"
                @click="handleAssociateBooks(itemSub)"
              >
                <img
                  src="./icon-close.png"
                  alt=""
                  style="width: 7px; height: 8px; margin-right: 4px"
                />
                {{ itemSub.name }}
              </div>
              <!-- isShowBtnType == 'classifyBook' &&  -->
              <div
                style="display: flex"
                v-show="
                  isShowBtnType == 'classifyBook' && category_id == itemSub.id
                "
              >
                <img
                  src="./icon-edit.png"
                  alt=""
                  style="width: 20px; height: 20px; margin-right: 5px"
                  @click="handleShowEditClassify(item, itemSub)"
                />
                <div
                  class="add-btn add-btn-sub2"
                  style="height: 11px; line-height: 10px"
                  @click="handleShowClassifyBook(itemSub.id)"
                >
                  +
                </div>
              </div>
              <div
                v-if="showClassifyBook && category_id == itemSub.id"
                class="classifyPopup"
                style="top: 22px; left: 0px"
              >
                <!--:pid="pid"  -->
                <Classify
                  :category_id="category_id"
                  type="book"
                  @closeClassify="closeClassify"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 编辑&删除 -->
        <el-dialog
          :visible="isShowEditClasssify"
          @close="closeShowEditClassify()"
          class="bookClassify-dialog"
          title="图书分类"
        >
          <!-- 弹窗内容 -->
          <el-input v-model="inputBookClassify"></el-input>
          <el-button type="primary" size="small" @click="updateClassify"
            >更新</el-button
          >
          <el-button type="info" size="small" @click="deleteClassify"
            >删除</el-button
          >
        </el-dialog>
      </div>
      <div class="right">
        <!-- 搜索和筛选 -->
        <div class="search-and-filter-box">
          <input
            class="search-input"
            type="text"
            placeholder="搜索"
            @change="handleSearchInput"
            v-model="searchInput"
            maxlength="100"
          />
          <div class="tabs">
            <div
              class="tab"
              v-for="tab in tabs"
              :key="tab.name"
              @click="filterData(tab)"
              :class="{ 'tab-active': currentTab === tab.name }"
            >
              {{ tab.name }}
            </div>
          </div>
        </div>
        <!-- 待租售书列表 -->
        <!-- 书单样式 -->
        <div v-show="currentTab == '书单'">
          <div
            v-if="specialList.length > 0"
            style="
              display: flex;
              flex-wrap: wrap;
              overflow-y: scroll;
              height: 74vh;
            "
            class="no-scroll-bar"
          >
            <div
              v-for="(item, index) in specialList"
              :key="index"
              @click="clickSpecialBookListItem(item)"
            >
              <div
                v-if="item.result.shareBookList.length > 0"
                style="
                  background: #fff;
                  overflow: hidden;
                  height: 300px;
                  position: relative;
                  width: 350px;
                  margin: 15px 15px 15px;
                  box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5);
                  cursor: pointer;
                "
              >
                <div
                  style="
                    box-sizing: border-box;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    padding-top: 4vh;
                    z-index: 1;
                    position: absolute;
                    height: 220px;
                    width: 160px;
                    left: 5%;
                    top: 10%;
                    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.5);
                  "
                >
                  <span
                    style="
                      word-break: break-all;
                      margin-bottom: 10px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-line-clamp: 5;
                      -webkit-box-orient: vertical;
                      font-size: 16px;
                      font-weight: 600;
                      text-align: center;
                      width: 100%;
                      padding: 0 10%;
                      box-sizing: border-box;
                      line-height: 20px;
                    "
                  >
                    {{ item.result.bookListInfo.title }}
                  </span>
                  <div
                    style="
                      word-break: break-all;
                      text-align: center;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-line-clamp: 4;
                      -webkit-box-orient: vertical;
                      color: rgba(0, 0, 0, 0.5);
                      width: 100%;
                      padding: 0 10%;
                      box-sizing: border-box;
                      font-size: 15px;
                      margin-top: 10px;
                    "
                  >
                    {{ item.result.shareIntro }}
                  </div>
                </div>
                <div
                  style="
                    transform: translateX(-24%) translateY(-50px) rotate(45deg);
                    height: 650px;
                    width: 650px;
                    overflow: hidden;
                  "
                >
                  <div style="width: 100%; display: flex; flex-wrap: wrap">
                    <div
                      v-for="(itemSub, indexSub) in item.result.shareBookList"
                      :key="indexSub"
                      style="
                        box-sizing: border-box;
                        overflow: hidden;
                        margin: 0px;
                        width: 110px;
                        height: 135px;
                      "
                    >
                      <img
                        :src="itemSub.book_image"
                        mode="heightFix"
                        style="min-width: 100%; height: 100%; display: block"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else>暂无书单</div>
        </div>

        <!-- 待售图书 -->
        <div v-show="currentTab == '畅销榜'">
          <div
            v-if="sell_book_list.length > 0"
            class="sell-book-list-box no-scroll-bar"
          >
            <div
              v-for="(item, index) in sell_book_list"
              :key="index"
              class="sell-book-list-item"
              @click="handleBookSellDetails(item)"
            >
              <img
                style="width: 120px; height: 180px"
                :src="item.book_image"
                alt=""
              />
              <div class="sell-book-info-box">
                <div class="sell-book-title">{{ item.book_name }}</div>
                <div class="rebook_name_tel">
                  <div
                    v-if="item.is_sell == '1'"
                    style="margin-top: 5px; color: #c00000; font-size: 14px"
                  >
                    现价：￥{{ item.sellprice }}
                  </div>
                  <div
                    v-if="
                      item.is_sell == '1' &&
                      item.original_price >= item.sellprice
                    "
                    style="text-decoration: line-through; font-size: 13px"
                  >
                    定价：￥{{ item.original_price }}
                  </div>
                  <div v-else style="margin-top: 5px"></div>
                  <div
                    v-if="item.is_lease == '1'"
                    style="margin-top: 5px; color: #c00000; font-size: 14px"
                  >
                    日租金：{{ item.lease_price }}元/天
                  </div>
                  <div
                    v-if="item.is_lease == '1'"
                    style="color: #c00000; font-size: 14px"
                  >
                    保证金：￥{{ item.deposit }}
                  </div>
                  <div style="font-size: 12px; color: rgb(192, 0, 0)">
                    佣金：{{ item.commission }}%
                  </div>
                </div>
              </div>

              <div v-if="item.discount > 0" class="sell-book-tag">
                <img src="@/assets/img/sale-tag.png" />
                <div class="sell-book-tag-content">{{ item.discount }}/折</div>
              </div>

              <!-- 删除分类-->
              <div
                v-if="category_id"
                style="position: absolute; bottom: 0; right: 0"
              >
                <img
                  src="./icon-delete.png"
                  alt=""
                  style="width: 20px; height: 20px; cursor: pointer"
                  @click.stop="handleDeleteAssociateBooks(item)"
                />
              </div>
            </div>
          </div>

          <div v-else>暂无待售图书</div>
        </div>
      </div>
    </div>

    <!-- 弹窗详情 -->
    <div class="mask" @click="isShowDetailsPop = false" v-if="isShowDetailsPop">
      <div class="details" @click.stop>
        <div class="details-top">
          <img
            :src="curBookDetail.book_image"
            alt=""
            style="
              width: 130px;
              height: 180px;
              box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.5);
            "
          />
          <!-- 右侧 -->
          <div class="details-top-right">
            <div style="font-size: 18px;color: #494949;font-weight: 600;}">
              {{ curBookDetail.book_name }}
            </div>
            <div
              style="overflow-y: scroll; height: 120px"
              class="no-scroll-bar"
            >
              <!-- 现价 -->
              <div
                v-if="curBookDetail.is_sell == '1'"
                style="
                  color: #e62222;
                  display: flex;
                  align-items: baseline;
                  margin: 16px 0px 10px;
                "
              >
                <div style="font-size: 21px; margin-right: 13px">
                  <span style="font-size: 12px">¥</span
                  >{{ curBookDetail.sellprice }}
                </div>
                <div
                  style="font-size: 12px"
                  v-if="curBookDetail.sellprice < curBookDetail.original_price"
                >
                  ({{ curBookDetail.discount }}折)
                </div>
              </div>

              <div
                style="color: #969696; margin: 7px 0"
                v-if="curBookDetail.sellprice < curBookDetail.original_price"
              >
                定价<span
                  style="font-weight: 600; text-decoration: line-through"
                  >¥{{ curBookDetail.original_price }}</span
                >
              </div>

              <!-- 租借 -->
              <div class="rebook_name_tel">
                <div
                  v-if="curBookDetail.is_lease == '1'"
                  style="margin-top: 5px; color: #c00000; font-size: 14px"
                >
                  日租金：{{ curBookDetail.lease_price }}元/天
                </div>
                <div
                  v-if="curBookDetail.is_lease == '1'"
                  style="color: #c00000; font-size: 14px"
                >
                  保证金：￥{{ curBookDetail.deposit }}
                </div>
              </div>

              <div style="color: #c00000; font-weight: 600">
                提成佣金：{{ curBookDetail.commission | toFixed }}%
              </div>
            </div>

            <!-- 购物车模块 -->
            <div class="shop">
              <el-input-number
                v-model="num"
                controls-position="right"
                :step="1"
                step-strictly
                :min="1"
                :max="999"
                size="mini"
              ></el-input-number>
              <div
                style="
                  background-color: rgb(255, 40, 50);
                  font-size: 14px;
                  color: white;
                  margin: 0 2px;
                  padding: 0 5px;
                  display: flex;
                  align-items: center;
                  border-radius: 3px;
                  cursor: pointer;
                "
              >
                <img
                  style="width: 20px; height: 20px; margin-right: 2px"
                  src="@/assets/img/shopcar.png"
                  alt=""
                />加入购物车
              </div>
              <div
                style="
                  font-size: 14px;
                  color: #ff4049;
                  background-color: #ffedee;
                  border: 1px solid #ff4049;
                  border-radius: 3px;
                  padding: 0 5px;
                  cursor: pointer;
                "
              >
                立即购买
              </div>
            </div>
          </div>
        </div>

        <!-- tabs -->
        <div class="tabs" style="margin-top: 10px">
          <el-tabs
            v-model="activeName"
            :stretch="false"
          >
            <el-tab-pane label="基本信息" name="first">
              <div
                class="details-content"
                style="font-size: 15px; color: #80848f"
              >
                <!--  -->
                <div
                  v-if="curBookDetail.author && curBookDetail.author.length > 0"
                >
                  <span>作者：</span>
                  <span
                    v-for="(author, index) in curBookDetail.author"
                    :key="index"
                  >
                    {{ author.author_name }}
                    <span v-if="index !== curBookDetail.author.length - 1"
                      >、</span
                    >
                  </span>
                </div>
                <div v-if="curBookDetail.press_name">
                  <span>出版社：</span> {{ curBookDetail.press_name }}
                </div>
                <div v-if="curBookDetail.publishing_brand">
                  <span>出品方：</span>{{ curBookDetail.publishing_brand }}
                </div>
                <div v-if="curBookDetail.subtitle">
                  <span>副标题：</span>{{ curBookDetail.subtitle }}
                </div>
                <div v-if="curBookDetail.original_title">
                  <span>原作名：</span>{{ curBookDetail.original_title }}
                </div>
                <!--  -->
                <div
                  v-if="
                    curBookDetail.translator &&
                    curBookDetail.translator.length > 0
                  "
                >
                  <span>译者：</span>
                  <span
                    v-for="(translator, index) in curBookDetail.translator"
                    :key="index"
                  >
                    {{ translator }}
                    <span v-if="index !== curBookDetail.translator.length - 1"
                      >、</span
                    >
                  </span>
                </div>
                <div v-if="curBookDetail.publishing_time">
                  <span>出版年：</span
                  >{{ curBookDetail.publishing_time | formatDate }}
                </div>
                <div v-if="curBookDetail.page_num">
                  <span>页数：</span>{{ curBookDetail.page_num }}
                </div>
                <div v-if="curBookDetail.binding_layout">
                  <span>装帧：</span>{{ curBookDetail.binding_layout }}
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="内容详情"
              name="second"
              style="font-size: 15px; color: #787575"
            >
              <div
                style="max-height: 220px; overflow-y: scroll"
                class="no-scroll-bar"
              >
                <div
                  style="max-height: 220px; overflow-y: scroll"
                  v-html="introduce"
                  class="no-scroll-bar"
                ></div>
                <div v-html="curBookDetail.poster_information"></div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="购书须知" name="third">
              <div
                v-html="shoppingNotice"
                style="
                  font-size: 15px;
                  color: #787575;
                  overflow-y: scroll;
                  height: 220px;
                "
                class="no-scroll-bar"
              ></div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 弹窗书单 -->
    <div
      class="mask"
      @click="isShowSpecialList = false"
      v-if="isShowSpecialList"
    >
      <SpecialList :data="specialListItemData" />
    </div>
  </div>
</template>

<script>
// import Fetch from '@/http/fetch.js'
import request from '@/http'
import { MessageBox } from 'element-ui'
import { mapState, mapMutations } from 'vuex'
import Classify from '@/components/corporateAccount/entry-add-classify.vue'
import SpecialList from '@/components/corporateAccount/special-book-list.vue'
export default {
  mounted () {
    this.getUserInfo()
    this.getAboutusInfo()

    // 获取已有词条分类数据
    this.$store.dispatch('corporateAccountClassify/getBookClassifyList')

    // 获取书单数据
    this.getSpecialBookData()
  },
  components: {
    Classify,
    SpecialList
  },
  data () {
    return {
      isContentVisible: false,
      avator: '',
      user_name: '',
      mini_personal_bg: '',

      sell_book_list: [],

      isShowAboutus: false,
      searchInput: '',

      currentTab: '畅销榜', // 当前选中的标签
      books: [
        /* 所有图书的数据数组 */
      ],
      tabs: [
        { name: '畅销榜', filter: 'bestsellers' },
        { name: '新书', filter: 'new' },
        { name: '书单', filter: 'booklist' }
      ],

      isShowPopup: false, // 控制弹窗显示与隐藏
      inputText: '', // 输入框中的文本
      introductionText: '', // 获取的简介（关于我们）

      isShowDetailsPop: false,
      curBookDetail: null,

      activeName: 'first',

      introduce: '', // 图书详情简介
      shoppingNotice: `价格说明<br>
·Bookor价:为商品的销售价，是您最终决定是否购买商品的依据。具体的成交价可能因会员使用优惠券等发生变化，最终以订单结算页价格为准。<br>
·定价:是图书封底定价。<br>
<br>
·划线价:划线价为参考价，划线价格可能是图书封底定价或曾经展示过的销售价等，由于地区、时间的差异化和市场行情波动，划线价可能会与您购物时展示的不一致，该价格仅供您参考。<br>
<br>
·折扣：如无特殊说明，折扣指销售商在原价、或划线价等某一价格基础上计算出的优惠比例或优惠金额；如有疑问，您可在购买前联系销售商进行咨询。<br>
·异常问题:如您发现活动商品销售价或促销信息有异常，请立即联系
我们补正，以便您能顺利购物。`,

      num: 1, // 加购数量

      isShowBtnType: '', // 分类按钮显示隐藏
      // 分类
      showClassifyFirst: false,
      showClassifySecond: false,
      showClassifyBook: false,

      // 分类的pid和id
      pid: null,
      category_id: null,

      isShowEditClasssify: false, // 分类编辑弹窗
      inputBookClassify: '', // 当前选中的分类名称

      specialList: [],
      shareBookList: [], // 书单
      isShowSpecialList: false // 书单详情弹窗显隐
    }
  },
  filters: {
    toFixed (value) {
      return Number(value).toFixed(0)
    },
    formatDate (value) {
      if (!value) return ''
      const date = new Date(value)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      return `${year}-${month.toString().padStart(2, '0')}`
    }
  },
  computed: {
    filteredBooks () {
      // 根据当前标签筛选图书数据
      if (this.currentTab === 'bestsellers') {
        return this.books.filter((book) => book.isBestseller)
      } else if (this.currentTab === 'new') {
        return this.books.filter((book) => book.isNew)
      } else if (this.currentTab === 'booklist') {
        return this.books.filter((book) => book.isInBooklist)
      }
      return this.books // 默认情况下返回所有图书数据
    },
    ...mapState('corporateAccountClassify', {
      bookClassifyList: (state) => state.bookClassifyList,
      sellBookListOrigin: (state) => state.sellBookList,
      sellBookListClassify: (state) => state.sellBookListClassify
    })
  },
  watch: {
    sellBookListClassify (newVal) {
      this.sell_book_list = newVal?.map(this.calculateDiscount)
      // .map(this.addPrefixToBookImage);
    }
  },
  methods: {
    ...mapMutations('corporateAccountClassify', ['SET_SELL_BOOK_LIST']),
    // 计算折扣
    calculateDiscount (item) {
      if (item.sellprice <= item.original_price) {
        const discount =
          ((item.original_price - item.sellprice) / item.original_price) * 10
        // 判断折扣是否有小数位
        if (discount % 1 === 0) {
          return { ...item, discount: discount.toFixed(0) } // 如果没有小数位，则显示整数形式
        } else {
          return { ...item, discount: discount.toFixed(1) } // 如果有小数位，则保留一位小数
        }
      } else {
        return item
      }
    },

    // 获取用户信息
    async getUserInfo () {
      // 获取用户数据
      try {
        const params = {
          member_type: 'self'
        }
        const result = await request('/mobile/member/index', 'post', params)
        this.avator = result.avator
        this.user_name = result.user_name
        this.mini_personal_bg = result.mini_personal_bg.data
        this.sell_book_list = result.sell_book_list?.map(
          this.calculateDiscount
        )
        this.SET_SELL_BOOK_LIST(this.sell_book_list)
      } catch (error) {
        console.error(error)
      }
    },

    // 关于我们show
    handleShowAboutus () {
      this.isShowAboutus = !this.isShowAboutus
    },

    // 搜索输入
    handleSearchInput () {
      const value = this.searchInput
      // 数据源
      const res = this.sellBookListOrigin
      const source = []
      const reg = new RegExp(value, 'i')
      res.forEach((item, index) => {
        if (reg.test(item.book_name)) {
          source.push(item)
        }
      })
      this.source = source
      this.sell_book_list = source
    },
    filterData (tab) {
      this.currentTab = tab.name // 更新当前选中的标签
    },

    // 新增简介
    async addAboutus () {
      if (!this.inputText.trim()) {
        this.$message({
          message: '简介不能为空',
          type: 'error',
          duration: 1000
        })
        this.inputText = ''
        return
      }

      const params = {
        content: this.inputText.trim()
      }
      request('/api/Aboutus/addAboutus', 'post', params).then((result) => {
        this.$message({
          message: '保存简介成功',
          type: 'success',
          duration: 1000
        })
      }).catch((res) => {
        this.$message({
          message: res.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 获取简介
    async getAboutusInfo () {
      const params = {}

      const result = await request('/api/Aboutus/getAboutusInfo', params)

      if (result?.content) {
        this.introductionText = result.content
        this.inputText = result.content
      } else {
        this.introductionText = '暂无简介'
      }
    },

    // 编辑简介
    async updateAboutus () {
      if (this.inputText == this.introductionText) {
        this.inputText = this.introductionText
        return
      }
      const params = {
        content: this.inputText
      }

      request('/api/Aboutus/updateAboutus', params).then((result) => {
        this.$message({
          message: '编辑简介成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    showPopup () {
      this.isShowPopup = true // 点击加号按钮时显示弹窗
    },
    closePopup () {
      this.isShowPopup = false // 关闭弹窗时重置输入框的值
      // this.inputText = "";
    },
    save () {
      // 在这里处理保存逻辑，可以将输入的文本发送给后端进行保存等操作
      // 如果简介内容为：暂无简介
      if (this.introductionText == '暂无简介') {
        this.addAboutus()
      } else {
        this.updateAboutus()
      }
      this.getAboutusInfo()
      this.isShowPopup = false // 保存后关闭弹窗
    },

    // 弹窗商品详情
    handleBookSellDetails (item) {
      this.isShowDetailsPop = true
      this.introduce = ''
      this.curBookDetail = item
      this.activeName = 'first'
      this.shuaxin(this.curBookDetail.book_id)
    },

    // 获取对应书详情
    async shuaxin (bookId, search_click = false) {
      const params = {
        book_id: bookId,
        search_click
      }
      const result = await request('/api/book/bookCoverDetail', params)
      const introduce = result.bookcover?.book_introduce
      if (introduce) {
        const reg = /。/g
        this.introduce = introduce.replace(reg, '。<br>')
      } else {
        this.introduce = '暂无详情'
      }
    },
    // 鼠标悬停展示按钮
    showBtn (type, id) {
      this.isShowBtnType = type
      if (type == 'classifySecond') {
        this.pid = id
      }
      if (type == 'classifyBook') {
        this.category_id = id
      }
    },
    hideBtn () {
      this.isShowBtnType = ''
    },
    // 分类功能
    // 一级分类弹窗展示
    handleShowClassifyFirst () {
      this.showClassifySecond = false
      this.showClassifyBook = false
      this.showClassifyFirst = !this.showClassifyFirst
    },
    handleShowClassifySecond (id) {
      this.pid = id
      this.showClassifyFirst = false

      this.showClassifyBook = false
      this.showClassifySecond = !this.showClassifySecond
    },
    // 关联图书弹窗展示
    handleShowClassifyBook (id) {
      this.category_id = id
      this.showClassifyFirst = false
      this.showClassifySecond = false
      this.showClassifyBook = !this.showClassifyBook
    },

    // 关闭弹窗
    closeClassify () {
      this.showClassifyFirst = false
      this.showClassifySecond = false
      this.showClassifyBook = false
    },

    // 展示某分类下的图书
    handleAssociateBooks (itemSub) {
      this.searchInput = ''
      this.category_id = itemSub.id
      this.$store.dispatch(
        'corporateAccountClassify/getGoodsBookByCategory',
        itemSub.id
      )
    },

    // 不分类展示所有待售图书
    showAllSellBooks () {
      this.category_id = null
      this.searchInput = ''
      this.sell_book_list = this.sellBookListOrigin
    },

    // 编辑&删除分类弹窗
    handleShowEditClassify (item, itemSub) {
      this.isShowEditClasssify = true
      if (itemSub) {
        this.pid = item.firstCategory.id
        this.inputBookClassify = itemSub.name
        this.category_id = itemSub.id
      } else {
        this.category_id = item.firstCategory.id
        this.inputBookClassify = item.firstCategory.name
      }
    },

    closeShowEditClassify () {
      this.isShowEditClasssify = false
      this.pid = null
      this.category_id = null
      this.inputBookClassify = ''
    },

    // 编辑分类名
    async updateClassify () {
      if (!this.inputBookClassify.trim()) {
        this.$message({
          message: '分类名不能为空',
          type: 'error',
          duration: 1000
        })
        return
      }
      const params = {
        name: this.inputBookClassify,
        id: this.category_id,
        pid: this.pid
      }

      request('/api/BookCategory/updateCategory', params).then((result) => {
        this.$message({
          message: '更新分类成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 删除分类
    deleteClassify () {
      MessageBox.confirm('是否删除该分类?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.apiDeleteClassify()
        })
    },
    async apiDeleteClassify () {
      const params = {
        id: this.category_id
      }
      request('/api/BookCategory/deleteCategory', params).then((result) => {
        this.$message({
          message: '删除分类成功',
          type: 'success',
          duration: 1000
        })
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 删除某分类下的图书
    handleDeleteAssociateBooks (item) {
      MessageBox.confirm('是否删除在分类中删除本书?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 用户点击确定按钮时触发的回调函数
          this.apiDeleteAssociateBooks(item)
        })
    },
    async apiDeleteAssociateBooks (item) {
      const params = {
        category_id: this.category_id,
        goods_book_id: item.goods_book_id
      }
      request(
        '/api/BookCategory/deleteGoodsBookCategory',
        params
      ).then((result) => {
        this.$message({
          message: '删除图书成功',
          type: 'success',
          duration: 1000
        })
        this.$store.dispatch(
          'corporateAccountClassify/getGoodsBookByCategory',
          this.category_id
        )
      }).catch((result) => {
        this.$message({
          message: result.message,
          type: 'error',
          duration: 1000
        })
      })
    },

    // 获取单个书单的详情
    async getSpecialListDetail (aggregateId) {
      const params = {
        aggregate_id: aggregateId
      }
      const result = await request('/api/content/getAggregateInfo', params)
      const bookListInfo = result.aggregate_info
      if (
        bookListInfo.describe !== null &&
          bookListInfo.describe !== undefined
      ) {
        bookListInfo.describe = bookListInfo.describe.replace(
          /<img/g,
          "<img style='display:block'"
        )
      }

      let shareBookList = result.books

      if (shareBookList && shareBookList.length > 0) {
        while (shareBookList.length < 50) {
          shareBookList = shareBookList.concat(shareBookList)
        }
      }

      let shareIntro = ''
      if (bookListInfo.describe) {
        shareIntro = this.replaceHtmlToPlainText(bookListInfo.describe)
      }

      return { bookListInfo, shareBookList, shareIntro }
    },
    sliceArray: function (array, size) {
      var result = []
      for (var x = 0; x < Math.ceil(array.length / size); x++) {
        var start = x * size
        var end = start + size
        result.push(array.slice(start, end))
      }
      return result
    },
    replaceHtmlToPlainText (htmlText) {
      var plainText = htmlText.replace(/(<([^>]+)>)/gi, '')
      var entityMap = {
        '&nbsp;': '+',
        '&#160;': '+',
        '&lt;': '<',
        '&#60;': '<',
        '&gt;': '>',
        '&#62;': '>',
        '&amp;': '&',
        '&#38;': '&',
        '&quot;': '"',
        '&#34;': '"',
        '&apos;': "'",
        '&#39;': "'",
        '&cent;': '￠',
        '&#162;': '￠',
        '&pound;': '£',
        '&#163;': '£',
        '&yen;': '¥',
        '&#165;': '¥',
        '&euro;': '€',
        '&#8364;': '€',
        '&sect;': '§',
        '&#167;': '§',
        '&copy;': '©',
        '&#169;': '©',
        '&reg;': '®',
        '&#174;': '®',
        '&trade;': '™',
        '&#8482;': '™',
        '&times;': '×',
        '&#215;': '×',
        '&divide;': '÷',
        '&#247;': '÷'
      }
      var entityStr = ''
      for (var k in entityMap) {
        entityStr += '|' + k
      }
      entityStr = entityStr.slice(1)
      var entityRegx = new RegExp(entityStr, 'g')
      var plainTextResult = plainText.replace(entityRegx, function (matchStr) {
        return entityMap[matchStr]
      })
      return plainTextResult
    },

    // 获取书单列表
    async getSpecialBookData () {
      const params = {
        member_type: 'self'
      }
      const url = '/mobile/member/getSpecialAggregate'
      const resust = await request(url, params)
      const specialList = resust
      // 构建一个 Promise 数组
      const promises = specialList.map((item) => {
        return this.getSpecialListDetail(item.aggregate_id).then((result) => {
          // 将结果保存在对应项里
          item.result = result
        })
      })
      await Promise.all(promises)

      // 完成部分结果处理后即可赋值
      this.specialList = specialList
    },

    // 单击某个书单
    clickSpecialBookListItem (item) {
      this.isShowSpecialList = true
      this.specialListItemData = item
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
.container {
  position: relative;
  .top-box {
    position: relative;
    .background-cover {
      width: 100vw;
      height: 120px;
      object-fit: cover; /* 自动裁剪适应 */
    }
    .avator-box {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      img {
        border-radius: 50%;
        width: 65px;
        height: 65px;
      }

      .user-name {
        text-align: center;
        color: white;
        font-size: 23px;
        font-weight: 600;
      }
    }
  }
  .content-box {
    display: flex;
    height: calc(100vh - 125px);
    .left {
      width: 10vw;
      padding: 2px;
      margin: 0 2px;

      .level1-box {
        @include flex-between;
        background-color: #d9d9d9;
        font-weight: 600;
        padding: 5px 8px;
        margin-bottom: 2px;
        cursor: pointer;
        .add-btn {
          border: 1px solid;
          padding: 2px;
          height: 15px;
          line-height: 13px;
          border-radius: 3px;
          cursor: pointer;
        }
      }
      .aboutus-content {
        padding: 0px 8px;
        overflow-y: scroll;
        max-height: 140px;
        @include no-scrollbar;
        margin-bottom: 2px;
        width: 120px;
        word-wrap: break-word;
      }

      // 图书分类
      .bookClassify {
        position: relative;
        .classifyPopup {
          position: absolute;
          z-index: 1;
        }
        .bookClassifyItem1 {
          border: 1px solid #eeeeee;
          padding: 0 8px 0 15px;
          background: linear-gradient(#ffffff, #f5f5f5);
          font-weight: 600;
          font-size: 15px;
          color: #333333;
          .bookClassifyItem1-title {
            display: flex;
            justify-content: space-between;
            position: relative;
            align-items: center;
          }
        }
      }

      .add-btn-sub2 {
        border: 1px solid;
        padding: 2px;
        height: 13px;
        font-size: 14px;
        line-height: 11px;
        border-radius: 3px;
        font-weight: 600;
        cursor: pointer;
      }
    }
    .right {
      flex: 1;
      padding: 0 15px;
      .search-and-filter-box {
        @include center-y;
        .search-input {
          padding: 5px 11px;
          font-size: 16px;
          border: 1px solid #cacaca;
          border-radius: 4px;
          &::placeholder {
            color: #bfbfbf;

          }
        }
        .tabs {
          display: flex;
          margin-left: 10px;
          background-color: #f7f1ed;
          font-size: 16px;
          font-weight: 600;
          color: #a29b9c;

          .tab {
            margin-right: 10px;
            padding: 7px 8px;
            cursor: pointer;
          }
          .tab-active {
            color: black;
            text-decoration: underline;
            text-underline-offset: 0.2em;
          }
        }
      }

      .sell-book-list-box {
        display: flex;
        flex-wrap: wrap;
        overflow-y: scroll;
        height: 74vh;
        .sell-book-list-item {
          background-color: #d9d9d9;
          padding: 2px 10px;
          width: 120px;
          border-radius: 5px;
          margin: 15px;
          position: relative;
          height: 270px;
          .sell-book-info-box {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 80px;
            overflow-y: scroll;
            &::-webkit-scrollbar {
              display: none;
            }
          }
          .sell-book-title {
            font-size: 14px;
            font-weight: 600;
          }
          .sell-book-tag {
            img {
              width: 42px;
              height: 30px;
              position: absolute;
              right: 1px;
              top: 3px;
            }
            .sell-book-tag-content {
              font-weight: bold;
              font-size: 12px;
              position: absolute;
              right: 4px;
              top: 10px;
              transform: rotate(22deg);
            }
          }
        }
      }
    }
  }
  .mask {
    width: 100vw;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0.3);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
  }

  .details {
    // width: 30vw;
    // height: 70vh;
    width: 380px;
    height: 450px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 15px 12px;

    .details-top {
      display: flex;
      // justify-content: space-between;
      .details-top-right {
        margin-left: 20px;
        flex: 1;
        height: 180px;
        position: relative;
        .shop {
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 26px;
          position: absolute;
          bottom: 0;
          .quantity {
            display: flex;
            align-items: center;
          }

          input {
            width: 50px;
            padding: 5px;
          }

          button {
            margin-left: 10px;
            padding: 5px 10px;
          }
        }
      }
    }
  }
}
.no-scroll-bar::-webkit-scrollbar {
  display: none;
}
</style>

<style lang="scss" scoped>
.aboutus-dialog {
  .el-dialog__header {
    text-align: center;
  }
  .el-dialog__body {
    text-align: center;
    padding: 0px 20px 20px;
  }
  .el-button {
    margin-top: 10px;
  }
}
.el-tabs {
  .el-tabs__header {
    .el-tabs__nav-wrap {
      .el-tabs__nav-scroll {
        .el-tabs__nav {
          .el-tabs__active-bar {
            display: none;
          }
          .el-tabs__item {
            padding: 0 10px;
            text-align: center;
            color: #a6a6b6;
            border: 1px solid #d9d9d9;
            line-height: 34px;
            height: 35px;
            font-weight: 600;
          }
          .el-tabs__item.is-active {
            color: #333f50;
            background: #d9d9d9;
            border: 1px solid #d9d9d9;
          }
        }
      }
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
}

//购物车上下页
.el-input-number {
  width: 55px;
}
.el-input-number.is-controls-right .el-input__inner {
  padding-left: 0px;
  padding-right: 20px;
}
.el-icon-arrow-up:before {
  content: "+";
}
.el-icon-arrow-down:before {
  content: "-";
}
.el-input-number__increase {
  width: 18px !important;
}
.el-input-number__decrease {
  width: 18px !important;
}

//分类
.bookClassify-dialog {
  .el-dialog__wrapper {
    z-index: 1000 !important;
  }
  .el-dialog__header {
    text-align: center;
  }
  .el-dialog__body {
    text-align: center;
    padding: 0px 20px 20px;
  }
  .el-button {
    margin-top: 10px;
  }
}
</style>
