<template>
<div class="edit-padding-box">
  <RichTextEditor
    :note="note"
    :uptitle="editBookorNoteTitle"
    :upcontent="editBookorNoteContent"
    :update="isUpdateBookorNote"
    :paperMp3="paperMp3"
  ></RichTextEditor>
</div>
</template>

<script>
import RichTextEditor from '@/components/editor/rich-text-editor';
import request from '@/http/request';
import { Loading } from 'element-ui';

export default {
  data () {
    return {
      editBookorNoteTitle: '',
      editBookorNoteContent: '',
      paperMp3: null,
      isUpdateBookorNote: false,
      loadingInstance: null,
      note: null
    }
  },
  components: {
    RichTextEditor
  },
  created () {
    this.$watch('$route.query', (newQuery) => {
      const origin = this.$route.query.origin
      if (origin === 'Crawling') {
        this.editBookorNoteContent = this.$store.state.urlContent
        return
      }
      if (!newQuery?.paperId) {
        this.editBookorNoteContent = ''
      }
      this.getWordMes(newQuery.paperId)
    })
  },
  props: ['noteContent'],
  methods: {
    getWordMes (paperId) {
      this.loadingInstance = Loading.service({
        fullscreen: true, // 是否全屏遮罩
        text: '加载中...', // 显示的文本
        background: 'rgba(0, 0, 0, 0.7)' // 遮罩的背景颜色
      })
      request('/api/Paper/getPaperInfo', {
        paper_id: paperId
      }).then((result) => {
        this.note = result
        this.editBookorNoteTitle = result.paper_title
        this.editBookorNoteContent = result.paper_url
        this.paperMp3 = result.paper_mp3
        this.loadingInstance.close()
        this.$emit('getArticleData', {
          ...result,
          type: 'note',
          url: result.paper_url
        })
      }).catch(() => {
        this.loadingInstance.close()
      })
    },
    share() {
      this.paperId = this.$route.query.paperId
      const userId = this.$route.query.userId
      const userinfo = JSON.parse(localStorage.getItem('userinfo'))
      if (!userinfo) {
        return
      }
      if (userId && userId !== userinfo?.userId) {
        this.$confirm('有用户给你分享了一篇文章，是否接受该文章', '提示', {
          confirmButtonText: '接受',
          cancelButtonText: '不接受',
          type: 'warning'
        }).then(() => {
          // 说明是别人分享给我的
          request('/api/Paper/paperShare', {
            paper_id: this.paperId,
            member_ids: userinfo.userid.toString()
          }).then((res) => {
            this.dialogTableVisible = false
          }).catch((err) => {
            this.$message.error(err.message)
          })
        })
      }
    }
  },
  mounted() {
    const origin = this.$route.query.origin
    if (origin === 'Crawling') {
      this.editBookorNoteContent = this.$store.state.urlContent
      return
    }
    const paperId = this.$route.query.paperId
    if (!paperId) {
      if (this.noteContent) {
        this.editBookorNoteContent = this.noteContent
      }
      return
    }
    this.getWordMes(paperId)
    this.share()
  }
}
</script>
<style lang="scss">
.edit-padding-box {
  position: relative;
  float: left;
  height: 100%;
  padding: 5px 0;
  width: 80vw;
  display: flex;
  justify-content: center;
}
@media (max-width: 600px) {
  .edit-padding-box {
    height: 100vh;
    width: 80vw;
  }
  .change-to-word {
    display: none;
  }
}
</style>
