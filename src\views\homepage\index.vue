<template>
  <div class="container">
    <div
      @mouseenter="showLayouts = true"
      @mouseleave="showLayouts = false"
      class="global-cover"
      :style="{ height: '10vh', width: '100vw', zIndex: '1000' }"
    >
      <transition name="slide">
        <div v-show="showLayouts"><Layouts /></div>
      </transition>
    </div>

    <div class="content" v-show="isShowPreContent">
      <div class="mycanvas global-cover" style="overflow: hidden">
        <Galaxy />
      </div>
      <div
        class="global-cover"
        style="left: 50%; transform: translateX(-50%); overflow: hidden"
      >
        <Splash />
      </div>
    </div>
    <el-dialog :title="title" custom-class="vision-dialog"  width="1000px" :visible.sync="dialogTableVisible" @close="closeVision">
      <div class="video-con">
        <video id="visionDom" controls autoplay :src="url" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import store from '@/store'
import Galaxy from '@/components/homepage/galaxy'
import CommitInfo from '@/components/homepage/commit-info.vue'
import Splash from '@/components/splash-screen/indexHomepage_new.vue'
import Layouts from '@/layouts/BasicLayoutday.vue'
export default {
  data () {
    return {
      isShowPreContent: true,
      isShowCommitInfo: false,
      isShowEmailBox: false,
      isShowJoinBox: true,
      email: '',
      showLayouts: false,
      dialogTableVisible: false,
      url: 'https://bookor-application.oss-cn-beijing.aliyuncs.com/public_video.mp4',
      title: '企业愿景'
    }
  },
  components: {
    CommitInfo,
    Galaxy,
    Splash,
    Layouts
  },
  methods: {
    openVision (type) {
      if (type) {
        this.title = '使用说明'
        this.url = 'https://oss.bookor.com.cn/video/article.mp4'
      } else {
        this.title = '企业愿景'
        this.url = 'https://bookor-application.oss-cn-beijing.aliyuncs.com/public_video.mp4'
      }
      this.dialogTableVisible = true
    },
    closeVision () {
      document.querySelector('#visionDom').pause()
    },
    showEmailBox () {
      this.isShowEmailBox = true
      this.isShowJoinBox = false
    },

    handleEnter () {
      const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/
      if (regex.test(this.email)) {
        this.isShowCommitInfo = true
        this.isShowPreContent = false
      } else {
        this.$message.error({
          message: '请输入正确的邮箱地址',
          duration: 1500
        })
        this.email = ''
      }
    },

    getCodeMes () {
      const code = this.getQueryString('code')
      if (code) {
        this.getLoginMes(code)
      }
    },
    getQueryString (name) {
      var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
      var r = window.location.search.substr(1).match(reg)
      if (r != null) {
        return unescape(r[2])
      }
      return ''
    },
    getLoginMes (code) {
      const params = {
        code: code
      }
      const queryParams = new URLSearchParams(params)
      fetch('https://api.bookor.com.cn/index.php/mobile/login/wx_sian_login', {
        method: 'POST',
        body: queryParams
      })
        .then((response) => {
          if (response.ok) {
            return response.json()
          }
          throw new Error('Network response was not ok.')
        })
        .then((data) => {
          if (data.code === 100) {
            this.$message({
              message: '参数错误',
              type: 'error',
              duration: 1000
            })
            window.history.replaceState(
              null,
              null,
              window.location.href.split('?')[0] + window.location.hash
            )
            this.$router.push('/')
          }
          if (data.code === 200) {
            if (typeof data.result === 'string') {
              // 是字符串表示是扫码结果，用户未注册过
              this.$message({
                message: '未绑定微信',
                type: 'error',
                duration: 1000
              })
              window.history.replaceState(
                null,
                null,
                window.location.href.split('?')[0] + window.location.hash
              )
              this.$router.push('/')
            } else {
              localStorage.setItem('authorization', data.result.key)
              localStorage.setItem('userinfo', JSON.stringify(data.result))
              this.$message({
                message: '登录成功',
                type: 'success',
                duration: 1000
              })
              setTimeout(async () => {
                window.history.replaceState(
                  null,
                  null,
                  window.location.href.split('?')[0] + window.location.hash
                )
                this.$router.push('/index')
                await store.dispatch('user/getUserInfoMore')
                // 调用 getUserInfoEnterprise，并等待其完成
                await store.dispatch('user/getUserInfoEnterprise')
                this.$store.commit('user/SET_USERINFO')
              }, 2000)
            }
          }
        })
        .catch((error) => {
          console.error('There was a problem with the network request:', error)
        })
    }
  },
  mounted () {
    this.getCodeMes()
    const urlParams = new URLSearchParams(window.location.href.split('?')[1])
    const showVision = urlParams.get('showVision')
    if (showVision) {
      this.dialogTableVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/global.scss";
.video-con{
  width: 100%;
  display: flex;
  justify-content: center;
  video{
    width: 750px;
  }
}
.container {
  // width: 100vw;
  height: 100vh;
  position: relative;
  .top-bar {
    @include flex-between;
    width: 100vw;
    height: 10vh;
    background-color: white;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.6); /* 在这里添加阴影 */
    margin: 0 0 10px 0;
    position: absolute;
    top: 0;
    padding: 0 3vw;
    box-sizing: border-box;

    .nav-box {
      display: flex;
      .nav-item {
        margin: 0 4vw 0 0;
        padding: 0 2vw;
        border: 1px solid;
        border-radius: 5px;
        font-size: 14px;
      }
      .nav-lang {
      }
    }
  }
  .content {
    padding-top: 10vh;
    height: 90vh;
    width: 100vw;
    position: relative;
    .top-section {
      @include flex-ccc;
      justify-content: flex-end;
      height: 55vh;
      padding-bottom: 40px;
      box-sizing: border-box;
      .top-sent {
        font-size: 55px;
        font-weight: 900;
        font-family: system-ui;
        text-shadow: 1px 0 0 BLACK;
        color: #9ab6c8;
        @include zindex1;
        span {
          font-family: math;
          font-weight: 100;
          font-size: 70px;
        }
      }
      .bottom-sent {
        @include zindex1;
        font-size: 55px;
        span {
          color: #9dc3e6;
          font-weight: 900;
        }
      }
    }
    .bottom-section {
      height: 20vh;
      @include flex-ccc;

      .join {
        // height: 35vh;
        @include flex-ccc;
        justify-content: space-around;
        flex-direction: initial;
        @include zindex1;
        .join-test {
          color: white;
          background-color: black;
          border-radius: 10px;
          padding: 4px 20px;
          font-size: 14px;
          border: 1px white solid;
          box-shadow: 1px 2px 1px rgba(0, 0, 0, 0.5);
          cursor: pointer;
          margin-right: 5vw;
        }
        .join-newread {
          color: black;
          background-color: #d9d9d9;
          border-radius: 10px;
          padding: 4px 14px;
          font-size: 14px;
          border: 1px #000 solid;
          box-shadow: 1px 2px 1px rgba(0, 0, 0, 0.5);
        }
      }
      .email-box {
        @include zindex1;
        color: white;
        background-color: black;
        padding: 4px 14px;
        border-radius: 10px;
        border: 1px white solid;
        box-shadow: 1px 2px 1px rgba(0, 0, 0, 0.5);
        font-size: 14px;
        text-align: center;
      }
      .email-box::placeholder {
        text-align: center; /* 让 placeholder 居中 */
        color: #d7d7d7;
      }
    }
  }
}

.global-cover {
  position: absolute;
  top: 0;
  // width: 100vw;
  // height: 100vh;
}

.slide-enter-active,
.slide-leave-active {
  transition: all s;
}

.slide-enter,
.slide-leave-to {
  transform: translate(0, -10vh);
  // opacity: 0;
}

.slide-enter-to,
.slide-leave {
  transform: translate(0, 0);
  // opacity: 1;
}

.video-con{
  width: 100%;
  display: flex;
  justify-content: center;
  video{
    // width: 750px;
    width: 850px;
  }
}
.vision-dialog{
  background-color: #000;
  box-shadow: 0 0 10px 4px rgba(255, 255, 255, 1);
}
.el-dialog__title{
  color: #fff;
}
</style>
