window.onload = function () {
  var _uuid

  // TEXT HIGHTLIGHT
  var hltr = new TextHighlighter(document.body, {
    onAfterHighlight: function (range, hlts, keyword) {
      serialized = hltr.serializeHighlights(keyword)
      saveLocalstorage(serialized)
    }
  }); var serialized

  // 取消高亮
  // $(document).on('click', '.highlighted', function (e) {
  //     var item = $(this).attr('class').replace('highlighted ', '');
  //     var target = $('.' + item);
  //     var length = target.length;
  //     for (var i = 0; i < length; i++) {
  //         hltr.removeHighlights(target[i]);
  //     }
  //     serialized = hltr.serializeHighlights();
  //     saveLocalstorage(serialized);

  //     // 스토리지에서 value 체크
  //     var _data = localStorage.getItem(PDFFILENAME);

  //     if (_data) {
  //         if (_data == '[]') {
  //             localStorage.removeItem(PDFFILENAME);
  //         }
  //     }

  //     // console.log('[Remove Target highlight]');
  // });
  const loading_overlay_default =
  {
    background: 'rgba(255, 255, 255, 0.1)',
    image: "<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'><circle r='80' cx='500' cy='90'/><circle r='80' cx='500' cy='910'/><circle r='80' cx='90' cy='500'/><circle r='80' cx='910' cy='500'/><circle r='80' cx='212' cy='212'/><circle r='80' cx='788' cy='212'/><circle r='80' cx='212' cy='788'/><circle r='80' cx='788' cy='788'/></svg>",
    imageAnimation: '2000ms rotate_right',
    // imageAutoResize         : true,
    imageResizeFactor: 0.2,
    imageColor: '#00cbb4'
  }

  // 开始的loading
  $('#outerContainer').LoadingOverlay('show', loading_overlay_default)

  async function main () {
    // 启动
    // var _data = localStorage.getItem('default_pdf')

    // if (_data == '[]' || _data == null) {
    //   // await searchingArray();
    //   console.log('Load Search Array')
    // } else {
    await localStorageGetItem()
    // }
  }

  // 강제로 검색 결과 표시
  setTimeout(function () {
    try {
      main()
    } catch (error) {
      console.warn('无法进行。error: ', error)
    }
  }, 1000)

  // ----------------------------------------------------------------------------- //

  // async function searchingArray () {
  //   if (TESTKEYWORDARRAY != '') {
  //     for (let i = 0; i < TESTKEYWORDARRAY.length; i++) {
  //       setTimeout(function timer () {
  //         // 添加高亮
  //         searchKeyword(TESTKEYWORDARRAY[i])
  //         console.log(TESTKEYWORDARRAY[i])
  //       }, i * 1000)
  //     }
  //     setTimeout(function () {
  //       $('#outerContainer').LoadingOverlay('hide', true)
  //     }, TESTKEYWORDARRAY.length * 1000)
  //   }
  // };

  // 마우스 다운 했을 때 페이지 저장
  // $(document).on('mousedown', '.page', function () {
  //     SELECTEDPAGE = $(this).attr('data-page-number');
  // });

  // $(document).on('mousewheel', 'body', function () {
  //   hltr.deserializeHighlights(serialized)
  // })

  // 검색 입력 이벤트
  async function searchKeyword (keyword) {
    await bindEvent(keyword)
  };

  async function bindEvent (keyword) {
    try {
      if (typeof window.PDFViewerApplication.findController !== 'undefined') {
        window.PDFViewerApplication.findBar.open()
        $(window.PDFViewerApplication.findBar.findField).val(keyword)
        PDFViewerApplication.findController.executeCommand('find', {
          query: keyword,
          phraseSearch: false,
          caseSensitive: false,
          entireWord: false,
          highlightAll: true,
          findPrevious: undefined
        })
        window.PDFViewerApplication.findBar.dispatchEvent('')
        _uuid = makeUUID()
      }
      // TODO 要加上的
      await makeHighlightSpan()

      setTimeout(function () {
        // 역직렬화
        // console.log(serialized)
        hltr.deserializeHighlights(serialized)
      }, 500)
    } catch (error) {
      return 'fail...'
    }
  };

  async function makeHighlightSpan () {
    setTimeout(function () {
      if (!$('span.selected').hasClass('middle') && !$('span.selected').hasClass('end')) {

      } else {
        // begin, end 클래스 정리
        _uuid = makeUUID()
        var begin_html = ''
        var findBeginClass = $('.textLayer .highlight.begin')
        var getBeginText = findBeginClass.text()
        begin_html += '<span class="highlight selected ' + _uuid + '">' + getBeginText + '</span>'
        $('.textLayer .highlight.begin').parent().html(begin_html)
        var end_html = ''
        var findEndClass = $('.textLayer .highlight.end')
        var getEndText = findEndClass.text()
        end_html += '<span class="highlight selected ' + _uuid + '">' + getEndText + '</span>'
        $('.textLayer .highlight.end').parent().html(end_html)

        // middle 클래스 길이
        var middleSpanLength = $('span.middle').length
        // console.log('middleSpanLength :', middleSpanLength);

        for (var i = 0; i < middleSpanLength; i++) {
          var _html = ''
          // middle 클래스 텍스트
          var _text = $('span.middle')[i].innerText
          // 텍스트 뽑아내서 html 새로 생성
          // _html += '<span class="highlight selected">' + _text + '</span>';
          _html += '<span class="highlight">' + _text + '</span>'
          // 생성한 html을 자식요소로 append
          // $('span.middle')[i].innerHTML = _html;
          document.getElementsByClassName('middle')[i].innerHTML = _html
        }

        $('span.middle.highlight.selected').removeClass('highlight middle selected')

        findSearchResultSelectionHighlight()
      }
    }, 1000)
  };

  async function findSearchResultSelectionHighlight () {
    setTimeout(function () {
      var searchResultLengt = $('.highlight').length

      // console.log('searchResultLengt :', searchResultLengt);

      if (searchResultLengt == 0) {
        findSearchResultSelectionHighlight()
        return
      };

      // // 검색 결과 위치값 저장 - 배열에 오브젝트 형태로.
      for (var i = 0; i < searchResultLengt; i++) {
        var _thisText = $('span.highlight')[i].innerHTML
        var _thisWidth = $('span.highlight')[i].getBoundingClientRect().width
        var _thisHeigt = $('span.highlight')[i].getBoundingClientRect().height

        // 해당 위치에 highlighted 생성
        var el = document.createElement('span')
        el.setAttribute('class', 'highlighted ' + _uuid)
        el.setAttribute('style', 'position: absolute; cursor : pointer; background-color: red;' +
                  'width:' + _thisWidth + 'px; height:' + _thisHeigt + 'px;' +
                  'display:table;')
        el.setAttribute('data-highlighted', 'true')
        el.setAttribute('data-timestamp', new Date().getTime())
        el.innerText = _thisText

        $('.highlight')[i].parentElement.appendChild(el)

        // console.log(el)
      };

      // 직렬화
      serialized = hltr.serializeHighlights()
      $('.highlight').remove()
    }, 200)
  };

  function makeUUID () {
    function s4 () { return ((1 + Math.random()) * 0x10000 | 0).toString(16).substring(1) }
    return s4() + s4() + '_' + s4() + '_' + s4() + '_' + s4() + '_' + s4() + s4() + s4()
  };

  function saveLocalstorage (serialized) {
    var _data = serialized
    var key = 'default_pdf'
    localStorage.setItem(key, _data)
  };

  async function localStorageGetItem () {
    var _data = localStorage.getItem('default_pdf')
    serialized = _data

    setTimeout(function () {
      try {
        hltr.deserializeHighlights(serialized)
        $('#outerContainer').LoadingOverlay('hide', true)
      } catch (error) {
        console.warn('无法加载。error : ', error)
      }
    }, 1000)
  };
}
