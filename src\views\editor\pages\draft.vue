
<template>
<div class="draft-canvas-con">
  <div class="main">
    <Draft
      :draftId="draftId"
       @editDraftId="editDraftId"
       @move="move"
    />
  </div>

  <el-dialog title="登陆" :visible.sync="dialogTableVisible"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escap="false"
    >
    <div class="login-container">
      <h3>您的好友向你推荐了一个文档，请登陆或者注册之后查看</h3>
      <LoginFrom :type="1" @loginSuccess="loginSuccess"></LoginFrom>
    </div>
  </el-dialog>
</div>

</template>

<script>
import request from '@/http/request'
import Draft from '../components/draft'
import LoginFrom from '@/views/login/index.vue'

export default {
  data () {
    return {
      dialogTableVisible: false,
      draftId: null
    }
  },
  components: {
    Draft,
    LoginFrom
  },
  methods: {
    loginSuccess () {
      this.dialogTableVisible = false
    },
    editDraftId (val) {
      this.draftId = val
      this.$router.push({
        name: 'draft',
        query: {
          id: val
        }
      })
    },
    move () {
      this.draftId = this.$route.query.id
      const userinfo = JSON.parse(localStorage.getItem('userinfo'))
      if (!userinfo) {
        this.dialogTableVisible = true
      }
    }
  },

  created () {
    this.$watch('$route.query', (newQuery) => {
      this.draftId = newQuery.id
    })
  },

  mounted () {
    this.draftId = this.$route.query.id
    const userId = this.$route.query.userId
    const userinfo = JSON.parse(localStorage.getItem('userinfo'))
    if (!userinfo) {
      return
    }
    if (userId && userId !== userinfo?.userId) {
      this.$confirm('有用户给你分享了一篇白板，是否接受该白板', '提示', {
        confirmButtonText: '接受',
        cancelButtonText: '不接受',
        type: 'warning'
      }).then(() => {
        // 说明是别人分享给我的
        request('/api/Draft/draftShare', {
          draft_id: this.draftId,
          member_ids: userinfo.userid.toString()
        }).then((res) => {
          this.dialogTableVisible = false
        }).catch((err) => {
          this.$message.error(err.message)
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>

.login-container{
  text-align: center;
  h3{
    font-size: 16px;
    margin-bottom: 20px;
    color: #000;
  }
  :deep(.register){
    width: auto;
    height: auto;
    padding-top: 0;
  }
}
</style>
