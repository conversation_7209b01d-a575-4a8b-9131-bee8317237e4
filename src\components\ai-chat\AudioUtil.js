
// 来用处理整个音频的播放
class AudioUtil {
  constructor (callback) {
    // debugger
    this.currentIndex = 0 // 当前播放音频
    this.audio = document.createElement('audio')
    this.list = [] // 存放所有的音频url
    this.playing = false // 视频播放状态
    this.init(callback)
  }

  init (callback) {
    this.audio.addEventListener('ended', () => {
      if (this.list.length > this.currentIndex) {
        this.currentIndex += 1
        if (this.currentIndex < this.list.length) {
          this.audio.pause()
          this.playing = false
          this.play(callback)
          // callback(this.currentIndex)
        } else {
          this.playing = false
          this.currentIndex = this.list.length
          callback(this.currentIndex)
        }
      }
    })
  }

  play (callback) {
    if (!this.playing) {
      if (this.list[this.currentIndex] === '') {
        this.currentIndex += 1
      }
      this.audio.src = this.list[this.currentIndex]
      this.playing = true
      this.audio.play()
      callback(this.currentIndex)
    }
  }

  add (url) {
    this.list.push(url)
  }
}

export default AudioUtil
