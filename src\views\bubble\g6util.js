export const clusterList = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
export const colorList = ['#1783FF', '#00C9C9', '#F08F56', '#D580FF', '#FADC19', '#9FDB1D', '#F7BA1E', '#14C9C9', '#3491FA', '#F5319D', '#165DFF', '#722ED1', '#D91AD9', '#14C9C9', '#3491FA', '#E8FFEA', '#165DFF', '#722ED1', '#D91AD9', '#14C9C9', '#3491FA', '#E8FFEA', '#165DFF', '#722ED1', '#D91AD9', '#14C9C9', '#3491FA', '#E8FFEA', '#165DFF', '#722ED1', '#D91AD9'];

let groupedNodes = {};
export const converNodeData = (data) => {
  let nodeData = [];
  let edgeData = [];
  const list = data[0].friend_list[0];
  nodeData.push({
    id: `${data.book_keywords_id}`,
    label: data.keywords,
    data: {
      cluster: 'aaa'
    },
    style: {
      size: 35
    }
  })
  for(let i = 0; i < list.length; i+=1) {
    const item = list[i];
    nodeData.push({
      id: `${item.book_keywords_id}`,
      label: item.keywords,
      data: {
        cluster: clusterList[i]
      },
      style: {
        size: 30
      }
    });
    edgeData.push({
      source: `${data.book_keywords_id}`,
      target: `${item.book_keywords_id}`,
      type: 'line'
    });
    if(item.label_list) {
      for(let j = 0; j < item.label_list.length; j+=1) {
        const labelItem = item.label_list[j];
        const nodeId = `${item.book_keywords_id}${labelItem.id}`;
        nodeData.push({
          id: nodeId,
          label: labelItem.name,
          data: {
            cluster: clusterList[i]
          },
          style: {
            size: 20
          }
        });
        edgeData.push({
          source: `${item.book_keywords_id}`,
          target: nodeId,
          type: 'line'
        });
      }
    }
  }
  return {
    nodeData,
    edgeData
  }
}

const getOtherList = (list, pList) => {
  let nodeData = []
  for(let i = 0; i < list.length; i+=1) {
    const item = list[i];
    const nodeId = `${item.id}`;
    if(!pList.includes(nodeId)) {
      nodeData.push({
        id: nodeId,
        label: item.name,
      });
    }
  }
  return nodeData;
}

const getNodeList = (item, i) => {
  let nodeData = []
  if(item.label_list) {
    for(let j = 0; j < item.label_list.length; j+=1) {
      const labelItem = item.label_list[j];
      const nodeId = `${item.book_keywords_id}${labelItem.id}`;
      nodeData.push({
        id: nodeId,
        label: labelItem.name,
        data: {
          originId: labelItem.id,
          cluster: clusterList[i]
        }
      });
    }
  }
  return nodeData
}


export const getBiData = (data) => {
  let nodeData = [];
  const current = data.keywords_info
  const labelList = data.label_all_list

  const list = current.friend_list.flat();
  let currentArr = getNodeList(current, list.length)
  let aa = []
  list.forEach((l) => {
    aa = aa.concat(l.label.split(','))
  })
  let p = new Set([...current?.label?.split(',') || [], ...aa]);
  const pList = Array.from(p);
  nodeData = nodeData.concat(currentArr)
  let aaa = getOtherList(labelList, pList);
  nodeData = nodeData.concat(aaa)
  for(let i = 0; i < list.length; i+=1) {
    const item = list[i];
    let arr = getNodeList(item,i)
    nodeData = nodeData.concat(arr)
  }

  // 找出具有相同label的节点
  const intersectionNodes = [];
  const labelMap = new Map();
  const uniqueNodeData = [];  // 新增：存储不重复label的节点
  // 首先按label对节点进行分组
  nodeData.forEach(node => {
    if(node.label) {
      if (!labelMap.has(node.label)) {
        labelMap.set(node.label, []);
        uniqueNodeData.push(node);  // 只添加第一次出现的节点
      }
      labelMap.get(node.label).push(node);
    }
  });

  // 找出具有相同label的节点组
  labelMap.forEach((nodes, label) => {
    if (nodes.length > 1) {
      intersectionNodes.push({
        label,
        id: `${nodes[0].data.originId}`,
        nodes: nodes,
        data: {
          originId: nodes[0].data.originId,
          cluster: clusterList[Math.floor(Math.random() * clusterList.length)]
        }
      });
    }
  });
  // 对 uniqueNodeData 进行去重，确保不包含 intersectionNodes 中的节点
  const uniqueLabels = intersectionNodes.map(node => node.label);
  const filteredIntersectionNodes = uniqueNodeData.filter(item => {
    return uniqueLabels.indexOf(item.label) === -1
  });
  return {
    nodeData,
    intersectionNodes,
    uniqueNodeData: filteredIntersectionNodes  // 返回不含重复label的节点数组
  };
}

export const getComboData = (data) => {
  let nodeData = [];
  let combosData = [];
  const list = data[0].friend_list[0];
  for(let i = 0; i < list.length; i+=1) {
    const item = list[i];
    nodeData.push({
      id: `${item.book_keywords_id}`,
      label: item.keywords,
      combo: clusterList[i],
      style: {
        size: 50
      }
    })
    combosData.push({
      id: clusterList[i],
      data: {
        label: item.keywords,
      },
      style: {
        padding: 40,
        x: 0,
        y: 0,
      }
    })
    if(item.label_list) {
      for(let j = 0; j < item.label_list.length; j+=1) {
        const labelItem = item.label_list[j];
        const nodeId = `${item.book_keywords_id}${labelItem.id}`;
        nodeData.push({
          id: nodeId,
          label: labelItem.name,
          combo: clusterList[i],
          style: {
            size: 50
          }
        });
      }
    }
  }
  return {nodeData, combosData};
}

const createStyle = (baseColor) => ({
  fill: baseColor,
  stroke: baseColor,
  labelFill: '#fff',
  labelPadding: 2,
  labelBackgroundFill: baseColor,
  labelBackgroundRadius: 5,
});

const groupedNodesByCluster = (acc, node) => {
  const arr = [];
  node.nodes.forEach(item => {
    if(item.data?.cluster === acc) {
      arr.push(item.id);
    } else if(item?.nodes?.length){
      const idx = item.nodes?.findIndex((child) => child?.data?.cluster === acc)
      if(idx !== -1) {
        arr.push(item.id)
      }
    }
  })
  return arr;
}

const someLabel = (labels, labelList) => {
  for(let item of labelList) {
    if(labels.includes(item.name)) {
      return true
    }
  }
  return false
}

export const getPlugins = (data, graphData) => {
  const plugins = [];
  const current = data.keywords_info
  const list = current.friend_list.flat();
  const total = list.length
  const labels = current?.label_list?.map((item) => item.name) || []
  const group = groupedNodesByCluster(clusterList[total], graphData);
  plugins.push({
    key: `bubble-sets-${clusterList[total]}`,
    type: 'bubble-sets',
    members: group,
    labelText: current.keywords,
    ...createStyle(colorList[total]),
  })
  for(let i = 0; i < list.length; i+=1) {
    const item = list[i];
    if(someLabel(labels, item.label_list)) {
      const groupedNodes = groupedNodesByCluster(clusterList[i], graphData);
      if(groupedNodes.length) {
        plugins.push({
          key: `bubble-sets-${clusterList[i]}`,
          type: 'bubble-sets',
          members: groupedNodes,
          labelText: item.keywords,
          ...createStyle(colorList[i]),
        })
      }
    }
  }
  debugger
  return plugins;
}


export const toFriendList = (data) => {
  return data.map((item) => {
    const label = Array.from(new Set(item.tag_ids.split(','))).join(',');
    return [
      {
        book_keywords_id: item.keywordsinfo.book_keywords_id,
        keywords: item.keywordsinfo.keywords,
        label,
        label_list: item.tag_list.map((item) => ({
            id: item.mk_tag_id,
            name: item.tag
        }))
      }
    ]
  })
}
export const getLabelList = (data) => {
  // 使用 Map 来去重，以 id 为键
  const uniqueMap = new Map();

  data.forEach((item) => {
    const id = item.tags.mk_tag_id;
    // 只有当 id 不存在时才添加到 Map 中
    if (!uniqueMap.has(id)) {
      uniqueMap.set(id, {
        id: id,
        name: item.tags.tag
      });
    }
  });

  // 将 Map 的值转换为数组
  return Array.from(uniqueMap.values());
}
